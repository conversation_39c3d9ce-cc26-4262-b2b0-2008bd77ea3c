package io.noties.markwon.core.factory;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

import io.noties.markwon.MarkwonConfiguration;
import io.noties.markwon.RenderProps;
import io.noties.markwon.SpanFactory;
import io.noties.markwon.core.CoreProps;
import io.noties.markwon.core.spans.HeadingSpan;

public class HeadingSpanFactory implements SpanFactory {

    private static final int MAX_CACHE_SIZE = 100;
    private final Map<RenderProps, HeadingSpan> cacheMap;
    private final ReentrantLock lock = new ReentrantLock();

    public HeadingSpanFactory() {
        // 使用 LinkedHashMap 实现 LRU 缓存
        this.cacheMap = new LinkedHashMap<RenderProps, HeadingSpan>(
            MAX_CACHE_SIZE + 1, // 初始容量
            0.75f,             // 负载因子
            true              // 访问顺序
        ) {
            @Override
            protected boolean removeEldestEntry(Map.Entry<RenderProps, HeadingSpan> eldest) {
                return size() > MAX_CACHE_SIZE;
            }
        };
    }

    @Nullable
    @Override
    public Object getSpans(@NonNull MarkwonConfiguration configuration, @NonNull RenderProps props) {
        lock.lock();
        try {
            // 尝试从缓存中获取
            HeadingSpan span = cacheMap.get(props);
            if (span != null) {
                return span;
            }

            // 创建新的 span
            span = new HeadingSpan(
                configuration.theme(),
                CoreProps.HEADING_LEVEL.require(props)
            );

            // 添加到缓存
            cacheMap.put(props, span);
            return span;
        } finally {
            lock.unlock();
        }
    }
}
