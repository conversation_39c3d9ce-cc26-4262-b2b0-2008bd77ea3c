apply plugin: 'com.android.library'

android {
    namespace 'io.noties.markwon'
    compileSdk rootProject.ext.android.compileSdk

    defaultConfig {
        minSdk rootProject.ext.android.minSdk
        targetSdk rootProject.ext.android.targetSdk
        versionCode 4
        versionName "1.0.4"
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    api rootProject.ext.dependencies.appcompat
    api('org.commonmark:commonmark:0.22.0')

    implementation 'androidx.core:core-ktx:1.8.0'
    implementation 'androidx.annotation:annotation:1.3.0'
    //不能升到23，有个bug requireNonNullElseGet，android sdk 需要30以上才能用
//    api('org.commonmark:commonmark-ext-gfm-tables:0.22.0') {
//        exclude group: 'com.atlassian.commonmark', module: 'commonmark'
//        exclude group: 'io.noties.markwon', module: 'core'
//    }    //markdown 文本sdk地址
    //https://github.com/noties/Markwon

    //markdown 核心地址
    //https://github.com/commonmark/commonmark-java
    //implementation 'org.commonmark:commonmark:0.24.0'
    //implementation 'org.commonmark:commonmark-ext-autolink:0.24.0'
    //implementation 'org.commonmark:commonmark-ext-footnotes:0.24.0'
    //implementation 'org.commonmark:commonmark-ext-gfm-strikethrough:0.24.0'
    //implementation 'org.commonmark:commonmark-ext-gfm-tables:0.24.0'
    //implementation 'org.commonmark:commonmark-ext-heading-anchor:0.24.0'
    //implementation 'org.commonmark:commonmark-ext-image-attributes:0.24.0'
    //implementation 'org.commonmark:commonmark-ext-ins:0.24.0'
    //implementation 'org.commonmark:commonmark-ext-task-list-items:0.24.0'
    //implementation 'org.commonmark:commonmark-ext-yaml-front-matter:0.24.0'
}
