package com.lgfzd.base.base;

import android.Manifest;
import android.app.Dialog;
import android.content.Context;
import android.content.pm.ActivityInfo;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.PermissionChecker;
import androidx.viewbinding.ViewBinding;

import com.gyf.immersionbar.ImmersionBar;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.XXPermissions;
import com.lgfzd.base.R;
import com.lgfzd.base.XBaseApp;
import com.lgfzd.base.utils.WatermarkUtils;
import com.lgfzd.base.utils.XAppUtil;
import com.lgfzd.base.utils.XDialogUtils;
import com.lgfzd.base.utils.XPermissionUtil;
import com.lgfzd.base.view.XToolbar;

import java.util.ArrayList;
import java.util.List;


/**
 * 基本Activity
 * 根据需要申请权限页面
 */
public abstract class XBase3Activity<T extends ViewBinding> extends AppCompatActivity {

    protected XToolbar toolbar;
    protected Context mContext;
    protected T binding;
    protected final int PERMISSION_REQUEST_CODE = 0x831;//权限请求码
    private Dialog dialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.mContext = this;
        binding = getViewBinding();
        if (binding != null) {
            setContentView(binding.getRoot());
            WatermarkUtils.showWatermarkIfNeeded(this);
        }
        if (isStatusBarEnabled()) {
            createStatusBarConfig();
        }
        XBaseApp.getActivities().add(this);
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        toolbar = findViewById(R.id.toolbar);
        if (toolbar != null) {
            setSupportActionBar(toolbar);
            Drawable upArrow = getResources().getDrawable(R.drawable.ic_black_back);
            getSupportActionBar().setHomeAsUpIndicator(upArrow);
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        isOpenGps();
        initView();
        getData();
        initListener();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 绑定视图
     */
    protected abstract T getViewBinding();

    /**
     * 页面渲染
     */
    protected abstract void initView();

    /**
     * 事件监听
     */
    protected abstract void initListener();

    /**
     * 获取数据
     */
    protected abstract void getData();


    /**
     * 是否使用沉浸式状态栏
     */
    protected boolean isStatusBarEnabled() {
        return true;
    }


    /**
     * 已经授权全部权限
     */
    protected void onPermissionOpenAll() {
    }

    /**
     * 检查是否全部授权权限
     *
     * @param permissions 权限组
     */
    protected void requestPermission(String[] permissions) {
        XXPermissions.with(this).permission(permissions).request(new OnPermissionCallback() {
            @Override
            public void onGranted(List<String> permissions, boolean all) {
                if (all) {
                    //获取全部权限成功
                    onPermissionOpenAll();
                } else {
                    //获取部分权限成功，但部分权限未正常授予
                    List<String> list = new ArrayList<>();
                    for (String permission : permissions) {
                        if (PermissionChecker.checkSelfPermission(XBase3Activity.this, permission) != PermissionChecker.PERMISSION_GRANTED) {
                            list.add(permission);
                        }
                    }
                    int size = list.size();
                    if (size > 0) {
                        ActivityCompat.requestPermissions(XBase3Activity.this, list.toArray(new String[size]), PERMISSION_REQUEST_CODE);
                    }
                }
            }

            @Override
            public void onDenied(List<String> permissions, boolean never) {
                if (never) {
                    StringBuffer builder = new StringBuffer();
                    for (int i = 0; i < permissions.size(); i++) {
                        // 如果是被永久拒绝就跳转到应用权限系统设置页面
                        if (permissions.get(i).contains("LOCATION")) {
                            builder.append("定位");
                        }
                        if (permissions.get(i).contains("READ_PHONE_STATE")) {
                            builder.append("手机状态");
                        }
                        if (permissions.get(i).contains("RECORD_AUDIO")) {
                            builder.append("录音");
                        }
                        if (permissions.get(i).contains("WRITE_EXTERNAL_STORAGE") || permissions.contains("READ_EXTERNAL_STORAGE")) {
                            builder.append("存储");
                        }
                        if (permissions.get(i).contains("CAMERA")) {
                            builder.append("相机");
                        }
                    }
                    if (dialog == null) {
                        dialog = XDialogUtils.showPermissionsErrorDialog(XBase3Activity.this, TextUtils.isEmpty(builder.toString()) ? "" : builder.toString(), (code, id) -> {
                            if (TextUtils.isEmpty(code)) {
                                XXPermissions.startPermissionActivity(XBase3Activity.this, permissions);
                            }
                            dialog = null;
                        });
                        dialog.show();
                    }
                }
            }
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode != PERMISSION_REQUEST_CODE) return;
        List<String> list = new ArrayList<>();
        for (String permission : permissions) {
            if (permission.equals(Manifest.permission.READ_PHONE_STATE) && Build.VERSION.SDK_INT >= 29) {
                //如果是安卓10则不检验这个权限
                continue;
            }
            //再一次检测权限是否被授权成功
            if (PermissionChecker.checkSelfPermission(this, permission) != PermissionChecker.PERMISSION_GRANTED) {
                list.add(permission);
            }
        }
        if (list.isEmpty()) {
            onPermissionOpenAll();
        } else {
            // 如果是被永久拒绝就跳转到应用权限系统设置页面
            String info = "";
            if (list.contains("LOCATION")) {
                info = "定位";
            } else {
                info = "部分";
            }
            XDialogUtils.showPermissionsErrorDialog(XBase3Activity.this, info, (code, id) -> XXPermissions.startPermissionActivity(XBase3Activity.this, list));
        }
    }

    /**
     * 初始化沉浸式状态栏
     */
    protected void createStatusBarConfig() {
        ImmersionBar.with(this)
                // 默认状态栏字体颜色为黑色
                .transparentStatusBar().statusBarDarkFont(true)
                // 指定导航栏背景颜色
                .navigationBarColor(R.color.white)
                // 状态栏字体和导航栏内容自动变色，必须指定状态栏颜色和导航栏颜色才可以自动变色
                .autoDarkModeEnable(true, 0.2f).init();
    }

    /**
     * 判断是否打开定位服务
     */
    protected void isOpenGps() {
        if (!XPermissionUtil.isLocServiceEnable(this)) {
            XDialogUtils.showOpenGps(this, (code, value) -> {
            });
        }
    }

    @Override
    protected void onDestroy() {
        WatermarkUtils.removeWatermark(this);
        // 在最后一个Activity销毁时清理网络监听
        if (XBaseApp.getActivities().size() <= 1) {
            WatermarkUtils.cleanup(this);
        }
        super.onDestroy();
        XAppUtil.closeSoftInput(this);
        XBaseApp.getActivities().remove(this);
    }


}