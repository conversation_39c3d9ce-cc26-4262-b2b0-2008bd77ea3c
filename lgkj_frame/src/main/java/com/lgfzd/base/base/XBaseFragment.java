package com.lgfzd.base.base;

import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.PermissionChecker;
import androidx.fragment.app.Fragment;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.XXPermissions;
import com.lgfzd.base.utils.XDialogUtils;

import org.xutils.x;

import java.util.ArrayList;
import java.util.List;

/**
 * Xutils懒加载
 */
public abstract class XBaseFragment extends Fragment {

    protected boolean isViewInitiated;
    protected boolean isVisibleToUser;
    protected boolean isDataInitiated;
    private boolean injected;
    protected final int PERMISSION_REQUEST_CODE = 0x831;//权限请求码
    private Dialog dialog;


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        injected = true;
        return x.view().inject(this, inflater, container);
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        isViewInitiated = true;
        prepareFetchData();
        if (!injected) {
            x.view().inject(this, this.getView());
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        this.isVisibleToUser = isVisibleToUser;
        prepareFetchData();
    }

    public boolean prepareFetchData() {
        return prepareFetchData(true);
    }

    public boolean prepareFetchData(boolean forceUpdate) {
        if (isVisibleToUser && isViewInitiated && (!isDataInitiated || forceUpdate)) {
            lazyLoad();
            isDataInitiated = true;
            return true;
        }
        return false;
    }

    protected abstract void lazyLoad();

    /**
     * 已经授权全部权限
     */
    protected void onPermissionOpenAll() {
    }

    /**
     * 检查是否全部授权权限
     *
     * @param permissions 权限组
     */
    protected void requestPermission(String[] permissions) {
        XXPermissions.with(this).permission(permissions).request(new OnPermissionCallback() {
            @Override
            public void onGranted(List<String> permissions, boolean all) {
                Log.i("jujue", "通过: " + permissions.toString() + "--" + all);
                if (all) {
                    //获取全部权限成功
                    onPermissionOpenAll();
                } else {
                    //获取部分权限成功，但部分权限未正常授予
                    List<String> list = new ArrayList<>();
                    for (String permission : permissions) {
                        if (PermissionChecker.checkSelfPermission(getActivity(), permission) != PermissionChecker.PERMISSION_GRANTED) {
                            list.add(permission);
                        }
                    }
                    int size = list.size();
                    if (size > 0) {
                        ActivityCompat.requestPermissions(getActivity(), list.toArray(new String[size]), PERMISSION_REQUEST_CODE);
                    }
                }
            }

            @Override
            public void onDenied(List<String> permissions, boolean never) {
                Log.i("jujue", "onDenied: " + permissions.toString() + "--" + never);
                if (never) {
                    // 如果是被永久拒绝就跳转到应用权限系统设置页面--弹窗提示
                    StringBuffer builder = new StringBuffer();
                    for (int i = 0; i < permissions.size(); i++) {
                        // 如果是被永久拒绝就跳转到应用权限系统设置页面
                        if (permissions.get(i).contains("LOCATION")) {
                            builder.append("定位");
                        }
                        if (permissions.get(i).contains("READ_PHONE_STATE")) {
                            builder.append("手机状态");
                        }
                        if (permissions.get(i).contains("RECORD_AUDIO")) {
                            builder.append("录音");
                        }
                        if (permissions.get(i).contains("WRITE_EXTERNAL_STORAGE") || permissions.contains("READ_EXTERNAL_STORAGE")) {
                            builder.append("存储");
                        }
                        if (permissions.get(i).contains("CAMERA")) {
                            builder.append("相机");
                        }
                    }
                    if (dialog == null) {
                        dialog = XDialogUtils.showPermissionsErrorDialog(getActivity(), TextUtils.isEmpty(builder.toString()) ? "" : builder.toString(), (code, id) -> {
                            if (TextUtils.isEmpty(code)) {
                                XXPermissions.startPermissionActivity(getActivity(), permissions);
                            }
                            dialog = null;
                        });
                        dialog.show();
                    }
                }
            }
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        List<String> list = new ArrayList<>();
        for (String permission : permissions) {
            //再一次检测权限是否被授权成功
            if (PermissionChecker.checkSelfPermission(getActivity(), permission) != PermissionChecker.PERMISSION_GRANTED) {
                list.add(permission);
            }
        }
        if (list.isEmpty()) {
            onPermissionOpenAll();
        } else {
            // 如果是被永久拒绝就跳转到应用权限系统设置页面
            StringBuffer builder = new StringBuffer();
            for (String permission : permissions) {
                if (permission.contains("LOCATION")) {
                    builder.append("定位");
                }
                if (permission.contains("READ_PHONE_STATE")) {
                    builder.append("手机状态");
                }
                if (permission.contains("RECORD_AUDIO")) {
                    builder.append("录音");
                }
                if (permission.contains("WRITE_EXTERNAL_STORAGE") || permission.contains("READ_EXTERNAL_STORAGE")) {
                    builder.append("存储");
                }
                if (permission.contains("CAMERA")) {
                    builder.append("相机");
                }
            }
            if (dialog == null) {
                dialog = XDialogUtils.showPermissionsErrorDialog(getActivity(), builder.toString(), (code, id) -> {
                    if (TextUtils.isEmpty(code)) {
                        XXPermissions.startPermissionActivity(getActivity(), permissions);
                    }
                    dialog = null;
                });
                dialog.show();
            }
        }
    }
}
