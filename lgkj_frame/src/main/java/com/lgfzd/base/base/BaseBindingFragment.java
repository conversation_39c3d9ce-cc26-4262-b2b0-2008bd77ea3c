package com.lgfzd.base.base;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewbinding.ViewBinding;

/**
 * Fragment基础来
 * viewBing
 */
public abstract class BaseBindingFragment<T extends ViewBinding> extends Fragment {

    private T binding;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        // 调用onCreateViewBinding方法获取binding
        binding = inflateViewBinding(inflater, container);
        initView();
        initData();
        initListener();
        return binding.getRoot();
    }

    // 子类使用该方法来使用binding
    public T getBinding() {
        return binding;
    }

    // 由子类去重写
    protected abstract T inflateViewBinding(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent);

    /**
     * 页面渲染
     */
    protected abstract void initView();

    /**
     * 事件监听
     */
    protected abstract void initListener();

    /**
     * 获取数据
     */
    protected abstract void initData();


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 引用置空处理
        binding = null;
    }
}