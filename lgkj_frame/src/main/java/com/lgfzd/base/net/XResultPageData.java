package com.lgfzd.base.net;

import java.util.List;

/**
 * 数据解析基础头部-分页
 */
public class XResultPageData<T> {

    private int total;
    private int current;
    private int currentNum;
    private List<T> records;
    private int totalPage;
    private ExtraBean extra;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getCurrent() {
        return current;
    }

    public void setCurrent(int current) {
        this.current = current;
    }

    public int getCurrentNum() {
        return currentNum;
    }

    public void setCurrentNum(int currentNum) {
        this.currentNum = currentNum;
    }

    public List<T> getRecords() {
        return records;
    }

    public void setRecords(List<T> records) {
        this.records = records;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public ExtraBean getExtra() {
        return extra;
    }

    public void setExtra(ExtraBean extra) {
        this.extra = extra;
    }

    public static class ExtraBean {
        private String notice;
        private String startTime;
        private String endTime;
        private RwRwlxEnumBean rwRwlxEnum;

        public String getNotice() {
            return notice;
        }

        public void setNotice(String notice) {
            this.notice = notice;
        }

        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public RwRwlxEnumBean getRwRwlxEnum() {
            return rwRwlxEnum;
        }

        public void setRwRwlxEnum(RwRwlxEnumBean rwRwlxEnum) {
            this.rwRwlxEnum = rwRwlxEnum;
        }

        public static class RwRwlxEnumBean {
            private String code;
            private String name;
            public String getCode() {
                return code;
            }

            public void setCode(String code) {
                this.code = code;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }
        }
    }
}
