package com.lgfzd.base.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.annotation.Nullable;
import androidx.annotation.StyleRes;

/**
 * 自定义标题栏
 */
public class XToolbar extends androidx.appcompat.widget.Toolbar {

    private TextView mTitleTextView;
    private Context mContext;
    private int mTitleTextColor = 0xffffffff;
    private float titleSize = 0;
    private float mTitleMaxWidthScale = 0.5f; // 默认标题最大宽度为屏幕宽度的60%
    private float mOriginalTextSize = 18f; // 保存原始字体大小
    private float mMinTextSize = 12f; // 最小字体大小

    public XToolbar(Context context) {
        super(context);
        init(null);
    }

    public XToolbar(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(attrs);
    }

    public XToolbar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(attrs);
    }

    private void init(AttributeSet attrs) {
        mContext = getContext();
        mTitleTextView = new TextView(mContext);
        // 超出一行后省略号
        mTitleTextView.setSingleLine(true);
        mTitleTextView.setEllipsize(android.text.TextUtils.TruncateAt.END);
        LayoutParams lp = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
        // 此处相当于布局文件中的Android:layout_gravity属性
        lp.gravity = Gravity.CENTER;
        mTitleTextView.setLayoutParams(lp);
        mTitleTextView.setGravity(Gravity.CENTER);
        mTitleTextView.setTextSize(18);
        mOriginalTextSize = mTitleTextView.getTextSize();
        if (attrs != null) {
            TypedArray a = mContext.obtainStyledAttributes(attrs,
                    androidx.appcompat.R.styleable.Toolbar);
            int c = a.getColor(androidx.appcompat.R.styleable.Toolbar_titleTextColor, mTitleTextColor);
            mTitleTextView
                    .setTextColor(a.getColor(androidx.appcompat.R.styleable.Toolbar_titleTextColor, mTitleTextColor));
            mTitleTextView.setText(a.getString(androidx.appcompat.R.styleable.Toolbar_title));
        } else {
            mTitleTextView.setTextColor(mTitleTextColor);
        }
        if (titleSize != 0) {
            mTitleTextView.setTextSize(titleSize);
        }
        addView(mTitleTextView);
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);
        adjustTitleMaxWidth();
        adjustTitleTextSize();
    }

    private void adjustTitleMaxWidth() {
        if (mTitleTextView != null) {
            int screenWidth = getResources().getDisplayMetrics().widthPixels;
            int maxWidth = (int) (screenWidth * mTitleMaxWidthScale);
            mTitleTextView.setMaxWidth(maxWidth);
        }
    }

    private void adjustTitleTextSize() {
        if (mTitleTextView != null) {
            String text = mTitleTextView.getText().toString();
            float textSize = mOriginalTextSize;
            int maxWidth = mTitleTextView.getMaxWidth();

            while (textSize > mMinTextSize) {
                mTitleTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
                float textWidth = mTitleTextView.getPaint().measureText(text);
                if (textWidth <= maxWidth) {
                    break;
                }
                textSize -= 0.5f;
            }
        }
    }

    public void setTitleSize(float size) {
        if (mTitleTextView != null) {
            mTitleTextView.setTextSize(size);
        }
        titleSize = size;
    }

    public void setTitle(CharSequence title) {
        if (mTitleTextView != null) {
            mTitleTextView.setText(title);
            mTitleTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, mOriginalTextSize);
            adjustTitleTextSize();
        }
    }

    /**
     * Sets the text color, size, style, hint color, and highlight color
     * from the specified TextAppearance resource.
     */
    public void setTitleTextAppearance(Context context, @StyleRes int resId) {
        if (mTitleTextView != null) {
            mTitleTextView.setTextAppearance(context, resId);
        }
    }

    /**
     * Sets the text color of the title, if present.
     *
     * @param color The new text color in 0xAARRGGBB format
     */
    public void setTitleTextColor(@ColorInt int color) {
        mTitleTextColor = color;
        if (mTitleTextView != null) {
            mTitleTextView.setTextColor(color);
        }
    }

    public void setTitleMaxWidth(float scaleFactor, int screenWidth) {
        if (mTitleTextView != null) {
            int maxWidth = (int) (screenWidth * scaleFactor);
            mTitleTextView.setMaxWidth(maxWidth);
        }
    }

    public void setTitleMaxWidthScale(float scale) {
        mTitleMaxWidthScale = scale;
        adjustTitleMaxWidth();
    }

    public void setMinTextSize(float minTextSize) {
        mMinTextSize = minTextSize;
    }
}