<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base"
        layout="@layout/base_top_ground_img" />

    <LinearLayout
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:paddingLeft="12dp"
            android:paddingTop="5dp"
            android:paddingRight="12dp"
            android:paddingBottom="5dp">

            <EditText
                android:id="@+id/list_search"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:background="@drawable/bg_gray_big"
                android:drawableLeft="@mipmap/search"
                android:drawablePadding="14dp"
                android:hint="输入关键词"
                android:imeOptions="actionSearch"
                android:lines="1"
                android:paddingLeft="@dimen/dp_12"
                android:paddingRight="@dimen/dp_12"
                android:singleLine="true"
                android:textSize="14sp" />
        </LinearLayout>

        <com.lgfzd.base.view.recycle.XRefreshLayout
            android:id="@+id/refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent">


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <ImageView
                    android:id="@+id/tv_no_data"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:src="@mipmap/nothing"
                    android:visibility="gone" />
            </RelativeLayout>
        </com.lgfzd.base.view.recycle.XRefreshLayout>
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>