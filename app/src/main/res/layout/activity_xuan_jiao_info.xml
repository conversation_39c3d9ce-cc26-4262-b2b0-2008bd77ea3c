<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/ll_v"
    android:background="@drawable/bg_gradient_blue_write">

    <RelativeLayout
        android:id="@+id/re"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_40"
        android:paddingLeft="@dimen/dp_12"
        android:paddingRight="@dimen/dp_12">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:paddingRight="@dimen/dp_10"
            android:src="@drawable/ic_write_back" />

        <TextView
            android:id="@+id/tv_top_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="签到页面"
            android:textColor="@color/white"
            android:textSize="18sp" />
    </RelativeLayout>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:tabIndicatorColor="@color/white"
        app:tabIndicatorFullWidth="false"
        app:tabIndicatorHeight="2dp"
        app:tabRippleColor="@color/color_tran"
        app:tabSelectedTextColor="@color/white"
        app:tabTextAppearance="@style/tab_text_size_stytle"
        app:tabTextColor="@color/white" />

    <com.lgfzd.base.view.XViewPager
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>