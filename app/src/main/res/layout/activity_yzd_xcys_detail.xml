<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <androidx.core.widget.NestedScrollView
        style="@style/ll_m"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">


        <LinearLayout style="@style/ll_v">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="25dp"
                    android:paddingTop="@dimen/dp_15"
                    android:paddingRight="25dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="巡查对象类型："
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_xcdx_type"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="-"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="25dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="25dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="巡查对象名称："
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_xcdx_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="-"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="25dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="25dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="巡查内容：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_xc_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="-"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="25dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="25dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="巡查地点：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_xc_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="-"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="25dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="25dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="巡查日期：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_xc_date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="-"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="25dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="25dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="巡查留痕：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <com.linggan.jd831.widget.XGridViewForScrollView
                        android:id="@+id/grid_xc_photos"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:horizontalSpacing="@dimen/dp_10"
                        android:numColumns="4"
                        android:verticalSpacing="@dimen/dp_10" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="25dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="25dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="巡查人：\u3000\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_xc_user"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="-"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>


        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>