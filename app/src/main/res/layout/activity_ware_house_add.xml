<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <androidx.core.widget.NestedScrollView
        style="@style/ll_m"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout style="@style/ll_v">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="仓库名称"
                android:textColor="@color/color_33"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/et_warehouse_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:background="@null"
                android:hint="请输入仓库名，限20个中文"
                android:maxLength="20"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"

                android:drawablePadding="3dp"
                android:text="储存化学品"
                android:textColor="#333333"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_chemical_storage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:drawableRight="@mipmap/ic_right"
                android:hint="请初始化储存信息"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawablePadding="3dp"
                android:text="摄像机接入"
                android:textColor="#333333"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_operation_machine"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:drawableRight="@mipmap/ic_right"
                android:hint="请选择需已接入摄像机"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

<!--            <TextView-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginLeft="30dp"-->
<!--                android:layout_marginTop="5dp"-->
<!--                android:layout_marginRight="30dp"-->
<!--                android:text="摄像机接入信息可先选择否，线下联系客服新增"-->
<!--                android:textColor="#FF0000"-->
<!--                android:textSize="12sp" />-->

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <LinearLayout
                android:id="@+id/ll_machine_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"

                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@mipmap/ic_start"
                    android:text="摄像机类型"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_operation_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="30dp"
                    android:drawableRight="@mipmap/ic_right"
                    android:hint="请选择操作机类型"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="30dp"
                    android:background="@color/color_line" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@mipmap/ic_start"
                    android:text="设备序列号"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <EditText
                    android:id="@+id/et_device_serial"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="30dp"
                    android:background="@null"
                    android:hint="请输入设备序列号"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="30dp"
                    android:background="@color/color_line" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@mipmap/ic_start"
                    android:text="视图库序列号"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <EditText
                    android:id="@+id/et_view_library_serial"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="30dp"
                    android:background="@null"
                    android:hint="请输入视图库序列号"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="30dp"
                    android:background="@color/color_line" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@mipmap/ic_start"
                    android:text="国际库序列号"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <EditText
                    android:id="@+id/et_international_library_serial"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="30dp"
                    android:background="@null"
                    android:hint="请输入国际库序列号"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="30dp"
                    android:background="@color/color_line" />

                <TextView
                    android:layout_width="wrap_content"

                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@mipmap/ic_start"
                    android:text="国际通道号"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <EditText
                    android:id="@+id/et_international_channel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="30dp"
                    android:background="@null"
                    android:hint="请输入国际通道号"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="30dp"
                    android:background="@color/color_line" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:drawableLeft="@mipmap/ic_start"
                android:layout_marginTop="10dp"
                android:text="仓库状态"
                android:textColor="#333333"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_warehouse_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:drawableRight="@mipmap/ic_right"
                android:hint="请选择仓库状态"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <Button
                android:id="@+id/bt_sure"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginRight="30dp"
                android:layout_marginBottom="30dp"
                android:background="@drawable/bg_color_main"
                android:text="确定"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
