<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/bg_white_five"
    android:orientation="vertical"
    tools:viewBindingIgnore="true">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:paddingLeft="14dp"
        android:paddingTop="14dp"
        android:paddingRight="14dp"
        android:textColor="@color/color_26"
        android:textSize="16sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_info"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="14dp"
        android:textColor="@color/color_33"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/color_line" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/bt_cancel"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="取消"
            android:textColor="#666666"
            android:textSize="15sp" />

        <View
            android:layout_width="@dimen/dp_1"
            android:layout_height="match_parent"
            android:background="@color/color_line" />

        <TextView
            android:id="@+id/bt_sure"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="确定"
            android:textColor="@color/color_main"
            android:textSize="15sp" />
    </LinearLayout>
</LinearLayout>