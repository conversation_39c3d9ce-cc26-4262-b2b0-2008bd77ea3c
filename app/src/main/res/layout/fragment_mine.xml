<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_color"
    android:orientation="vertical"
    tools:viewBindingIgnore="true">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@mipmap/ic_msg_top"
        android:orientation="vertical"
        android:paddingLeft="16dp"
        android:paddingTop="45dp"
        android:paddingRight="16dp"
        android:paddingBottom="16dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="15dp"
            android:paddingTop="22dp"
            android:paddingRight="@dimen/dp_15"
            android:paddingBottom="30dp">

            <com.makeramen.roundedimageview.RoundedImageView
                android:id="@+id/iv_head"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:scaleType="centerCrop"
                android:src="@mipmap/ic_work_head"
                app:riv_corner_radius="@dimen/dp_10" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_5"
                android:layout_toRightOf="@+id/iv_head"
                android:textColor="#262626"
                android:textSize="18sp" />

            <TextView
                android:id="@+id/tv_mobile"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_name"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_5"
                android:layout_toRightOf="@+id/iv_head"
                android:textColor="#8c8c8c"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_work_unit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_mobile"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_5"
                android:layout_toRightOf="@+id/iv_head"
                android:textColor="#8c8c8c"
                android:textSize="14sp" />
        </RelativeLayout>
    </RelativeLayout>

    <ImageView
        android:id="@+id/imageView2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:src="@mipmap/ic_me_center" />

    <Button
        android:id="@+id/bt_web"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerInParent="true"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="180dp"
        android:background="@drawable/bg_white_five"
        android:padding="@dimen/dp_10"
        android:text="测试跳转到公众号"
        android:textColor="@color/color_red_low"
        android:textSize="14sp"
        android:visibility="gone" />

    <Button
        android:id="@+id/bt_feedback"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_above="@+id/bt_exit"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/bg_white_five"
        android:padding="@dimen/dp_10"
        android:text="意见反馈"
        android:textColor="@color/ps_color_black"
        android:textSize="14sp" />

    <Button
        android:id="@+id/bt_exit"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_centerInParent="true"
        android:layout_above="@+id/tv_location"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/bg_white_five"
        android:padding="@dimen/dp_10"
        android:text="退出登录"
        android:textColor="@color/color_red_low"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_location"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/tv_version"
        android:layout_marginTop="15dp"
        android:layout_centerHorizontal="true"
        android:text=""
        android:textColor="#8c8c8c"
        android:paddingHorizontal="5dp"
        android:textSize="13sp" />

    <TextView
        android:id="@+id/tv_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:padding="5dp"
        android:text="版本号：1.0.0"
        android:textColor="#8c8c8c"
        android:textSize="13sp" />
</RelativeLayout>