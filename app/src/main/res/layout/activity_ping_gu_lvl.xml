<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base_top"
          layout="@layout/base_top_ground_img" />

    <androidx.core.widget.NestedScrollView
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout style="@style/ll_v">


            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="30dp"
                android:paddingBottom="@dimen/dp_15"
                android:text="风险等级"
                android:textColor="#262626"
                android:textSize="24sp"
                android:textStyle="bold" />

            <com.lgfzd.base.view.recycle.XRecyclerView
                android:id="@+id/recycler"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="30dp"
                android:paddingBottom="@dimen/dp_10"
                android:text="定级原因："
                android:textColor="@color/color_26"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/et_case"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginRight="30dp"
                android:layout_marginBottom="30dp"
                android:background="@drawable/bg_gray_line_bf"
                android:gravity="top"
                android:hint="请输入定级原因"
                android:paddingLeft="@dimen/dp_5"
                android:paddingTop="@dimen/dp_12"
                android:paddingRight="@dimen/dp_5"
                android:paddingBottom="@dimen/dp_12"
                android:textColor="@color/color_26"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>