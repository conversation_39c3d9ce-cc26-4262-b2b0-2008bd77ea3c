<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/iv_head"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:scaleType="centerCrop"
            android:src="@mipmap/ic_place_img"
            app:riv_corner_radius="@dimen/dp_5" />

        <LinearLayout
            android:id="@+id/lin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_toRightOf="@+id/iv_head"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#262626"
                android:textSize="20sp" />

            <TextView
                android:id="@+id/tv_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_5"
                android:background="@drawable/bg_blue_low_five"
                android:paddingLeft="5dp"
                android:paddingTop="@dimen/dp_1"
                android:paddingRight="5dp"
                android:paddingBottom="@dimen/dp_1"
                android:textColor="#0053E2"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_address"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_5"
                android:drawableRight="@mipmap/oc_daohang"
                android:gravity="center_vertical"
                android:textColor="#bfbfbf"
                android:textSize="14sp" />
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>