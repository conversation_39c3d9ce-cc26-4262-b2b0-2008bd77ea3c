<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/ll_v"
    android:background="@color/white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="90dp"
        android:background="@mipmap/bg_top_img"
        android:gravity="center_vertical"
        android:paddingTop="30dp">

        <include
            android:id="@+id/top"
            layout="@layout/base_search_top" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/bg_color"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/dp_12"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_10">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_area"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableRight="@mipmap/ic_right_blue"
                android:singleLine="true"
                android:textColor="#ff0053e2"
                android:textSize="13sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#ff8c8c8c"
            android:textSize="13sp" />
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@color/bg_color">

        <com.lgfzd.base.view.recycle.XRecyclerView
            android:id="@+id/recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/iv_no_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:src="@mipmap/nothing"
            android:visibility="gone" />
    </RelativeLayout>

    <Button
        android:id="@+id/bt_submit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="15dp"
        android:background="@drawable/bg_color_main"
        android:text="@string/sure"
        android:textColor="@color/white"
        android:textSize="15sp"
        android:visibility="gone" />
</LinearLayout>