<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:visibility="gone">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="120dp"
            android:text="执行时间："
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lin_yyxq"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="120dp"
            android:text="原因详情"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_yyxq"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="120dp"
            android:text="二级管控地："
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_ejgkd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lin_jdcs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="120dp"
            android:text="戒毒场所"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_jd_cs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="备注："
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_remark"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>
</LinearLayout>