<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="@dimen/dp_12"
    android:layout_marginRight="@dimen/dp_12"
    android:layout_marginBottom="@dimen/dp_10"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_po"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="车辆1"
        android:textColor="#8c8c8c"
        android:textSize="14sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/bg_gray_line_five"
        android:orientation="vertical"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="10dp"
            android:text="车辆信息"
            android:textColor="#ff262626"
            android:textSize="14sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingLeft="10dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="品牌：\u3000"
                android:textColor="@color/color_26"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_pp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#262626"
                android:textSize="14sp" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingLeft="10dp"
            android:paddingBottom="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="车牌号："
                android:textColor="@color/color_26"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_cph"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#262626"
                android:textSize="14sp" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/lin_hc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:background="@color/color_line" />


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="10dp"
                android:paddingTop="@dimen/dp_10"
                android:paddingBottom="10dp"
                android:text="核查结果"
                android:textColor="#262626"
                android:textSize="14sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="是否本人车辆："
                    android:textColor="@color/color_26"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_use"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#262626"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lin_zx"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingTop="@dimen/dp_10"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="是否报废："
                    android:textColor="@color/color_26"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_bf"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#262626"
                    android:textSize="14sp" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
