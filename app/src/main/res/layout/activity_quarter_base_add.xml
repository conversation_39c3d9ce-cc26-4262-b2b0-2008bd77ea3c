<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base"
        layout="@layout/base_top_ground_img" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nestedScrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            style="@style/ll_v"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <TextView
                android:id="@+id/add_tips"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#FFF9F5"
                android:text="注意，思想汇报可督促吸毒人员在互助家园上自主完成"
                android:textColor="#EE4F49"
                android:textSize="14sp"
                android:paddingVertical="10dp"
                android:paddingHorizontal="12dp"
                android:visibility="gone"
                android:textStyle="bold" />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="22dp"
                android:paddingBottom="11dp"
                android:paddingHorizontal="24dp"
                android:text="基础信息"
                android:textColor="#333333"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/base_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <include
                    android:id="@+id/quarter_info_name"
                    layout="@layout/quarter_info_item" />

                <include
                    android:id="@+id/quarter_info_card"
                    layout="@layout/quarter_info_item" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:background="#F5F5F5" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="24dp">

                <TextView
                    android:id="@+id/quarter_summary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="季度小结"
                    android:textColor="#333333"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/quarter_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textSize="14sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/edit_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <EditText
                    android:id="@+id/et_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="24dp"
                    android:layout_marginRight="24dp"
                    android:background="@null"
                    android:hint="请输入季度小结内容"
                    android:maxLength="1000"
                    android:paddingTop="@dimen/dp_15"
                    android:paddingBottom="@dimen/dp_15"
                    android:textColor="@color/color_33"
                    android:textColorHint="#bfbfbf"
                    android:visibility="gone"
                    android:textSize="14sp" />
            </LinearLayout>

<!--            <View-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="1dp"-->
<!--                android:background="#EEEEEE" />-->

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <Button
                android:id="@+id/bt_sure"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_margin="30dp"
                android:background="@drawable/bg_color_main"
                android:text="确定"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>


</androidx.coordinatorlayout.widget.CoordinatorLayout>