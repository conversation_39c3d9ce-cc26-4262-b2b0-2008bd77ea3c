<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@mipmap/bg_top_img"
    android:gravity="center_vertical"
    android:minHeight="90dp"
    app:elevation="0dp">

    <com.lgfzd.base.view.XToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:layout_marginTop="@dimen/dp_10"
        app:popupTheme="@style/AppTheme.PopupOverlay"
        app:titleTextColor="@color/color_33">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center">

            <TextView
                android:id="@+id/tv_bt_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/yu_jing_cen"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_17" />

            <TextView
                android:id="@+id/tv_bt_title1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_bt_title"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp_1"
                android:gravity="center"
                android:textColor="@color/black"
                android:textSize="11sp" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/bt_right"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="right|center"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:paddingRight="15dp">

            <ImageView
                android:id="@+id/iv_right"
                android:layout_width="wrap_content"
                android:layout_height="match_parent" />

            <TextView
                android:id="@+id/tv_right"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="8dp"
                android:gravity="center"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_15" />
        </LinearLayout>
    </com.lgfzd.base.view.XToolbar>
</com.google.android.material.appbar.AppBarLayout>