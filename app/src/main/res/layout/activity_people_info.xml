<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/ll_m">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/mScrollView"
        style="@style/ll_m"
        android:scrollbars="none">

        <LinearLayout
            style="@style/ll_v"
            android:focusable="true"
            android:focusableInTouchMode="true">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="250dp">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="220dp"
                    android:scaleType="centerCrop"
                    android:src="@mipmap/ic_peo_info" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_marginLeft="@dimen/dp_12"
                    android:layout_marginTop="@dimen/dp_15"
                    android:layout_marginRight="@dimen/dp_12"
                    android:layout_marginBottom="@dimen/dp_12">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="40dp"
                        app:cardCornerRadius="@dimen/dp_10"
                        app:cardElevation="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="130dp"
                            android:orientation="vertical"
                            android:paddingTop="15dp"
                            android:paddingRight="@dimen/dp_10"
                            android:paddingBottom="20dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/tv_name"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:textColor="#ff262626"
                                    android:textSize="17sp" />

                                <TextView
                                    android:id="@+id/bt_edit"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:drawableRight="@mipmap/ic_right_blue"
                                    android:drawablePadding="@dimen/dp_8"
                                    android:text="编辑"
                                    android:textColor="#ff0053e2"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />
                            </LinearLayout>

                            <TextView
                                android:id="@+id/tv_id_card"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_10"
                                android:text="证件号："
                                android:textColor="#8c8c8c"
                                android:textSize="13sp" />

                            <LinearLayout
                                android:id="@+id/bt_phone"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_8"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="电话：\u3000"
                                    android:textColor="#8c8c8c"
                                    android:textSize="13sp" />

                                <TextView
                                    android:id="@+id/tv_phone"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:textColor="#0053E2"
                                    android:textSize="13sp" />
                            </LinearLayout>
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <com.makeramen.roundedimageview.RoundedImageView
                        android:id="@+id/iv_head"
                        android:layout_width="90dp"
                        android:layout_height="90dp"
                        android:layout_marginLeft="15dp"
                        android:layout_marginTop="20dp"
                        android:scaleType="centerCrop"
                        app:riv_corner_radius="@dimen/dp_10" />
                </RelativeLayout>
            </RelativeLayout>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_12"
                android:layout_marginRight="@dimen/dp_12"
                app:cardCornerRadius="@dimen/dp_10"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="12dp"
                        android:text="常用功能"
                        android:textColor="#262626"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/mRecycle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp_12"
                app:cardCornerRadius="@dimen/dp_10"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="12dp"
                        android:text="基础信息"
                        android:textColor="#262626"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingLeft="12dp"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingBottom="@dimen/dp_10">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="人员类型：\u3000"
                            android:textColor="#8C8C8C"
                            android:textSize="14sp" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/mRecycle_peo_type"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:nestedScrollingEnabled="false" />

                        <TextView
                            android:id="@+id/bt_type_edit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableRight="@mipmap/ic_right_blue"
                            android:paddingLeft="@dimen/dp_5"
                            android:text="更改"
                            android:textColor="#ff0053e2"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:visibility="gone" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/bt_fx_edit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:paddingLeft="12dp"
                        android:paddingBottom="@dimen/dp_10">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="风险等级：\u3000"
                            android:textColor="#8C8C8C"
                            android:textSize="14sp" />

                        <ImageView
                            android:id="@+id/iv_fx_tag"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="@dimen/dp_10" />

                        <TextView
                            android:id="@+id/tv_fx_lvl"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textColor="#262626"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_fx_gg"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableRight="@mipmap/ic_right_blue"
                            android:paddingLeft="@dimen/dp_5"
                            android:text="更改"
                            android:textColor="#ff0053e2"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:paddingLeft="12dp"
                        android:paddingRight="12dp"
                        android:paddingBottom="@dimen/dp_10">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="涉毒人员\n风险积分：\u3000"
                            android:textColor="#8C8C8C"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="无"
                            android:textColor="#262626"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:paddingLeft="12dp"
                        android:paddingTop="@dimen/dp_5"
                        android:paddingBottom="@dimen/dp_10">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="人员状态：\u3000"
                            android:textColor="#8C8C8C"
                            android:textSize="14sp" />


                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center_vertical">

                            <TextView
                                android:id="@+id/tv_ryzt_wu"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/wu"
                                android:textColor="#262626"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_one"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginRight="5dp"
                                android:gravity="center"
                                android:paddingLeft="5dp"
                                android:paddingTop="@dimen/dp_1"
                                android:paddingRight="5dp"
                                android:paddingBottom="1dp"
                                android:textColor="#262626"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/tv_two"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:paddingLeft="5dp"
                                android:paddingTop="@dimen/dp_1"
                                android:paddingRight="5dp"
                                android:paddingBottom="1dp"
                                android:textColor="#262626"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/bt_status_edit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableRight="@mipmap/ic_right_blue"
                            android:paddingLeft="@dimen/dp_5"
                            android:text="查看"
                            android:textColor="#ff0053e2"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/bt_fmqk_edit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:paddingLeft="12dp"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingBottom="@dimen/dp_10"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="赋码情况：\u3000"
                            android:textColor="#8C8C8C"
                            android:textSize="14sp" />

                        <ImageView
                            android:id="@+id/iv_fm_tag"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="@dimen/dp_10" />

                        <TextView
                            android:id="@+id/tv_fmqk"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textColor="#262626"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableRight="@mipmap/ic_right_blue"
                            android:paddingLeft="@dimen/dp_5"
                            android:text="更改"
                            android:textColor="#ff0053e2"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="12dp"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingRight="12dp"
                        android:paddingBottom="@dimen/dp_10">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="涉毒标签：\u3000"
                            android:textColor="#8C8C8C"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_sd_tag"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="#262626"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="12dp"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingRight="12dp"
                        android:paddingBottom="@dimen/dp_10">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="居住地址：\u3000"
                            android:textColor="#8C8C8C"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_address"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="#262626"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="12dp"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingRight="12dp"
                        android:paddingBottom="@dimen/dp_10">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="户籍地址：\u3000"
                            android:textColor="#8C8C8C"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_hj_address"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="#262626"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="12dp"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingRight="12dp"
                        android:paddingBottom="@dimen/dp_10">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="管理人员：\u3000"
                            android:textColor="#8C8C8C"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_manager"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="#262626"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="12dp"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingRight="12dp"
                        android:paddingBottom="@dimen/dp_10">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="管理人电话："
                            android:textColor="#8C8C8C"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_manager_phone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="@color/color_main"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="30dp"
                        android:orientation="horizontal"
                        android:paddingLeft="12dp"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingRight="12dp"
                        android:paddingBottom="@dimen/dp_10">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="决定书：\u3000\u3000"
                            android:textColor="#8C8C8C"
                            android:textSize="14sp" />

                        <com.linggan.jd831.widget.XGridViewForScrollView
                            android:id="@+id/grid_img"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:horizontalSpacing="@dimen/dp_10"
                            android:listSelector="@color/color_tran"
                            android:numColumns="3"
                            android:scrollbars="none"
                            android:verticalSpacing="@dimen/dp_10" />

                        <TextView
                            android:id="@+id/tv_jsd"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="#262626"
                            android:textSize="14sp"
                            android:visibility="gone" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>


    <RelativeLayout
        android:id="@+id/re"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/dp_12"
        android:paddingRight="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_12">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginTop="50dp"
            android:paddingRight="@dimen/dp_10"
            android:src="@drawable/ic_write_back" />

        <TextView
            android:id="@+id/tv_top_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="人员详情"
            android:textColor="@color/white"
            android:textSize="18sp" />
    </RelativeLayout>
</RelativeLayout>
