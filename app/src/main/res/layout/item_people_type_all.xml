<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="@dimen/dp_15"
    android:layout_marginTop="@dimen/dp_5"
    android:layout_marginRight="@dimen/dp_15"
    android:layout_marginBottom="@dimen/dp_5"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="5dp"
        android:paddingBottom="@dimen/dp_5"
        android:textColor="#262626"
        android:textSize="14sp" />

    <LinearLayout
        android:id="@+id/lin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_gray_five"
        android:orientation="horizontal"
        android:padding="@dimen/dp_10">

        <TextView
            android:id="@+id/tv_left_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_white_five"
            android:padding="@dimen/dp_12"
            android:textColor="@color/color_main"
            android:textSize="24sp" />

        <TextView
            android:id="@+id/tv_cen_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="@dimen/dp_15"
            android:drawableRight="@mipmap/ic_downs"
            android:hint="请选择"
            android:textColor="@color/color_26"
            android:textColorHint="#bfbfbf"
            android:textSize="16sp" />
    </LinearLayout>
</LinearLayout>