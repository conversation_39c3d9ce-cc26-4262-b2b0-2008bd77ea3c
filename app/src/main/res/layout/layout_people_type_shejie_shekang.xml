<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="120dp"
            android:text="决定时间:"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_jd_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lin_bdsj"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="120dp"
            android:text="报到时间:"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_bd_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="120dp"
            android:text="决定机关:"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_jd_jg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="120dp"
            android:text="执行单位名称:"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_unit_zxd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="120dp"
            android:text="执行单位地址:"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_unit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="120dp"
            android:text="感染传染疾病:"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_crb"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lin_qjcr_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">


        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:minWidth="120dp"
            android:text="强戒出所日期:"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_qj_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lin_sk_glcs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">


        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="接受社区康复前所在的隔离场所:"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_glcs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">


        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="参加药物维持治疗门诊:"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_mzzl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lin_kfcs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="是否进入康复场所:"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_kfcs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">


        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="是否自愿戒毒:"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_zyjd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="二级管控地:"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_ejgkd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="协议书:"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <com.linggan.jd831.widget.XGridViewForScrollView
            android:id="@+id/grid_img"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="40dp"
            android:horizontalSpacing="10dp"
            android:numColumns="3"
            android:scrollbars="none"
            android:verticalSpacing="@dimen/dp_10" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="责任书:"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <com.linggan.jd831.widget.XGridViewForScrollView
            android:id="@+id/grid_zrs_img"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="40dp"
            android:horizontalSpacing="10dp"
            android:numColumns="3"
            android:scrollbars="none"
            android:verticalSpacing="@dimen/dp_10" />
    </LinearLayout>
</LinearLayout>