<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/re"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/dp_12">

    <FrameLayout
        android:id="@+id/fe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/iv_head"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:src="@mipmap/ic_system_msg"
            app:riv_corner_radius="@dimen/dp_10" />

        <LinearLayout
            android:id="@+id/lin_head"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:background="@drawable/bg_gradient_blue_ten"
            android:gravity="center"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_pic_name"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_read"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_marginLeft="5dp"
            android:background="@drawable/bg_red_yuan"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="8dp"
            android:visibility="gone" />
    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_toRightOf="@+id/fe">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="系统消息"
            android:textColor="#262626"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_id_card"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#8c8c8c"
            android:textSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/fe"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/fe"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#8C8C8C"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#8C8C8C"
            android:textSize="14sp" />
    </LinearLayout>
</RelativeLayout>