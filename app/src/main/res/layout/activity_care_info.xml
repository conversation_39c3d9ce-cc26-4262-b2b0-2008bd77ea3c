<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base"
        layout="@layout/base_top_ground_img" />

    <androidx.core.widget.NestedScrollView
        style="@style/ll_m"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout style="@style/ll_v">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="12dp"
                android:text="基础信息"
                android:textColor="#262626"
                android:textSize="16sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="12dp"
                android:paddingTop="@dimen/dp_10"
                android:paddingRight="12dp"
                android:paddingBottom="@dimen/dp_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="姓名：\u3000\u3000\u3000\u3000"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:lines="1"
                    android:textColor="#262626"
                    android:textSize="14sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="12dp"
                android:paddingTop="@dimen/dp_10"
                android:paddingRight="12dp"
                android:paddingBottom="@dimen/dp_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="身份证：\u3000\u3000\u3000"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_id_card"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:lines="1"
                    android:textColor="#262626"
                    android:textSize="14sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="12dp"
                android:paddingTop="@dimen/dp_10"
                android:paddingRight="12dp"
                android:paddingBottom="@dimen/dp_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="电话：\u3000\u3000\u3000\u3000"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_mobile"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:lines="1"
                    android:textColor="#262626"
                    android:textSize="14sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="12dp"
                android:paddingTop="@dimen/dp_10"
                android:paddingRight="12dp"
                android:paddingBottom="@dimen/dp_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="人员类型：\u3000\u3000"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_rylx"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:lines="1"
                    android:textColor="#262626"
                    android:textSize="14sp" />

            </LinearLayout>


            <LinearLayout
                android:id="@+id/lin_ssd"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="12dp"
                android:paddingTop="@dimen/dp_10"
                android:paddingRight="12dp"
                android:paddingBottom="@dimen/dp_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="所属地区：\u3000\u3000"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_ssdq"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#262626"
                    android:textSize="14sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lin_help"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="12dp"
                android:paddingTop="@dimen/dp_10"
                android:paddingRight="12dp"
                android:paddingBottom="@dimen/dp_10"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="帮扶类型：\u3000\u3000"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_help_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:lines="1"
                    android:textColor="#262626"
                    android:textSize="14sp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_margin="@dimen/dp_12"
                android:background="@color/color_line" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:nestedScrollingEnabled="false" />

            <LinearLayout
                android:id="@+id/lin_no_case"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:padding="@dimen/dp_12"
                android:visibility="gone">


                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="@dimen/dp_12"
                    android:paddingBottom="@dimen/dp_12"
                    android:text="无法关爱原因"
                    android:textColor="#ff262626"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_15"
                    android:drawableLeft="@mipmap/ic_start"
                    android:text="无法关爱原因"
                    android:textColor="#262626"
                    android:textSize="12sp" />

                <EditText
                    android:id="@+id/et_case"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_12"
                    android:background="@null"
                    android:hint="请输入无法关爱原因"
                    android:paddingTop="@dimen/dp_12"
                    android:paddingBottom="@dimen/dp_12"
                    android:textColor="@color/color_26"
                    android:textColorHint="#bfbfbf"
                    android:textSize="14sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:background="@color/color_line" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_12"
                    android:text="附件"
                    android:textColor="#262626"
                    android:textSize="12sp" />

                <com.linggan.jd831.widget.XGridViewForScrollView
                    android:id="@+id/grid_img"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:horizontalSpacing="@dimen/dp_10"
                    android:listSelector="@color/color_tran"
                    android:numColumns="4"
                    android:scrollbars="none"
                    android:verticalSpacing="@dimen/dp_10" />

                <TextView
                    android:id="@+id/tv_img_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="#8c8c8c"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/bt_sure"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="20dp"
                    android:background="@drawable/bg_color_main"
                    android:text="提交"
                    android:textColor="@color/white"
                    android:textSize="14sp" />
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>