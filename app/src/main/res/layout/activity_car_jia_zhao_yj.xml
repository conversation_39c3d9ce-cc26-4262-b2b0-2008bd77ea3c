<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <LinearLayout
        style="@style/ll_v"
        android:background="@color/bg_color"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            style="@style/ll_m"
            android:layout_weight="1"
            android:scrollbars="none">

            <LinearLayout
                style="@style/ll_v"
                android:paddingBottom="20dp">

                <include
                    android:id="@+id/peo"
                    layout="@layout/layout_people_info_car_jz"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_10" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="@color/bg_color" />

                <TextView
                    android:id="@+id/tv_jz_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:paddingLeft="12dp"
                    android:paddingTop="12dp"
                    android:paddingBottom="@dimen/dp_12"
                    android:text="驾照信息核查"
                    android:textColor="#262626"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:nestedScrollingEnabled="false" />

                <TextView
                    android:id="@+id/tv_cl_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_10"
                    android:background="@color/white"
                    android:paddingLeft="12dp"
                    android:paddingTop="12dp"
                    android:paddingBottom="@dimen/dp_12"
                    android:text="车辆信息核查"
                    android:textColor="#262626"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycle_car"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:nestedScrollingEnabled="false" />

                <include
                    android:id="@+id/dcl"
                    layout="@layout/layout_car_jz_yj_dcl" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <LinearLayout
            android:id="@+id/lin_bo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@color/white">

            <Button
                android:id="@+id/bt_submit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="15dp"
                android:background="@drawable/bg_blue_five"
                android:text="提交"
                android:textColor="@color/white"
                android:textSize="15sp" />
        </LinearLayout>
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>