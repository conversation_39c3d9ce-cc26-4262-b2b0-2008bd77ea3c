<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="12dp"
    android:layout_marginTop="5dp"
    android:layout_marginRight="12dp"
    android:layout_marginBottom="5dp"
    app:cardCornerRadius="5dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/dp_10">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/ic_ware_house_item" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ellipsize="end"
                android:lines="1"
                android:textColor="#262626"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>
        <!-- 新增储存化学品信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="硕警原因：\u3000"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_id_card"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#262626"
                android:textSize="14sp" />
        </LinearLayout>

        <!-- 新增摄像机接入信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"

                android:text="硕警时间：\u3000"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />  <!-- 超出部分用省略号表示 -->

            <TextView
                android:id="@+id/tv_regional"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="#262626"
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
