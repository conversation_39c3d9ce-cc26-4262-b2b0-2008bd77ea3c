<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <androidx.core.widget.NestedScrollView
        style="@style/ll_m"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout style="@style/ll_v">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="姓名"
                android:textColor="@color/color_33"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/et_warehouse_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:background="@null"
                android:hint="请输入姓名"
                android:maxLength="20"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="身份证"
                android:textColor="#333333"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/tv_chemical_storage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:hint="请输入身份证"
                android:background="@null"
                android:maxLines="1"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@mipmap/ic_start"
                    android:drawablePadding="3dp"
                    android:text="不戴口罩人像"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="30dp"
                    android:orientation="horizontal">

                    <com.linggan.jd831.widget.XGridViewForScrollView
                        android:id="@+id/grid_img"
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="5dp"
                        android:horizontalSpacing="10dp"
                        android:numColumns="1"
                        android:scrollbars="none"
                        android:verticalSpacing="10dp" />

                    <FrameLayout
                        android:layout_width="100dp"
                        android:layout_height="100dp"
                        android:layout_margin="5dp">

                        <ImageView
                            android:id="@+id/imageView"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="centerCrop"
                            android:src="@drawable/ic_lab_person" />

                        <TextView
                            android:id="@+id/textView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="bottom"
                            android:background="#80000000"
                            android:gravity="center"
                            android:padding="5dp"
                            android:text="示意图"
                            android:textColor="#FFFFFF"
                            android:textSize="12sp" />
                    </FrameLayout>
                    <TextView
                        android:id="@+id/tv_img"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent">
                    </TextView>
                </LinearLayout>



            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@mipmap/ic_start"
                    android:drawablePadding="3dp"
                    android:text="戴口罩人像"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="30dp"
                    android:orientation="horizontal">

                    <com.linggan.jd831.widget.XGridViewForScrollView
                        android:id="@+id/grid_img_no"
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="5dp"
                        android:horizontalSpacing="10dp"
                        android:numColumns="1"
                        android:scrollbars="none"
                        android:verticalSpacing="10dp" />

                    <FrameLayout
                        android:layout_width="100dp"
                        android:layout_height="100dp"
                        android:layout_margin="5dp">

                        <ImageView
                            android:id="@+id/imageView_no"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="centerCrop"
                            android:src="@drawable/ic_lab_person_no" />

                        <TextView
                            android:id="@+id/textView_no"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="bottom"
                            android:background="#80000000"
                            android:gravity="center"
                            android:padding="5dp"
                            android:text="示意图"
                            android:textColor="#FFFFFF"
                            android:textSize="12sp" />
                    </FrameLayout>
                    <TextView
                        android:id="@+id/tv_img_no"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent">
                    </TextView>
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                >
<!--                区域选择-->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@mipmap/ic_start"
                    android:drawablePadding="3dp"
                    android:text="区域选择"
                    android:textColor="#333333"
                    android:textSize="14sp" />
                <TextView
                    android:id="@+id/tv_regional"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="30dp"
                    android:drawableRight="@mipmap/ic_right"
                    android:hint="请选择区域权限"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />

            </LinearLayout>
            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <Button
                android:id="@+id/bt_sure"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginRight="30dp"
                android:layout_marginBottom="30dp"
                android:background="@drawable/bg_color_main"
                android:text="保存"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
