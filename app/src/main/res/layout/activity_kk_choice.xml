<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base"
        layout="@layout/base_top_ground_img" />

    <LinearLayout
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_12"
            android:layout_marginBottom="@dimen/dp_5">

            <EditText
                android:id="@+id/et_search"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:background="@drawable/bg_gray_big"
                android:drawableLeft="@mipmap/ic_search_gray"
                android:hint="请输入卡口名称搜索"
                android:imeOptions="actionSearch"
                android:lines="1"
                android:paddingLeft="15dp"
                android:paddingRight="@dimen/dp_15"
                android:singleLine="true"
                android:textColor="@color/color_26"
                android:textSize="14sp" />
        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.lgfzd.base.view.recycle.XRecyclerView
                android:id="@+id/recycler"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <ImageView
                android:id="@+id/tv_no_data"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:src="@mipmap/nothing"
                android:visibility="gone" />
        </RelativeLayout>

    </LinearLayout>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="70dp"
        android:backgroundTint="@color/color_main"
        android:tint="@android:color/white"
        app:srcCompat="@android:drawable/ic_input_add" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>