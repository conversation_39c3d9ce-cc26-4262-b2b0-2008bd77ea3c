<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:visibility="gone"
    android:orientation="vertical">



    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:id="@+id/tv_unit_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="确认时间：\u3000\u3000\u3000\u3000\u3000"
            android:textColor="#262626"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#262626"
            android:textSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="情况说明：\u3000\u3000\u3000\u3000\u3000"
            android:textColor="#262626"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_num"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#262626"
            android:textSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="是否通知公安：\u3000\u3000\u3000"
            android:textColor="#262626"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_tzga"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#262626"
            android:textSize="14sp" />
    </LinearLayout>
</LinearLayout>