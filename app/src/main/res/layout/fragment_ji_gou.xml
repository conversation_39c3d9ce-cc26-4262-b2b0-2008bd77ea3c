<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:viewBindingIgnore="true">

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp" />

    <androidx.core.widget.NestedScrollView
        style="@style/ll_m"
        android:scrollbars="none">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@mipmap/ic_bg_mager" />

            <TextView
                android:id="@+id/tv_manager_unit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_15"
                android:layout_marginTop="40dp"
                android:layout_marginRight="@dimen/dp_15"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:lines="1"
                android:paddingLeft="12dp"
                android:paddingTop="7dp"
                android:paddingRight="12dp"
                android:paddingBottom="7dp"
                android:textColor="@color/white"
                android:textSize="25sp"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/lin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_manager_unit"
                android:orientation="horizontal"
                android:padding="20dp">

                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_jc_cs"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:text="0"
                        android:textColor="@color/white"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/tv_jc_cs"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="@dimen/dp_15"
                        android:text="检测样本数"
                        android:textColor="@color/white"
                        android:textSize="14sp" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_scbg"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:drawablePadding="@dimen/dp_2"
                        android:text="0"
                        android:textColor="@color/white"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/tv_scbg"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="@dimen/dp_15"
                        android:text="上传报告(份)"
                        android:textColor="@color/white"
                        android:textSize="14sp" />
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/lin"
                android:layout_marginTop="@dimen/dp_10"
                android:background="@drawable/bg_white_top"
                android:minHeight="500dp"
                android:orientation="vertical"
                android:padding="@dimen/dp_15">

                <include layout="@layout/layout_jigou_index_menu" />

            </LinearLayout>
        </RelativeLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>