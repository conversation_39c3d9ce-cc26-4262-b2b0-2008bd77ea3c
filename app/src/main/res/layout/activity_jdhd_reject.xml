<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base_top"
        layout="@layout/base_top_ground_img" />

    <RelativeLayout
        style="@style/ll_m"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <EditText
            android:id="@+id/et_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@null"
            android:hint="请填写不通过原因"
            android:maxLength="200"
            android:padding="@dimen/dp_20"
            android:textColor="@color/color_33"
            android:textSize="14sp" />

        <Button
            android:id="@+id/bt_sure"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_margin="30dp"
            android:background="@drawable/bg_color_main"
            android:gravity="center"
            android:text="提交"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </RelativeLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>