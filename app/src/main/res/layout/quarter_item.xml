<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/quarter_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="24dp"
    android:layout_marginVertical="6dp"
    android:background="@drawable/gray_rounded_background"
    android:orientation="horizontal"
    android:paddingHorizontal="20dp"
    android:paddingVertical="18dp">

    <TextView
        android:id="@+id/quarter_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#333333"
        android:text="第一季度"
        android:textSize="14sp" />

    <ImageView
        android:id="@+id/status_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="3dp"
        android:layout_marginTop="3dp" />


    <View
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_weight="1" />

    <TextView
        android:id="@+id/more_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#8C8C8C"
        android:text="请日期开始后再完善"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/iv_sp_tag"
        android:layout_marginTop="1dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/arrow_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="详情"
        android:src="@drawable/ic_right_arrow"
        app:tint="#707070" />

</LinearLayout>