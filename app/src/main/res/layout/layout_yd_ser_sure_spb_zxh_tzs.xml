<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:visibility="gone">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="10dp"
        android:drawablePadding="3dp"
        android:text="变更社区戒毒社区康复执行地审批表"
        android:textColor="#333333"
        android:textSize="14sp" />

    <com.linggan.jd831.widget.XGridViewForScrollView
        android:id="@+id/grid_sjsk_sqb_img"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="12dp"
        android:horizontalSpacing="@dimen/dp_10"
        android:listSelector="@color/color_tran"
        android:numColumns="4"
        android:verticalSpacing="@dimen/dp_10" />


    <TextView
        android:id="@+id/tv_sjsk_tzs_img_info1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="12dp"
        android:textColor="#8c8c8c"
        android:textSize="12sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="12dp"
        android:background="@color/color_line" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="10dp"
        android:drawablePadding="3dp"
        android:text="关于变更社区戒毒社区康复执行地的函"
        android:textColor="#333333"
        android:textSize="14sp" />

    <com.linggan.jd831.widget.XGridViewForScrollView
        android:id="@+id/grid_sjsk_han_img"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="12dp"
        android:horizontalSpacing="@dimen/dp_10"
        android:listSelector="@color/color_tran"
        android:numColumns="4"
        android:verticalSpacing="@dimen/dp_10" />


    <TextView
        android:id="@+id/tv_sjsk_tzs_img_info2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="12dp"
        android:textColor="#8c8c8c"
        android:textSize="12sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="12dp"
        android:background="@color/color_line" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="10dp"
        android:drawablePadding="3dp"
        android:text="变更社区戒毒社区康复执行地通知书"
        android:textColor="#333333"
        android:textSize="14sp" />

    <com.linggan.jd831.widget.XGridViewForScrollView
        android:id="@+id/grid_sjsk_tzs_img"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="12dp"
        android:horizontalSpacing="@dimen/dp_10"
        android:listSelector="@color/color_tran"
        android:numColumns="4"
        android:verticalSpacing="@dimen/dp_10" />

    <TextView
        android:id="@+id/tv_sjsk_tzs_img_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="12dp"
        android:textColor="#8c8c8c"
        android:textSize="12sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="12dp"
        android:background="@color/color_line" />
</LinearLayout>
