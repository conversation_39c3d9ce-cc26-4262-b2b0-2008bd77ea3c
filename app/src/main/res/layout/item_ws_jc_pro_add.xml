<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="5dp"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="检测项目"
            android:textColor="#333333"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_jc_pro"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="20dp"
            android:drawableRight="@mipmap/ic_right"
            android:hint="请选择检测项目"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textColorHint="@color/color_8c"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="@dimen/dp_10"
            android:background="@color/color_line" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="浓度"
            android:textColor="#333333"
            android:textSize="14sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="20dp">

            <EditText
                android:id="@+id/et_nd"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@null"
                android:hint="请输入浓度"
                android:inputType="numberDecimal|number"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textColorHint="@color/color_8c"
                android:textSize="14sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ng/L"
                android:textColor="#ff262626"
                android:textSize="14sp" />
        </LinearLayout>


        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="@dimen/dp_10"
            android:background="@color/color_line" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:drawablePadding="3dp"
            android:text="千人均消费量(毫克/千人/天)"
            android:textColor="#333333"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/et_xfl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="20dp"
            android:background="@null"
            android:hint="请输入千人均消费量(毫克/千人/天）"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textColorHint="@color/color_8c"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="@dimen/dp_10"
            android:background="@color/color_line" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_cha"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginRight="@dimen/dp_10"
        android:padding="@dimen/dp_10"
        android:src="@mipmap/ic_cha" />
</LinearLayout>