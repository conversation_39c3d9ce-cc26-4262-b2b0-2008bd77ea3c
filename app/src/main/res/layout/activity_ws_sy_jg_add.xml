<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <LinearLayout
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            style="@style/ll_m"
            android:layout_weight="1"
            android:background="@color/bg_color"
            android:scrollbars="none">

            <LinearLayout style="@style/ll_v">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_10"
                    android:background="@color/white"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="12dp"
                        android:text="样本编号"
                        android:textColor="#262626"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/mRecycle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />

                    <TextView
                        android:id="@+id/bt_add"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_12"
                        android:layout_marginTop="@dimen/dp_10"
                        android:layout_marginRight="@dimen/dp_12"
                        android:layout_marginBottom="@dimen/dp_10"
                        android:background="@drawable/bg_blue_xu"
                        android:gravity="center"
                        android:paddingTop="@dimen/dp_12"
                        android:paddingBottom="@dimen/dp_12"
                        android:text="新增"
                        android:textColor="#0053e2"
                        android:textSize="14sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_10"
                        android:background="@color/bg_color" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawableLeft="@mipmap/ic_start"
                        android:padding="@dimen/dp_12"
                        android:text="样本照片"
                        android:textColor="#262626"
                        android:textSize="14sp" />

                    <com.linggan.jd831.widget.XGridViewForScrollView
                        android:id="@+id/grid_img"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="12dp"
                        android:layout_marginRight="12dp"
                        android:horizontalSpacing="@dimen/dp_10"
                        android:listSelector="@color/color_tran"
                        android:numColumns="4"
                        android:scrollbars="none"
                        android:verticalSpacing="@dimen/dp_10" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="12dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="12dp"
                        android:layout_marginBottom="15dp"
                        android:text="请拍摄样本的照片，保存所有的样本编号能够清晰的查看"
                        android:textColor="#8c8c8c"
                        android:textSize="14sp" />
                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <Button
            android:id="@+id/bt_submit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="15dp"
            android:background="@drawable/bg_color_main"
            android:text="@string/sure"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>