<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="12dp"
    android:layout_marginTop="5dp"
    android:layout_marginRight="12dp"
    android:layout_marginBottom="5dp"
    android:orientation="vertical"
    android:padding="@dimen/dp_1">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="5dp"
        app:cardElevation="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/dp_8">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">


                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="#262626"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/iv_tag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:src="@drawable/bg_choice_yuan_select"
                    android:visibility="gone" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="采样点地址："
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_ks_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />
            </LinearLayout>


            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginTop="@dimen/dp_10"
                android:background="@color/color_line" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/bt_sy"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingBottom="@dimen/dp_10"
                    android:text="无需送样"
                    android:textColor="@color/color_8c"
                    android:textSize="14sp" />

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/dp_5"
                    android:layout_marginBottom="@dimen/dp_5"
                    android:background="@color/color_line" />

                <TextView
                    android:id="@+id/bt_shou_yang"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingBottom="@dimen/dp_10"
                    android:textColor="@color/color_8c"
                    android:textSize="14sp" />

                <View
                    android:id="@+id/view1"
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/dp_5"
                    android:layout_marginBottom="@dimen/dp_5"
                    android:background="@color/color_line" />

                <TextView
                    android:id="@+id/bt_sc"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingBottom="@dimen/dp_10"
                    android:text="待上传检测结果"
                    android:textColor="#0053e2"
                    android:textSize="14sp" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</LinearLayout>