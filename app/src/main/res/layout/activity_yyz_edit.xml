<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <LinearLayout
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="营运证编号"
            android:textColor="#333333"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/tv_yyz"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="30dp"
            android:background="@null"
            android:hint="请输入营运证编号"
            android:inputType="number"
            android:maxLength="50"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:singleLine="true"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="30dp"
            android:background="@color/color_line" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="营运证有效期至"
            android:textColor="#333333"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="30dp"
            android:drawableRight="@mipmap/ic_right"
            android:hint="请选择营运证有效期"
            android:maxLength="18"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:singleLine="true"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="30dp"
            android:background="@color/color_line" />


        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_weight="1" />

        <Button
            android:id="@+id/bt_sure"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="30dp"
            android:background="@drawable/bg_color_main"
            android:text="@string/sure"
            android:textColor="@color/white"
            android:textSize="14sp" />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>