<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:visibility="gone">

    <LinearLayout
        android:id="@+id/lin_yyqk"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="开始执行时间：\u3000\u3000\u3000"
            android:textColor="#262626"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_qbhs_kssj"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#262626"
            android:textSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="决定机关：\u3000\u3000\u3000\u3000\u3000"
            android:textColor="#262626"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_qbhs_jdjg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#262626"
            android:textSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="执行原因：\u3000\u3000\u3000\u3000\u3000"
            android:textColor="#262626"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_qbhs_case"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#262626"
            android:textSize="14sp" />
    </LinearLayout>
</LinearLayout>