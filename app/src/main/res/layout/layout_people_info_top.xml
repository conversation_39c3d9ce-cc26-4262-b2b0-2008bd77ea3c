<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/lin_info"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/iv_head"
            android:layout_width="80dp"
            android:layout_height="80dp"
            app:riv_corner_radius="@dimen/dp_10" />

        <LinearLayout
            android:id="@+id/lin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="15dp"
            android:layout_toRightOf="@+id/iv_head"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#ff262626"
                android:textSize="20sp" />

            <TextView
                android:id="@+id/tv_sex"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_10"
                android:ellipsize="end"
                android:lines="1"
                android:textColor="#ff8c8c8c"
                android:textSize="14sp" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_peo_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/lin"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_toRightOf="@+id/iv_head"
            android:nestedScrollingEnabled="false" />
    </RelativeLayout>
</LinearLayout>