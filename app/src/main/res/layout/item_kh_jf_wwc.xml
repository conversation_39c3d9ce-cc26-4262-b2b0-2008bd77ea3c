<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="12dp"
    android:layout_marginRight="12dp"
    android:layout_marginBottom="10dp"
    android:background="@drawable/bg_white_five"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingLeft="@dimen/dp_12"
        android:paddingTop="@dimen/dp_12"
        android:paddingRight="@dimen/dp_12">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/iv_head"
            android:layout_width="85dp"
            android:layout_height="85dp"
            android:scaleType="centerCrop"
            app:riv_corner_radius="@dimen/dp_10" />


        <LinearLayout
            android:id="@+id/lin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_toRightOf="@+id/iv_head"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#ff262626"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_10"
                android:ellipsize="end"
                android:lines="1"
                android:textColor="#ff8c8c8c"
                android:textSize="13sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_glry"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/lin"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="5dp"
            android:layout_toRightOf="@+id/iv_head"
            android:textColor="#ff8c8c8c"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/tv_kssj"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_glry"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="2dp"
            android:layout_toRightOf="@+id/iv_head"
            android:textColor="#ff8c8c8c"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/tv_jssj"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_kssj"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="2dp"
            android:layout_toRightOf="@+id/iv_head"
            android:textColor="#ff8c8c8c"
            android:textSize="13sp" />

        <ImageView
            android:id="@+id/iv_yq"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="12dp"
            android:src="@mipmap/ic_yuqis"
            android:visibility="invisible" />
    </RelativeLayout>

    <View
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@color/color_line" />

    <LinearLayout
        android:id="@+id/lin_bo"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/bt_no"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:clickable="true"
            android:gravity="center"
            android:paddingTop="@dimen/dp_5"
            android:paddingBottom="5dp"
            android:text="联系吸毒人员"
            android:textColor="#0053e2"
            android:textSize="14sp" />

        <View
            android:id="@+id/view1"
            android:layout_width="1dp"
            android:layout_height="20dp"
            android:layout_gravity="center"
            android:background="#D9D9D9" />

        <TextView
            android:id="@+id/bt_post"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingTop="@dimen/dp_5"
            android:paddingBottom="5dp"
            android:text="联系管理人员"
            android:textColor="#0053e2"
            android:textSize="14sp" />
    </LinearLayout>
</LinearLayout>