<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="12dp"
    android:layout_marginTop="5dp"
    android:layout_marginRight="12dp"
    android:layout_marginBottom="5dp"
    app:cardCornerRadius="5dp"
    app:cardElevation="1dp">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/dp_8">

        <ImageView
            android:id="@+id/iv_tag"
            android:layout_width="30dp"
            android:layout_height="30dp" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_10">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/dp_5"
                android:textColor="#262626"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_title"
                android:paddingTop="@dimen/dp_15"
                android:textColor="#262626"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_ks_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_name"
                android:layout_marginTop="@dimen/dp_10"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <!--            退回-->
            <LinearLayout
                android:id="@+id/lin_th"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_ks_time"
                android:layout_marginTop="@dimen/dp_10"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_th_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="退回原因：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_th_case"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_th_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_10"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_10"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="退回人电话：\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_th_phone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#0053E2"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>
            <!--            审批中-->
            <LinearLayout
                android:id="@+id/lin_sp_ing"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_ks_time"
                android:layout_marginTop="@dimen/dp_10"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="执行人：\u3000"
                    android:textColor="#8c8c8c"
                    android:textSize="12sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />

                    <TextView
                        android:id="@+id/bt_more"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:drawableRight="@mipmap/ic_top_down"
                        android:drawablePadding="@dimen/dp_10"
                        android:gravity="center"
                        android:padding="@dimen/dp_10"
                        android:textColor="#262626"
                        android:textSize="12sp" />
                </LinearLayout>
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>