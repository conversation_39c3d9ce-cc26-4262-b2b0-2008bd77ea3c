<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="姓名"
                android:textColor="#333333"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/et_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:background="@null"
                android:drawableRight="@mipmap/ic_right"
                android:hint="请输入帮扶小组人员姓名"
                android:inputType="text"
                android:maxLength="10"
                android:maxLines="1"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="@dimen/dp_10"
                android:background="@color/color_line" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="5dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="联系电话"
                android:textColor="#333333"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/et_relative"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:background="@null"
                android:drawableRight="@mipmap/ic_right"
                android:hint="请输入联系电话"
                android:inputType="number"
                android:maxLength="11"
                android:maxLines="1"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="@dimen/dp_10"
                android:background="@color/color_line" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="5dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="职务"
                android:textColor="#333333"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/et_position"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:background="@null"
                android:drawableRight="@mipmap/ic_right"
                android:hint="请输入职务"
                android:inputType="text"
                android:maxLength="20"
                android:maxLines="1"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="@dimen/dp_10"
                android:background="@color/color_line" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="5dp"
                android:drawablePadding="3dp"
                android:text="备注"
                android:textColor="#333333"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/et_remark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:background="@null"
                android:drawableRight="@mipmap/ic_right"
                android:hint="请输入备注说明"
                android:inputType="text"
                android:maxLength="25"
                android:maxLines="2"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="@dimen/dp_10"
                android:background="@color/color_line" />


        </LinearLayout>

        <ImageView
            android:id="@+id/iv_cha"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="@dimen/dp_10"
            android:padding="@dimen/dp_8"
            android:src="@mipmap/ic_cha" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@color/color_line" />
</LinearLayout> 