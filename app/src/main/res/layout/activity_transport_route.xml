<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white">

    <include layout="@layout/base_top_ground_img" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 起始城市 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@mipmap/ic_start"
                android:text="起始城市"
                android:textColor="#262626"
                android:textSize="14sp"/>

            <TextView
                android:id="@+id/et_start_city"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="8dp"
                android:background="@null"
                android:hint="请选择起始城市"
                android:textColor="#333333"
                android:textSize="14sp"
                android:padding="8dp"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E5E5E5"/>

            <!-- 途径城市容器 -->
            <LinearLayout
                android:id="@+id/ll_via_cities"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="16dp"/>

            <!-- 添加途径城市按钮 -->
            <TextView
                android:id="@+id/tv_add_via_city"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/bg_dashed_border"
                android:gravity="center"
                android:text="+ 添加途经城市"
                android:textColor="#666666"
                android:textSize="14sp"/>

            <!-- 终点城市 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:drawableLeft="@mipmap/ic_start"
                android:text="终点城市"
                android:textColor="#262626"
                android:textSize="14sp"/>

            <TextView
                android:id="@+id/tv_end_city"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="8dp"
                android:background="@null"
                android:drawableEnd="@mipmap/ic_right"
                android:gravity="center_vertical"
                android:hint="请选择终点城市"
                android:textColor="#333333"
                android:padding="8dp"
                android:textSize="14sp"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E5E5E5"/>

        </LinearLayout>
    </ScrollView>

    <!-- 底部保存按钮 -->
    <Button
        android:id="@+id/btn_save"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_margin="16dp"
        android:background="@drawable/bg_button_primary"
        android:text="保存"
        android:textColor="@color/white"/>

</LinearLayout> 