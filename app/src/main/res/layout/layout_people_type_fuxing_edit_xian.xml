<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:visibility="visible">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="涉案类型"
        android:textColor="#333333"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/et_fx_lx"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@null"
        android:hint="请选择涉案类型"
        android:drawableRight="@mipmap/ic_right"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="入狱时间"
        android:textColor="#333333"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_fx_ry_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@null"
        android:drawableRight="@mipmap/ic_right"
        android:hint="请选择入狱时间"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="visible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="15dp"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="刑期"
            android:textColor="#333333"
            android:textSize="14sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/et_fx_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_15"
                android:layout_marginTop="10dp"
                android:layout_marginRight="@dimen/dp_15"
                android:layout_weight="1"
                android:background="@null"
                android:hint="请输入阿拉伯数字"
                android:inputType="number"
                android:digits="0123456789"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginRight="@dimen/dp_15"
                android:text="年"
                android:textColor="#333333"
                android:textSize="14sp" />
        </LinearLayout>


        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="15dp"
            android:background="@color/color_line" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="visible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="15dp"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="服刑单位名称"
            android:textColor="#333333"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/et_fx_unit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/dp_15"
            android:background="@null"
            android:hint="请输入服刑单位名称"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="15dp"
            android:background="@color/color_line" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="15dp"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="执行单位地址"
            android:textColor="#333333"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/et_fx_zxdz"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/dp_15"
            android:background="@null"
            android:hint="请输入执行单位地址"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="15dp"
            android:background="@color/color_line" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="15dp"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="入狱原因"
            android:textColor="#333333"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_fx_case"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/dp_15"
            android:background="@null"
            android:drawableRight="@mipmap/ic_right"
            android:hint="请输入入狱原因"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="15dp"
            android:background="@color/color_line" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="15dp"
            android:drawablePadding="3dp"
            android:text="二级管控地"
            android:textColor="#333333"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_fx_ejgkd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/dp_15"
            android:background="@null"
            android:drawableRight="@mipmap/ic_right"
            android:hint="请选择二级管控地"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="15dp"
            android:background="@color/color_line" />
    </LinearLayout>


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawablePadding="3dp"
        android:text="备注"
        android:textColor="#333333"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_fx_remark"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@null"
        android:drawableRight="@mipmap/ic_right"
        android:hint="请输入备注"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />
</LinearLayout>