<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base"
        layout="@layout/base_top_ground_img" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            style="@style/ll_m"
            android:scrollbars="none">

            <LinearLayout
                style="@style/ll_v"
                android:paddingBottom="300dp">

                <include
                    android:id="@+id/top"
                    layout="@layout/layout_people_info_top" />

                <TextView
                    android:id="@+id/tv_base_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="12dp"
                    android:text="人员状态信息"
                    android:textColor="#262626"
                    android:textSize="16sp" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="开始时间：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_ks_time"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:lines="1"
                        android:textColor="#262626"
                        android:textSize="14sp" />

                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="结束时间：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_js_time"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:lines="1"
                        android:textColor="#262626"
                        android:textSize="14sp" />

                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="结束状态：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_end_zt"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="证明文书：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <com.linggan.jd831.widget.XGridViewForScrollView
                        android:id="@+id/grid_img"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:horizontalSpacing="10dp"
                        android:numColumns="3"
                        android:scrollbars="none"
                        android:verticalSpacing="10dp" />
                </LinearLayout>

                <include
                    android:id="@+id/peo"
                    layout="@layout/layout_people_info_base" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <include
            android:id="@+id/lin_popup"
            layout="@layout/layout_popup_sp" />
    </FrameLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>