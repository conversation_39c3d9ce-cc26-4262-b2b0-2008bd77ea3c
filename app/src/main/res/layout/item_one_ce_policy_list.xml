<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="12dp"
    android:layout_marginTop="5dp"
    android:layout_marginRight="12dp"
    android:layout_marginBottom="5dp"
    app:cardCornerRadius="5dp"
    app:cardElevation="2dp">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/dp_10">

        <LinearLayout
            android:id="@+id/ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="#262626"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/iv_sp_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/lin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll"
            android:paddingTop="@dimen/dp_10">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="帮扶时人员类型："
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#262626"
                android:textSize="14sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/lin1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/lin"
            android:paddingTop="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_10">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="帮扶时间：\u3000\u3000\u3000"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#262626"
                android:textSize="14sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/lin1">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="工作人员：\u3000\u3000\u3000"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_people"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#262626"
                android:textSize="14sp" />
        </LinearLayout>
    </RelativeLayout>
</androidx.cardview.widget.CardView>