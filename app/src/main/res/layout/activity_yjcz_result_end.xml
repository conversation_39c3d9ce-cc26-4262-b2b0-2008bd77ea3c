<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/ll_m"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <LinearLayout
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            style="@style/ll_m"
            android:layout_weight="1"
            android:scrollbars="none">

            <LinearLayout
                style="@style/ll_v"
                android:paddingBottom="@dimen/dp_30">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_15"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@mipmap/ic_start"
                    android:drawablePadding="3dp"
                    android:text="结束药物维持治疗脱失时间"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_ts_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_15"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="@dimen/dp_15"
                    android:background="@null"
                    android:drawableRight="@mipmap/ic_right"
                    android:hint="请选择结束药物维持治疗脱失时间"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <View
                    android:id="@+id/view1"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="15dp"
                    android:background="@color/color_line" />

                <LinearLayout
                    android:id="@+id/lin_jsyy"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/tv_yy_bt"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_15"
                        android:layout_marginTop="10dp"
                        android:drawableLeft="@mipmap/ic_start"
                        android:drawablePadding="3dp"
                        android:text="结束原因"
                        android:textColor="#333333"
                        android:textSize="14sp" />

                    <EditText
                        android:id="@+id/et_jsyy"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_15"
                        android:layout_marginTop="10dp"
                        android:layout_marginRight="@dimen/dp_15"
                        android:background="@null"
                        android:hint="请填写结束原因， 比如已找回，继续服药"
                        android:maxLength="500"
                        android:paddingTop="10dp"
                        android:paddingBottom="10dp"
                        android:textColor="#333333"
                        android:textSize="14sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_marginLeft="15dp"
                        android:layout_marginTop="@dimen/dp_10"
                        android:layout_marginRight="15dp"
                        android:background="@color/color_line" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="10dp"
                    android:text="说明文件"
                    android:textColor="#333333"
                    android:textSize="14sp" />


                <com.linggan.jd831.widget.XGridViewForScrollView
                    android:id="@+id/grid_img"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="15dp"
                    android:horizontalSpacing="@dimen/dp_10"
                    android:listSelector="@color/color_tran"
                    android:numColumns="4"
                    android:verticalSpacing="@dimen/dp_10" />

                <TextView
                    android:id="@+id/tv_img_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="15dp"
                    android:text="@string/czyj_fj_info"
                    android:textColor="#8c8c8c"
                    android:textSize="12sp" />

                <LinearLayout
                    android:id="@+id/lin_jxzl"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_marginLeft="15dp"
                        android:layout_marginTop="@dimen/dp_10"
                        android:layout_marginRight="15dp"
                        android:background="@color/color_line" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_15"
                        android:layout_marginTop="10dp"
                        android:drawableLeft="@mipmap/ic_start"
                        android:drawablePadding="3dp"
                        android:text="继续药物维持治疗"
                        android:textColor="#333333"
                        android:textSize="14sp" />

                    <RadioGroup
                        android:id="@+id/radio_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp_12">

                        <RadioButton
                            android:id="@+id/rb_yes"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:background="@color/transparent"
                            android:button="@null"
                            android:drawableLeft="@drawable/bg_radio_seleect"
                            android:drawablePadding="@dimen/dp_10"
                            android:gravity="center_vertical"
                            android:text="是"
                            android:textColor="@drawable/text_gray_black_select"
                            android:textSize="14sp" />

                        <RadioButton
                            android:id="@+id/rb_no"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:background="@color/transparent"
                            android:button="@null"
                            android:drawableLeft="@drawable/bg_radio_seleect"
                            android:drawablePadding="@dimen/dp_10"
                            android:gravity="center_vertical"
                            android:text="否"
                            android:textColor="@drawable/text_gray_black_select"
                            android:textSize="14sp" />
                    </RadioGroup>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_1"
                        android:layout_marginLeft="15dp"
                        android:layout_marginTop="@dimen/dp_10"
                        android:layout_marginRight="15dp"
                        android:background="@color/color_line" />

                    <LinearLayout
                        android:id="@+id/lin_jx_shi"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/dp_15"
                            android:layout_marginTop="10dp"
                            android:drawablePadding="3dp"
                            android:text="服药机构"
                            android:textColor="#333333"
                            android:textSize="14sp" />

                        <EditText
                            android:id="@+id/et_fyjg"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/dp_15"
                            android:layout_marginTop="10dp"
                            android:layout_marginRight="@dimen/dp_15"
                            android:background="@null"
                            android:hint="请填写服药机构"
                            android:paddingTop="10dp"
                            android:paddingBottom="10dp"
                            android:textColor="#333333"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_1"
                            android:layout_marginLeft="15dp"
                            android:layout_marginTop="@dimen/dp_10"
                            android:layout_marginRight="15dp"
                            android:background="@color/color_line" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/dp_15"
                            android:layout_marginTop="10dp"
                            android:drawablePadding="3dp"
                            android:text="备注"
                            android:textColor="#333333"
                            android:textSize="14sp" />

                        <EditText
                            android:id="@+id/et_bz"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/dp_15"
                            android:layout_marginTop="10dp"
                            android:layout_marginRight="@dimen/dp_15"
                            android:background="@null"
                            android:hint="可以填写维持治疗连续性等信息，方便了解"
                            android:paddingTop="10dp"
                            android:paddingBottom="10dp"
                            android:textColor="#333333"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_1"
                            android:layout_marginLeft="15dp"
                            android:layout_marginTop="@dimen/dp_10"
                            android:layout_marginRight="15dp"
                            android:background="@color/color_line" />
                    </LinearLayout>
                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <TextView
            android:id="@+id/bt_end"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="@drawable/bg_color_main"
            android:gravity="center"
            android:paddingTop="12dp"
            android:paddingBottom="@dimen/dp_12"
            android:text="@string/submit"
            android:textColor="@color/white"
            android:textSize="14sp" />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>