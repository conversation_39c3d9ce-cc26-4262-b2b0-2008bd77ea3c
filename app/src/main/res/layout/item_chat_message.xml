<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- 用户消息 -->
    <LinearLayout
        android:id="@+id/userMessageLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="50dp"
            android:background="@drawable/bg_chat_user_message"
            android:maxWidth="280dp"
            android:minWidth="50dp"
            android:orientation="vertical"
            android:padding="12dp">

            <TextView
                android:id="@+id/userMessageText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#FFFFFF"
                android:textSize="15sp"
                android:contextClickable="true"
                android:longClickable="true"
                android:textIsSelectable="true" />
        </LinearLayout>
    </LinearLayout>

    <!-- AI回复 -->
    <LinearLayout
        android:id="@+id/aiMessageLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="0dp"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/thinkingContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="3dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/thinkingStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="思考中..."
                    android:textColor="#666666"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/thinkingTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textColor="#FCFCFC"
                    android:textSize="12sp"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/thinkingExpand"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginStart="8dp"
                    android:src="@drawable/ic_expand_more"
                    android:visibility="gone" />
            </LinearLayout>
            <!-- 思考内容 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <View
                    android:layout_width="2dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="8dp"
                    android:background="#DEDEDE" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/thinkingContent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="4dp"
                        android:background="#fafafa"
                        android:contextClickable="true"
                        android:longClickable="true"
                        android:padding="12dp"
                        android:textColor="@color/color_33"
                        android:textIsSelectable="true"
                        android:textSize="15sp"
                        android:visibility="gone" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>


        <!-- AI回复内容容器 -->
        <LinearLayout
            android:id="@+id/aiMessageContentContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <!-- AI回复内容 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxWidth="280dp"
                android:minWidth="50dp"
                android:orientation="vertical">

                <com.linggan.jd831.ui.works.ai.StreamTextView
                    android:id="@+id/ai_message_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_ai_message"
                    android:contextClickable="true"
                    android:longClickable="true"
                    android:minWidth="200dp"
                    android:overScrollMode="never"
                    android:paddingLeft="12dp"
                    android:paddingTop="8dp"
                    android:paddingRight="12dp"
                    android:paddingBottom="8dp"
                    android:scrollbars="none"
                    android:textIsSelectable="true" />
            </LinearLayout>

            <!-- 消息操作按钮 -->
            <GridLayout
                android:id="@+id/messageActions"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginStart="8dp"
                android:layout_marginTop="8dp"
                android:columnCount="2"
                android:useDefaultMargins="false"
                android:visibility="gone">

                <ImageButton
                    android:id="@+id/btnRefresh"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_marginEnd="16dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="刷新"
                    android:src="@drawable/ic_refresh" />

                <ImageButton
                    android:id="@+id/btnCopy"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="复制"
                    android:src="@drawable/ic_copy" />

            </GridLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout> 