<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:visibility="visible">


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="决定时间"
        android:textColor="#333333"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_sk_jd_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@null"
        android:drawableRight="@mipmap/ic_right"
        android:hint="请选择决定时间"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />

    <LinearLayout
        android:id="@+id/lin_sk_bdsj"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="15dp"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="报到时间"
            android:textColor="#333333"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_sk_bd_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/dp_15"
            android:background="@null"
            android:drawableRight="@mipmap/ic_right"
            android:hint="请选择报到时间"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="15dp"
            android:background="@color/color_line" />
    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="决定机关"
        android:textColor="#333333"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/et_sk_jd_jg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@null"
        android:hint="请输入决定机关"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="执行单位名称"
        android:textColor="#333333"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/et_sk_zxd_unit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@null"
        android:hint="请输入执行单位名称"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="执行单位地址"
        android:textColor="#333333"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/et_zxd_unit_dizhi"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@null"
        android:hint="请输入执行单位地址"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawablePadding="3dp"
        android:text="感染传染疾病"
        android:textColor="#333333"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_sk_crb"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:drawableRight="@mipmap/ic_right"
        android:hint="请选择是否感染传染疾病"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />


    <LinearLayout
        android:id="@+id/lin_sk_qj_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="15dp"
            android:drawablePadding="3dp"
            android:text="强戒出所日期"
            android:textColor="#333333"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_sk_qj_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/dp_15"
            android:drawableRight="@mipmap/ic_right"
            android:hint="请选择强戒出所日期"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="15dp"
            android:background="@color/color_line" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lin_sk_glcs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="15dp"
            android:drawablePadding="3dp"
            android:text="接受社区康复前所在的隔离场所"
            android:textColor="#333333"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/et_sk_glcs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/dp_15"
            android:background="@null"
            android:hint="请输入接受社区康复前所在的隔离场所"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="15dp"
            android:background="@color/color_line" />
    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawablePadding="3dp"
        android:text="参加药物维持治疗门诊"
        android:textColor="#333333"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/et_wzzl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@null"
        android:hint="请输入参加药物维持治疗门诊"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />

    <LinearLayout
        android:id="@+id/lin_kfcs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="15dp"
            android:drawablePadding="3dp"
            android:text="是否进入康复场所"
            android:textColor="#333333"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_kfcs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/dp_15"
            android:drawableRight="@mipmap/ic_right"
            android:hint="请选择是否进入康复场所"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="15dp"
            android:background="@color/color_line" />

    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawablePadding="3dp"
        android:text="是否自愿戒毒"
        android:textColor="#333333"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_sk_zyjd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:drawableRight="@mipmap/ic_right"
        android:hint="请选择是否自愿戒毒"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawablePadding="3dp"
        android:text="二级管控地"
        android:textColor="#333333"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_sk_ejgkd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:drawableRight="@mipmap/ic_right"
        android:hint="请选择二级管控地"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="协议书上传"
        android:textColor="#333333"
        android:textSize="14sp" />

    <com.linggan.jd831.widget.XGridViewForScrollView
        android:id="@+id/grid_img"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="30dp"
        android:horizontalSpacing="@dimen/dp_10"
        android:listSelector="@color/color_tran"
        android:numColumns="4"
        android:verticalSpacing="@dimen/dp_10" />

    <TextView
        android:id="@+id/tv_img_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="30dp"
        android:textColor="#8c8c8c"
        android:textSize="12sp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawablePadding="3dp"
        android:text="责任书上传"
        android:textColor="#333333"
        android:textSize="14sp" />

    <com.linggan.jd831.widget.XGridViewForScrollView
        android:id="@+id/grid_zrs_img"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="30dp"
        android:horizontalSpacing="@dimen/dp_10"
        android:listSelector="@color/color_tran"
        android:numColumns="4"
        android:verticalSpacing="@dimen/dp_10" />

    <TextView
        android:id="@+id/tv_img_info1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="30dp"
        android:textColor="#8c8c8c"
        android:textSize="12sp" />
</LinearLayout>