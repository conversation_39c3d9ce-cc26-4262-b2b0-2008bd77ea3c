<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base"
        layout="@layout/base_top_ground_img" />

    <androidx.core.widget.NestedScrollView
        style="@style/ll_m"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout style="@style/ll_v">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="25dp"
                android:paddingVertical="10dp"
                android:text="预警说明：预警的目标是为了实验室的规范管理，以提醒为主。 \n\n目前预警有非工作时段出入、非白名单出入两种，如果有临时加班超出工作时段请及时调整需要加班区域的开放时段；如果有非白名单出入的情况请时更新维护人员名单并尽量避免临时出入。"
                android:textColor="#343434"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:background="#F0F0F0" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="30dp"
                    android:text="关联预警条目"
                    android:layout_marginLeft="30dp"
                    android:textColor="#262626"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_alert_data"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="-"
                    android:layout_marginLeft="30dp"
                    android:textColor="#262626"
                    android:textSize="14sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="30dp"
                    android:background="@color/color_line" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@mipmap/ic_start"
                    android:drawablePadding="3dp"
                    android:text="处置方式"
                    android:textColor="@color/color_33"
                    android:textSize="14sp" />

                <EditText
                    android:id="@+id/et_warehouse_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="30dp"
                    android:background="@null"
                    android:hint="请输入处置方式"
                    android:maxLength="20"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:drawablePadding="3dp"
                    android:text="备注"
                    android:textColor="@color/color_33"
                    android:textSize="14sp" />

                <EditText
                    android:id="@+id/et_remark"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="30dp"
                    android:background="@null"
                    android:hint="请输入备注"
                    android:maxLength="20"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />
                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="30dp"
                    android:background="@color/color_line" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:drawablePadding="3dp"
                    android:text="附件"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <com.linggan.jd831.widget.XGridViewForScrollView
                    android:id="@+id/grid_fj"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="30dp"
                    android:horizontalSpacing="@dimen/dp_10"
                    android:listSelector="@color/color_tran"
                    android:numColumns="4"
                    android:verticalSpacing="@dimen/dp_10" />
                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="30dp"
                    android:background="@color/color_line" />

            </LinearLayout>
            <Button
                android:id="@+id/bt_sure"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginRight="30dp"
                android:layout_marginBottom="30dp"
                android:background="@drawable/bg_color_main"
                android:text="确定"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
