<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/ll_m"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <LinearLayout
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="50dp"
            android:layout_marginRight="50dp"
            android:layout_marginBottom="25dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginRight="20dp"
                android:background="@drawable/bg_blue_yuan"
                android:gravity="center"
                android:text="1"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <View
                android:id="@+id/view_one"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_1"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:background="@color/color_line" />

            <TextView
                android:id="@+id/tv_two"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:background="@drawable/bg_gray_yuan"
                android:gravity="center"
                android:text="2"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <View
                android:id="@+id/view_two"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_1"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:background="@color/color_line" />

            <TextView
                android:id="@+id/tv_three"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginLeft="20dp"
                android:background="@drawable/bg_gray_yuan"
                android:gravity="center"
                android:text="3"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>

        <LinearLayout style="@style/ll_v">

            <androidx.core.widget.NestedScrollView
                style="@style/ll_m"
                android:layout_weight="1"
                android:scrollbars="none">

                <LinearLayout
                    style="@style/ll_v"
                    android:focusable="true"
                    android:focusableInTouchMode="true">

                    <include
                        android:id="@+id/one"
                        layout="@layout/layout_msger_reg_one" />

                    <include
                        android:id="@+id/two"
                        layout="@layout/layout_msger_reg_two" />

                    <include
                        android:id="@+id/three"
                        layout="@layout/layout_msger_reg_three" />
                </LinearLayout>
            </androidx.core.widget.NestedScrollView>

            <TextView
                android:id="@+id/bt_add_ck"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="15dp"
                android:layout_marginRight="15dp"
                android:layout_marginBottom="@dimen/dp_10"
                android:gravity="center"
                android:text="新建仓库"
                android:textColor="#0053e2"
                android:textSize="14sp"
                android:visibility="gone" />

            <Button
                android:id="@+id/bt_next"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="15dp"
                android:layout_marginRight="15dp"
                android:layout_marginBottom="@dimen/dp_10"
                android:background="@drawable/bg_color_main"
                android:text="@string/next"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>