<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <androidx.core.widget.NestedScrollView
        style="@style/ll_m"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">


        <LinearLayout style="@style/ll_v">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="经办人"
                android:textColor="@color/color_33"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_jbr"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint=""
                    android:maxLength="20"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />


            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="报损数量"
                android:textColor="@color/color_33"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_zhu_ti"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint="请输入报损数量"
                    android:maxLength="20"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="毫升"
                    android:textColor="#333333"
                    android:textSize="14sp" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <LinearLayout
                android:id="@+id/lin_sjsk"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"

                android:layout_marginRight="30dp"
                android:orientation="vertical"
                android:visibility="visible">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableLeft="@mipmap/ic_start"
                    android:drawablePadding="@dimen/dp_5"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="@dimen/dp_12"
                    android:paddingBottom="@dimen/dp_12"
                    android:text="现场照片"
                    android:textColor="@color/color_26"
                    android:textSize="14sp" />

                <com.linggan.jd831.widget.XGridViewForScrollView
                    android:id="@+id/grid_baosun"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp"
                    android:horizontalSpacing="@dimen/dp_10"
                    android:listSelector="@color/color_tran"
                    android:numColumns="4"
                    android:verticalSpacing="@dimen/dp_10" />

                <TextView
                    android:id="@+id/tv_sjsk_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="#8c8c8c"
                    android:textSize="12sp" />


            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginRight="30dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawablePadding="@dimen/dp_5"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="@dimen/dp_12"
                    android:paddingBottom="@dimen/dp_12"
                    android:text="备注"
                    android:textColor="@color/color_26"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_no_dx_remark"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:drawableRight="@mipmap/ic_right"
                    android:hint="请输入备注"
                    android:paddingTop="@dimen/dp_12"
                    android:paddingRight="@dimen/dp_12"
                    android:paddingBottom="@dimen/dp_12"
                    android:textColor="@color/color_26"
                    android:textSize="14sp" />


            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <Button
                android:id="@+id/bt_sure"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginRight="30dp"
                android:layout_marginBottom="30dp"
                android:background="@drawable/bg_color_main"
                android:text="@string/sure"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>