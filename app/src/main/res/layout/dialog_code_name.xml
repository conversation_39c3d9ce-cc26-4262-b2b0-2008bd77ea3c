<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    xmlns:tools="http://schemas.android.com/tools"
    tools:viewBindingIgnore="true"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/bg_color"
        android:padding="@dimen/dp_10">

        <TextView
            android:id="@+id/bt_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:text="@string/cancle"
            android:textColor="@color/color_33"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/bt_sure"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:clickable="true"
            android:text="@string/sure"
            android:textColor="@color/color_33"
            android:textSize="16sp" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/color_line" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        android:orientation="horizontal">

        <com.linggan.jd831.widget.WheelCodeView
            android:id="@+id/mWheelView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:isCyclic="false"
            app:itemNumber="5"
            app:lineColor="@color/white"
            app:normalTextColor="@color/color_66"
            app:normalTextSize="16sp"
            app:selectedTextColor="@color/color_33"
            app:selectedTextSize="18sp"
            app:unitHeight="35dp" />

    </LinearLayout>
</LinearLayout>