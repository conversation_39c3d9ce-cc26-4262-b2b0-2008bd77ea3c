<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base"
        layout="@layout/base_top_ground_img" />

    <LinearLayout
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            style="@style/ll_m"
            android:layout_weight="1"
            android:scrollbars="none">

            <LinearLayout style="@style/ll_v">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_10"
                    android:background="@color/bg_color" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingBottom="@dimen/dp_12"
                    android:text="报备信息"
                    android:textColor="#262626"
                    android:textSize="16sp" />

                <include
                    android:id="@+id/sure"
                    layout="@layout/layout_baobei_sure" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_10"
                    android:background="@color/bg_color" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingBottom="@dimen/dp_10"
                    android:text="确认信息"
                    android:textColor="#262626"
                    android:textSize="16sp" />

                <include
                    android:id="@+id/msg"
                    layout="@layout/layout_baobei_sure_msg" />

                <View
                    android:id="@+id/view1"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_10"
                    android:background="@color/bg_color"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tv_zpbt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:text="抓拍信息"
                    android:textColor="#262626"
                    android:textSize="16sp"
                    android:visibility="gone" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/mRecycle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="4dp"
                    android:layout_marginTop="@dimen/dp_5"
                    android:layout_marginRight="4dp"
                    android:nestedScrollingEnabled="false" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <TextView
            android:id="@+id/bt_cx"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="15dp"
            android:background="@drawable/bg_blue_five"
            android:gravity="center"
            android:paddingTop="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_10"
            android:text="撤销报备"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:visibility="gone" />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>