<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:visibility="gone">


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="入所时间"
        android:textColor="#333333"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_xz_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@null"
        android:drawableRight="@mipmap/ic_right"
        android:hint="请选择入所时间"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="决定机关"
        android:textColor="#333333"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/et_xz_jd_jg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@null"
        android:hint="请输入决定机关"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="入所原因"
        android:textColor="#333333"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_xz_case"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@null"
        android:drawableRight="@mipmap/ic_right"
        android:hint="请选择入所原因"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="拘留地点"
        android:textColor="#333333"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/et_xz_di_dian"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@null"
        android:hint="请输入拘留地点"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />
</LinearLayout>