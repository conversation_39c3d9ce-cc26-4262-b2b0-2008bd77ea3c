<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="8dp"
    android:layout_marginHorizontal="10dp"
    android:background="@drawable/bg_transport_pending"
    android:orientation="vertical"
    android:paddingHorizontal="20dp"
    android:paddingVertical="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">


<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text=" |  "-->
<!--            android:textColor="#666666"-->
<!--            android:textSize="16sp" />-->

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="#333333"
            android:textSize="16sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/tv_status"
            android:layout_width="60dp"
            android:layout_height="25dp"
            android:src="@drawable/ic_pending_departure" />
    </LinearLayout>

<!--    <TextView-->
<!--        android:id="@+id/tv_city"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text=""-->
<!--        android:textColor="#0053E2"-->
<!--        android:textSize="16sp"-->
<!--        android:textStyle="bold" />-->

    <TextView
        android:id="@+id/tv_city"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp" />

    <TextView
        android:id="@+id/tv_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp" />

    <TextView
        android:id="@+id/tv_publish_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp" />

</LinearLayout> 