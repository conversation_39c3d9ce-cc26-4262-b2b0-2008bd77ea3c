<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/ll_m"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />


    <androidx.core.widget.NestedScrollView
        style="@style/ll_m"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            style="@style/ll_v"
            android:focusable="true"
            android:focusableInTouchMode="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="50dp"
                android:layout_marginTop="5dp"
                android:layout_marginRight="50dp"
                android:layout_marginBottom="25dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_marginRight="20dp"
                    android:background="@drawable/bg_blue_yuan"
                    android:gravity="center"
                    android:text="1"
                    android:textColor="@color/white"
                    android:textSize="14sp" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp_1"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:background="@color/color_line" />

                <TextView
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:background="@drawable/bg_gray_yuan"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="@color/white"
                    android:textSize="14sp" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp_1"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:background="@color/color_line" />

                <TextView
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_marginLeft="20dp"
                    android:background="@drawable/bg_gray_yuan"
                    android:gravity="center"
                    android:text="3"
                    android:textColor="@color/white"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:padding="12dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minWidth="120dp"
                    android:text="当前类型"
                    android:textColor="@color/color_33"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_peo_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="40dp"
                    android:textColor="@color/color_33"
                    android:textSize="15sp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp"
                android:background="@color/color_line" />

            <!--    设戒社康-->
            <include
                android:id="@+id/shejie_shekang"
                layout="@layout/layout_people_type_shejie_shekang" />
            <!--    强戒人员 /强戒未执行人员-->
            <include
                android:id="@+id/qiangjie"
                layout="@layout/layout_people_type_qiangjie" />

            <!--    强戒出所未执行社区康复//社会面有吸毒史/自愿戒毒人员-->
            <include
                android:id="@+id/wzx_xds"
                layout="@layout/layout_people_type_wzx_xds" />

            <!--  查获登记-->
            <include
                android:id="@+id/chahuo"
                layout="@layout/layout_people_type_chahuo" />

            <!--  服刑人员-->
            <include
                android:id="@+id/fuxing"
                layout="@layout/layout_people_type_fuxing" />

            <Button
                android:id="@+id/bt_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="20dp"
                android:background="@drawable/bg_color_main"
                android:text="更改"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>