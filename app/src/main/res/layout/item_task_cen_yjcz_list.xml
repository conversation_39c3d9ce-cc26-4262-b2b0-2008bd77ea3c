<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="12dp"
    android:layout_marginTop="5dp"
    android:layout_marginRight="12dp"
    android:layout_marginBottom="5dp"
    app:cardCornerRadius="5dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/dp_8">

        <ImageView
            android:id="@+id/iv_tag"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginTop="@dimen/dp_10"
            android:src="@mipmap/ic_yujing" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/dp_10">

            <LinearLayout
                android:id="@+id/lins"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/tv_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_8"
                        android:background="@drawable/bg_blue_two"
                        android:paddingLeft="4dp"
                        android:paddingTop="@dimen/dp_1"
                        android:paddingRight="4dp"
                        android:paddingBottom="@dimen/dp_1"
                        android:textColor="@color/white"
                        android:textSize="10sp"
                        android:visibility="gone" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_day"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="距离结束：0天"
                    android:textColor="#dd392f"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/lins"
                android:paddingTop="@dimen/dp_15">

                <TextView
                    android:id="@+id/tv_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:lines="1"
                    android:textColor="#262626"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="#ff8c8c8c"
                    android:textSize="14sp"
                    android:visibility="gone" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lin3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/lin"
                android:layout_marginTop="@dimen/dp_15">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="开始时间："
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_ks_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#333333"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/lin3"
                android:layout_marginTop="@dimen/dp_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="结束时间："
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_end_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#333333"
                    android:textSize="14sp" />
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>