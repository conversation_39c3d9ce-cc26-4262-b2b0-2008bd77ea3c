<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="@dimen/dp_15"
    android:layout_marginTop="@dimen/dp_5"
    android:layout_marginRight="@dimen/dp_15"
    android:layout_marginBottom="@dimen/dp_5"
    android:orientation="vertical">


    <TextView
        android:id="@+id/bt_title_del"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="@dimen/dp_10"
        android:drawablePadding="3dp"
        android:text="仓库1"
        android:textColor="#333333"
        android:textSize="13sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_blue_gray_line_five_kong"
        android:orientation="vertical">

        <EditText
            android:id="@+id/et_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:background="@null"
            android:hint="请输入仓库名"
            android:paddingLeft="@dimen/dp_10"
            android:paddingTop="5dp"
            android:paddingRight="@dimen/dp_10"
            android:paddingBottom="5dp"
            android:singleLine="true"
            android:textColor="#333333"
            android:textColorHint="#999999"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="@dimen/dp_10"
            android:background="@color/color_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/dp_10"
            android:layout_marginBottom="@dimen/dp_15">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="是否接入摄像头"
                android:textColor="#333333"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/iv_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/ic_swibut_gb" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/lin_xlh"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <EditText
                android:id="@+id/et_xxt_xlh"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@null"
                android:hint="请输入摄像头序列号(必填)"
                android:paddingLeft="@dimen/dp_10"
                android:paddingTop="5dp"
                android:paddingRight="@dimen/dp_10"
                android:paddingBottom="5dp"
                android:singleLine="true"
                android:textColor="#333333"
                android:textColorHint="#999999"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="@dimen/dp_10"
                android:background="@color/color_line" />

            <EditText
                android:id="@+id/et_xxt_yzm"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@null"
                android:hint="海康摄像头请输入验证码"
                android:paddingLeft="@dimen/dp_10"
                android:paddingTop="5dp"
                android:paddingRight="@dimen/dp_10"
                android:paddingBottom="5dp"
                android:singleLine="true"
                android:textColor="#333333"
                android:textColorHint="#999999"
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>