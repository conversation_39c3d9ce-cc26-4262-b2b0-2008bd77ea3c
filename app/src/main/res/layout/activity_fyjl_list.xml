<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white">

    <include
        android:id="@+id/base"
        layout="@layout/base_top_ground_img" />

    <LinearLayout
        style="@style/ll_v"
        android:background="@color/bg_color"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical"
            android:paddingLeft="@dimen/dp_10"
            android:paddingRight="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_10"
            android:visibility="gone"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:orientation="horizontal">

                <FrameLayout
                    android:id="@+id/search_container"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="10dp"
                    android:layout_weight="0.5"

                    android:background="@drawable/bg_gray_big_no_press">

                    <EditText
                        android:id="@+id/et_search"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:background="@null"
                        android:drawablePadding="14dp"
                        android:hint="请输入服药者姓名"
                        android:imeOptions="actionSearch"
                        android:lines="1"
                        android:paddingLeft="@dimen/dp_12"
                        android:paddingRight="4dp"
                        android:singleLine="true"
                        android:stateListAnimator="@null"
                        android:textSize="14sp" />
                </FrameLayout>

                <FrameLayout
                    android:id="@+id/date_container"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="0.5"
                    android:background="@drawable/bg_gray_big_no_press"
                    android:clickable="true"
                    android:focusable="true">

                    <TextView
                        android:id="@+id/tv_date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@null"
                        android:clickable="false"
                        android:focusable="false"
                        android:gravity="left|center_vertical"
                        android:minHeight="40dp"
                        android:paddingLeft="12dp"
                        android:paddingTop="8dp"
                        android:paddingRight="4dp"
                        android:paddingBottom="8dp"
                        android:stateListAnimator="@null"
                        android:text="请选择时间段"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />
                </FrameLayout>


            </LinearLayout>

            <!--            <LinearLayout-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginTop="10dp"-->
            <!--                android:orientation="horizontal">-->

            <!--                <LinearLayout-->
            <!--                    android:id="@+id/linearLayout2"-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_alignParentStart="true"-->
            <!--                    android:gravity="center_vertical"-->
            <!--                    android:orientation="horizontal">-->

            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="累计服药人员："-->
            <!--                        android:textColor="@color/color_33"-->
            <!--                        android:textSize="14sp" />-->

            <!--                    <TextView-->
            <!--                        android:id="@+id/tv_current_stock"-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="30升"-->
            <!--                        android:textColor="@color/color_33"-->
            <!--                        android:textSize="14sp" />-->
            <!--                </LinearLayout>-->
            <!--                <TextView-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:paddingHorizontal="5dp"-->
            <!--                    android:text=";"-->
            <!--                    android:textColor="@color/color_33"-->
            <!--                    android:textSize="14sp" />-->
            <!--                <LinearLayout-->
            <!--                    android:id="@+id/right_content"-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:gravity="center_vertical"-->
            <!--                    android:orientation="horizontal">-->


            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="累计服药量："-->
            <!--                        android:textColor="@color/color_33"-->
            <!--                        android:textSize="14sp" />-->

            <!--                    <TextView-->
            <!--                        android:id="@+id/tv_total_stock"-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="300升"-->
            <!--                        android:textColor="@color/color_33"-->
            <!--                        android:textSize="14sp" />-->
            <!--                </LinearLayout>-->
            <!--            </LinearLayout>-->

        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/linearLayout2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_marginLeft="4dp"
                android:layout_marginTop="4dp"
                android:layout_marginRight="6dp"
                android:visibility="gone"
                >


                <HorizontalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="none"
                    >

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="6dp"
                            android:background="@drawable/bg_gray_big"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="累计服药人员"
                                android:textColor="#666666"
                                android:textSize="12.5sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="4dp"
                                android:text="30人"
                                android:textColor="@color/black"
                                android:textSize="13sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="6dp"
                            android:background="@drawable/bg_gray_big"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="累计服药量"
                                android:textColor="#666666"
                                android:textSize="12.5sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="4dp"
                                android:text="10升"
                                android:textColor="@color/black"
                                android:textSize="13sp" />
                        </LinearLayout>


                    </LinearLayout>
                </HorizontalScrollView>
            </LinearLayout>

            <com.lgfzd.base.view.recycle.XRecyclerView
                android:id="@+id/recycler"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/linearLayout2" />

            <ImageView
                android:id="@+id/tv_no_data"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:src="@mipmap/nothing"
                android:visibility="gone" />
        </RelativeLayout>
    </LinearLayout>

    <!--    <com.google.android.material.floatingactionbutton.FloatingActionButton-->
    <!--        android:id="@+id/fab"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_gravity="bottom|end"-->
    <!--        android:layout_marginRight="20dp"-->
    <!--        android:layout_marginBottom="70dp"-->
    <!--        android:backgroundTint="@color/color_main"-->
    <!--        android:tint="@android:color/white"-->
    <!--        app:srcCompat="@android:drawable/ic_input_add" />-->
</androidx.coordinatorlayout.widget.CoordinatorLayout>