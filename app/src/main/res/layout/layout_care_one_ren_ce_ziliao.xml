<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lin_two"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:visibility="gone">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="是否需要提交资料\n审批："
            android:textColor="#8C8C8C"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_help"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_10"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#262626"
            android:textSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lin_is"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="12dp"
            android:paddingTop="@dimen/dp_10"
            android:paddingRight="12dp"
            android:paddingBottom="@dimen/dp_10">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="资料提交的平安关\n爱的类别"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_tj_lb"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_10"
                android:ellipsize="end"
                android:lines="1"
                android:textColor="#262626"
                android:textSize="14sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="12dp"
            android:paddingTop="@dimen/dp_10"
            android:paddingRight="12dp"
            android:paddingBottom="@dimen/dp_10">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="资料说明：\u3000\u3000\u3000"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_sm"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_10"
                android:ellipsize="end"
                android:lines="1"
                android:textColor="#262626"
                android:textSize="14sp" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="12dp"
            android:paddingTop="@dimen/dp_10"
            android:paddingRight="12dp"
            android:paddingBottom="@dimen/dp_10">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="资料上传时间:\u3000\u3000"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_10"
                android:ellipsize="end"
                android:lines="1"
                android:textColor="#262626"
                android:textSize="14sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="12dp"
            android:paddingTop="@dimen/dp_10"
            android:paddingRight="12dp"
            android:paddingBottom="@dimen/dp_10">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="附件：\u3000\u3000\u3000\u3000\u3000"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <com.linggan.jd831.widget.XGridViewForScrollView
                android:id="@+id/grid_img"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:numColumns="4" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>