<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nestedScrollView2"
        style="@style/ll_m"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout style="@style/ll_v">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="12dp"
                android:paddingTop="@dimen/dp_10"
                android:paddingRight="12dp"
                android:paddingBottom="@dimen/dp_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="系统剩余库存：\u3000\u3000\u3000"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_jbr"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#262626"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="12dp"
                android:paddingTop="@dimen/dp_10"
                android:paddingRight="12dp"
                android:paddingBottom="@dimen/dp_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="实际剩余库存：\u3000\u3000\u3000"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/et_zhu_ti"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@null"
                        android:hint="-"
                        android:maxLength="20"
                        android:textColor="#262626"
                        android:textSize="14sp" />


                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lin_sjsk"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="12dp"
                android:paddingTop="@dimen/dp_10"
                android:paddingRight="12dp"
                android:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_sjsk_info"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#8c8c8c"
                        android:textSize="12sp" />
                </LinearLayout>

                <com.linggan.jd831.widget.XGridViewForScrollView
                    android:id="@+id/grid_xczp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:horizontalSpacing="10dp"
                    android:listSelector="@color/color_tran"
                    android:numColumns="4"
                    android:verticalSpacing="10dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="12dp"
                android:paddingTop="@dimen/dp_10"
                android:paddingRight="12dp"
                android:paddingBottom="@dimen/dp_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="检查人员：\u3000\u3000\u3000\u3000\u3000"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_checker"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:hint="-"
                    android:textColor="#262626"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="12dp"
                android:paddingTop="@dimen/dp_10"
                android:paddingRight="12dp"
                android:paddingBottom="@dimen/dp_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="检测时间：\u3000\u3000\u3000\u3000\u3000"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_check_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:hint="-"
                    android:textColor="#262626"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="12dp"
                android:paddingTop="@dimen/dp_10"
                android:paddingRight="12dp"
                android:paddingBottom="@dimen/dp_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="提交人：\u3000\u3000\u3000\u3000\u3000\u3000"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_submitter"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:hint="-"
                    android:textColor="#262626"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="12dp"
                android:paddingTop="@dimen/dp_10"
                android:paddingRight="12dp"
                android:paddingBottom="@dimen/dp_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="联系电话：\u3000\u3000\u3000\u3000\u3000"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:hint="-"
                    android:textColor="#262626"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="12dp"
                android:paddingTop="@dimen/dp_10"
                android:paddingRight="12dp"
                android:paddingBottom="@dimen/dp_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="检查情况备注：\u3000\u3000\u3000"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_no_dx_remark"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:hint="-"
                    android:textColor="#262626"
                    android:textSize="14sp" />
            </LinearLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>