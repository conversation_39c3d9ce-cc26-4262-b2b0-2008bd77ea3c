<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/gen_lay"
    style="@style/coordinatorLayout_Style1"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base"
        layout="@layout/base_top_ground_img1" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/bg_color">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:nestedScrollingEnabled="false" />

            <ImageView
                android:id="@+id/tv_no_data"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:src="@mipmap/nothing"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/iv_yjz"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerInParent="true"
                android:layout_marginRight="50dp"
                android:src="@mipmap/ic_yjz_wj"
                android:visibility="gone" />
        </RelativeLayout>

        <Button
            android:id="@+id/bt_submit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="15dp"
            android:background="@drawable/bg_color_main"
            android:text="@string/submit"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>