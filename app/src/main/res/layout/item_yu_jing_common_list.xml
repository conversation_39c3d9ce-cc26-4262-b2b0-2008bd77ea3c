<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingTop="@dimen/dp_10"
    android:paddingBottom="@dimen/dp_10">

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#8C8C8C"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_code"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#262626"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_code1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/dp_12"
        android:visibility="gone"
        android:paddingRight="@dimen/dp_12"
        android:textColor="#262626"
        android:textSize="14sp" />
</LinearLayout>