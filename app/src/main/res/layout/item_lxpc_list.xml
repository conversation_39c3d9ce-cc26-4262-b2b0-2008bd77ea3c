<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="12dp"
    android:layout_marginTop="5dp"
    android:layout_marginRight="12dp"
    android:layout_marginBottom="5dp"
    app:cardCornerRadius="5dp"
    app:cardElevation="2dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/dp_10">

        <LinearLayout
            android:id="@+id/ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_marginEnd="5dp"
                android:paddingTop="5dp"
                android:src="@drawable/offline_clock" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="离线排查时间：\u3000"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="#333333"
                android:textSize="14sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/lin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll"
            android:paddingTop="@dimen/dp_10">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:paddingTop="5dp"
                android:layout_marginEnd="5dp"
                android:src="@drawable/offline_remark" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="备注：\u3000"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_remark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#333333"
                android:textSize="14sp"
                android:maxLines="1"
                android:singleLine="true" />
        </LinearLayout>

        <!-- 新增的删除按钮 -->
        <ImageButton
            android:id="@+id/btn_delete"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ps_ic_delete"
            android:visibility="gone" />

    </RelativeLayout>
</androidx.cardview.widget.CardView>
