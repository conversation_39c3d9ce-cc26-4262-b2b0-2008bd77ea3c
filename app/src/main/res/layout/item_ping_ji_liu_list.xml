<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingLeft="@dimen/dp_15"
    android:paddingRight="@dimen/dp_10">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:src="@mipmap/ic_choice_t" />

            <View
                android:id="@+id/view_two"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/color_main" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_10"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/bg_gray_five"
            android:orientation="vertical"
            android:padding="@dimen/dp_5">

            <TextView
                android:id="@+id/tv_df"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#1cba49"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_yj"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_5"
                android:text="审批意见："
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_sm"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_5"
                android:text="审批说明："
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_5"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="评审时间："
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_ps_sj"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#333333"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_5"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="评审附件："
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_psfj_wu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#333333"
                    android:textSize="14sp"
                    android:visibility="gone" />

                <com.linggan.jd831.widget.XGridViewForScrollView
                    android:id="@+id/grid_fj"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:horizontalSpacing="10dp"
                    android:numColumns="4"
                    android:scrollbars="none"
                    android:verticalSpacing="10dp" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>