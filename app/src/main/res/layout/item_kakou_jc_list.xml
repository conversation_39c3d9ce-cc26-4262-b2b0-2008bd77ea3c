<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="12dp"
    android:layout_marginTop="5dp"
    android:layout_marginRight="12dp"
    android:layout_marginBottom="5dp"
    app:cardCornerRadius="5dp"
    app:cardElevation="2dp">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/dp_10">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableLeft="@mipmap/ic_jccp"
            android:drawablePadding="@dimen/dp_10"
            android:textColor="#262626"
            android:textSize="16sp"
            android:textStyle="bold" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp_10">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="检测卡口：\u3000\u3000"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_kk"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#262626"
                android:textSize="14sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_10">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="被检人员：\u3000\u3000"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_jcr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#262626"
                android:textSize="14sp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/dp_10">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="检测时间：\u3000\u3000"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#262626"
                android:textSize="14sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="检测结果：\u3000\u3000"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_jg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#262626"
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>


</androidx.cardview.widget.CardView>