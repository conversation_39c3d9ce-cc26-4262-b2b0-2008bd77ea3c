<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="400dp"
    android:background="@color/white"
    android:orientation="vertical"
    tools:viewBindingIgnore="true">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/dp_12">

        <TextView
            android:id="@+id/tv_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="@dimen/dp_5"
            android:paddingRight="@dimen/dp_5"
            android:text="取消"
            android:textColor="@color/color_33"
            android:textSize="14sp"
            android:visibility="gone" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:text="选择辖区"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_btn_sure"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:paddingLeft="@dimen/dp_5"
            android:paddingRight="@dimen/dp_5"
            android:text="确定"
            android:textColor="@color/color_main"
            android:textSize="14sp"
            android:visibility="gone" />
    </RelativeLayout>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tablayout"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:tabIndicatorColor="#0053E2"
        app:tabIndicatorHeight="2dp"
        app:tabMode="scrollable"
        app:tabRippleColor="@null"
        app:tabSelectedTextColor="#0053E2"
        app:tabTextAppearance="@style/TabLayoutTextStyle"
        app:tabTextColor="@color/color_33" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/color_line" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="never" />
</LinearLayout>