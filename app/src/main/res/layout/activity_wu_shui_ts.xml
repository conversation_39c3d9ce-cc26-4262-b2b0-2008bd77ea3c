<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <LinearLayout
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_10"
            android:background="@color/bg_color" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:paddingLeft="@dimen/dp_12"
            android:paddingTop="@dimen/dp_12"
            android:text="酸碱度调整全流程"
            android:textColor="#333333"
            android:textSize="14sp" />

        <com.linggan.jd831.widget.XGridViewForScrollView
            android:id="@+id/grid_video_ts"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_12"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/dp_12"
            android:horizontalSpacing="@dimen/dp_10"
            android:listSelector="@color/color_tran"
            android:numColumns="4"
            android:scrollbars="none"
            android:verticalSpacing="@dimen/dp_10" />

        <TextView
            android:id="@+id/tv_video_ts_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_12"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/dp_12"
            android:textColor="@color/color_8c"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="@dimen/dp_12"
            android:background="@color/color_line" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_12"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/dp_12"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="无法当日送达原因"
            android:textColor="#333333"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/et_yy"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_12"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/dp_12"
            android:background="@null"
            android:hint="请输入无法当日送达原因"
            android:maxLength="200"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="@dimen/dp_12"
            android:layout_marginBottom="@dimen/dp_10"
            android:background="@color/color_line" />

        <View
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <Button
            android:id="@+id/bt_submit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="15dp"
            android:background="@drawable/bg_color_main"
            android:text="@string/submit"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>