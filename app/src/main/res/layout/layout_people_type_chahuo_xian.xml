<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:visibility="gone">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="查获时间："
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_cha_huo_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp"
        android:visibility="gone">

        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="毒品来源："
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_lai_yuan"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="查获单位名称："
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_unit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp"
        android:visibility="gone">


        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="执行单位地址："
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_address"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp"
        android:visibility="gone"

        >


        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="违法事实："
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_wfss"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">


        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="尿检结果："
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_urie"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp"
        android:visibility="gone">

        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="查获类型："
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_cha_huo_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp"
        android:visibility="gone">


        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="查获来源："
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_ch_ly"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">

        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text="备注："
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_remark"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lin_tjr_xm"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="12dp">


        <TextView
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:minWidth="120dp"
            android:text="提交人:"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_tjrxm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="40dp"
            android:textColor="@color/color_33"
            android:textSize="15sp" />
    </LinearLayout>
</LinearLayout>