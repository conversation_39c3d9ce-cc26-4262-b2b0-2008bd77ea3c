<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base_top"
          layout="@layout/base_top_ground_img" />

    <LinearLayout
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="30dp"
            android:paddingTop="20dp"
            android:paddingBottom="@dimen/dp_10"
            android:text="违反协议类型"
            android:textColor="#262626"
            android:textSize="24sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:drawablePadding="3dp"
            android:text="违反协议类型"
            android:textColor="#333333"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="30dp"
            android:drawableRight="@mipmap/ic_right"
            android:hint="请选择违反协议类型 "
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="30dp"
            android:background="@color/color_line" />

        <LinearLayout
            android:id="@+id/lin_urine"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawablePadding="3dp"
                android:text="无故外出日期"
                android:textColor="#333333"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_out_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:background="@null"
                android:drawableRight="@mipmap/ic_right"
                android:hint="请选择无故外出日期"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawablePadding="3dp"
                android:text="确认回归日期"
                android:textColor="#333333"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_hui_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:drawableRight="@mipmap/ic_right"
                android:hint="请选择确认回归日期"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="回归证明"
                android:textColor="#333333"
                android:textSize="14sp" />

            <com.linggan.jd831.widget.XGridViewForScrollView
                android:id="@+id/grid_img"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:horizontalSpacing="@dimen/dp_10"
                android:listSelector="@color/color_tran"
                android:numColumns="4"
                android:verticalSpacing="@dimen/dp_10" />

            <TextView
                android:id="@+id/tv_img_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:textColor="#8c8c8c"
                android:textSize="12sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/lin_other"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawablePadding="3dp"
                android:text="其他类型描述"
                android:textColor="#333333"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/et_other"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:background="@null"
                android:drawableRight="@mipmap/ic_right"
                android:hint="请输入其他类型描述"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />
        </LinearLayout>
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>