<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="派出所责任民警"
        android:textColor="#333333"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/et_pcs_mj"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="30dp"
        android:background="@null"
        android:hint="请输入派出所责任民警"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="30dp"
        android:background="@color/color_line" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="派出所责任领导"
        android:textColor="#333333"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/et_pcs_zrld"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="30dp"
        android:background="@null"
        android:hint="请输入派出所责任领导"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />


    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="30dp"
        android:background="@color/color_line" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="责任派出所"
        android:textColor="#333333"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/et_zr_pcs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="30dp"
        android:background="@null"
        android:hint="请输入责任派出所"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="30dp"
        android:background="@color/color_line" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="所属网格"
        android:textColor="#333333"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/et_wang_ge"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="30dp"
        android:background="@null"
        android:hint="请输入所属网格"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="30dp"
        android:background="@color/color_line" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="网格员"
        android:textColor="#333333"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/et_wang_geyuan"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="30dp"
        android:background="@null"
        android:hint="请输入网格员"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="30dp"
        android:background="@color/color_line" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="村(社)干部"
        android:textColor="#333333"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/et_cun_she_gb"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="30dp"
        android:background="@null"
        android:hint="请输入村(社)干部"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="30dp"
        android:background="@color/color_line" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="镇街干部"
        android:textColor="#333333"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/et_zhen_jie_gb"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="30dp"
        android:background="@null"
        android:hint="请输入镇街干部"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="30dp"
        android:background="@color/color_line" />
</LinearLayout>