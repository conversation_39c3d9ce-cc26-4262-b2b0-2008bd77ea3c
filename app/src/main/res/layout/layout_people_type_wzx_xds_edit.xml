<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:visibility="gone">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="执行时间"
        android:textColor="#333333"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_wzx_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@null"
        android:drawableRight="@mipmap/ic_right"
        android:hint="请选择执行时间"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />

    <LinearLayout
        android:id="@+id/lin_yyxq"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="15dp"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="原因详情"
            android:textColor="#333333"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_wzx_yyxq"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/dp_15"
            android:background="@null"
            android:drawableRight="@mipmap/ic_right"
            android:hint="请输入原因详情"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="15dp"
            android:background="@color/color_line" />
    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawablePadding="3dp"
        android:text="二级管控地"
        android:textColor="#333333"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_wzx_ejgkd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@null"
        android:drawableRight="@mipmap/ic_right"
        android:hint="请选择二级管控地"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />

    <LinearLayout
        android:id="@+id/lin_jdcs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="15dp"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="戒毒场所"
            android:textColor="#333333"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/et_wzx_jdcs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/dp_15"
            android:background="@null"
            android:hint="请输入戒毒场所"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="15dp"
            android:background="@color/color_line" />

    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="15dp"
        android:drawablePadding="3dp"
        android:text="备注"
        android:textColor="#333333"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_wzx_remark"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@null"
        android:drawableRight="@mipmap/ic_right"
        android:hint="请输入备注"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="15dp"
        android:background="@color/color_line" />
</LinearLayout>