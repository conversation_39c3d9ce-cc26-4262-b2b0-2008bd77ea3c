<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <androidx.core.widget.NestedScrollView
        style="@style/ll_m"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">


        <LinearLayout style="@style/ll_v">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawablePadding="3dp"
                android:text="排查内容"
                android:textColor="#333333"
                android:textSize="14sp"
                android:textStyle="bold" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawablePadding="3dp"
                android:text="现场照片"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <com.linggan.jd831.widget.XGridViewForScrollView
                android:id="@+id/grid_img"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:horizontalSpacing="@dimen/dp_10"
                android:listSelector="@color/color_tran"
                android:numColumns="4"
                android:verticalSpacing="@dimen/dp_10" />



            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawablePadding="3dp"
                android:text="现场视频"
                android:textColor="#8C8C8C"
                android:textSize="14sp" />

            <com.linggan.jd831.widget.XGridViewForScrollView
                android:id="@+id/grid_video"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:horizontalSpacing="@dimen/dp_10"
                android:listSelector="@color/color_tran"
                android:numColumns="4"
                android:verticalSpacing="@dimen/dp_10" />



            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="30dp"
                android:paddingTop="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="排查结果：\u3000\u3000\u3000"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_investigation_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#262626"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="30dp"
                android:paddingTop="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="备注：\u3000\u3000\u3000\u3000\u3000"
                    android:textColor="#8C8C8C"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_remark"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#262626"
                    android:textSize="14sp" />
            </LinearLayout>

            <View
                android:id="@+id/line_view"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_10"
                android:background="@color/color_line" />

            <LinearLayout
                android:id="@+id/add_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@mipmap/ic_start"
                    android:drawablePadding="3dp"
                    android:text="请选择离线记录对应排查的场所"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_place_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="30dp"
                    android:drawableRight="@mipmap/ic_right"
                    android:hint="请选择离线记录对应排查的场所"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <!--            <TextView-->
                <!--                android:layout_width="wrap_content"-->
                <!--                android:layout_height="wrap_content"-->
                <!--                android:layout_marginLeft="30dp"-->
                <!--                android:layout_marginTop="10dp"-->
                <!--                android:drawablePadding="3dp"-->
                <!--                android:text="备注："-->
                <!--                android:textColor="#8C8C8C"-->
                <!--                android:textSize="14sp" />-->

                <!--            <TextView-->
                <!--                android:id="@+id/tv_remark"-->
                <!--                android:layout_width="match_parent"-->
                <!--                android:layout_height="wrap_content"-->
                <!--                android:layout_marginHorizontal="30dp"-->
                <!--                android:layout_marginTop="10dp"-->
                <!--                android:paddingTop="10dp"-->
                <!--                android:paddingBottom="10dp"-->
                <!--                android:textColor="#262626"-->
                <!--                android:textSize="14sp" />-->

                <!--            <View-->
                <!--                android:layout_width="match_parent"-->
                <!--                android:layout_height="@dimen/dp_1"-->
                <!--                android:layout_marginLeft="30dp"-->
                <!--                android:layout_marginTop="@dimen/dp_10"-->
                <!--                android:layout_marginRight="30dp"-->
                <!--                android:background="@color/color_line" />-->

                <Button
                    android:id="@+id/bt_sure"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginRight="30dp"
                    android:layout_marginBottom="30dp"
                    android:background="@drawable/bg_color_main"
                    android:text="确定"
                    android:textColor="@color/white"
                    android:textSize="14sp" />
            </LinearLayout>
            <!--            <TextView-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginLeft="30dp"-->
            <!--                android:layout_marginTop="10dp"-->
            <!--                android:drawableLeft="@mipmap/ic_start"-->
            <!--                android:drawablePadding="3dp"-->
            <!--                android:text="请选择离线记录对应排查的场所"-->
            <!--                android:textColor="#333333"-->
            <!--                android:textSize="14sp" />-->

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>