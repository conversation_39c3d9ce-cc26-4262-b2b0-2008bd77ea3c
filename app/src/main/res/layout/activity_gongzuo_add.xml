<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <androidx.core.widget.NestedScrollView
        style="@style/ll_m"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">


        <LinearLayout style="@style/ll_v">

            <!--            <TextView-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginLeft="@dimen/dp_30"-->
            <!--                android:layout_marginTop="10dp"-->
            <!--                android:drawableLeft="@mipmap/ic_start"-->
            <!--                android:drawablePadding="3dp"-->
            <!--                android:text="身份证上传"-->
            <!--                android:textColor="#333333"-->
            <!--                android:textSize="14sp" />-->

            <!--            <LinearLayout-->
            <!--                android:id="@+id/ll_idcard"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginLeft="@dimen/dp_30"-->
            <!--                android:layout_marginTop="@dimen/dp_10"-->
            <!--                android:layout_marginRight="@dimen/dp_30"-->
            <!--                android:orientation="horizontal">-->

            <!--                <LinearLayout-->
            <!--                    android:id="@+id/ll_idcard_tips"-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_gravity="center_vertical"-->

            <!--                    android:layout_marginRight="@dimen/dp_15"-->
            <!--                    android:orientation="vertical">-->

            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="人像面"-->
            <!--                        android:textColor="#333333"-->
            <!--                        android:textSize="16sp"-->
            <!--                        android:textStyle="bold" />-->

            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:layout_marginTop="8dp"-->
            <!--                        android:text="上传身份证的人像面"-->
            <!--                        android:textColor="#666666"-->
            <!--                        android:textSize="14sp" />-->

            <!--                </LinearLayout>-->

            <!--                <com.makeramen.roundedimageview.RoundedImageView-->
            <!--                    android:id="@+id/iv_head"-->
            <!--                    android:layout_width="0dp"-->
            <!--                    android:layout_height="180dp"-->
            <!--                    android:layout_gravity="center"-->
            <!--                    android:layout_weight="1"-->
            <!--                    android:src="@drawable/ic_sfz_demo" />-->

            <!--            </LinearLayout>-->

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="姓名"
                android:textColor="@color/color_33"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.8"
                    android:background="@null"
                    android:hint="请输入姓名"
                    android:maxLength="20"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <Button
                    android:id="@+id/btn_idcard_scan"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/bg_btn_hollow_round"
                    android:drawablePadding="5dp"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:text="身份证识别录入"
                    android:textColor="@color/color_main"
                    android:textSize="12sp"
                    android:visibility="gone" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="身份证"
                android:textColor="@color/color_33"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/etIdCard"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@null"
                        android:hint="请输入身份证"
                        android:inputType="number"
                        android:maxLength="18"
                        android:maxLines="1"
                        android:paddingTop="10dp"
                        android:paddingBottom="10dp"
                        android:textColor="#333333"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_idcard_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="5dp"
                        android:text="0/18"
                        android:textColor="#999999"
                        android:textSize="12sp" />

                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="职务"
                android:textColor="@color/color_33"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/zhiwu"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint="请选择职务"
                    android:maxLength="20"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />


            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="联系电话"
                android:textColor="@color/color_33"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:hint="请输入联系电话"
                    android:inputType="phone"
                    android:maxLength="11"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />


            <LinearLayout
                android:id="@+id/lin_sjsk"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"

                android:layout_marginRight="30dp"
                android:orientation="vertical"
                android:visibility="visible">

                <TextView
                    android:id="@+id/tv_zyzs"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableLeft="@mipmap/ic_start"
                    android:drawablePadding="@dimen/dp_5"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="@dimen/dp_12"
                    android:paddingBottom="@dimen/dp_12"
                    android:text="执业证书"
                    android:textColor="@color/color_26"
                    android:textSize="14sp" />

                <com.linggan.jd831.widget.XGridViewForScrollView
                    android:id="@+id/grid_zyzs"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp"
                    android:horizontalSpacing="@dimen/dp_10"
                    android:listSelector="@color/color_tran"
                    android:numColumns="4"
                    android:verticalSpacing="@dimen/dp_10" />

                <TextView
                    android:id="@+id/tv_sjsk_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="#8c8c8c"
                    android:textSize="12sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginTop="@dimen/dp_10"
                    android:background="@color/color_line" />
            </LinearLayout>


            <Button
                android:id="@+id/bt_sure"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginRight="30dp"
                android:layout_marginBottom="30dp"
                android:background="@drawable/bg_color_main"
                android:text="@string/sure"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>