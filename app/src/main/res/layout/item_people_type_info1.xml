<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="5dp"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tv_dj_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_tran_write"
        android:gravity="center"
        android:paddingLeft="4dp"
        android:paddingTop="@dimen/dp_1"
        android:paddingRight="4dp"
        android:paddingBottom="@dimen/dp_1"
        android:textColor="@color/white"
        android:textSize="13sp" />

    <TextView
        android:id="@+id/tv_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_10"
        android:textColor="@color/white"
        android:textSize="13sp" />
</LinearLayout>