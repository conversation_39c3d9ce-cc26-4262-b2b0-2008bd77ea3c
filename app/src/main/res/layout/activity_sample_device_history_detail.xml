<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white">

    <include
        android:id="@+id/base"
        layout="@layout/base_top_ground_img" />

    <LinearLayout
        style="@style/ll_v"
        android:background="@color/bg_color"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- 设备详情内容 -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#FFFFFF"
                    android:orientation="vertical"
                    android:paddingHorizontal="26dp">

                    <!-- 基础信息 -->
                    <!--                    <TextView-->
                    <!--                        android:layout_width="wrap_content"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_marginBottom="8dp"-->
                    <!--                        android:text="基础信息"-->
                    <!--                        android:textColor="#333333"-->
                    <!--                        android:textSize="14sp"-->
                    <!--                        android:textStyle="bold" />-->

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:background="@android:color/white"
                        android:orientation="vertical"
                        android:paddingVertical="10dp">

                        <!-- 设备名称 -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="下发人：\u3000\u3000\u3000\u3000\u3000"
                                android:textColor="#8C8C8C"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_name"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="-"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>
                        <!-- 设备IMEI -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="18dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="开始采样时间：\u3000\u3000"
                                android:textColor="#8C8C8C"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_time"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="-"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <!-- 槽位数 -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="18dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="任务描述：\u3000\u3000\u3000\u3000"
                                android:textColor="#8C8C8C"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_desc"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="-"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>

                    </LinearLayout>
                </LinearLayout>


                <!-- 设备控制和设备日志部分 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#FFFFFF"
                    android:orientation="vertical"
                    android:paddingHorizontal="26dp"
                    android:paddingVertical="10dp">


                    <!-- 设备控制容器 -->
                    <LinearLayout
                        android:id="@+id/deviceControlContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:orientation="vertical" />

                </LinearLayout>

            </LinearLayout>

        </ScrollView>

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>