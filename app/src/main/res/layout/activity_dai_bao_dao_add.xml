<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base"
        layout="@layout/base_top_ground_img" />


    <LinearLayout
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            style="@style/ll_m"
            android:layout_weight="1"
            android:scrollbars="none">

            <LinearLayout style="@style/ll_v">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="姓名：\u3000\u3000\u3000\u3000\u3000\u3000"
                        android:textColor="#262626"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_aj_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="身份证：\u3000\u3000\u3000\u3000\u3000"
                        android:textColor="#262626"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_id_card"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="人员类型：\u3000\u3000\u3000\u3000"
                        android:textColor="#262626"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_peo_type"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="户籍地：\u3000\u3000\u3000\u3000\u3000"
                        android:textColor="#262626"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_hjd"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />

                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="决定时间：\u3000\u3000\u3000\u3000"
                        android:textColor="#262626"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_jd_time"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="最晚报到时间：\u3000\u3000"
                        android:textColor="#262626"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_zw_bd_time"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_margin="15dp"
                    android:background="@color/color_line" />

                <TextView
                    android:id="@+id/bt_look"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:drawableLeft="@mipmap/ic_edit"
                    android:drawablePadding="5dp"
                    android:gravity="center_vertical"
                    android:padding="10dp"
                    android:text="查看决定文书"
                    android:textColor="@color/color_main"
                    android:textSize="15sp" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <Button
            android:id="@+id/bt_update"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="15dp"
            android:background="@drawable/bg_color_main"
            android:gravity="center"
            android:text="已报到，前往更新人员类型"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>