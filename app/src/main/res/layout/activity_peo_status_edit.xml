<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/ll_m"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <androidx.core.widget.NestedScrollView
        style="@style/ll_m"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">


        <LinearLayout style="@style/ll_v">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="15dp"
                android:background="@drawable/bg_gray_sold_five"
                android:orientation="vertical"
                android:padding="@dimen/dp_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableLeft="@mipmap/ic_start"
                    android:drawablePadding="3dp"
                    android:text="状态"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <LinearLayout
                    android:id="@+id/bt_peo_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_10"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/tv_peo_type"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="请选择类型"
                        android:paddingTop="10dp"
                        android:paddingBottom="10dp"
                        android:textColor="#333333"
                        android:textSize="14sp" />

                    <ImageView
                        android:id="@+id/iv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_right"
                        android:visibility="gone" />
                </LinearLayout>
            </LinearLayout>

            <!--看守羁押（刑事）/（逮捕）-->
            <include
                android:id="@+id/ksjy_xs"
                layout="@layout/layout_people_status_kanshou_xingshi_edit" />

            <!--    精神异常    -->
            <include
                android:id="@+id/jsyc"
                layout="@layout/layout_people_status_jsyc_edit" />
            <!--    出国出境    -->
            <include
                android:id="@+id/cgcj"
                layout="@layout/layout_people_status_cgcj_edit" />
            <!--    死亡    -->
            <include
                android:id="@+id/siwang"
                layout="@layout/layout_people_status_siwang_edit" />
            <!--    行政拘留    -->
            <include
                android:id="@+id/xzjl"
                layout="@layout/layout_people_status_xzjuliu_edit" />
            <!--    精神异常入院治疗    -->
            <include
                android:id="@+id/jsyc_zyzl"
                layout="@layout/layout_people_status_jsyc_zl_edit" />
            <!--    指定居所监视居住 -->
            <include
                android:id="@+id/zdju_js"
                layout="@layout/layout_people_status_zdzs_js_edit" />
            <!--    脱失脱管    -->
            <include
                android:id="@+id/tstg"
                layout="@layout/layout_people_status_tstg_edit" />

            <!--    入监    -->
            <include
                android:id="@+id/rj"
                layout="@layout/layout_people_status_rj_edit" />

            <!--    异地服务    -->
            <include
                android:id="@+id/ydfw"
                layout="@layout/layout_people_status_ydfw_edit" />

            <!--    户籍地迁移    -->
            <include
                android:id="@+id/hjdqy"
                layout="@layout/layout_people_status_hjdqy_edit" />

            <!--    异地服务/户籍地迁移都有的 申请书/变更材料   -->
            <include
                android:id="@+id/ydfw_hjdqy"
                layout="@layout/layout_people_status_sqs_bgcl" />

            <!--    异地服务-工作函/信息表/复印件/协议书  -->
            <include
                android:id="@+id/yd_hj_sq_bg"
                layout="@layout/layout_people_status_gzh_xxb_fyj_xys" />

            <!--    异地服务/户籍地迁移都有的 审批表/执行含/通知书   -->
            <include
                android:id="@+id/yd_hj_zx_tz"
                layout="@layout/layout_people_status_spb_zxh_tzs" />

            <!--      户籍地迁移///证明材料   -->
            <include
                android:id="@+id/ydfw_hjzm"
                layout="@layout/layout_people_status_ydfw_hjzm" />

            <!--    取保候审   -->
            <include
                android:id="@+id/qbhs"
                layout="@layout/layout_people_status_spb_qbhs" />

            <!--    信息错误上报   -->
            <include
                android:id="@+id/cwxx"
                layout="@layout/layout_people_status_cwxx_sb" />
            <!--    超期未报到   -->
            <include
                android:id="@+id/cqwbd"
                layout="@layout/layout_people_status_cqwbd" />

            <!--    参加药物维持治疗   -->
            <include
                android:id="@+id/ywzl"
                layout="@layout/layout_people_status_ywwczl" />

            <!--            异地服务:以前的基础上增加的文书模块-->
            <include
                android:id="@+id/yd_zj_cl"
                layout="@layout/layout_people_status_ydfw_zjcl" />

            <!--            异地服务:以前的基础上增加的文书模块-->
            <include
                android:id="@+id/yslsjy"
                layout="@layout/layout_people_status_yslsjy" />

            <!-- 药物维持治疗-->
            <include
                android:id="@+id/ywwczl"
                layout="@layout/layout_people_status_ywzl" />


            <LinearLayout
                android:id="@+id/lin_di_fj"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="10dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:id="@+id/iv_fj_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="3dp"
                        android:src="@mipmap/ic_start"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tv_fj_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="附件"
                        android:textColor="#333333"
                        android:textSize="14sp" />
                </LinearLayout>

                <com.linggan.jd831.widget.XGridViewForScrollView
                    android:id="@+id/grid_img"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="15dp"
                    android:horizontalSpacing="@dimen/dp_10"
                    android:listSelector="@color/color_tran"
                    android:numColumns="4"
                    android:verticalSpacing="@dimen/dp_10" />

                <TextView
                    android:id="@+id/tv_img_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="15dp"
                    android:textColor="#8c8c8c"
                    android:textSize="12sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="15dp"
                    android:layout_marginBottom="@dimen/dp_10"
                    android:background="@color/color_line" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lin_remark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="10dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:id="@+id/iv_bz_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="3dp"
                        android:src="@mipmap/ic_start"
                        android:visibility="gone" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="备注"
                        android:textColor="#333333"
                        android:textSize="14sp" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_remark"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="15dp"
                    android:drawableRight="@mipmap/ic_right"
                    android:hint="请输入备注"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="15dp"
                    android:background="@color/color_line" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_cwxx_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="15dp"
                android:text="@string/cwxx_info"
                android:textColor="#ff0000"
                android:textSize="14sp"
                android:visibility="gone" />

            <Button
                android:id="@+id/bt_sure"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="15dp"
                android:background="@drawable/bg_color_main"
                android:text="确定"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>