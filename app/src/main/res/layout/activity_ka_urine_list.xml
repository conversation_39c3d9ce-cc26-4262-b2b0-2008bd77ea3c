<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <LinearLayout
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:background="@mipmap/bg_top_img"
            android:gravity="center_vertical"
            android:paddingTop="30dp">

            <include
                android:id="@+id/top"
                layout="@layout/base_search_top" />
        </RelativeLayout>


        <LinearLayout
            android:id="@+id/lin_type_top"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/dp_12"
            android:paddingTop="@dimen/dp_10"
            android:paddingRight="@dimen/dp_12"
            android:paddingBottom="@dimen/dp_10">


            <LinearLayout
                android:id="@+id/lin_type"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_area"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableRight="@mipmap/ic_downs"
                    android:drawablePadding="5dp"
                    android:singleLine="true"
                    android:text="地区"
                    android:textColor="#333333"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lin_lvl"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_lvl"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableRight="@mipmap/ic_downs"
                    android:drawablePadding="5dp"
                    android:singleLine="true"
                    android:text="检测卡口"
                    android:textColor="#333333"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lin_status"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableRight="@mipmap/ic_downs"
                    android:drawablePadding="5dp"
                    android:text="检测结果"
                    android:textColor="#333333"
                    android:textSize="14sp" />
            </LinearLayout>
        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/bg_color">

            <com.lgfzd.base.view.recycle.XRecyclerView
                android:id="@+id/recycler"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <ImageView
                android:id="@+id/iv_no_data"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:src="@mipmap/nothing"
                android:visibility="gone" />
        </RelativeLayout>
    </LinearLayout>


    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="70dp"
        android:backgroundTint="@color/color_main"
        android:tint="@android:color/white"
        app:srcCompat="@android:drawable/ic_input_add" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>