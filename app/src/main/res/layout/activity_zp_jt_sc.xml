<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base"
        layout="@layout/base_top_ground_zp" />

    <LinearLayout
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <RelativeLayout
            android:id="@+id/bt_close"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_12">

            <TextView
                android:id="@+id/bt_sxt_mc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:drawableRight="@mipmap/ic_downs"
                android:drawablePadding="@dimen/dp_5"
                android:textColor="#363636"
                android:textSize="14sp" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:src="@mipmap/ic_del2" />
        </RelativeLayout>


        <xyz.doikki.videoplayer.player.VideoView
            android:id="@+id/mVideo"
            android:layout_width="match_parent"
            android:layout_height="200dp" />

        <LinearLayout
            android:id="@+id/lin_ts"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:padding="@dimen/dp_12">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:src="@mipmap/ic_jgao" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_10"
                android:text="请将人脸正对摄像头，并点击下方的截屏按钮进行截屏，再选中包含您清晰人像的照片提交认证。"
                android:textColor="#363636"
                android:textSize="15sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_jp_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_12"
            android:text="我的截屏"
            android:textColor="#ff262626"
            android:textSize="16sp"
            android:visibility="gone" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mRecycle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:nestedScrollingEnabled="false"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dp_12">

            <TextView
                android:id="@+id/bt_tj"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/bg_blue_line_big"
                android:gravity="center"
                android:paddingTop="@dimen/dp_10"
                android:paddingBottom="@dimen/dp_10"
                android:text="提交"
                android:textColor="@color/color_main"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/bt_sure"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_weight="2"
                android:background="@drawable/bg_blue_big"
                android:gravity="center"
                android:paddingTop="@dimen/dp_10"
                android:paddingBottom="@dimen/dp_10"
                android:text="截屏认证"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>