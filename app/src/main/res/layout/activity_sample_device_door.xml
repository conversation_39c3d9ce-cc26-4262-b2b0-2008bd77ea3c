<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base_top"
        layout="@layout/base_top_ground_img" />

    <LinearLayout
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">


        <LinearLayout
            android:id="@+id/bt_yy"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="@dimen/dp_10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

<!--            <TextView-->
<!--                android:id="@+id/tv_title"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_weight="1"-->
<!--                android:text="家访内容"-->
<!--                android:textColor="#262626"-->
<!--                android:textSize="24sp"-->
<!--                android:textStyle="bold" />-->

            <ImageView
                android:id="@+id/iv_yy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_yy_ht"
                android:visibility="gone" />
        </LinearLayout>

        <EditText
            android:id="@+id/et_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:background="@null"
            android:hint="请输入开关门原因，比如放置采样瓶/拿去采样瓶， 不超过200字。"
            android:paddingTop="@dimen/dp_15"
            android:paddingBottom="@dimen/dp_15"
            android:textColor="@color/color_33"
            android:textColorHint="#bfbfbf"
            android:textSize="14sp" />


        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:background="@color/color_line" />

        <View
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:visibility="gone" />

        <Button
            android:id="@+id/bt_save"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="30dp"
            android:background="@drawable/bg_color_main"
            android:gravity="center"
            android:text="@string/sure"
            android:textColor="@color/white"
            android:textSize="15sp"
             />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>