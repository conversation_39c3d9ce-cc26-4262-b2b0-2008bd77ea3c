<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingLeft="@dimen/dp_12"
    android:paddingTop="@dimen/dp_12"
    android:paddingRight="@dimen/dp_12">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center">

        <ImageView
            android:id="@+id/iv_tag"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@mipmap/ic_bqian" />

        <TextView
            android:id="@+id/tv_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="2dp"
            android:gravity="center"
            android:text="1"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold" />
    </FrameLayout>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_5">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#ff262626"
            android:textSize="16sp" />

        <LinearLayout
            android:id="@+id/lin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_title"
            android:layout_marginTop="@dimen/dp_10"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_num"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:drawableLeft="@mipmap/ic_look"
                android:drawablePadding="5dp"
                android:gravity="center_vertical"
                android:text="0"
                android:textColor="#8c8c8c"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_gjc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:lines="1"
                android:textColor="#ff8c8c8c"
                android:textSize="14sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_below="@+id/lin"
            android:layout_marginTop="12dp"
            android:background="@color/color_line" />
    </RelativeLayout>
</LinearLayout>