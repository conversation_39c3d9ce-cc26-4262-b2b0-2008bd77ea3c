<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/card"
    android:visibility="gone"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:id="@+id/tv_unit_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="执行单位名称：\u3000\u3000\u3000"
            android:textColor="#262626"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_xs_unit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#262626"
            android:textSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lin_dizhi"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="执行单位详细地址：\u3000"
            android:textColor="#262626"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_xs_address"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#262626"
            android:textSize="14sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="决定机关：\u3000\u3000\u3000\u3000\u3000"
            android:textColor="#262626"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_xs_jg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#262626"
            android:textSize="14sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="入所时间：\u3000\u3000\u3000\u3000\u3000"
            android:textColor="#262626"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_xs_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#262626"
            android:textSize="14sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="入所原因：\u3000\u3000\u3000\u3000\u3000"
            android:textColor="#262626"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_xs_case"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#262626"
            android:textSize="14sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="涉案类型：\u3000\u3000\u3000\u3000\u3000"
            android:textColor="#262626"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_xs_salx"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#262626"
            android:textSize="14sp" />
    </LinearLayout>
</LinearLayout>