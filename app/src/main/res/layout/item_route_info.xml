<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="42.5dp"
    android:orientation="horizontal"
    android:gravity="center_vertical">

    <ImageView
        android:layout_width="42.5dp"
        android:layout_height="42.5dp"
        android:src="@drawable/ic_transport_road"
        android:padding="0dp"
        android:scaleType="fitXY"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="8dp"
        android:paddingBottom="8dp">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="途径："
            android:textColor="#0053E2"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/tv_route"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#0053E2"
            android:textSize="15sp"
            android:singleLine="true"
            android:ellipsize="marquee"
            android:marqueeRepeatLimit="marquee_forever"
            android:scrollHorizontally="true"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:freezesText="true"/>
    </LinearLayout>

</LinearLayout> 