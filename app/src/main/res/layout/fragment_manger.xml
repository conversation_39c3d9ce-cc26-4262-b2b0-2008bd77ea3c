<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:viewBindingIgnore="true">

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp" />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/mSwipe"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/mNested"
            style="@style/ll_m"
            android:fillViewport="true"
            android:scrollbars="none">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@mipmap/ic_bg_mager" />

                <TextView
                    android:id="@+id/tv_manager_unit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_15"
                    android:layout_marginTop="50dp"
                    android:layout_marginRight="@dimen/dp_15"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:paddingLeft="12dp"
                    android:paddingTop="7dp"
                    android:paddingRight="12dp"
                    android:paddingBottom="7dp"
                    android:textColor="@color/white"
                    android:textSize="24sp"
                    android:textStyle="bold" />

                <RelativeLayout
                    android:id="@+id/lin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_manager_unit"
                    android:orientation="horizontal"
                    android:paddingLeft="20dp"
                    android:paddingTop="@dimen/dp_20"
                    android:paddingRight="@dimen/dp_20"
                    android:paddingBottom="@dimen/dp_20">

                    <LinearLayout
                        android:id="@+id/ll_lab_sxt"
                        android:paddingLeft="10dp"

                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone" />
                    <TextView
                        android:id="@+id/tv_dj"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/white"
                        android:textSize="35sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_dj_df"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignBottom="@+id/tv_dj"
                        android:layout_marginLeft="@dimen/dp_10"
                        android:layout_toRightOf="@+id/tv_dj"
                        android:textColor="@color/white"
                        android:textSize="25sp" />

                    <ImageView
                        android:id="@+id/iv_sxt"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true" />
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/lin"
                    android:layout_marginTop="@dimen/dp_10"
                    android:background="@drawable/bg_white_top"
                    android:minHeight="500dp"
                    android:orientation="vertical">
                    <!-- 在适当的位置添加以下代码 -->
                    <LinearLayout
                        android:id="@+id/menu_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" />
<!--                    -->
<!--                    <include-->
<!--                        android:id="@+id/lin_cang_guan_menu"-->
<!--                        layout="@layout/layout_cang_guan_menu" />-->

<!--                    <include-->
<!--                        android:id="@+id/lin_ren_yuan_menu"-->
<!--                        layout="@layout/layout_ren_yuan_menu" />-->

                    <TextView
                        android:id="@+id/tv_bb_bt"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="@dimen/dp_15"
                        android:paddingTop="@dimen/dp_15"
                        android:paddingBottom="@dimen/dp_15"
                        android:text="最新报备动态"
                        android:textColor="#262626"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <com.lgfzd.base.view.recycle.XRecyclerView
                        android:id="@+id/recycle_mag"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="3dp"
                        android:layout_marginRight="3dp"
                        android:nestedScrollingEnabled="false" />

                    <TextView
                        android:id="@+id/tv_pl_bt"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="@dimen/dp_15"
                        android:paddingTop="@dimen/dp_15"
                        android:paddingBottom="@dimen/dp_15"
                        android:text="等级评定中心"
                        android:textColor="#262626"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <com.lgfzd.base.view.recycle.XRecyclerView
                        android:id="@+id/recycle_dj"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="3dp"
                        android:layout_marginRight="3dp"
                        android:nestedScrollingEnabled="false" />
                </LinearLayout>
            </RelativeLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
</LinearLayout>