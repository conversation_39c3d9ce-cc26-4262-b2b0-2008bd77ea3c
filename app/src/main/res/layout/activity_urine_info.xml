<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base"
        layout="@layout/base_top_ground_img" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">


        <androidx.core.widget.NestedScrollView
            style="@style/ll_m"
            android:scrollbars="none">

            <LinearLayout
                style="@style/ll_v"
                android:paddingBottom="300dp">

                <include
                    android:id="@+id/top"
                    layout="@layout/layout_people_info_top" />

                <TextView
                    android:id="@+id/tv_base_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="12dp"
                    android:text="检测信息"
                    android:textColor="#262626"
                    android:textSize="16sp" />

                <LinearLayout
                    android:id="@+id/ll_work_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="工作人员：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_work_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_test_method"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="检测方式：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_type"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/ll_test_person"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="检测人员：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_jcry"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_test_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="检测类型：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_lx"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_test_cycle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="检测周期：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_zhouqi"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_test_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="检测时间：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_time"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_test_result"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="检测结果：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_result"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_test_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="检测地址：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/tvPhdPjLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="配合度评价：\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_phd_pj"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/phdPjLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="不配合尿检\n的具体表现：\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_bphbx"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_test_report"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="检测报告：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <com.linggan.jd831.widget.XGridViewForScrollView
                        android:id="@+id/grid_img"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:horizontalSpacing="10dp"
                        android:numColumns="3"
                        android:scrollbars="none"
                        android:verticalSpacing="10dp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_notification_report"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="尿检通知单：\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <com.linggan.jd831.widget.XGridViewForScrollView
                        android:id="@+id/grid_img_notification"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:horizontalSpacing="10dp"
                        android:numColumns="3"
                        android:scrollbars="none"
                        android:verticalSpacing="10dp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_video_upload"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="视频上传：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <com.linggan.jd831.widget.XGridViewForScrollView
                        android:id="@+id/grid_video"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:horizontalSpacing="10dp"
                        android:numColumns="3"
                        android:scrollbars="none"
                        android:verticalSpacing="10dp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_attachment_upload"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="附件上传：\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <com.linggan.jd831.widget.XGridViewForScrollView
                        android:id="@+id/grid_fj"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:horizontalSpacing="10dp"
                        android:numColumns="3"
                        android:scrollbars="none"
                        android:verticalSpacing="10dp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_remark"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp_10"
                    android:paddingRight="12dp"
                    android:paddingBottom="@dimen/dp_10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="备注：\u3000\u3000\u3000\u3000"
                        android:textColor="#8C8C8C"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_remark"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#262626"
                        android:textSize="14sp" />
                </LinearLayout>

                <include
                    android:id="@+id/peo"
                    layout="@layout/layout_people_info_base" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <include
            android:id="@+id/lin_popup"
            layout="@layout/layout_popup_sp" />
    </FrameLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>