<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include
        android:id="@+id/base"
        layout="@layout/base_top_ground_img" />

    <LinearLayout
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">


        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableLeft="@mipmap/ic_start"
            android:padding="@dimen/dp_12"
            android:text="参与信息评分"
            android:textColor="#262626"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/et_jz_lx"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@null"
            android:hint="请输入评分"
            android:inputType="number|numberDecimal"
            android:lines="1"
            android:padding="@dimen/dp_12"
            android:singleLine="true"
            android:textColor="@color/color_26"
            android:textColorHint="#bfbfbf"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="@dimen/dp_12"
            android:layout_marginRight="@dimen/dp_12"
            android:background="@color/color_line" />


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="12dp"
            android:text="分数X，0.0≤X≤100.0，保留1位小数"
            android:textColor="#8c8c8c"
            android:textSize="12sp" />


        <View
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <Button
            android:id="@+id/bt_sure"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_margin="30dp"
            android:background="@drawable/bg_color_main"
            android:gravity="center"
            android:text="提交"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>