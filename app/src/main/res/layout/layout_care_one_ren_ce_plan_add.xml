<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lin_one"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="3dp"
        android:text="是否启用帮扶计划"
        android:textColor="#333333"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_one_pfjh"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="30dp"
        android:drawableRight="@mipmap/ic_right"
        android:hint="请选择是否启用帮扶计划"
        android:paddingTop="10dp"
        android:text="不启用"
        android:paddingBottom="10dp"
        android:textColor="#333333"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="30dp"
        android:background="@color/color_line" />

    <LinearLayout
        android:id="@+id/lin_is"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="帮扶计划说明"
            android:textColor="#333333"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/et_plan_dec"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="30dp"
            android:background="@null"
            android:hint="请输入帮扶计划说明"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="30dp"
            android:background="@color/color_line" />

        <LinearLayout
            android:id="@+id/lin_plan_person"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="计划参与平安关爱的人员"
                android:textColor="#333333"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/et_plan_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:background="@null"
                android:hint="请输入姓名"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="30dp"
                android:background="@color/color_line" />
        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="计划平安关爱的类别"
            android:textColor="#333333"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_ga_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="30dp"
            android:background="@null"
            android:drawableRight="@mipmap/ic_right"
            android:hint="请选择计划平安关爱的类别"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textSize="14sp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycle_nr"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            android:visibility="gone" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="30dp"
            android:background="@color/color_line" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="帮扶计划（预计）完成时间"
            android:textColor="#333333"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_one_plan_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="30dp"
            android:drawableRight="@mipmap/ic_right"
            android:hint="请选择帮扶计划（预计）完成时间"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#333333"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="30dp"
            android:background="@color/color_line" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="3dp"
            android:text="附件"
            android:textColor="#333333"
            android:textSize="14sp" />

        <com.linggan.jd831.widget.XGridViewForScrollView
            android:id="@+id/grid_img"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="30dp"
            android:horizontalSpacing="@dimen/dp_10"
            android:listSelector="@color/color_tran"
            android:numColumns="4"
            android:scrollbars="none"
            android:verticalSpacing="@dimen/dp_10" />

        <TextView
            android:id="@+id/tv_photo_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="30dp"
            android:textColor="#8c8c8c"
            android:textSize="12sp" />
    </LinearLayout>
</LinearLayout>