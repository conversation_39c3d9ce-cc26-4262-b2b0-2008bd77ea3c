<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <LinearLayout
        style="@style/ll_v"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            style="@style/ll_m"
            android:layout_weight="1"
            android:background="@color/bg_color"
            android:scrollbars="none">

            <LinearLayout
                style="@style/ll_w"
                android:background="@color/white">

                <include
                    android:id="@+id/top"
                    layout="@layout/layout_wushui_cy_info" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_8"
                    android:layout_marginTop="@dimen/dp_10"
                    android:background="@color/bg_color" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dp_10"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="18dp"
                        android:padding="12dp"
                        android:text="送样信息"
                        android:textColor="#262626"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_30"
                        android:layout_marginRight="@dimen/dp_30"
                        android:background="@drawable/bg_gray_five"
                        android:orientation="vertical">


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingLeft="12dp"
                            android:paddingTop="@dimen/dp_10"
                            android:paddingRight="12dp"
                            android:paddingBottom="@dimen/dp_10">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="收样人姓名：\u3000"
                                android:textColor="#8C8C8C"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_syr_name"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#262626"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingLeft="12dp"
                            android:paddingTop="@dimen/dp_10"
                            android:paddingRight="12dp"
                            android:paddingBottom="@dimen/dp_10">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="收样人电话：\u3000"
                                android:textColor="#8C8C8C"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_syr_dh"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#262626"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingLeft="12dp"
                            android:paddingTop="@dimen/dp_10"
                            android:paddingRight="12dp"
                            android:paddingBottom="@dimen/dp_10">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="送样地址：\u3000\u3000"
                                android:textColor="#8C8C8C"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_sy_dizzhi"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#262626"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:drawableLeft="@mipmap/ic_start"
                    android:drawablePadding="3dp"
                    android:text="送样时间："
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_sys_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="30dp"
                    android:drawableRight="@mipmap/ic_right"
                    android:hint="请选择送样时间"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="30dp"
                    android:background="@color/color_line" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@mipmap/ic_start"
                    android:drawablePadding="3dp"
                    android:text="送样方式："
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_sys_fs"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="30dp"
                    android:background="@null"
                    android:drawableRight="@mipmap/ic_right"
                    android:hint="请选择送样方式"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="30dp"
                    android:background="@color/color_line" />


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@mipmap/ic_start"
                    android:drawablePadding="3dp"
                    android:text="送样人："
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <EditText
                    android:id="@+id/et_sys_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="30dp"
                    android:background="@null"
                    android:hint="请输入送样人姓名"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="30dp"
                    android:background="@color/color_line" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@mipmap/ic_start"
                    android:drawablePadding="3dp"
                    android:text="送样证明（2张照片）:"
                    android:textColor="#333333"
                    android:textSize="14sp" />

                <com.linggan.jd831.widget.XGridViewForScrollView
                    android:id="@+id/grid_img"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="30dp"
                    android:horizontalSpacing="@dimen/dp_10"
                    android:listSelector="@color/color_tran"
                    android:numColumns="4"
                    android:scrollbars="none"
                    android:verticalSpacing="@dimen/dp_10" />

                <TextView
                    android:id="@+id/tv_img_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="30dp"
                    android:layout_marginBottom="@dimen/dp_12"
                    android:text="@string/sy_zp_sm"
                    android:textColor="#8c8c8c"
                    android:textSize="12sp" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <Button
            android:id="@+id/bt_submit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="15dp"
            android:background="@drawable/bg_color_main"
            android:text="@string/sure"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>