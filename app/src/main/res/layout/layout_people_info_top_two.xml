<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lin_info"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">


    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp"
        android:text="基础信息"
        android:textColor="#262626"
        android:textSize="16sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="姓名：\u3000\u3000\u3000\u3000"
            android:textColor="#8C8C8C"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_mobile"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#262626"
            android:textSize="14sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="性别：\u3000\u3000\u3000\u3000"
            android:textColor="#8C8C8C"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_sex"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#262626"
            android:textSize="14sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="身份证：\u3000\u3000\u3000"
            android:textColor="#8C8C8C"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_id_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#262626"
            android:textSize="14sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="出生日期：\u3000\u3000"
            android:textColor="#8C8C8C"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_brith"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#262626"
            android:textSize="14sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="户籍地详址：\u3000"
            android:textColor="#8C8C8C"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_hjdxz"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#262626"
            android:textSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="现执行地：\u3000\u3000"
            android:textColor="#8C8C8C"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_xzxd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#262626"
            android:textSize="14sp" />
    </LinearLayout>

    <!--    <LinearLayout-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:paddingLeft="12dp"-->
    <!--        android:paddingTop="@dimen/dp_10"-->
    <!--        android:paddingRight="12dp"-->
    <!--        android:paddingBottom="@dimen/dp_10">-->

    <!--        <TextView-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:text="社戒社康执行日\n期："-->
    <!--            android:textColor="#8C8C8C"-->
    <!--            android:textSize="14sp" />-->

    <!--        <TextView-->
    <!--            android:id="@+id/tv_edu"-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:ellipsize="end"-->
    <!--            android:lines="1"-->
    <!--            android:textColor="#262626"-->
    <!--            android:textSize="14sp" />-->
    <!--    </LinearLayout>-->
</LinearLayout>