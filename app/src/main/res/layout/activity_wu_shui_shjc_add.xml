<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />


    <androidx.core.widget.NestedScrollView
        style="@style/ll_m"
        android:background="@color/bg_color"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout style="@style/ll_v">

            <include
                android:id="@+id/top"
                layout="@layout/layout_wushui_base_info" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                android:background="@color/white"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="12dp"
                    android:text="采样信息"
                    android:textColor="#262626"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/mRecycle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginLeft="@dimen/dp_12"
                    android:layout_marginRight="@dimen/dp_12"
                    android:background="@color/color_line" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="12dp"
                    android:text="混样信息"
                    android:textColor="#262626"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/bt_hy_cj"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_12"
                    android:layout_marginRight="@dimen/dp_12"
                    android:layout_marginBottom="15dp"
                    android:background="@drawable/bg_gray_five"
                    android:drawableRight="@mipmap/ic_right"
                    android:padding="11dp"
                    android:text="混合采样"
                    android:textColor="#bfbfbf"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lin_sy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginBottom="@dimen/dp_10"
                android:background="@color/white"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="12dp"
                    android:text="送样信息"
                    android:textColor="#262626"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginBottom="@dimen/dp_12"
                    android:background="@drawable/bg_gray_five"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="12dp"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingRight="12dp"
                        android:paddingBottom="@dimen/dp_10">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="收样人姓名：\u3000"
                            android:textColor="#8C8C8C"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_syr_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="#262626"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="12dp"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingRight="12dp"
                        android:paddingBottom="@dimen/dp_10">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="收样人电话：\u3000"
                            android:textColor="#8C8C8C"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_syr_dh"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="#262626"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="12dp"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingRight="12dp"
                        android:paddingBottom="@dimen/dp_10">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="送样地址：\u3000\u3000"
                            android:textColor="#8C8C8C"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_sy_dzz"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="#262626"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>