<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableLeft="@mipmap/ic_start"
        android:drawablePadding="@dimen/dp_5"
        android:paddingLeft="@dimen/dp_12"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_12"
        android:textColor="@color/color_26"
        android:textSize="14sp" />

    <com.linggan.jd831.widget.XGridViewForScrollView
        android:id="@+id/grid_xx_img"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:horizontalSpacing="@dimen/dp_10"
        android:listSelector="@color/color_tran"
        android:numColumns="4"
        android:verticalSpacing="@dimen/dp_10" />

    <TextView
        android:id="@+id/tv_xx_img_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="12dp"
        android:textColor="#8c8c8c"
        android:textSize="12sp" />
</LinearLayout>