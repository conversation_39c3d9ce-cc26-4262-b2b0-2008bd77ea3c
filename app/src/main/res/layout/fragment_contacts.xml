<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:viewBindingIgnore="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
   >


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@mipmap/ic_work_bg" />

        <TextView
            android:id="@+id/bt_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginTop="40dp"
            android:layout_marginRight="@dimen/dp_15"
            android:layout_marginBottom="@dimen/dp_15"
            android:drawableLeft="@mipmap/ic_dw_b"
            android:gravity="center_vertical"
            android:paddingLeft="12dp"
            android:paddingTop="7dp"
            android:paddingRight="12dp"
            android:paddingBottom="7dp"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/bt_area"
            android:layout_marginTop="44dp"
            android:background="@color/white" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/bt_area"
            android:layout_marginTop="@dimen/dp_10"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/lin_bg_txl"
                android:layout_width="match_parent"
                android:layout_height="55dp"
                android:background="@mipmap/ic_txl_top"
                android:gravity="center">

                <TextView
                    android:id="@+id/bt_ser1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp"
                    android:layout_weight="1"
                    android:clickable="true"
                    android:gravity="center"
                    android:text="服务人员"
                    android:textColor="#262626"
                    android:textSize="15sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:id="@+id/bt_work1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_work"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginTop="5dp"
                        android:gravity="center"
                        android:text="成员单位"
                        android:textColor="#8C8C8C"
                        android:textSize="15sp" />

                    <ImageView
                        android:id="@+id/iv_jt"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:layout_marginTop="5dp"
                        android:src="@mipmap/ic_down2"
                        android:visibility="gone" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical">

                <EditText
                    android:id="@+id/et_search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/dp_12"
                    android:background="@drawable/bg_gray_big"
                    android:drawableLeft="@mipmap/ic_search_gray"
                    android:drawablePadding="10dp"
                    android:gravity="center_vertical"
                    android:hint="输入关键词"
                    android:imeOptions="actionSearch"
                    android:lines="1"
                    android:padding="10dp"
                    android:singleLine="true"
                    android:textColor="@color/color_33"
                    android:textColorHint="#BFBFBF"
                    android:textSize="14sp" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <com.lgfzd.base.view.recycle.XRecyclerView
                        android:id="@+id/mRecycle"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                    <com.lgfzd.base.view.recycle.XRecyclerView
                        android:id="@+id/mRecycle1"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="gone" />

                    <ImageView
                        android:id="@+id/iv_no_data"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:src="@mipmap/nothing"
                        android:visibility="gone" />
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>