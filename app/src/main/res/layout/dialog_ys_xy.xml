<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_white_five"
    xmlns:tools="http://schemas.android.com/tools"
    tools:viewBindingIgnore="true"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:padding="15dp"
        android:text="用户协议和隐私政策"
        android:textColor="@color/color_33"
        android:textSize="17sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:lineSpacingMultiplier="1.2"
        android:paddingLeft="12dp"
        android:paddingRight="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_12"
        android:text="@string/yinsi"
        android:textColor="@color/color_33"
        android:textSize="15sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:paddingBottom="15dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="查看"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_xy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="《用户协议》"
            android:textColor="@color/color_main"
            android:textSize="15sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="和"
            android:textColor="@color/color_33"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_yinsi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="《隐私政策》"
            android:textColor="@color/color_main"
            android:textSize="15sp" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#999999" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/bt_cancel"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/no_tongyi"
            android:textColor="@color/color_33"
            android:textSize="14sp" />

        <View
            android:layout_width="1px"
            android:layout_height="match_parent"
            android:background="#999999" />

        <TextView
            android:id="@+id/bt_sure"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/tongyi"
            android:textColor="@color/color_main"
            android:textSize="14sp" />
    </LinearLayout>
</LinearLayout>