<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/coordinatorLayout_Style"
    android:background="@color/white"
    app:statusBarBackground="#000053E2">

    <include layout="@layout/base_top_ground_img" />

    <androidx.core.widget.NestedScrollView
        style="@style/ll_m"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout style="@style/ll_v">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_15"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="姓名"
                android:textColor="#333333"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_15"
                android:layout_marginTop="10dp"
                android:layout_marginRight="@dimen/dp_15"
                android:background="@null"
                android:hint="请输入姓名"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="15dp"
                android:background="@color/color_line" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_15"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="身份证号"
                android:textColor="#333333"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_id_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_15"
                android:layout_marginTop="10dp"
                android:layout_marginRight="@dimen/dp_15"
                android:background="@null"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="15dp"
                android:background="@color/color_line" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_15"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="人员类型"
                android:textColor="#333333"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_peo_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_15"
                android:layout_marginTop="10dp"
                android:layout_marginRight="@dimen/dp_15"
                android:background="@null"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:text="-"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="15dp"
                android:background="@color/color_line" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="区域选择"
                android:textColor="#333333"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_area"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="15dp"
                android:drawableRight="@mipmap/ic_right"
                android:hint="@string/choice_area"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="15dp"
                android:background="@color/color_line" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="3dp"
                android:text="责任人"
                android:textColor="#333333"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_zrr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="15dp"
                android:background="@null"
                android:drawableRight="@mipmap/ic_right"
                android:hint="请选择责任人"
                android:maxLength="18"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#333333"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="15dp"
                android:background="@color/color_line" />


            <Button
                android:id="@+id/bt_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="30dp"
                android:layout_marginRight="20dp"
                android:background="@drawable/bg_color_main"
                android:gravity="center"
                android:text="确认"
                android:textColor="@color/white"
                android:textSize="15sp" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>