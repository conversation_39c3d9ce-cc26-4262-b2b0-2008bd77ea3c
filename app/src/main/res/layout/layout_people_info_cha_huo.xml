<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">


    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp"
        android:text="基础信息"
        android:textColor="#262626"
        android:textSize="16sp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="姓名：\u3000\u3000\u3000\u3000"
            android:textColor="#8C8C8C"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#262626"
            android:textSize="14sp" />
    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="身份证号：\u3000\u3000"
            android:textColor="#8C8C8C"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_id_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#262626"
            android:textSize="14sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="人员类型：\u3000\u3000"
            android:textColor="#8C8C8C"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_mobile"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#262626"
            android:textSize="14sp" />

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="户籍地：\u3000\u3000\u3000"
            android:textColor="#8C8C8C"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_hjd_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#262626"
            android:textSize="14sp" />
    </LinearLayout>
</LinearLayout>