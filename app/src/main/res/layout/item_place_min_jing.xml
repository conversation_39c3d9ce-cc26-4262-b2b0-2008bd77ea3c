<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingLeft="14dp"
        android:paddingTop="@dimen/dp_12"
        android:paddingRight="14dp"
        android:paddingBottom="@dimen/dp_12">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#262626"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_id_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:textColor="#666666"
            android:textSize="14sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="户籍地址："
                android:textColor="#666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#666666"
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginLeft="@dimen/dp_12"
        android:layout_marginRight="@dimen/dp_12"
        android:background="@color/color_line" />
</LinearLayout>