<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/ll_m"
    android:scrollbars="none">

    <LinearLayout style="@style/ll_v">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="220dp"
            android:background="@mipmap/ic_work_login">

            <LinearLayout
                android:id="@+id/lin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="80dp"
                android:layout_marginRight="15dp"
                android:visibility="gone">

                <EditText
                    android:id="@+id/et_zs"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/bg_white_five"
                    android:lines="1"
                    android:padding="13dp"
                    android:singleLine="true"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/bt_dialog"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:background="@drawable/bg_white_five"
                    android:gravity="center"
                    android:padding="13dp"
                    android:text="选择"
                    android:textColor="@color/black" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="36dp"
                android:layout_marginBottom="36dp"
                android:text="登录"
                android:textColor="@color/white"
                android:textSize="32sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/bt_area"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="36dp"
                android:layout_marginTop="55dp"
                android:text="站点切换"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </RelativeLayout>


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="20dp"
            android:text="用户名"
            android:textColor="#333333"
            android:textSize="15sp" />

        <com.lgfzd.base.view.XClearEditText
            android:id="@+id/et_mobile"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="30dp"
            android:background="@null"
            android:hint="请输入用户名"
            android:lines="1"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:singleLine="true"
            android:textColor="#333333"
            android:textSize="15sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="30dp"
            android:background="@color/color_line" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="15dp"
            android:layout_marginRight="30dp"
            android:text="密码"
            android:textColor="#333333"
            android:textSize="15sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="30dp"
            android:orientation="horizontal">

            <com.lgfzd.base.view.XClearEditText
                android:id="@+id/et_pass_word"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@null"
                android:hint="请输入密码"
                android:inputType="textPassword"
                android:lines="1"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:singleLine="true"
                android:textColor="#333333"
                android:textSize="15sp" />

            <ImageView
                android:id="@+id/bt_show_pwd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:padding="@dimen/dp_10"
                android:src="@mipmap/icon_chakan_1" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="30dp"
            android:background="@color/color_line" />

        <!--吸毒人员没有注册-->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp">

            <TextView
                android:id="@+id/bt_register"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="15dp"
                android:paddingBottom="15dp"
                android:text="注册账户"
                android:textColor="#0053e2"
                android:textSize="15sp"
                android:visibility="visible" />

            <TextView
                android:id="@+id/bt_pass_word"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:paddingTop="15dp"
                android:paddingBottom="15dp"
                android:text="忘记密码?"
                android:textColor="#bfbfbf"
                android:textSize="15sp" />
        </RelativeLayout>

        <Button
            android:id="@+id/bt_login"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="30dp"
            android:background="@drawable/bg_color_main"
            android:gravity="center"
            android:text="登录"
            android:textColor="@color/white"
            android:textSize="15sp" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/dp_5"
            android:paddingHorizontal="10dp"
            android:gravity="center">

            <TextView
                android:id="@+id/bt_code"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="@dimen/dp_15"
                android:text="验证码登录"
                android:textColor="#ff0053e2"
                android:textSize="14sp" />

            <View
                android:id="@+id/ofline_line"
                android:layout_width="0dp"
                android:layout_height="14sp"
                android:layout_weight="1"
                />

            <TextView
                android:id="@+id/bt_offline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="@dimen/dp_15"
                android:text="离线排查"
                android:textColor="#ff0053e2"
                android:textSize="14sp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginBottom="30dp"
            android:gravity="bottom|center">

            <CheckBox
                android:id="@+id/checkbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:button="@drawable/bg_choice_yuan_check"
                android:checked="true"
                android:text="@string/xieyi"
                android:textColor="#bfbfbf"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/bt_xy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:gravity="center|bottom"
                android:text="@string/xieyi_xy"
                android:textColor="@color/color_main"
                android:textSize="13sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/xieyi_a"
                android:textColor="#ffbfbfbf"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/bt_tk"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:text="@string/xieyi_tk"
                android:textColor="@color/color_main"
                android:textSize="13sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center"
            android:text="版本号：1.0.0"
            android:textColor="#8c8c8c"
            android:textSize="13sp" />
    </LinearLayout>
</androidx.core.widget.NestedScrollView>