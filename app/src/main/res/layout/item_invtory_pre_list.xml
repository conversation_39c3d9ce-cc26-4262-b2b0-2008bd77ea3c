<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center_vertical"
    android:orientation="vertical"
    android:paddingTop="10dp"
    android:paddingBottom="10dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/color_26"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_value"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/color_26"
            android:textSize="14sp" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@color/color_line" />
</LinearLayout>
