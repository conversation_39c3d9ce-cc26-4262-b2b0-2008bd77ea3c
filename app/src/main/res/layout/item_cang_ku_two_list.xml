<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp_10"
    android:orientation="vertical"
    android:padding="@dimen/dp_12">

    <TextView
        android:id="@+id/bt_title_del"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="@dimen/dp_10"
        android:drawableRight="@mipmap/ic_cha"
        android:drawablePadding="3dp"
        android:textColor="#333333"
        android:textSize="13sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_gray_five"
        android:orientation="vertical"
        android:padding="5dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:drawableLeft="@mipmap/ic_start"
                android:drawablePadding="@dimen/dp_2"
                android:text="化学品名称"
                android:textColor="#262626"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_hcp_class"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv"
                android:hint="请选择化学品名称"
                android:paddingTop="@dimen/dp_15"
                android:paddingBottom="@dimen/dp_15"
                android:textColor="@color/color_33"
                android:textColorHint="#bfbfbf"
                android:textSize="14sp" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:src="@mipmap/ic_right" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="#D9D9D9" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginBottom="@dimen/dp_15"
            android:drawableLeft="@mipmap/ic_start"
            android:drawablePadding="@dimen/dp_2"
            android:text="库存规格"
            android:textColor="#262626"
            android:textSize="14sp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycle_hxp_gg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false" />

        <TextView
            android:id="@+id/bt_add_gg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:gravity="center"
            android:paddingTop="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_10"
            android:text="新建规格"
            android:textColor="#ff0053e2"
            android:textSize="12sp" />
    </LinearLayout>
</LinearLayout>