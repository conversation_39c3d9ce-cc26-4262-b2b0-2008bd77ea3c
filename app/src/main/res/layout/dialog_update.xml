<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_margin="30dp"
    android:background="@drawable/bg_white_ten"
    xmlns:tools="http://schemas.android.com/tools"
    tools:viewBindingIgnore="true"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="10dp"
        android:textColor="@color/color_26"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/tvDialogContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:gravity="center"
        android:lineSpacingMultiplier="1.2"
        android:padding="15dp"
        android:textColor="@color/color_26"
        android:textSize="16sp" />

    <LinearLayout
        android:id="@+id/lin_pro"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingBottom="@dimen/dp_10"
        android:visibility="gone">

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="6dp"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_weight="1"
            android:max="100" />

        <TextView
            android:id="@+id/tvProgress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_marginRight="@dimen/dp_15"
            android:text="0%"
            android:textColor="@color/color_26"
            android:textSize="16sp" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/color_line" />

    <Button
        android:id="@+id/btnDialogConfirm"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?android:attr/selectableItemBackground"
        android:text="立即更新"
        android:textColor="@color/color_26"
        android:textSize="17sp" />
</LinearLayout>