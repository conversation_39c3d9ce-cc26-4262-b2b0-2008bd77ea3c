<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/bg_region_delete"
    android:orientation="vertical"
    tools:viewBindingIgnore="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:paddingBottom="10dp">

        <ImageView
            android:id="@+id/iv_delete"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_gravity="center"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="5dp"
            android:src="@drawable/ic_region_delete" />

        <TextView
            android:id="@+id/tv_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:lineSpacingMultiplier="1.2"
            android:padding="10dp"
            android:text="您已阅读合规教程，请按照合规教程\n要求进行运输"
            android:textColor="#343434"
            android:textSize="14sp" />

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_marginTop="5dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/bt_cancel"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="暂不发起"
            android:textColor="#343434"
            android:textSize="15sp" />

        <View
            android:layout_width="@dimen/dp_1"
            android:layout_height="match_parent"
            android:background="@color/color_line" />

        <TextView
            android:id="@+id/bt_sure"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="发起流程"
            android:textColor="#0F5DE4"
            android:textSize="15sp" />
    </LinearLayout>
</LinearLayout>
