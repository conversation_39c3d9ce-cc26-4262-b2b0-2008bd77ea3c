<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 停止状态 -->
    <item android:state_activated="true"
          android:drawable="@drawable/ic_stop" />
    <item android:state_selected="true"
          android:drawable="@drawable/ic_stop" />
    <item android:state_pressed="true"
          android:drawable="@drawable/ic_stop" />
          
    <!-- 发送状态（默认） -->
    <item android:drawable="@drawable/ic_send_message" />
</selector> 