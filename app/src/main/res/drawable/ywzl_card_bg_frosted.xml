<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/frosted_background">
    <!-- 底层模糊效果 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#AAFFFFFF" />
            <corners android:radius="8dp" />
        </shape>
    </item>

    <!-- 顶部毛玻璃效果层 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:angle="270"
                android:startColor="#93F3FEFF"
                android:centerColor="#80FFFFFF"
                android:endColor="#FFFFFF"
                android:centerY="0.3"
                android:type="linear" />
            <corners android:radius="8dp" />
        </shape>
    </item>

    <!-- 半透明覆盖层，增强毛玻璃效果 -->
    <item android:top="0dp" android:bottom="120dp">
        <shape android:shape="rectangle">
            <solid android:color="#66FFFFFF" />
            <corners android:topLeftRadius="8dp" android:topRightRadius="8dp" />
        </shape>
    </item>

    <!-- 额外的模糊层 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#33FFFFFF" />
            <corners android:radius="8dp" />
        </shape>
    </item>
</layer-list>