<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 底层白色背景，延伸出16dp -->
    <item android:top="-16dp">
        <shape>
            <solid android:color="#00000000"/>
            <corners android:topLeftRadius="16dp"
                     android:topRightRadius="16dp"/>
        </shape>
    </item>
    <!-- 上层渐变背景 -->
    <item android:top="0dp">
        <shape>
            <gradient
                android:startColor="#FFFFFF"
                android:endColor="#F6F8F9"
                android:angle="0"/>
            <corners android:topLeftRadius="16dp"
                     android:topRightRadius="16dp"/>
        </shape>
    </item>
</layer-list>