package com.linggan.jd831.widget.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.AreaEntity;

import java.util.List;

/**
 * 地区选择适配器
 */
public class ProvinceNumAdapter extends RecyclerView.Adapter<ProvinceNumAdapter.Holder> {

    private List<AreaEntity> list;
    private Context context;
    private OnItemClickListener onItemClickListener;
    private int last = -1;

    public ProvinceNumAdapter(Context context, List<AreaEntity> list) {
        this.context = context;
        this.list = list;
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_area_list, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        holder.textView.setText(list.get(position).getXzqhmc());


        if (list.get(position).getStatus()) {
            holder.textView.setTextColor(Color.parseColor("#0053E2"));
        } else {
            holder.textView.setTextColor(Color.parseColor("#333333"));
        }

        if (last == position) {
            list.get(position).setStatus(true);
        } else {
            list.get(position).setStatus(false);
        }

        holder.itemView.setOnClickListener(v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(list.get(position), position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    class Holder extends RecyclerView.ViewHolder {
        TextView textView;

        Holder(@NonNull View itemView) {
            super(itemView);
            this.textView = itemView.findViewById(R.id.textview);
        }
    }

    public interface OnItemClickListener {
        void onItemClick(AreaEntity area, int position);
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public void setLast(int last) {
        this.last = last;
        notifyDataSetChanged();
    }

    public List<AreaEntity> getList() {
        return list;
    }
}
