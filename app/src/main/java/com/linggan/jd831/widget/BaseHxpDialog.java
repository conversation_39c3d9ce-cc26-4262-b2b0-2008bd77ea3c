package com.linggan.jd831.widget;

import android.app.Activity;
import android.app.Dialog;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.HxpEntity;

import java.util.ArrayList;


/**
 * 类名： 字段数据选择器
 */
public class BaseHxpDialog extends Dialog {

    private final Activity mContext;
    private final View rootView;
    protected WheelHxpView mWheelView;
    private TextView btSure, btCancel;
    private OnClickDataListener onClickDataListener;


    public BaseHxpDialog(Activity context, ArrayList<HxpEntity> arrayList) {
        super(context, com.lgfzd.base.R.style.dialogStyle);
        mContext = context;
        rootView = LayoutInflater.from(context).inflate(R.layout.dialog_hxp, null);
        initView();
        setContentView(rootView);
        setData(arrayList);
    }

    public void show() {
        super.show();
        Window window = getWindow();
        window.setGravity(Gravity.BOTTOM);  //此处可以设置dialog显示的位置

        WindowManager m = mContext.getWindowManager();
        Display d = m.getDefaultDisplay();  //为获取屏幕宽、高
        WindowManager.LayoutParams p = window.getAttributes();  //获取对话框当前的参数值
        p.width = d.getWidth();    //宽度设置为屏幕的0.8
        window.setAttributes(p);     //设置生效
    }

    /**
     * 设置数据
     */
    private void setData(ArrayList<HxpEntity> arrayList) {
        mWheelView.setDefault(0);
        mWheelView.setData(arrayList);

    }

    public HxpEntity getData() {
        if (mWheelView.getWork() == null) {
            return new HxpEntity();
        } else {
            return mWheelView.getWork();
        }
    }

    private void initView() {
        mWheelView = (WheelHxpView) rootView.findViewById(R.id.mWheelView);
        btSure = (TextView) rootView.findViewById(R.id.bt_sure);
        btCancel = (TextView) rootView.findViewById(R.id.bt_cancel);
        btSure.setOnClickListener(v -> {
            dismiss();
            onClickDataListener.onSuccess();
        });
        btCancel.setOnClickListener(v -> {
            dismiss();
        });
    }


    public void setOnClickDataListener(OnClickDataListener onClickDataListener) {
        this.onClickDataListener = onClickDataListener;
    }

    /**
     * 点击回调
     */
    public interface OnClickDataListener {
        void onSuccess();

    }
}