package com.linggan.jd831.bean;

/**
 * 类  名：异地服务--待确认名单列表
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2023/3/27 15:59
 * 版  权：LGKJ
 */
public class YiDiSerSureListEntity {

    private String tlscStr;//管控类型（包括：户籍地迁移、异地执行、临时管控、执行地变更）
    private String sqsj;//申请时间
    private String xm;//发起人
    private String bh;// 查询详情所需的编号
    private String ejztCode;// 状态类型，用于查询详情时区别查询的表
    private String xyrbh;
    private String tlsc;
    private String sfz;
    private String ryLx;
    private Object ryyjzt;
    private int sfTg;
    private int sfSc;

    //名单
    private String zjhm;
    private String gklxStr;
    private String hjd;
    private String jzd;

    public String getTlscStr() {
        return tlscStr;
    }

    public void setTlscStr(String tlscStr) {
        this.tlscStr = tlscStr;
    }

    public String getSqsj() {
        return sqsj;
    }

    public void setSqsj(String sqsj) {
        this.sqsj = sqsj;
    }

    public String getXm() {
        return xm;
    }

    public void setXm(String xm) {
        this.xm = xm;
    }

    public String getBh() {
        return bh;
    }

    public void setBh(String bh) {
        this.bh = bh;
    }

    public String getEjztCode() {
        return ejztCode;
    }

    public void setEjztCode(String ejztCode) {
        this.ejztCode = ejztCode;
    }

    public String getXyrbh() {
        return xyrbh;
    }

    public void setXyrbh(String xyrbh) {
        this.xyrbh = xyrbh;
    }

    public String getTlsc() {
        return tlsc;
    }

    public void setTlsc(String tlsc) {
        this.tlsc = tlsc;
    }

    public String getSfz() {
        return sfz;
    }

    public void setSfz(String sfz) {
        this.sfz = sfz;
    }

    public String getRyLx() {
        return ryLx;
    }

    public void setRyLx(String ryLx) {
        this.ryLx = ryLx;
    }

    public Object getRyyjzt() {
        return ryyjzt;
    }

    public void setRyyjzt(Object ryyjzt) {
        this.ryyjzt = ryyjzt;
    }

    public int getSfTg() {
        return sfTg;
    }

    public void setSfTg(int sfTg) {
        this.sfTg = sfTg;
    }

    public int getSfSc() {
        return sfSc;
    }

    public void setSfSc(int sfSc) {
        this.sfSc = sfSc;
    }

    public String getZjhm() {
        return zjhm;
    }

    public void setZjhm(String zjhm) {
        this.zjhm = zjhm;
    }

    public String getGklxStr() {
        return gklxStr;
    }

    public void setGklxStr(String gklxStr) {
        this.gklxStr = gklxStr;
    }

    public String getHjd() {
        return hjd;
    }

    public void setHjd(String hjd) {
        this.hjd = hjd;
    }

    public String getJzd() {
        return jzd;
    }

    public void setJzd(String jzd) {
        this.jzd = jzd;
    }
}
