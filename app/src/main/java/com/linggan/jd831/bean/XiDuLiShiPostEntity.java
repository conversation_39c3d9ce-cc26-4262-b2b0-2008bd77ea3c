package com.linggan.jd831.bean;


import java.util.List;

/**
 * 类  名：吸毒人员--吸毒历史提交实体类
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/9/26 9:24
 * 版  权：凌感科技
 */
public class XiDuLiShiPostEntity {
    /**
     * 吸毒人员主键id
     */
    private String xyrbh;
    /**
     * 一级状态编号
     */
    private String yjztbh;
    /**
     * 当前状态
     */
    private String ryyjzt;
    /**
     * 管控信息编号
     */
    private String gkxxbh;
    /**
     * 初次吸毒时间
     */

    private String cjxdsj;
    /**
     * 初次吸毒被查获时间
     */

    private String cjxdbchsj;
    /**
     * 前科情况 0是1否
     */
    private String qkqk;
    /**
     * 最后一次吸毒被拘留/戒毒开始时间
     */

    private String xdbjlKssj;
    /**
     * 最后一次吸毒被拘留/戒毒结束时间
     */

    private String xdbjlJssj;
    /**
     * 是否与他人吸毒 0是 1否
     */
    private String sfYtryqxd;
    /**
     * 累计戒毒次数
     */
    private String ljjdcs;
    /**
     * 累计戒毒时间
     */
    private String gkxxLjjdsj;
    /**
     * 周均吸毒次数
     */
    private String gkxxZjxdcs;
    /**
     * 吸毒后自我感觉(对身体)
     */
    private String gkxxXdgsDst;
    /**
     * 吸毒后自我感觉(对家庭)
     */
    private String gkxxXdgsDjt;
    /**
     * 吸毒后自我感觉(对社会)
     */
    private String gkxxXdgsDsh;
    /**
     * 吸毒方式
     */
    private List<String> gkxxXdfs;
    /**
     * 毒品来源
     */
    private List<String> gkxxDply;
    /**
     * 吸毒场所
     */
    private List<String> gkxxXdcs;
    /**
     * 吸毒氛围
     */
    private List<String> gkxxXdfw;
    /**
     * 初次吸毒原因
     */
    private List<String> gkxxCcxdyy;
    /**
     * 吸毒资金来源
     */
    private List<String> gkxxXdzjly;
    /**
     * 戒毒后产生负面情绪原因
     */
    private List<String> gkxxJdhfmqxyy;
    /**
     * 复吸毒品原因(心理因素)
     */
    private List<String> gkxxDpfxyyXlys;
    /**
     * 复吸毒品原因(身体因素)
     */
    private List<String> gkxxDpfxyyStys;
    /**
     * 复吸毒品原因(社会环境因素)
     */
    private List<String> gkxxDpfxyyShys;
    /**
     * 风险管控多选
     */
    private List<String> fxgk;

    private String sjLy;


    public String getXyrbh() {
        return xyrbh;
    }

    public void setXyrbh(String xyrbh) {
        this.xyrbh = xyrbh;
    }

    public String getYjztbh() {
        return yjztbh;
    }

    public void setYjztbh(String yjztbh) {
        this.yjztbh = yjztbh;
    }

    public String getRyyjzt() {
        return ryyjzt;
    }

    public void setRyyjzt(String ryyjzt) {
        this.ryyjzt = ryyjzt;
    }

    public String getGkxxbh() {
        return gkxxbh;
    }

    public void setGkxxbh(String gkxxbh) {
        this.gkxxbh = gkxxbh;
    }

    public String getCjxdsj() {
        return cjxdsj;
    }

    public void setCjxdsj(String cjxdsj) {
        this.cjxdsj = cjxdsj;
    }

    public String getCjxdbchsj() {
        return cjxdbchsj;
    }

    public void setCjxdbchsj(String cjxdbchsj) {
        this.cjxdbchsj = cjxdbchsj;
    }

    public String getQkqk() {
        return qkqk;
    }

    public void setQkqk(String qkqk) {
        this.qkqk = qkqk;
    }

    public String getXdbjlKssj() {
        return xdbjlKssj;
    }

    public void setXdbjlKssj(String xdbjlKssj) {
        this.xdbjlKssj = xdbjlKssj;
    }

    public String getXdbjlJssj() {
        return xdbjlJssj;
    }

    public void setXdbjlJssj(String xdbjlJssj) {
        this.xdbjlJssj = xdbjlJssj;
    }

    public String getSfYtryqxd() {
        return sfYtryqxd;
    }

    public void setSfYtryqxd(String sfYtryqxd) {
        this.sfYtryqxd = sfYtryqxd;
    }

    public String getLjjdcs() {
        return ljjdcs;
    }

    public void setLjjdcs(String ljjdcs) {
        this.ljjdcs = ljjdcs;
    }

    public String getGkxxLjjdsj() {
        return gkxxLjjdsj;
    }

    public void setGkxxLjjdsj(String gkxxLjjdsj) {
        this.gkxxLjjdsj = gkxxLjjdsj;
    }

    public String getGkxxZjxdcs() {
        return gkxxZjxdcs;
    }

    public void setGkxxZjxdcs(String gkxxZjxdcs) {
        this.gkxxZjxdcs = gkxxZjxdcs;
    }

    public String getGkxxXdgsDst() {
        return gkxxXdgsDst;
    }

    public void setGkxxXdgsDst(String gkxxXdgsDst) {
        this.gkxxXdgsDst = gkxxXdgsDst;
    }

    public String getGkxxXdgsDjt() {
        return gkxxXdgsDjt;
    }

    public void setGkxxXdgsDjt(String gkxxXdgsDjt) {
        this.gkxxXdgsDjt = gkxxXdgsDjt;
    }

    public String getGkxxXdgsDsh() {
        return gkxxXdgsDsh;
    }

    public void setGkxxXdgsDsh(String gkxxXdgsDsh) {
        this.gkxxXdgsDsh = gkxxXdgsDsh;
    }

    public List<String> getGkxxXdfs() {
        return gkxxXdfs;
    }

    public void setGkxxXdfs(List<String> gkxxXdfs) {
        this.gkxxXdfs = gkxxXdfs;
    }

    public List<String> getGkxxDply() {
        return gkxxDply;
    }

    public void setGkxxDply(List<String> gkxxDply) {
        this.gkxxDply = gkxxDply;
    }

    public List<String> getGkxxXdcs() {
        return gkxxXdcs;
    }

    public void setGkxxXdcs(List<String> gkxxXdcs) {
        this.gkxxXdcs = gkxxXdcs;
    }

    public List<String> getGkxxXdfw() {
        return gkxxXdfw;
    }

    public void setGkxxXdfw(List<String> gkxxXdfw) {
        this.gkxxXdfw = gkxxXdfw;
    }

    public List<String> getGkxxCcxdyy() {
        return gkxxCcxdyy;
    }

    public void setGkxxCcxdyy(List<String> gkxxCcxdyy) {
        this.gkxxCcxdyy = gkxxCcxdyy;
    }

    public List<String> getGkxxXdzjly() {
        return gkxxXdzjly;
    }

    public void setGkxxXdzjly(List<String> gkxxXdzjly) {
        this.gkxxXdzjly = gkxxXdzjly;
    }

    public List<String> getGkxxJdhfmqxyy() {
        return gkxxJdhfmqxyy;
    }

    public void setGkxxJdhfmqxyy(List<String> gkxxJdhfmqxyy) {
        this.gkxxJdhfmqxyy = gkxxJdhfmqxyy;
    }

    public List<String> getGkxxDpfxyyXlys() {
        return gkxxDpfxyyXlys;
    }

    public void setGkxxDpfxyyXlys(List<String> gkxxDpfxyyXlys) {
        this.gkxxDpfxyyXlys = gkxxDpfxyyXlys;
    }

    public List<String> getGkxxDpfxyyStys() {
        return gkxxDpfxyyStys;
    }

    public void setGkxxDpfxyyStys(List<String> gkxxDpfxyyStys) {
        this.gkxxDpfxyyStys = gkxxDpfxyyStys;
    }

    public List<String> getGkxxDpfxyyShys() {
        return gkxxDpfxyyShys;
    }

    public void setGkxxDpfxyyShys(List<String> gkxxDpfxyyShys) {
        this.gkxxDpfxyyShys = gkxxDpfxyyShys;
    }

    public List<String> getFxgk() {
        return fxgk;
    }

    public void setFxgk(List<String> fxgk) {
        this.fxgk = fxgk;
    }


    public String getSjLy() {
        return sjLy;
    }

    public void setSjLy(String sjLy) {
        this.sjLy = sjLy;
    }
}
