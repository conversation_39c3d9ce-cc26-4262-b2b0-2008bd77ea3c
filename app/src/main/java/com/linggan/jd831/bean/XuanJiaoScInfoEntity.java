package com.linggan.jd831.bean;

import java.util.List;

/**
 * 类  名：宣教上传-后详情
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2023/3/15 10:10
 * 版  权：LGKJ
 */
public class XuanJiaoScInfoEntity {

    /**
     * bh : 1375191785955328
     * xjhdBh : 1375178623451136
     * dz : 四川省成都市双流区菁蓉北三街98号靠近四川天府新区第一小学
     * lng : 104.082897
     * lat : 30.405528
     * lrrbh : 1361316218454016
     * lrrxm : 吴范
     * bjrbh : 1361316218454016
     * lrsj : 2023-03-13 17:48:31
     * bjsj : 2023-03-13 17:48:31
     * zp : [{"bh":1375191786307584,"mc":"1677648433672.jpg","lj":"https://static.lgfzd.com/drugControl/drug_control/1678700894263.jpg","hz":"jpg","dx":"139411","lx":"jdxj_xjhd_xchd_zp"},{"bh":1375191786323968,"mc":"Screenshot_20230310_075344.jpg","lj":"https://static.lgfzd.com/drugControl/drug_control/1678700894190.jpg","hz":"jpg","dx":"614227","lx":"jdxj_xjhd_xchd_zp"}]
     * sp : [{"bh":1375191786323969,"mc":"VID_20230227164808077.mp4","lj":"https://static.lgfzd.com/drugControl/drug_control/1678700904078.mp4","hz":"mp4","dx":"2836631","lx":"jdxj_xjhd_xchd_sp"}]
     */
    private String bh;
    private String xjhdBh;
    private String dz;
    private String lng;
    private String lat;
    private String lrrbh;
    private String lrrxm;
    private String bjrbh;
    private String lrsj;
    private String bjsj;
    private List<SendFileEntity> zp;
    private List<SendFileEntity> sp;

    public String getBh() {
        return bh;
    }

    public void setBh(String bh) {
        this.bh = bh;
    }

    public String getXjhdBh() {
        return xjhdBh;
    }

    public void setXjhdBh(String xjhdBh) {
        this.xjhdBh = xjhdBh;
    }

    public String getDz() {
        return dz;
    }

    public void setDz(String dz) {
        this.dz = dz;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLrrbh() {
        return lrrbh;
    }

    public void setLrrbh(String lrrbh) {
        this.lrrbh = lrrbh;
    }

    public String getLrrxm() {
        return lrrxm;
    }

    public void setLrrxm(String lrrxm) {
        this.lrrxm = lrrxm;
    }

    public String getBjrbh() {
        return bjrbh;
    }

    public void setBjrbh(String bjrbh) {
        this.bjrbh = bjrbh;
    }

    public String getLrsj() {
        return lrsj;
    }

    public void setLrsj(String lrsj) {
        this.lrsj = lrsj;
    }

    public String getBjsj() {
        return bjsj;
    }

    public void setBjsj(String bjsj) {
        this.bjsj = bjsj;
    }

    public List<SendFileEntity> getZp() {
        return zp;
    }

    public void setZp(List<SendFileEntity> zp) {
        this.zp = zp;
    }

    public List<SendFileEntity> getSp() {
        return sp;
    }

    public void setSp(List<SendFileEntity> sp) {
        this.sp = sp;
    }
}
