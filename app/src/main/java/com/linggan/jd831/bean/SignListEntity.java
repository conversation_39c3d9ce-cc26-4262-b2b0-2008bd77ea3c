package com.linggan.jd831.bean;

import java.util.List;

/**
 * 类  名：签到列表实体类
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/11/16 16:39
 * 版  权：凌感科技
 */
public class SignListEntity {


    /**
     * bh : 1366907105263616
     * ryyjzt : {"code":"1","name":"社区戒毒"}
     * xzqhdm : 510116
     * xzqhmcs : 成都市 双流区
     * dz : 四川省成都市双流区菁蓉路天府数智谷4栋2单元103号
     * lrsj : 2022-11-16
     * lgXdryQdZqs : []
     * lx : 1
     */
    private long bh;
    private CodeNameEntity ryyjzt;
    private String xzqhdm;
    private String xzqhmcs;
    private String dz;
    private String lrsj;
    private int lx;
    private List<ZHouQiEntity> lgXdryQdZqs;
    private String xyrbh;
    private String approval;

    public long getBh() {
        return bh;
    }

    public void setBh(long bh) {
        this.bh = bh;
    }

    public CodeNameEntity getRyyjzt() {
        return ryyjzt;
    }

    public void setRyyjzt(CodeNameEntity ryyjzt) {
        this.ryyjzt = ryyjzt;
    }

    public String getXzqhdm() {
        return xzqhdm;
    }

    public void setXzqhdm(String xzqhdm) {
        this.xzqhdm = xzqhdm;
    }

    public String getXzqhmcs() {
        return xzqhmcs;
    }

    public void setXzqhmcs(String xzqhmcs) {
        this.xzqhmcs = xzqhmcs;
    }

    public String getDz() {
        return dz;
    }

    public void setDz(String dz) {
        this.dz = dz;
    }

    public String getLrsj() {
        return lrsj;
    }

    public void setLrsj(String lrsj) {
        this.lrsj = lrsj;
    }

    public int getLx() {
        return lx;
    }

    public void setLx(int lx) {
        this.lx = lx;
    }

    public List<ZHouQiEntity> getLgXdryQdZqs() {
        return lgXdryQdZqs;
    }

    public void setLgXdryQdZqs(List<ZHouQiEntity> lgXdryQdZqs) {
        this.lgXdryQdZqs = lgXdryQdZqs;
    }

    public String getXyrbh() {
        return xyrbh;
    }

    public void setXyrbh(String xyrbh) {
        this.xyrbh = xyrbh;
    }

    public String getApproval() {
        return approval;
    }

    public void setApproval(String approval) {
        this.approval = approval;
    }
}
