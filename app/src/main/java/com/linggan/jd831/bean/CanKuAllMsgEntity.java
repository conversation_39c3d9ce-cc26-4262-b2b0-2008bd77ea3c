package com.linggan.jd831.bean;

import java.io.Serializable;
import java.util.List;

/**
 * 类  名：企业下全部仓库信息
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2023/4/11 9:28
 * 版  权：LGKJ
 */
public class CanKuAllMsgEntity implements Serializable {

    private String ckBh;
    private String mc;
    private List<YzdKcHxpListBean> yzdKcHxpList;

    public String getCkBh() {
        return ckBh;
    }

    public void setCkBh(String ckBh) {
        this.ckBh = ckBh;
    }

    public String getMc() {
        return mc;
    }

    public void setMc(String mc) {
        this.mc = mc;
    }

    public List<YzdKcHxpListBean> getYzdKcHxpList() {
        return yzdKcHxpList;
    }

    public void setYzdKcHxpList(List<YzdKcHxpListBean> yzdKcHxpList) {
        this.yzdKcHxpList = yzdKcHxpList;
    }

    public static class YzdKcHxpListBean implements Serializable {
        private String hxpBh;
        private String hxpMc;
        private List<HxpGgIntBean> yzdKcHxpGgList;

        public String getHxpBh() {
            return hxpBh;
        }

        public void setHxpBh(String hxpBh) {
            this.hxpBh = hxpBh;
        }

        public String getHxpMc() {
            return hxpMc;
        }

        public void setHxpMc(String hxpMc) {
            this.hxpMc = hxpMc;
        }

        public List<HxpGgIntBean> getYzdKcHxpGgList() {
            return yzdKcHxpGgList;
        }

        public void setYzdKcHxpGgList(List<HxpGgIntBean> yzdKcHxpGgList) {
            this.yzdKcHxpGgList = yzdKcHxpGgList;
        }
    }
}
