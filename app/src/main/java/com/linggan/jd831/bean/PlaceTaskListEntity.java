package com.linggan.jd831.bean;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * 类  名：场所排查-任务列表
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2023/1/5 13:40
 * 版  权：LGKJ
 */
public class PlaceTaskListEntity implements Serializable {
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @SerializedName("id")
    private String id;
    private String csId;//场所ID
    private String csmc;//场所名称
    private String rwkssj;//任务开始时间
    private String rwjssj;//任务结束时间
    private String sfrw;//是否任务
    private String rwId;//任务ID
    private String csdz;//场所地址
    private String jwd;//场所经纬度
    private String cslxId;//场所类型 类型id
    private String cslxLxmc;//场所类型 类型代码
    private int sflhpc;//是否联合排查
    private String rwms;//任务描述
    private String rwmc;//任务名称
    private String csszqx;

    private String fzrXm;//（负责人姓名）
    private String ssqy;//（所属区域）

    private CodeNameEntity rwlxEnum;

    private List<String> tp;

    public String getCsId() {
        return csId;
    }

    public void setCsId(String csId) {
        this.csId = csId;
    }

    public String getCsmc() {
        return csmc;
    }

    public void setCsmc(String csmc) {
        this.csmc = csmc;
    }

    public String getRwkssj() {
        return rwkssj;
    }

    public void setRwkssj(String rwkssj) {
        this.rwkssj = rwkssj;
    }

    public String getRwjssj() {
        return rwjssj;
    }

    public void setRwjssj(String rwjssj) {
        this.rwjssj = rwjssj;
    }

    public String getSfrw() {
        return sfrw;
    }

    public void setSfrw(String sfrw) {
        this.sfrw = sfrw;
    }

    public String getRwId() {
        return rwId;
    }

    public void setRwId(String rwId) {
        this.rwId = rwId;
    }

    public String getCsdz() {
        return csdz;
    }

    public void setCsdz(String csdz) {
        this.csdz = csdz;
    }

    public String getJwd() {
        return jwd;
    }

    public void setJwd(String jwd) {
        this.jwd = jwd;
    }

    public String getCslxId() {
        return cslxId;
    }

    public void setCslxId(String cslxId) {
        this.cslxId = cslxId;
    }

    public String getCslxLxmc() {
        return cslxLxmc;
    }

    public void setCslxLxmc(String cslxLxmc) {
        this.cslxLxmc = cslxLxmc;
    }

    public int getSflhpc() {
        return sflhpc;
    }

    public void setSflhpc(int sflhpc) {
        this.sflhpc = sflhpc;
    }

    public String getRwms() {
        return rwms;
    }

    public void setRwms(String rwms) {
        this.rwms = rwms;
    }

    public String getRwmc() {
        return rwmc;
    }

    public void setRwmc(String rwmc) {
        this.rwmc = rwmc;
    }

    public String getCsszqx() {
        return csszqx;
    }

    public void setCsszqx(String csszqx) {
        this.csszqx = csszqx;
    }

    public String getFzrXm() {
        return fzrXm;
    }

    public void setFzrXm(String fzrXm) {
        this.fzrXm = fzrXm;
    }

    public String getSsqy() {
        return ssqy;
    }

    public void setSsqy(String ssqy) {
        this.ssqy = ssqy;
    }


    public CodeNameEntity getRwlxEnum() {
        return rwlxEnum;
    }

    public void setRwlxEnum(CodeNameEntity rwlxEnum) {
        this.rwlxEnum = rwlxEnum;
    }

    public List<String> getTp() {
        return tp;
    }

    public void setTp(List<String> tp) {
        this.tp = tp;
    }
}
