package com.linggan.jd831.bean;

import java.io.Serializable;

public class MedicationRecordBean implements Serializable {
    private String name;
    private String time;
    private String idNumber;
    private String dose;
    private String avatarUrl;
    private int statusType; // 1: 长期未服药(红色), 2: 正常(绿色), 3: 出组审批中(橙色), 4: 已出组(灰色)

    public MedicationRecordBean(String name, String time, String idNumber, String dose, String avatarUrl) {
        this.name = name;
        this.time = time;
        this.idNumber = idNumber;
        this.dose = dose;
        this.avatarUrl = avatarUrl;
        this.statusType = 2; // 默认为正常状态
    }

    public MedicationRecordBean(String name, String time, String idNumber, String dose, String avatarUrl, int statusType) {
        this.name = name;
        this.time = time;
        this.idNumber = idNumber;
        this.dose = dose;
        this.avatarUrl = avatarUrl;
        this.statusType = statusType;
    }

    public String getName() {
        return name;
    }

    public String getTime() {
        return time;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public String getDose() {
        return dose;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public int getStatusType() {
        return statusType;
    }

    public void setStatusType(int statusType) {
        this.statusType = statusType;
    }
}