package com.linggan.jd831.bean;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * 类  名：评估详情
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/10/10 10:24
 * 版  权：凌感科技
 */
public class PingGuInfoEntity implements Serializable {

    private String bh;
    private String xyrbh;
    private String xyrxm;
    private String yjztbh;
    private String yjztmc;
    private String zqBh;
    private String pgQsrq;
    private String pgZzrq;
    private String pgdw;
    private String pgsj;
    private String brcs;
    private String zylxrcs;
    private String gzxzPg;
    private String gzxzQm;
    private String gzxzPgrq;
    private String xybgzjh;
    private String xybgzjhQm;
    private String xybgzjhRq;
    private String xzjdbPg;
    private String xzjdbPgrq;
    private String xzjdbQm;
    private String bz;
    private String zrPcs;
    private String fxdj;

    public String getFxdjCode() {
        return fxdjCode;
    }

    public void setFxdjCode(String fxdjCode) {
        this.fxdjCode = fxdjCode;
    }

    @SerializedName("fxdjCode")
    private String fxdjCode;

    private String bjrbh;
    private String bjrxm;
    private String approval;
    private List<FxdjListBean> fxdjList;
    private List<SendFileEntity> fjList;
    private String cbPgJg;//初步评估结果
    private String lhFz;// 量化评估得分：
    private String pgFz;//
    private String fxXwFz;// 风险行为分：
    private int sfTg;//  低风险额外验证：(1未通过；2通过)
    private String jcFz;
    private List<PgLowLvlEntity> subItemList;
    private List<LiangHuaEntity> lhpgTmXzList;

    private String pcszrmj;//派出所责任民警
    private String pcszrld;// 派出所责任领导
    private String sswg;//所属网格
    private String wgy;//网格员
    private String csgb;//村(社)干部
    private String zjpagb;//镇街平安办干部

    @SerializedName("fxlb")
    private Fxlb fxlb;

    public Fxlb getFxlb() {
        return fxlb;
    }

    public void setFxlb(Fxlb fxlb) {
        this.fxlb = fxlb;
    }

    public static class Fxlb implements Serializable {
        @SerializedName("code")
        private String code;
        @SerializedName("name")
        private String name;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    @SerializedName("dataType")
    private String dataType;

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    @SerializedName("lrsj")
    private String lrsj;

    public String getLrsj() {
        return lrsj;
    }

    public void setLrsj(String lrsj) {
        this.lrsj = lrsj;
    }

    @SerializedName("tjr")
    private String tjr;

    public String getTjr() {
        return tjr;
    }

    @SerializedName("tjrdw")
    private String tjrDw;

    public String getTjrDw() {
        return tjrDw;
    }

    public void setTjrDw(String tjrDw) {
        this.tjrDw = tjrDw;
    }

    public void setTjr(String tjr) {
        this.tjr = tjr;
    }

    public String getBh() {
        return bh;
    }

    public void setBh(String bh) {
        this.bh = bh;
    }

    public String getXyrbh() {
        return xyrbh;
    }

    public void setXyrbh(String xyrbh) {
        this.xyrbh = xyrbh;
    }

    public String getXyrxm() {
        return xyrxm;
    }

    public void setXyrxm(String xyrxm) {
        this.xyrxm = xyrxm;
    }

    public String getYjztbh() {
        return yjztbh;
    }

    public void setYjztbh(String yjztbh) {
        this.yjztbh = yjztbh;
    }

    public String getYjztmc() {
        return yjztmc;
    }

    public void setYjztmc(String yjztmc) {
        this.yjztmc = yjztmc;
    }

    public String getZqBh() {
        return zqBh;
    }

    public void setZqBh(String zqBh) {
        this.zqBh = zqBh;
    }

    public String getPgQsrq() {
        return pgQsrq;
    }

    public void setPgQsrq(String pgQsrq) {
        this.pgQsrq = pgQsrq;
    }

    public String getPgZzrq() {
        return pgZzrq;
    }

    public void setPgZzrq(String pgZzrq) {
        this.pgZzrq = pgZzrq;
    }

    public String getPgdw() {
        return pgdw;
    }

    public void setPgdw(String pgdw) {
        this.pgdw = pgdw;
    }

    public String getPgsj() {
        return pgsj;
    }

    public void setPgsj(String pgsj) {
        this.pgsj = pgsj;
    }

    public String getBrcs() {
        return brcs;
    }

    public void setBrcs(String brcs) {
        this.brcs = brcs;
    }

    public String getZylxrcs() {
        return zylxrcs;
    }

    public void setZylxrcs(String zylxrcs) {
        this.zylxrcs = zylxrcs;
    }

    public String getGzxzPg() {
        return gzxzPg;
    }

    public void setGzxzPg(String gzxzPg) {
        this.gzxzPg = gzxzPg;
    }

    public String getGzxzQm() {
        return gzxzQm;
    }

    public void setGzxzQm(String gzxzQm) {
        this.gzxzQm = gzxzQm;
    }

    public String getGzxzPgrq() {
        return gzxzPgrq;
    }

    public void setGzxzPgrq(String gzxzPgrq) {
        this.gzxzPgrq = gzxzPgrq;
    }

    public String getXybgzjh() {
        return xybgzjh;
    }

    public void setXybgzjh(String xybgzjh) {
        this.xybgzjh = xybgzjh;
    }

    public String getXybgzjhQm() {
        return xybgzjhQm;
    }

    public void setXybgzjhQm(String xybgzjhQm) {
        this.xybgzjhQm = xybgzjhQm;
    }

    public String getXybgzjhRq() {
        return xybgzjhRq;
    }

    public void setXybgzjhRq(String xybgzjhRq) {
        this.xybgzjhRq = xybgzjhRq;
    }

    public String getXzjdbPg() {
        return xzjdbPg;
    }

    public void setXzjdbPg(String xzjdbPg) {
        this.xzjdbPg = xzjdbPg;
    }

    public String getXzjdbPgrq() {
        return xzjdbPgrq;
    }

    public void setXzjdbPgrq(String xzjdbPgrq) {
        this.xzjdbPgrq = xzjdbPgrq;
    }

    public String getXzjdbQm() {
        return xzjdbQm;
    }

    public void setXzjdbQm(String xzjdbQm) {
        this.xzjdbQm = xzjdbQm;
    }

    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    public String getFxdj() {
        return fxdj;
    }

    public void setFxdj(String fxdj) {
        this.fxdj = fxdj;
    }

    public String getBjrbh() {
        return bjrbh;
    }

    public void setBjrbh(String bjrbh) {
        this.bjrbh = bjrbh;
    }

    public String getBjrxm() {
        return bjrxm;
    }

    public void setBjrxm(String bjrxm) {
        this.bjrxm = bjrxm;
    }

    public String getApproval() {
        return approval;
    }

    public void setApproval(String approval) {
        this.approval = approval;
    }

    public List<FxdjListBean> getFxdjList() {
        return fxdjList;
    }

    public void setFxdjList(List<FxdjListBean> fxdjList) {
        this.fxdjList = fxdjList;
    }

    public List<SendFileEntity> getFjList() {
        return fjList;
    }

    public void setFjList(List<SendFileEntity> fjList) {
        this.fjList = fjList;
    }

    public List<LiangHuaEntity> getLhpgTmXzList() {
        return lhpgTmXzList;
    }

    public void setLhpgTmXzList(List<LiangHuaEntity> lhpgTmXzList) {
        this.lhpgTmXzList = lhpgTmXzList;
    }

    public List<PgLowLvlEntity> getSubItemList() {
        return subItemList;
    }

    public void setSubItemList(List<PgLowLvlEntity> subItemList) {
        this.subItemList = subItemList;
    }

    public String getZrPcs() {
        return zrPcs;
    }

    public void setZrPcs(String zrPcs) {
        this.zrPcs = zrPcs;
    }

    @SerializedName("jcFs")
    private CodeNameEntity jcFs;  // 吸毒类型结果

    @SerializedName("jcJg")
    private CodeNameEntity jcJg;  // 检测结果

    @SerializedName("jdsg")
    private String jdsg;  // 禁毒社工

    public String getJdsgbh() {
        return jdsgbh;
    }

    public void setJdsgbh(String jdsgbh) {
        this.jdsgbh = jdsgbh;
    }

    @SerializedName("jdsgbh")
    private String jdsgbh;  // 禁毒社工

    @SerializedName("sqylwsry")
    private String sqylwsry;  // 社区医疗卫生人员

    public CodeNameEntity getJcFs() {
        return jcFs;
    }

    public void setJcFs(CodeNameEntity jcFs) {
        this.jcFs = jcFs;
    }

    public CodeNameEntity getJcJg() {
        return jcJg;
    }

    public void setJcJg(CodeNameEntity jcJg) {
        this.jcJg = jcJg;
    }

    public String getJdsg() {
        return jdsg;
    }

    public void setJdsg(String jdsg) {
        this.jdsg = jdsg;
    }

    public String getSqylwsry() {
        return sqylwsry;
    }

    public void setSqylwsry(String sqylwsry) {
        this.sqylwsry = sqylwsry;
    }

    public static class FxdjListBean implements Serializable {
        private String bh;
        private String xyrbh;
        private String zddm;
        private String dm;
        private String mc;
        private String dxxxDylx;
        private String ywbbh;

        public String getBh() {
            return bh;
        }

        public void setBh(String bh) {
            this.bh = bh;
        }

        public String getXyrbh() {
            return xyrbh;
        }

        public void setXyrbh(String xyrbh) {
            this.xyrbh = xyrbh;
        }

        public String getZddm() {
            return zddm;
        }

        public void setZddm(String zddm) {
            this.zddm = zddm;
        }

        public String getDm() {
            return dm;
        }

        public void setDm(String dm) {
            this.dm = dm;
        }

        public String getMc() {
            return mc;
        }

        public void setMc(String mc) {
            this.mc = mc;
        }

        public String getDxxxDylx() {
            return dxxxDylx;
        }

        public void setDxxxDylx(String dxxxDylx) {
            this.dxxxDylx = dxxxDylx;
        }

        public String getYwbbh() {
            return ywbbh;
        }

        public void setYwbbh(String ywbbh) {
            this.ywbbh = ywbbh;
        }
    }

    public String getCbPgJg() {
        return cbPgJg;
    }

    public void setCbPgJg(String cbPgJg) {
        this.cbPgJg = cbPgJg;
    }

    public String getPgFz() {
        return pgFz;
    }

    public void setPgFz(String pgFz) {
        this.pgFz = pgFz;
    }

    public String getFxXwFz() {
        return fxXwFz;
    }

    public void setFxXwFz(String fxXwFz) {
        this.fxXwFz = fxXwFz;
    }

    public int getSfTg() {
        return sfTg;
    }

    public void setSfTg(int sfTg) {
        this.sfTg = sfTg;
    }

    public String getLhFz() {
        return lhFz;
    }

    public void setLhFz(String lhFz) {
        this.lhFz = lhFz;
    }

    public String getJcFz() {
        return jcFz;
    }

    public void setJcFz(String jcFz) {
        this.jcFz = jcFz;
    }

    public String getPcszrmj() {
        return pcszrmj;
    }

    public void setPcszrmj(String pcszrmj) {
        this.pcszrmj = pcszrmj;
    }

    public String getPcszrld() {
        return pcszrld;
    }

    public void setPcszrld(String pcszrld) {
        this.pcszrld = pcszrld;
    }

    public String getSswg() {
        return sswg;
    }

    public void setSswg(String sswg) {
        this.sswg = sswg;
    }

    public String getWgy() {
        return wgy;
    }

    public void setWgy(String wgy) {
        this.wgy = wgy;
    }

    public String getCsgb() {
        return csgb;
    }

    public void setCsgb(String csgb) {
        this.csgb = csgb;
    }

    public String getZjpagb() {
        return zjpagb;
    }

    public void setZjpagb(String zjpagb) {
        this.zjpagb = zjpagb;
    }
}
