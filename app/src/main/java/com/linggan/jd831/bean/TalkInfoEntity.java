package com.linggan.jd831.bean;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * 类  名：谈话详情
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/10/10 15:21
 * 版  权：凌感科技
 */
public class TalkInfoEntity implements Serializable {


    private String bh;
    private String xyrbh;
    private String xyrxm;
    private String gzrybh;
    private String gzryxm;
    private String zq;
    private String zqBh;
    private String zqKssj;
    private String zqJssj;
    private String thsj;
    private String thdd;
    private String lng;
    private String lat;
    private String shengQhdm;
    private String shengQhmc;
    private String shiQhdm;
    private String shiQhmc;
    private String quQhdm;
    private String quQhmc;
    private String xzQhdm;
    private String xzQhmc;
    private String thgy;
    private CodeNameEntity thlx;
    private String yjztbh;
    private String yjztmc;
    private String approval;
    private CodeNameEntity thfs;

    private List<NrBean> nr;

    private List<SendFileEntity> thzp;

    public List<SendFileEntity> getXdth() {
        return xdth;
    }

    public void setXdth(List<SendFileEntity> xdth) {
        this.xdth = xdth;
    }

    @SerializedName("xdth")
    private List<SendFileEntity> xdth;

    private List<SendFileEntity> thfj;

    private String bz;

    private SendFileEntity dzqm;//电子签名

    public String getBh() {
        return bh;
    }

    public void setBh(String bh) {
        this.bh = bh;
    }

    public String getXyrbh() {
        return xyrbh;
    }

    public void setXyrbh(String xyrbh) {
        this.xyrbh = xyrbh;
    }

    public String getXyrxm() {
        return xyrxm;
    }

    public void setXyrxm(String xyrxm) {
        this.xyrxm = xyrxm;
    }

    public String getGzrybh() {
        return gzrybh;
    }

    public void setGzrybh(String gzrybh) {
        this.gzrybh = gzrybh;
    }

    public String getGzryxm() {
        return gzryxm;
    }

    public void setGzryxm(String gzryxm) {
        this.gzryxm = gzryxm;
    }

    public String getZq() {
        return zq;
    }

    public void setZq(String zq) {
        this.zq = zq;
    }

    public String getZqBh() {
        return zqBh;
    }

    public void setZqBh(String zqBh) {
        this.zqBh = zqBh;
    }

    public String getZqKssj() {
        return zqKssj;
    }

    public void setZqKssj(String zqKssj) {
        this.zqKssj = zqKssj;
    }

    public String getZqJssj() {
        return zqJssj;
    }

    public void setZqJssj(String zqJssj) {
        this.zqJssj = zqJssj;
    }

    public String getThsj() {
        return thsj;
    }

    public void setThsj(String thsj) {
        this.thsj = thsj;
    }

    public String getThdd() {
        return thdd;
    }

    public void setThdd(String thdd) {
        this.thdd = thdd;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getShengQhdm() {
        return shengQhdm;
    }

    public void setShengQhdm(String shengQhdm) {
        this.shengQhdm = shengQhdm;
    }

    public String getShengQhmc() {
        return shengQhmc;
    }

    public void setShengQhmc(String shengQhmc) {
        this.shengQhmc = shengQhmc;
    }

    public String getShiQhdm() {
        return shiQhdm;
    }

    public void setShiQhdm(String shiQhdm) {
        this.shiQhdm = shiQhdm;
    }

    public String getShiQhmc() {
        return shiQhmc;
    }

    public void setShiQhmc(String shiQhmc) {
        this.shiQhmc = shiQhmc;
    }

    public String getQuQhdm() {
        return quQhdm;
    }

    public void setQuQhdm(String quQhdm) {
        this.quQhdm = quQhdm;
    }

    public String getQuQhmc() {
        return quQhmc;
    }

    public void setQuQhmc(String quQhmc) {
        this.quQhmc = quQhmc;
    }

    public String getXzQhdm() {
        return xzQhdm;
    }

    public void setXzQhdm(String xzQhdm) {
        this.xzQhdm = xzQhdm;
    }

    public String getXzQhmc() {
        return xzQhmc;
    }

    public void setXzQhmc(String xzQhmc) {
        this.xzQhmc = xzQhmc;
    }

    public String getThgy() {
        return thgy;
    }

    public void setThgy(String thgy) {
        this.thgy = thgy;
    }

    public CodeNameEntity getThlx() {
        return thlx;
    }

    public void setThlx(CodeNameEntity thlx) {
        this.thlx = thlx;
    }

    public String getYjztbh() {
        return yjztbh;
    }

    public void setYjztbh(String yjztbh) {
        this.yjztbh = yjztbh;
    }

    public String getYjztmc() {
        return yjztmc;
    }

    public void setYjztmc(String yjztmc) {
        this.yjztmc = yjztmc;
    }

    public String getApproval() {
        return approval;
    }

    public void setApproval(String approval) {
        this.approval = approval;
    }

    public List<NrBean> getNr() {
        return nr;
    }

    public void setNr(List<NrBean> nr) {
        this.nr = nr;
    }

    public List<SendFileEntity> getThzp() {
        return thzp;
    }

    public void setThzp(List<SendFileEntity> thzp) {
        this.thzp = thzp;
    }

    public List<SendFileEntity> getThfj() {
        return thfj;
    }

    public void setThfj(List<SendFileEntity> thfj) {
        this.thfj = thfj;
    }

    public CodeNameEntity getThfs() {
        return thfs;
    }

    public void setThfs(CodeNameEntity thfs) {
        this.thfs = thfs;
    }

    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    public SendFileEntity getDzqm() {
        return dzqm;
    }

    public void setDzqm(SendFileEntity dzqm) {
        this.dzqm = dzqm;
    }
}
