package com.linggan.jd831.bean;

import java.io.Serializable;

public class MenuItem implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private int id;
    private String title;
    private int iconResId;
    private Class<?> targetActivity;

    public MenuItem() {
        // 无参构造函数
    }

    public MenuItem(int id, String title, int iconResId, Class<?> targetActivity) {
        this.id = id;
        this.title = title;
        this.iconResId = iconResId;
        this.targetActivity = targetActivity;
    }

    public int getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public int getIconResId() {
        return iconResId;
    }

    public Class<?> getTargetActivity() {
        return targetActivity;
    }
} 