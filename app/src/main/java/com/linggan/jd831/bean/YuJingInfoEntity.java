package com.linggan.jd831.bean;

import java.io.Serializable;
import java.util.List;

/**
 * 类  名：预警详情
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2023/9/6 9:24
 * 版  权：LGKJ
 */
public class YuJingInfoEntity implements Serializable {


    private FxczxxVOBean fxczxxVO;
    private RyjbxxVOBean ryjbxxVO;
    private CsxxVOVOBean csxxVO;
    private YzdyjxxVOBean yzdyjxx;
    private Djzlxx djzlxx;

    public FxczxxVOBean getFxczxxVO() {
        return fxczxxVO;
    }

    public void setFxczxxVO(FxczxxVOBean fxczxxVO) {
        this.fxczxxVO = fxczxxVO;
    }

    public RyjbxxVOBean getRyjbxxVO() {
        return ryjbxxVO;
    }

    public void setRyjbxxVO(RyjbxxVOBean ryjbxxVO) {
        this.ryjbxxVO = ryjbxxVO;
    }

    public YzdyjxxVOBean getYzdyjxx() {
        return yzdyjxx;
    }

    public void setYzdyjxx(YzdyjxxVOBean yzdyjxx) {
        this.yzdyjxx = yzdyjxx;
    }

    public Djzlxx getDjzlxx() {
        return djzlxx;
    }

    public void setDjzlxx(Djzlxx djzlxx) {
        this.djzlxx = djzlxx;
    }

    public static class FxczxxVOBean implements Serializable {
        private String bh;
        private String qrsj;
        private String qksm;
        private String sbr;
        private String lxdh;
        private String sbsj;
        private int cxyj;
        private String rwmc;
        private String rwms;
        private String dwdm;
        private String dwmc;
        private String zrrbh;
        private String zrrxm;
        private String zxrXm;
        private String rwjzsj;
        private int rwwczt;
        private String rssj;
        private String cssj;
        private CodeNameEntity bgqFxdj;
        private CodeNameEntity bghFxdj;
        private String fxdjbgBh;
        private String fxdjbgYy;
        private String fxbgsj;
        private String zjsysj;
        private String jjwdlts;
        private String lsdlcs;
        private String csdz;
        private String jjrs;
        private String jjsj;
        private String csfl;
        private String fxbgyy;
        private CodeNameEntity yjdx;
        private String yjdxBh;
        private int sfSc;
        private String czrXm;
        private String czrDh;
        private String yjxxBh;
        private CodeNameEntity yjlx;
        private String cqts;
        private String cxyy;
        private String cxfj;
        private String wcsj;
        private String czsj;
        private String rwbh;

        private String bgqFxdjDf;
        private String bghFxdjDf;

        private List<JzxxBean> jzxx;
        private List<ClxxBean> clxx;

        private String zjfysj;
        private String lxwfyts;
        private String lsfycs;
        private String yjsj;
        private String fysqsj;
        private String fwjg;
        private String fwjgDq;
        private String fwjgLxdh;
        private String tscz;
        private String lrsj;

        public String getLrsj() {
            return lrsj;
        }

        public void setLrsj(String lrsj) {
            this.lrsj = lrsj;
        }

        public String getTscz() {
            return tscz;
        }

        public void setTscz(String tscz) {
            this.tscz = tscz;
        }

        public String getFwjgLxdh() {
            return fwjgLxdh;
        }

        public void setFwjgLxdh(String fwjgLxdh) {
            this.fwjgLxdh = fwjgLxdh;
        }

        public String getFwjgDq() {
            return fwjgDq;
        }

        public void setFwjgDq(String fwjgDq) {
            this.fwjgDq = fwjgDq;
        }

        public String getFwjg() {
            return fwjg;
        }

        public void setFwjg(String fwjg) {
            this.fwjg = fwjg;
        }

        public String getFysqsj() {
            return fysqsj;
        }

        public void setFysqsj(String fysqsj) {
            this.fysqsj = fysqsj;
        }

        public String getYjsj() {
            return yjsj;
        }

        public void setYjsj(String yjsj) {
            this.yjsj = yjsj;
        }

        public String getLsfycs() {
            return lsfycs;
        }

        public void setLsfycs(String lsfycs) {
            this.lsfycs = lsfycs;
        }

        public String getLxwfyts() {
            return lxwfyts;
        }

        public void setLxwfyts(String lxwfyts) {
            this.lxwfyts = lxwfyts;
        }

        public String getBh() {
            return bh;
        }

        public void setBh(String bh) {
            this.bh = bh;
        }

        public String getQrsj() {
            return qrsj;
        }

        public void setQrsj(String qrsj) {
            this.qrsj = qrsj;
        }

        public String getQksm() {
            return qksm;
        }

        public void setQksm(String qksm) {
            this.qksm = qksm;
        }

        public String getSbr() {
            return sbr;
        }

        public void setSbr(String sbr) {
            this.sbr = sbr;
        }

        public String getLxdh() {
            return lxdh;
        }

        public void setLxdh(String lxdh) {
            this.lxdh = lxdh;
        }

        public String getSbsj() {
            return sbsj;
        }

        public void setSbsj(String sbsj) {
            this.sbsj = sbsj;
        }

        public int getCxyj() {
            return cxyj;
        }

        public void setCxyj(int cxyj) {
            this.cxyj = cxyj;
        }

        public String getRwmc() {
            return rwmc;
        }

        public void setRwmc(String rwmc) {
            this.rwmc = rwmc;
        }

        public String getRwms() {
            return rwms;
        }

        public void setRwms(String rwms) {
            this.rwms = rwms;
        }

        public String getDwdm() {
            return dwdm;
        }

        public void setDwdm(String dwdm) {
            this.dwdm = dwdm;
        }

        public String getDwmc() {
            return dwmc;
        }

        public void setDwmc(String dwmc) {
            this.dwmc = dwmc;
        }

        public String getZrrbh() {
            return zrrbh;
        }

        public void setZrrbh(String zrrbh) {
            this.zrrbh = zrrbh;
        }

        public String getZrrxm() {
            return zrrxm;
        }

        public String getZjfysj() {
            return zjfysj;
        }

        public void setZjfysj(String zjfysj) {
            this.zjfysj = zjfysj;
        }

        public void setZrrxm(String zrrxm) {
            this.zrrxm = zrrxm;
        }

        public String getRwjzsj() {
            return rwjzsj;
        }

        public void setRwjzsj(String rwjzsj) {
            this.rwjzsj = rwjzsj;
        }

        public int getRwwczt() {
            return rwwczt;
        }

        public void setRwwczt(int rwwczt) {
            this.rwwczt = rwwczt;
        }

        public String getRssj() {
            return rssj;
        }

        public void setRssj(String rssj) {
            this.rssj = rssj;
        }

        public String getCssj() {
            return cssj;
        }

        public void setCssj(String cssj) {
            this.cssj = cssj;
        }

        public CodeNameEntity getBgqFxdj() {
            return bgqFxdj;
        }

        public void setBgqFxdj(CodeNameEntity bgqFxdj) {
            this.bgqFxdj = bgqFxdj;
        }

        public CodeNameEntity getBghFxdj() {
            return bghFxdj;
        }

        public void setBghFxdj(CodeNameEntity bghFxdj) {
            this.bghFxdj = bghFxdj;
        }

        public String getFxbgsj() {
            return fxbgsj;
        }

        public void setFxbgsj(String fxbgsj) {
            this.fxbgsj = fxbgsj;
        }

        public String getZjsysj() {
            return zjsysj;
        }

        public void setZjsysj(String zjsysj) {
            this.zjsysj = zjsysj;
        }

        public String getLsdlcs() {
            return lsdlcs;
        }

        public void setLsdlcs(String lsdlcs) {
            this.lsdlcs = lsdlcs;
        }

        public String getCsdz() {
            return csdz;
        }

        public void setCsdz(String csdz) {
            this.csdz = csdz;
        }

        public String getJjrs() {
            return jjrs;
        }

        public void setJjrs(String jjrs) {
            this.jjrs = jjrs;
        }

        public String getJjsj() {
            return jjsj;
        }

        public void setJjsj(String jjsj) {
            this.jjsj = jjsj;
        }

        public String getCsfl() {
            return csfl;
        }

        public void setCsfl(String csfl) {
            this.csfl = csfl;
        }

        public String getFxbgyy() {
            return fxbgyy;
        }

        public void setFxbgyy(String fxbgyy) {
            this.fxbgyy = fxbgyy;
        }

        public CodeNameEntity getYjdx() {
            return yjdx;
        }

        public void setYjdx(CodeNameEntity yjdx) {
            this.yjdx = yjdx;
        }

        public String getYjdxBh() {
            return yjdxBh;
        }

        public void setYjdxBh(String yjdxBh) {
            this.yjdxBh = yjdxBh;
        }

        public int getSfSc() {
            return sfSc;
        }

        public void setSfSc(int sfSc) {
            this.sfSc = sfSc;
        }

        public String getCzrXm() {
            return czrXm;
        }

        public void setCzrXm(String czrXm) {
            this.czrXm = czrXm;
        }

        public String getCzrDh() {
            return czrDh;
        }

        public void setCzrDh(String czrDh) {
            this.czrDh = czrDh;
        }

        public String getYjxxBh() {
            return yjxxBh;
        }

        public void setYjxxBh(String yjxxBh) {
            this.yjxxBh = yjxxBh;
        }

        public CodeNameEntity getYjlx() {
            return yjlx;
        }

        public void setYjlx(CodeNameEntity yjlx) {
            this.yjlx = yjlx;
        }

        public String getCqts() {
            return cqts;
        }

        public void setCqts(String cqts) {
            this.cqts = cqts;
        }

        public String getCxyy() {
            return cxyy;
        }

        public void setCxyy(String cxyy) {
            this.cxyy = cxyy;
        }

        public String getCxfj() {
            return cxfj;
        }

        public void setCxfj(String cxfj) {
            this.cxfj = cxfj;
        }

        public String getWcsj() {
            return wcsj;
        }

        public void setWcsj(String wcsj) {
            this.wcsj = wcsj;
        }

        public String getCzsj() {
            return czsj;
        }

        public void setCzsj(String czsj) {
            this.czsj = czsj;
        }

        public String getJjwdlts() {
            return jjwdlts;
        }

        public void setJjwdlts(String jjwdlts) {
            this.jjwdlts = jjwdlts;
        }

        public String getRwbh() {
            return rwbh;
        }

        public void setRwbh(String rwbh) {
            this.rwbh = rwbh;
        }

        public String getFxdjbgBh() {
            return fxdjbgBh;
        }

        public void setFxdjbgBh(String fxdjbgBh) {
            this.fxdjbgBh = fxdjbgBh;
        }

        public String getFxdjbgYy() {
            return fxdjbgYy;
        }

        public void setFxdjbgYy(String fxdjbgYy) {
            this.fxdjbgYy = fxdjbgYy;
        }

        public String getBgqFxdjDf() {
            return bgqFxdjDf;
        }

        public void setBgqFxdjDf(String bgqFxdjDf) {
            this.bgqFxdjDf = bgqFxdjDf;
        }

        public String getBghFxdjDf() {
            return bghFxdjDf;
        }

        public void setBghFxdjDf(String bghFxdjDf) {
            this.bghFxdjDf = bghFxdjDf;
        }

        public String getZxrXm() {
            return zxrXm;
        }

        public void setZxrXm(String zxrXm) {
            this.zxrXm = zxrXm;
        }

        public List<JzxxBean> getJzxx() {
            return jzxx;
        }

        public void setJzxx(List<JzxxBean> jzxx) {
            this.jzxx = jzxx;
        }

        public List<ClxxBean> getClxx() {
            return clxx;
        }

        public void setClxx(List<ClxxBean> clxx) {
            this.clxx = clxx;
        }
    }

    public static class RyjbxxVOBean implements Serializable {
        private String xyrbh;
        private String xm;
        private String zjhm;
        private String lxdh;
        private CodeNameEntity ryyjzt;
        private CodeNameEntity fxdj;
        private String hjdzDzmc;
        private String sjjzdDzmc;
        private String fzrxm;
        private String fzrdh;
        private String fzmjxm;
        private String fzmjdh;
        private String pcsldxm;
        private String pcslddh;
        private String bfxzbh;
        private String hjdXzQhdm;
        private String hjdSqQhdm;
        private String xp;
        private String yhId;
        private List<?> ryejzt;

        public String getXyrbh() {
            return xyrbh;
        }

        public void setXyrbh(String xyrbh) {
            this.xyrbh = xyrbh;
        }

        public String getXm() {
            return xm;
        }

        public void setXm(String xm) {
            this.xm = xm;
        }

        public String getZjhm() {
            return zjhm;
        }

        public void setZjhm(String zjhm) {
            this.zjhm = zjhm;
        }

        public String getLxdh() {
            return lxdh;
        }

        public void setLxdh(String lxdh) {
            this.lxdh = lxdh;
        }

        public CodeNameEntity getRyyjzt() {
            return ryyjzt;
        }

        public void setRyyjzt(CodeNameEntity ryyjzt) {
            this.ryyjzt = ryyjzt;
        }

        public CodeNameEntity getFxdj() {
            return fxdj;
        }

        public void setFxdj(CodeNameEntity fxdj) {
            this.fxdj = fxdj;
        }

        public String getHjdzDzmc() {
            return hjdzDzmc;
        }

        public void setHjdzDzmc(String hjdzDzmc) {
            this.hjdzDzmc = hjdzDzmc;
        }

        public String getSjjzdDzmc() {
            return sjjzdDzmc;
        }

        public void setSjjzdDzmc(String sjjzdDzmc) {
            this.sjjzdDzmc = sjjzdDzmc;
        }

        public String getFzrxm() {
            return fzrxm;
        }

        public void setFzrxm(String fzrxm) {
            this.fzrxm = fzrxm;
        }

        public String getFzrdh() {
            return fzrdh;
        }

        public void setFzrdh(String fzrdh) {
            this.fzrdh = fzrdh;
        }

        public String getFzmjxm() {
            return fzmjxm;
        }

        public void setFzmjxm(String fzmjxm) {
            this.fzmjxm = fzmjxm;
        }

        public String getFzmjdh() {
            return fzmjdh;
        }

        public void setFzmjdh(String fzmjdh) {
            this.fzmjdh = fzmjdh;
        }

        public String getPcsldxm() {
            return pcsldxm;
        }

        public void setPcsldxm(String pcsldxm) {
            this.pcsldxm = pcsldxm;
        }

        public String getPcslddh() {
            return pcslddh;
        }

        public void setPcslddh(String pcslddh) {
            this.pcslddh = pcslddh;
        }

        public String getBfxzbh() {
            return bfxzbh;
        }

        public void setBfxzbh(String bfxzbh) {
            this.bfxzbh = bfxzbh;
        }

        public String getHjdXzQhdm() {
            return hjdXzQhdm;
        }

        public void setHjdXzQhdm(String hjdXzQhdm) {
            this.hjdXzQhdm = hjdXzQhdm;
        }

        public String getHjdSqQhdm() {
            return hjdSqQhdm;
        }

        public void setHjdSqQhdm(String hjdSqQhdm) {
            this.hjdSqQhdm = hjdSqQhdm;
        }

        public String getXp() {
            return xp;
        }

        public void setXp(String xp) {
            this.xp = xp;
        }

        public String getYhId() {
            return yhId;
        }

        public void setYhId(String yhId) {
            this.yhId = yhId;
        }

        public List<?> getRyejzt() {
            return ryejzt;
        }

        public void setRyejzt(List<?> ryejzt) {
            this.ryejzt = ryejzt;
        }


    }

    public CsxxVOVOBean getCsxxVO() {
        return csxxVO;
    }

    public void setCsxxVO(CsxxVOVOBean csxxVO) {
        this.csxxVO = csxxVO;
    }

    public static class CsxxVOVOBean implements Serializable {
        private String csmc;//场所名称
        private String csbh;//场所主键
        private String dz;// 场所地址
        private String cslx;//场所类型
        private String csly;//场所来源
        private String fxdj;//风险等级
        private String csZrrxm;// 场所责任人姓名
        private String lxdh;// 场所责任人电话
        private String ssdq;//所属地区
        private String syrs;//使用人
        private String cjsj;//场所创建时间
        private String csImg;//场所图片

        public String getCsmc() {
            return csmc;
        }

        public String getCsbh() {
            return csbh;
        }

        public String getDz() {
            return dz;
        }

        public String getCslx() {
            return cslx;
        }

        public String getCsly() {
            return csly;
        }

        public String getFxdj() {
            return fxdj;
        }

        public String getCsZrrxm() {
            return csZrrxm;
        }

        public String getLxdh() {
            return lxdh;
        }

        public String getSsdq() {
            return ssdq;
        }

        public String getSyrs() {
            return syrs;
        }

        public String getCjsj() {
            return cjsj;
        }

        public String getCsImg() {
            return csImg;
        }

        public void setCsmc(String csmc) {
            this.csmc = csmc;
        }

        public void setCsbh(String csbh) {
            this.csbh = csbh;
        }

        public void setDz(String dz) {
            this.dz = dz;
        }

        public void setCslx(String cslx) {
            this.cslx = cslx;
        }

        public void setCsly(String csly) {
            this.csly = csly;
        }

        public void setFxdj(String fxdj) {
            this.fxdj = fxdj;
        }

        public void setCsZrrxm(String csZrrxm) {
            this.csZrrxm = csZrrxm;
        }

        public void setLxdh(String lxdh) {
            this.lxdh = lxdh;
        }

        public void setSsdq(String ssdq) {
            this.ssdq = ssdq;
        }

        public void setSyrs(String syrs) {
            this.syrs = syrs;
        }

        public void setCjsj(String cjsj) {
            this.cjsj = cjsj;
        }

        public void setCsImg(String csImg) {
            this.csImg = csImg;
        }
    }

    public static class YzdyjxxVOBean implements Serializable {

        private String ckbh;
        private String ckmc;
        private String qybh;
        private String qymc;
        private String xxyxm;
        private String xxysj;
        private Object xxyzp;
        private String bbbh;
        private String bbzt;
        private BbqkBean bbqk;
        private String jrsj;
        private String lksj;
        private List<ZpBean> zps;
        private String qyssdq;
        private String qyzxdj;
        private SxtxxBean sxtxx;

        public String getCkbh() {
            return ckbh;
        }

        public void setCkbh(String ckbh) {
            this.ckbh = ckbh;
        }

        public String getCkmc() {
            return ckmc;
        }

        public void setCkmc(String ckmc) {
            this.ckmc = ckmc;
        }

        public String getQybh() {
            return qybh;
        }

        public void setQybh(String qybh) {
            this.qybh = qybh;
        }

        public String getQymc() {
            return qymc;
        }

        public void setQymc(String qymc) {
            this.qymc = qymc;
        }

        public String getXxyxm() {
            return xxyxm;
        }

        public void setXxyxm(String xxyxm) {
            this.xxyxm = xxyxm;
        }

        public String getXxysj() {
            return xxysj;
        }

        public void setXxysj(String xxysj) {
            this.xxysj = xxysj;
        }

        public Object getXxyzp() {
            return xxyzp;
        }

        public void setXxyzp(Object xxyzp) {
            this.xxyzp = xxyzp;
        }

        public String getBbbh() {
            return bbbh;
        }

        public void setBbbh(String bbbh) {
            this.bbbh = bbbh;
        }

        public String getBbzt() {
            return bbzt;
        }

        public void setBbzt(String bbzt) {
            this.bbzt = bbzt;
        }

        public BbqkBean getBbqk() {
            return bbqk;
        }

        public void setBbqk(BbqkBean bbqk) {
            this.bbqk = bbqk;
        }

        public String getJrsj() {
            return jrsj;
        }

        public void setJrsj(String jrsj) {
            this.jrsj = jrsj;
        }

        public String getLksj() {
            return lksj;
        }

        public void setLksj(String lksj) {
            this.lksj = lksj;
        }

        public List<ZpBean> getZps() {
            return zps;
        }

        public void setZps(List<ZpBean> zps) {
            this.zps = zps;
        }

        public String getQyssdq() {
            return qyssdq;
        }

        public void setQyssdq(String qyssdq) {
            this.qyssdq = qyssdq;
        }

        public String getQyzxdj() {
            return qyzxdj;
        }

        public void setQyzxdj(String qyzxdj) {
            this.qyzxdj = qyzxdj;
        }

        public SxtxxBean getSxtxx() {
            return sxtxx;
        }

        public void setSxtxx(SxtxxBean sxtxx) {
            this.sxtxx = sxtxx;
        }

        public static class BbqkBean implements Serializable {
            private String code;
            private String name;

            public String getCode() {
                return code;
            }

            public void setCode(String code) {
                this.code = code;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }
        }

        public static class SxtxxBean {
            private String id;
            private String sxtMc;
            private String sxtXlh;
            private String stkXlh;
            private String gbXlh;
            private String gbTdh;
            private String bfdz;

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getSxtMc() {
                return sxtMc;
            }

            public void setSxtMc(String sxtMc) {
                this.sxtMc = sxtMc;
            }

            public String getSxtXlh() {
                return sxtXlh;
            }

            public void setSxtXlh(String sxtXlh) {
                this.sxtXlh = sxtXlh;
            }

            public String getStkXlh() {
                return stkXlh;
            }

            public void setStkXlh(String stkXlh) {
                this.stkXlh = stkXlh;
            }

            public String getGbXlh() {
                return gbXlh;
            }

            public void setGbXlh(String gbXlh) {
                this.gbXlh = gbXlh;
            }

            public String getGbTdh() {
                return gbTdh;
            }

            public void setGbTdh(String gbTdh) {
                this.gbTdh = gbTdh;
            }

            public String getBfdz() {
                return bfdz;
            }

            public void setBfdz(String bfdz) {
                this.bfdz = bfdz;
            }
        }
    }

    public static class Djzlxx {
        private String jcBh;//检测编号
        private String cph;//车牌号
        private String lx;//车辆类型
        private String yyzbh;//运营证编号
        private String yyzJssj;//运营证有效期至
        private CodeNameEntity yyzZt;//营运证状态(0正常 1即将到期 2已过期)
        private String sj;//检测时间
        private String fs;//检测方式（0尿液检测 1毛发检测 2血液检测 3唾液检测 4虹膜检测）
        private String jcryxm;//检测人
        private String bjcrxm;//被检测人
        private String jg;//检测结果（0阴性 1阳性）
        private String kkMc;//检测卡口
        private String kkszdq;//卡口所在地区
        private String kkBh;// 卡口编号

        public String getJcBh() {
            return jcBh;
        }

        public void setJcBh(String jcBh) {
            this.jcBh = jcBh;
        }

        public String getCph() {
            return cph;
        }

        public void setCph(String cph) {
            this.cph = cph;
        }

        public String getLx() {
            return lx;
        }

        public void setLx(String lx) {
            this.lx = lx;
        }

        public String getYyzbh() {
            return yyzbh;
        }

        public void setYyzbh(String yyzbh) {
            this.yyzbh = yyzbh;
        }

        public String getYyzJssj() {
            return yyzJssj;
        }

        public void setYyzJssj(String yyzJssj) {
            this.yyzJssj = yyzJssj;
        }

        public CodeNameEntity getYyzZt() {
            return yyzZt;
        }

        public void setYyzZt(CodeNameEntity yyzZt) {
            this.yyzZt = yyzZt;
        }

        public String getSj() {
            return sj;
        }

        public void setSj(String sj) {
            this.sj = sj;
        }

        public String getFs() {
            return fs;
        }

        public void setFs(String fs) {
            this.fs = fs;
        }

        public String getJcryxm() {
            return jcryxm;
        }

        public void setJcryxm(String jcryxm) {
            this.jcryxm = jcryxm;
        }

        public String getBjcrxm() {
            return bjcrxm;
        }

        public void setBjcrxm(String bjcrxm) {
            this.bjcrxm = bjcrxm;
        }

        public String getJg() {
            return jg;
        }

        public void setJg(String jg) {
            this.jg = jg;
        }

        public String getKkMc() {
            return kkMc;
        }

        public void setKkMc(String kkMc) {
            this.kkMc = kkMc;
        }

        public String getKkszdq() {
            return kkszdq;
        }

        public void setKkszdq(String kkszdq) {
            this.kkszdq = kkszdq;
        }

        public String getKkBh() {
            return kkBh;
        }

        public void setKkBh(String kkBh) {
            this.kkBh = kkBh;
        }
    }
}
