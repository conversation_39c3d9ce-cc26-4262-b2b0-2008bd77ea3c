package com.linggan.jd831.bean;

import java.util.List;

/**
 * 类  名：查询排查项目
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2023/1/3 11:57
 * 版  权：LGKJ
 */
public class PlaceTypeXmEntity {

    private String id;
    private String pcxm;//排查项目
    private int sfdx;//是否多选 0否  1是
    private List<PcnrVOSBean> pcnrVOS;//排查内容
    private int sfyx;//是否已选 1：是

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPcxm() {
        return pcxm;
    }

    public void setPcxm(String pcxm) {
        this.pcxm = pcxm;
    }

    public int getSfdx() {
        return sfdx;
    }

    public void setSfdx(int sfdx) {
        this.sfdx = sfdx;
    }

    public List<PcnrVOSBean> getPcnrVOS() {
        return pcnrVOS;
    }

    public void setPcnrVOS(List<PcnrVOSBean> pcnrVOS) {
        this.pcnrVOS = pcnrVOS;
    }

    public int getSfyx() {
        return sfyx;
    }

    public void setSfyx(int sfyx) {
        this.sfyx = sfyx;
    }

    public static class PcnrVOSBean {
        private String sftx;//是否填写 0 否  1 是
        private String pcnr;//排查内容
        private String cztp;//参考图片
        private String id;

        //自定义字段
        private boolean isChoice;
        private boolean selected;
        private String editString;

        public boolean isChoice() {
            return isChoice;
        }

        public void setChoice(boolean choice) {
            isChoice = choice;
        }

        public String getSftx() {
            return sftx;
        }

        public void setSftx(String sftx) {
            this.sftx = sftx;
        }

        public String getPcnr() {
            return pcnr;
        }

        public void setPcnr(String pcnr) {
            this.pcnr = pcnr;
        }

        public String getCztp() {
            return cztp;
        }

        public void setCztp(String cztp) {
            this.cztp = cztp;
        }

        public boolean isSelected() {
            return selected;
        }

        public void setSelected(boolean selected) {
            this.selected = selected;
        }

        public String getEditString() {
            return editString;
        }

        public void setEditString(String editString) {
            this.editString = editString;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }


    }
}
