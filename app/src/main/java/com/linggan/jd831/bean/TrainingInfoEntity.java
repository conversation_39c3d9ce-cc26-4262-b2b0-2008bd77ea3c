package com.linggan.jd831.bean;

import java.io.Serializable;

/**
 * 培训信息实体类
 */
public class TrainingInfoEntity implements Serializable {
    public String getPxbh() {
        return pxbh;
    }

    public void setPxbh(String pxbh) {
        this.pxbh = pxbh;
    }

    private String pxbh; // 培训编号
    private String trainingTime; // 培训时间
    private String trainingAddress; // 培训地点
    private String trainingContent; // 培训内容


    private String jd; // 经度
    private String wd; // 纬度

    public String getTrainingTime() {
        return trainingTime;
    }

    public void setTrainingTime(String trainingTime) {
        this.trainingTime = trainingTime;
    }

    public String getTrainingAddress() {
        return trainingAddress;
    }

    public void setTrainingAddress(String trainingAddress) {
        this.trainingAddress = trainingAddress;
    }

    public String getTrainingContent() {
        return trainingContent;
    }

    public void setTrainingContent(String trainingContent) {
        this.trainingContent = trainingContent;
    }
    public String getJd() {
        return jd;
    }

    public void setJd(String jd) {
        this.jd = jd;
    }

    public String getWd() {
        return wd;
    }

    public void setWd(String wd) {
        this.wd = wd;
    }
} 