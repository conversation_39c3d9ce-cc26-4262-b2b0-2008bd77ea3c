package com.linggan.jd831.bean;

import java.util.List;

/**
 * 类  名：涉赌人员-一般情况 --数据提交实体类
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/11/30 17:00
 * 版  权：凌感科技
 */
public class GeneralInfoEntity {

    private String xyrbh;//吸毒人员主键id
    private String yjztbh;
    private List<GeneralGxEntity> jtgx;
    private List<GeneralGx1Entity> shgx;
    private String ryyjzt;//当前状态
    private CodeNameEntity gkxxYsrqk;//月收入情况，对应最字典表gkxx_ysrqk
    private CodeNameEntity gkxxStzk;//身体状况,对应字典表gkxx_stzk
    private CodeNameEntity gkxxWffzqk;//违法犯罪情况,对应字典表gkxx_wffzqk
    private CodeNameEntity gkxxDqzk;//当前情况,对应字典表gkxx_dqzk
    private String sfDxjz;//是否吊销驾驶证 0是 1否
    private String jwxdrs;//交往吸毒人数
    private String clxx;//车辆信息
    private List<CodeNameEntity> gkxxShbzqk;//社会保障情况,对应字典表gkxx_shbzqk（多选）
    private List<CodeNameEntity> gkxxJybzqk;//就业帮助情况,对应字典表gkxx_jybzqk（多选）
    private List<CodeNameEntity> gkxxJtqk;//
    private String gkxxbh;
    private String sjLy;

    public String getGkxxbh() {
        return gkxxbh;
    }

    public void setGkxxbh(String gkxxbh) {
        this.gkxxbh = gkxxbh;
    }

    public String getXyrbh() {
        return xyrbh;
    }

    public void setXyrbh(String xyrbh) {
        this.xyrbh = xyrbh;
    }

    public String getYjztbh() {
        return yjztbh;
    }

    public void setYjztbh(String yjztbh) {
        this.yjztbh = yjztbh;
    }

    public List<GeneralGxEntity> getJtgx() {
        return jtgx;
    }

    public void setJtgx(List<GeneralGxEntity> jtgx) {
        this.jtgx = jtgx;
    }

    public List<GeneralGx1Entity> getShgx() {
        return shgx;
    }

    public void setShgx(List<GeneralGx1Entity> shgx) {
        this.shgx = shgx;
    }

    public String getRyyjzt() {
        return ryyjzt;
    }

    public void setRyyjzt(String ryyjzt) {
        this.ryyjzt = ryyjzt;
    }

    public CodeNameEntity getGkxxYsrqk() {
        return gkxxYsrqk;
    }

    public void setGkxxYsrqk(CodeNameEntity gkxxYsrqk) {
        this.gkxxYsrqk = gkxxYsrqk;
    }

    public CodeNameEntity getGkxxStzk() {
        return gkxxStzk;
    }

    public void setGkxxStzk(CodeNameEntity gkxxStzk) {
        this.gkxxStzk = gkxxStzk;
    }

    public CodeNameEntity getGkxxWffzqk() {
        return gkxxWffzqk;
    }

    public void setGkxxWffzqk(CodeNameEntity gkxxWffzqk) {
        this.gkxxWffzqk = gkxxWffzqk;
    }

    public CodeNameEntity getGkxxDqzk() {
        return gkxxDqzk;
    }

    public void setGkxxDqzk(CodeNameEntity gkxxDqzk) {
        this.gkxxDqzk = gkxxDqzk;
    }

    public String getSfDxjz() {
        return sfDxjz;
    }

    public void setSfDxjz(String sfDxjz) {
        this.sfDxjz = sfDxjz;
    }

    public String getJwxdrs() {
        return jwxdrs;
    }

    public void setJwxdrs(String jwxdrs) {
        this.jwxdrs = jwxdrs;
    }

    public String getClxx() {
        return clxx;
    }

    public void setClxx(String clxx) {
        this.clxx = clxx;
    }

    public List<CodeNameEntity> getGkxxShbzqk() {
        return gkxxShbzqk;
    }

    public void setGkxxShbzqk(List<CodeNameEntity> gkxxShbzqk) {
        this.gkxxShbzqk = gkxxShbzqk;
    }

    public List<CodeNameEntity> getGkxxJybzqk() {
        return gkxxJybzqk;
    }

    public void setGkxxJybzqk(List<CodeNameEntity> gkxxJybzqk) {
        this.gkxxJybzqk = gkxxJybzqk;
    }

    public List<CodeNameEntity> getGkxxJtqk() {
        return gkxxJtqk;
    }

    public void setGkxxJtqk(List<CodeNameEntity> gkxxJtqk) {
        this.gkxxJtqk = gkxxJtqk;
    }

    public String getSjLy() {
        return sjLy;
    }

    public void setSjLy(String sjLy) {
        this.sjLy = sjLy;
    }
}
