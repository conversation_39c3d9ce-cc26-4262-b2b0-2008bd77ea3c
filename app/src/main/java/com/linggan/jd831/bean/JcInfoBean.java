package com.linggan.jd831.bean;

import java.util.List;

/**
 * 毒驾--检测详情
 */
public class JcInfoBean {

    private String bh;//
    private String gzryxm;// 检测人
    private String fs;// （0尿液检测 1毛发检测 2血液检测 3唾液检测 4虹膜检测）
    private String jg;// 检测结果（0阴性 1阳性）
    private List<SendFileEntity> fjList;// 附件
    private List<SendFileEntity> bgList;// 检测报告
    private List<SendFileEntity> spList;// 视频
    private String kkMc;// 检测卡口
    private String bz;// 备注
    private String sj;
    private String dz;
    private String shengQhmc;
    private String shiQhmc;
    private String quQhmc;
    private String xzQhmc;
    private String jcryxm;

    public String getBh() {
        return bh;
    }

    public void setBh(String bh) {
        this.bh = bh;
    }

    public String getGzryxm() {
        return gzryxm;
    }

    public void setGzryxm(String gzryxm) {
        this.gzryxm = gzryxm;
    }

    public String getFs() {
        return fs;
    }

    public void setFs(String fs) {
        this.fs = fs;
    }

    public String getJg() {
        return jg;
    }

    public void setJg(String jg) {
        this.jg = jg;
    }

    public List<SendFileEntity> getFjList() {
        return fjList;
    }

    public void setFjList(List<SendFileEntity> fjList) {
        this.fjList = fjList;
    }

    public List<SendFileEntity> getBgList() {
        return bgList;
    }

    public void setBgList(List<SendFileEntity> bgList) {
        this.bgList = bgList;
    }

    public List<SendFileEntity> getSpList() {
        return spList;
    }

    public void setSpList(List<SendFileEntity> spList) {
        this.spList = spList;
    }

    public String getKkMc() {
        return kkMc;
    }

    public void setKkMc(String kkMc) {
        this.kkMc = kkMc;
    }

    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    public String getSj() {
        return sj;
    }

    public void setSj(String sj) {
        this.sj = sj;
    }

    public String getDz() {
        return dz;
    }

    public void setDz(String dz) {
        this.dz = dz;
    }

    public String getShengQhmc() {
        return shengQhmc;
    }

    public void setShengQhmc(String shengQhmc) {
        this.shengQhmc = shengQhmc;
    }

    public String getShiQhmc() {
        return shiQhmc;
    }

    public void setShiQhmc(String shiQhmc) {
        this.shiQhmc = shiQhmc;
    }

    public String getQuQhmc() {
        return quQhmc;
    }

    public void setQuQhmc(String quQhmc) {
        this.quQhmc = quQhmc;
    }

    public String getXzQhmc() {
        return xzQhmc;
    }

    public void setXzQhmc(String xzQhmc) {
        this.xzQhmc = xzQhmc;
    }

    public String getJcryxm() {
        return jcryxm;
    }

    public void setJcryxm(String jcryxm) {
        this.jcryxm = jcryxm;
    }
}
