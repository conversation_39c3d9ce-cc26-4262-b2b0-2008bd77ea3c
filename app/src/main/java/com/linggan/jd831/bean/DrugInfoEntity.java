package com.linggan.jd831.bean;

import java.util.List;

/**
 * 类  名：涉赌人员
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/11/29 15:21
 * 版  权：凌感科技
 */
public class DrugInfoEntity {

    /**
     * bh : 1367753377243136
     * xm : 安卓开发蛇毒人员波
     * yjztbh : 1367753384976384
     * cyzjdm : {"code":"1","name":"居民身份证"}
     * zjhm : 510104200001012028
     * csrq : null
     * bmch : null
     * xbdm : {"code":"9","name":"未说明性别"}
     * mzdm : null
     * jggjdqdm : null
     * sg : null
     * xldm : null
     * xdryjyfsdm : null
     * lydpzlDpsczldm : []
     * hjdz : null
     * hjdzDzmc : 华西街道110
     * hjdzGajg : null
     * sjjzd : null
     * sjjzdDzmc : 华西接到110
     * sjjzdGajg : null
     * lxdh : 18825803692
     * hyzkdm : null
     * sfYfmjz : null
     * jn : []
     * zzmm : null
     * mqzt : null
     * gzdwDwmc : null
     * hjdShenQhdm : 510000
     * hjdShiQhdm : 510100
     * hjdQxQhdm : 510104
     * hjdXzQhdm : 510104017
     * hjdSqQhdm : 510104017001
     * jzdShenQhdm : 510000
     * jzdShiQhdm : 510000
     * jzdQxQhdm : 510100
     * jzdXzQhdm : 510104
     * jzdSqQhdm : 510104017
     * ryyjzt : 1
     */

    private String bh;
    private String xm;
    private String yjztbh;

    private CodeNameEntity cyzjdm;
    private String zjhm;
    private String csrq;
    private String bmch;
    private CodeNameEntity xbdm;
    private CodeNameEntity mzdm;
    private String jggjdqdm;
    private String sg;
    private CodeNameEntity xldm;
    private CodeNameEntity xdryjyfsdm;
    private String hjdz;
    private String hjdzDzmc;
    private String hjdzGajg;
    private String sjjzd;
    private String sjjzdDzmc;
    private String sjjzdGajg;
    private String lxdh;
    private CodeNameEntity hyzkdm;
    private String sfYfmjz;
    private CodeNameEntity zzmm;
    private CodeNameEntity mqzt;
    private String gzdwDwmc;
    private String hjdShenQhdm;
    private String hjdShiQhdm;
    private String hjdQxQhdm;
    private String hjdXzQhdm;
    private String hjdSqQhdm;
    private String jzdShenQhdm;
    private String jzdShiQhdm;
    private String jzdQxQhdm;
    private String jzdXzQhdm;
    private String jzdSqQhdm;
    private String ryyjzt;
    private List<CodeNameEntity> lydpzlDpsczldm;
    private List<CodeNameEntity> jn;

    public String getBh() {
        return bh;
    }

    public void setBh(String bh) {
        this.bh = bh;
    }

    public String getXm() {
        return xm;
    }

    public void setXm(String xm) {
        this.xm = xm;
    }

    public String getYjztbh() {
        return yjztbh;
    }

    public void setYjztbh(String yjztbh) {
        this.yjztbh = yjztbh;
    }

    public CodeNameEntity getCyzjdm() {
        return cyzjdm;
    }

    public void setCyzjdm(CodeNameEntity cyzjdm) {
        this.cyzjdm = cyzjdm;
    }

    public String getZjhm() {
        return zjhm;
    }

    public void setZjhm(String zjhm) {
        this.zjhm = zjhm;
    }

    public String getCsrq() {
        return csrq;
    }

    public void setCsrq(String csrq) {
        this.csrq = csrq;
    }

    public String getBmch() {
        return bmch;
    }

    public void setBmch(String bmch) {
        this.bmch = bmch;
    }

    public CodeNameEntity getXbdm() {
        return xbdm;
    }

    public void setXbdm(CodeNameEntity xbdm) {
        this.xbdm = xbdm;
    }

    public CodeNameEntity getMzdm() {
        return mzdm;
    }

    public void setMzdm(CodeNameEntity mzdm) {
        this.mzdm = mzdm;
    }

    public String getJggjdqdm() {
        return jggjdqdm;
    }

    public void setJggjdqdm(String jggjdqdm) {
        this.jggjdqdm = jggjdqdm;
    }

    public String getSg() {
        return sg;
    }

    public void setSg(String sg) {
        this.sg = sg;
    }

    public CodeNameEntity getXldm() {
        return xldm;
    }

    public void setXldm(CodeNameEntity xldm) {
        this.xldm = xldm;
    }

    public CodeNameEntity getXdryjyfsdm() {
        return xdryjyfsdm;
    }

    public void setXdryjyfsdm(CodeNameEntity xdryjyfsdm) {
        this.xdryjyfsdm = xdryjyfsdm;
    }

    public String getHjdz() {
        return hjdz;
    }

    public void setHjdz(String hjdz) {
        this.hjdz = hjdz;
    }

    public String getHjdzDzmc() {
        return hjdzDzmc;
    }

    public void setHjdzDzmc(String hjdzDzmc) {
        this.hjdzDzmc = hjdzDzmc;
    }

    public String getHjdzGajg() {
        return hjdzGajg;
    }

    public void setHjdzGajg(String hjdzGajg) {
        this.hjdzGajg = hjdzGajg;
    }

    public String getSjjzd() {
        return sjjzd;
    }

    public void setSjjzd(String sjjzd) {
        this.sjjzd = sjjzd;
    }

    public String getSjjzdDzmc() {
        return sjjzdDzmc;
    }

    public void setSjjzdDzmc(String sjjzdDzmc) {
        this.sjjzdDzmc = sjjzdDzmc;
    }

    public String getSjjzdGajg() {
        return sjjzdGajg;
    }

    public void setSjjzdGajg(String sjjzdGajg) {
        this.sjjzdGajg = sjjzdGajg;
    }

    public String getLxdh() {
        return lxdh;
    }

    public void setLxdh(String lxdh) {
        this.lxdh = lxdh;
    }

    public CodeNameEntity getHyzkdm() {
        return hyzkdm;
    }

    public void setHyzkdm(CodeNameEntity hyzkdm) {
        this.hyzkdm = hyzkdm;
    }

    public String getSfYfmjz() {
        return sfYfmjz;
    }

    public void setSfYfmjz(String sfYfmjz) {
        this.sfYfmjz = sfYfmjz;
    }

    public CodeNameEntity getZzmm() {
        return zzmm;
    }

    public void setZzmm(CodeNameEntity zzmm) {
        this.zzmm = zzmm;
    }

    public CodeNameEntity getMqzt() {
        return mqzt;
    }

    public void setMqzt(CodeNameEntity mqzt) {
        this.mqzt = mqzt;
    }

    public String getGzdwDwmc() {
        return gzdwDwmc;
    }

    public void setGzdwDwmc(String gzdwDwmc) {
        this.gzdwDwmc = gzdwDwmc;
    }

    public String getHjdShenQhdm() {
        return hjdShenQhdm;
    }

    public void setHjdShenQhdm(String hjdShenQhdm) {
        this.hjdShenQhdm = hjdShenQhdm;
    }

    public String getHjdShiQhdm() {
        return hjdShiQhdm;
    }

    public void setHjdShiQhdm(String hjdShiQhdm) {
        this.hjdShiQhdm = hjdShiQhdm;
    }

    public String getHjdQxQhdm() {
        return hjdQxQhdm;
    }

    public void setHjdQxQhdm(String hjdQxQhdm) {
        this.hjdQxQhdm = hjdQxQhdm;
    }

    public String getHjdXzQhdm() {
        return hjdXzQhdm;
    }

    public void setHjdXzQhdm(String hjdXzQhdm) {
        this.hjdXzQhdm = hjdXzQhdm;
    }

    public String getHjdSqQhdm() {
        return hjdSqQhdm;
    }

    public void setHjdSqQhdm(String hjdSqQhdm) {
        this.hjdSqQhdm = hjdSqQhdm;
    }

    public String getJzdShenQhdm() {
        return jzdShenQhdm;
    }

    public void setJzdShenQhdm(String jzdShenQhdm) {
        this.jzdShenQhdm = jzdShenQhdm;
    }

    public String getJzdShiQhdm() {
        return jzdShiQhdm;
    }

    public void setJzdShiQhdm(String jzdShiQhdm) {
        this.jzdShiQhdm = jzdShiQhdm;
    }

    public String getJzdQxQhdm() {
        return jzdQxQhdm;
    }

    public void setJzdQxQhdm(String jzdQxQhdm) {
        this.jzdQxQhdm = jzdQxQhdm;
    }

    public String getJzdXzQhdm() {
        return jzdXzQhdm;
    }

    public void setJzdXzQhdm(String jzdXzQhdm) {
        this.jzdXzQhdm = jzdXzQhdm;
    }

    public String getJzdSqQhdm() {
        return jzdSqQhdm;
    }

    public void setJzdSqQhdm(String jzdSqQhdm) {
        this.jzdSqQhdm = jzdSqQhdm;
    }

    public String getRyyjzt() {
        return ryyjzt;
    }

    public void setRyyjzt(String ryyjzt) {
        this.ryyjzt = ryyjzt;
    }

    public List<CodeNameEntity> getLydpzlDpsczldm() {
        return lydpzlDpsczldm;
    }

    public void setLydpzlDpsczldm(List<CodeNameEntity> lydpzlDpsczldm) {
        this.lydpzlDpsczldm = lydpzlDpsczldm;
    }

    public List<CodeNameEntity> getJn() {
        return jn;
    }

    public void setJn(List<CodeNameEntity> jn) {
        this.jn = jn;
    }


}
