package com.linggan.jd831.bean;

import java.util.List;

/**
 * 题目
 */
public class TmsBean {
    private String xh;
    private CodeNameEntity lx;
    private String nr;
    private String das;

    private List<XzsBean> xzs;

    //
    private String editString;
    private boolean isChoice;

    public String getXh() {
        return xh;
    }

    public void setXh(String xh) {
        this.xh = xh;
    }

    public CodeNameEntity getLx() {
        return lx;
    }

    public void setLx(CodeNameEntity lx) {
        this.lx = lx;
    }

    public String getNr() {
        return nr;
    }

    public void setNr(String nr) {
        this.nr = nr;
    }

    public String getDas() {
        return das;
    }

    public void setDas(String das) {
        this.das = das;
    }

    public List<XzsBean> getXzs() {
        return xzs;
    }

    public void setXzs(List<XzsBean> xzs) {
        this.xzs = xzs;
    }

    public String getEditString() {
        return editString;
    }

    public void setEditString(String editString) {
        this.editString = editString;
    }

    public boolean isChoice() {
        return isChoice;
    }

    public void setChoice(boolean choice) {
        isChoice = choice;
    }
}