package com.linggan.jd831.bean;

import com.google.gson.annotations.SerializedName;

/**
 * 身份证信息实体类
 */
public class IdCardInfo {
    @SerializedName("xm")
    private String name; // 姓名
    
    @SerializedName("zjhm")
    private String idCardNumber; // 证件号码
    
    @SerializedName("csrq")
    private String birthDate; // 出生日期
    
    @SerializedName("hjdzDzmc")
    private String householdAddress; // 户籍地址
    
    @SerializedName("bh")
    private String bh; // 编号(String类型)

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdCardNumber() {
        return idCardNumber;
    }

    public void setIdCardNumber(String idCardNumber) {
        this.idCardNumber = idCardNumber;
    }

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    public String getHouseholdAddress() {
        return householdAddress;
    }

    public void setHouseholdAddress(String householdAddress) {
        this.householdAddress = householdAddress;
    }

    public String getBh() {
        return bh;
    }

    public void setBh(String bh) {
        this.bh = bh;
    }
} 