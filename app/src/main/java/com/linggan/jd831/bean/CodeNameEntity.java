package com.linggan.jd831.bean;

import java.io.Serializable;

/**
 * 类  名：code-name通用实体类
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/16  9:37
 * 版  权：凌感科技
 */
public class CodeNameEntity implements Serializable {
    private String code;
    private String name;

    private String sjCode;
    private String sjName;

    //系统通知
    private String value;
    private int num;//备用
    private String sysNoticeValue;

    public CodeNameEntity() {
    }

    public CodeNameEntity(String name, String code) {
        this.code = code;
        this.name = name;
    }

    public CodeNameEntity(String name, String code, String sjCode) {
        this.code = code;
        this.name = name;
        this.sjCode = sjCode;
    }

    public CodeNameEntity(String name, String code, String sjCode, String sjName) {
        this.code = code;
        this.name = name;
        this.sjCode = sjCode;
        this.sjName = sjName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSjCode() {
        return sjCode;
    }

    public void setSjCode(String sjCode) {
        this.sjCode = sjCode;
    }

    public String getSjName() {
        return sjName;
    }

    public void setSjName(String sjName) {
        this.sjName = sjName;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getSysNoticeValue() {
        return sysNoticeValue;
    }

    public void setSysNoticeValue(String sysNoticeValue) {
        this.sysNoticeValue = sysNoticeValue;
    }
}
