//package com.linggan.jd831.bean;
//
//import java.io.Serializable;
//
//public class FydtItem implements Serializable {
//    private static final long serialVersionUID = 1L;
//
//    private String name;         // 姓名
//    private String idCard;       // 身份证
//    private String medicineTime; // 服药时间
//    private String medicineAmount; // 服药量
//
//    public FydtItem() {
//        // 无参构造函数
//    }
//
//    public FydtItem(String name, String idCard, String medicineTime, String medicineAmount) {
//        this.name = name;
//        this.idCard = idCard;
//        this.medicineTime = medicineTime;
//        this.medicineAmount = medicineAmount;
//    }
//
//    public String getName() {
//        return name;
//    }
//
//    public void setName(String name) {
//        this.name = name;
//    }
//
//    public String getIdCard() {
//        return idCard;
//    }
//
//    public void setIdCard(String idCard) {
//        this.idCard = idCard;
//    }
//
//    public String getMedicineTime() {
//        return medicineTime;
//    }
//
//    public void setMedicineTime(String medicineTime) {
//        this.medicineTime = medicineTime;
//    }
//
//    public String getMedicineAmount() {
//        return medicineAmount;
//    }
//
//    public void setMedicineAmount(String medicineAmount) {
//        this.medicineAmount = medicineAmount;
//    }
//}