package com.linggan.jd831.bean;

import java.util.List;

/**
 * 类  名：风险变更详情
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2023/9/7 9:09
 * 版  权：LGKJ
 */
public class FxbgInfoEntity {

    private String fxxgry;//风险修改人员
    private String ssdw;//所属单位
    private String lxdh;//联系电话
    private CodeNameEntity bgqfxdj;//变更前风险等级
    private CodeNameEntity bghfxdj;//变更后风险等级
    private String bz;//  备注
    private List<SendFileEntity> fj;//附件

    public String getFxxgry() {
        return fxxgry;
    }

    public void setFxxgry(String fxxgry) {
        this.fxxgry = fxxgry;
    }

    public String getSsdw() {
        return ssdw;
    }

    public void setSsdw(String ssdw) {
        this.ssdw = ssdw;
    }

    public String getLxdh() {
        return lxdh;
    }

    public void setLxdh(String lxdh) {
        this.lxdh = lxdh;
    }

    public CodeNameEntity getBgqfxdj() {
        return bgqfxdj;
    }

    public void setBgqfxdj(CodeNameEntity bgqfxdj) {
        this.bgqfxdj = bgqfxdj;
    }

    public CodeNameEntity getBghfxdj() {
        return bghfxdj;
    }

    public void setBghfxdj(CodeNameEntity bghfxdj) {
        this.bghfxdj = bghfxdj;
    }

    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    public List<SendFileEntity> getFj() {
        return fj;
    }

    public void setFj(List<SendFileEntity> fj) {
        this.fj = fj;
    }
}
