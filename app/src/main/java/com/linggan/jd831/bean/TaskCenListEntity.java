package com.linggan.jd831.bean;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * 类  名：JD831_V20
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/9/29 19:55
 * 版  权：凌感科技
 */
public class TaskCenListEntity implements Serializable {


    private CodeNameEntity rwRwlx;
    private String xm;
    private int surplusDay;
    private CodeNameEntity ryyjzt;
    private String bh;
    private String xyrbh;
    private String ywbbh;
    private String kssj;
    private String jssj;
    private CodeNameEntity rwRwzt;
    private String rwSpzt;
    private String wcsj;
    private String rwMs;//0正常任务；1抽检任务（只有检测存在抽检）；2安保；3动向确认签到--只展示2 和3
    private String abMbXzqhdm;//安保行政区
    private String fzcRwBh;//安保任务编号
    private String yjztbh;
    private int goingDay;
    private String type;
    private String fzrXm;
    @SerializedName("zjwc")
    private boolean zjwc;

    public boolean getZjwc() {
        return zjwc;
    }

    public void setZjwc(boolean zjwc) {
        this.zjwc = zjwc;
    }

    public CodeNameEntity getRwRwlx() {
        return rwRwlx;
    }

    public void setRwRwlx(CodeNameEntity rwRwlx) {
        this.rwRwlx = rwRwlx;
    }

    public String getXm() {
        return xm;
    }

    public void setXm(String xm) {
        this.xm = xm;
    }

    public int getSurplusDay() {
        return surplusDay;
    }

    public void setSurplusDay(int surplusDay) {
        this.surplusDay = surplusDay;
    }

    public CodeNameEntity getRyyjzt() {
        return ryyjzt;
    }

    public void setRyyjzt(CodeNameEntity ryyjzt) {
        this.ryyjzt = ryyjzt;
    }

    public String getBh() {
        return bh;
    }

    public void setBh(String bh) {
        this.bh = bh;
    }

    public String getXyrbh() {
        return xyrbh;
    }

    public void setXyrbh(String xyrbh) {
        this.xyrbh = xyrbh;
    }

    public String getYwbbh() {
        return ywbbh;
    }

    public void setYwbbh(String ywbbh) {
        this.ywbbh = ywbbh;
    }

    public String getKssj() {
        return kssj;
    }

    public void setKssj(String kssj) {
        this.kssj = kssj;
    }

    public String getJssj() {
        return jssj;
    }

    public void setJssj(String jssj) {
        this.jssj = jssj;
    }

    public CodeNameEntity getRwRwzt() {
        return rwRwzt;
    }

    public void setRwRwzt(CodeNameEntity rwRwzt) {
        this.rwRwzt = rwRwzt;
    }

    public String getRwSpzt() {
        return rwSpzt;
    }

    public void setRwSpzt(String rwSpzt) {
        this.rwSpzt = rwSpzt;
    }

    public String getWcsj() {
        return wcsj;
    }

    public void setWcsj(String wcsj) {
        this.wcsj = wcsj;
    }

    public String getRwMs() {
        return rwMs;
    }

    public void setRwMs(String rwMs) {
        this.rwMs = rwMs;
    }

    public String getAbMbXzqhdm() {
        return abMbXzqhdm;
    }

    public void setAbMbXzqhdm(String abMbXzqhdm) {
        this.abMbXzqhdm = abMbXzqhdm;
    }

    public String getFzcRwBh() {
        return fzcRwBh;
    }

    public void setFzcRwBh(String fzcRwBh) {
        this.fzcRwBh = fzcRwBh;
    }

    public String getYjztbh() {
        return yjztbh;
    }

    public void setYjztbh(String yjztbh) {
        this.yjztbh = yjztbh;
    }

    public int getGoingDay() {
        return goingDay;
    }

    public void setGoingDay(int goingDay) {
        this.goingDay = goingDay;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFzrXm() {
        return fzrXm;
    }

    public void setFzrXm(String fzrXm) {
        this.fzrXm = fzrXm;
    }
}
