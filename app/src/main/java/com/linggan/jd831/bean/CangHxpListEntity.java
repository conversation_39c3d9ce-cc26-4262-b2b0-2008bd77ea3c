package com.linggan.jd831.bean;

import java.io.Serializable;
import java.util.List;

/**
 * 类  名：仓库/化学品列表
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2023/3/15 11:34
 * 版  权：LGKJ
 */
public class CangHxpListEntity implements Serializable {

    private List<CkxxListBean> ckxxList;
    private List<HxpEntity> hxpXxList;


    public List<CkxxListBean> getCkxxList() {
        return ckxxList;
    }

    public void setCkxxList(List<CkxxListBean> ckxxList) {
        this.ckxxList = ckxxList;
    }

    public List<HxpEntity> getHxpXxList() {
        return hxpXxList;
    }

    public void setHxpXxList(List<HxpEntity> hxpXxList) {
        this.hxpXxList = hxpXxList;
    }

    public static class CkxxListBean implements Serializable {
        private String bh;
        private String mc;
        private int sfSxt;
        private int hxpsl;
        private List<HxpAllBean> hxpAllBeanList;

        public String getBh() {
            return bh;
        }

        public void setBh(String bh) {
            this.bh = bh;
        }

        public String getMc() {
            return mc;
        }

        public void setMc(String mc) {
            this.mc = mc;
        }

        public int getSfSxt() {
            return sfSxt;
        }

        public void setSfSxt(int sfSxt) {
            this.sfSxt = sfSxt;
        }

        public int getHxpsl() {
            return hxpsl;
        }

        public void setHxpsl(int hxpsl) {
            this.hxpsl = hxpsl;
        }

        public List<HxpAllBean> getHxpAllBeanList() {
            return hxpAllBeanList;
        }

        public void setHxpAllBeanList(List<HxpAllBean> hxpAllBeanList) {
            this.hxpAllBeanList = hxpAllBeanList;
        }
    }


}
