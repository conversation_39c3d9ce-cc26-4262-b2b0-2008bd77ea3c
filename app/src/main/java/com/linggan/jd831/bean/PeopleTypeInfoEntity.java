package com.linggan.jd831.bean;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 类  名：人员类型
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/9/21 19:10
 * 版  权：凌感科技
 */
public class PeopleTypeInfoEntity {

    private String chrq;//（查获时间）
    private List<CodeNameEntity> gkxxDply;//（毒品来源）
    private String chdwDwmc;//(查获单位名称)
    private CodeNameEntity njjgdm;//  （尿检结果）
    private String qhnxxdz;//（区域内详细地址）
    private String wfssJyaq;//(违法事实)
    private String xdrychlxdm;//（查获类型）
    private String xdrychlydm;//（查获来源）
    private String bz;// （备注）
    private String tjrxm;//（提交人姓名）
    private String zxsj;//  （执行时间）
    private String ejgkdQhdm;//  （二级管控地区划代码）
    private String jdcs;//（戒毒场所）
    private String yyxq;//（原因详情）
    private String xdryfxzldm;//（服刑方式）
    private String pxdd;//（判刑地点）
    private String ryrq;//（入狱时间）
    private String fuxdwDwmc;//（服刑单位名称）
    private String ryyy;//（入狱原因）
    private String jdjg;//（决定机关）
    private String rsrq;//（入所时间）
    private String zhxdwDwmc;//（执行单位名称）
    private String qjyy;//（强戒原因），
    @SerializedName("jdsmc")
    private String jdsmc;// 戒毒所名称

    @SerializedName("jdzljgmc")
    private String jdzljgmc;

    @SerializedName("jdzljgdz")
    private String jdzljgdz;

    @SerializedName("zyjdcs")
    private String zyjdcs;

    public String getJdzljgmc() {
        return jdzljgmc;
    }

    public void setJdzljgmc(String jdzljgmc) {
        this.jdzljgmc = jdzljgmc;
    }

    public String getJdzljgdz() {
        return jdzljgdz;
    }

    public void setJdzljgdz(String jdzljgdz) {
        this.jdzljgdz = jdzljgdz;
    }

    public String getZyjdcs() {
        return zyjdcs;
    }

    public void setZyjdcs(String zyjdcs) {
        this.zyjdcs = zyjdcs;
    }

    public String getZxdmc() {
        return zxdmc;
    }

    public void setZxdmc(String zxdmc) {
        this.zxdmc = zxdmc;
    }

    @SerializedName("zxdmc")
    private String zxdmc;// 戒毒所名称
    public String getJdsmc() {
        return jdsmc;
    }

    public void setJdsmc(String jdsmc) {
        this.jdsmc = jdsmc;
    }

    @SerializedName("csrq")
    private String csrq;// 出所时间


    public String getCsrq() {
        return csrq;
    }

    public void setCsrq(String csrq) {
        this.csrq = csrq;
    }


    /**
     * fzrbh : null
     * zhxdwDwdz : null
     * jdsj : 2022-09-21
     * ywwczlmz : null
     * sfZyjd : null
     * bdrq : 2022-09-21
     * gkxxGrcrjb : null
     * qjcsrq : null
     * sqkfqGlcs : null
     * sfJrkfcs : 0
     * xys : []
     * zrs : []
     * wsfj : []
     * jssj : null
     * qrrxm : null
     * fzrxm : null
     * flwsbh : null
     * ryyjzt : null
     */

    private String fzrbh;
    private String zhxdwDwdz;
    private String jdsj;
    private String ywwczlmz;
    private String sfZyjd;
    private String bdrq;
    private String gkxxGrcrjb;
    private String qjcsrq;
    private String sqkfqGlcs;
    private String sfJrkfcs;
    private String jssj;
    private String qrrxm;
    private String fzrxm;
    private String flwsbh;
    private String ryyjzt;
    private List<SendFileEntity> xys;
    private List<SendFileEntity> zrs;

    public CodeNameEntity getSalx() {
        return salx;
    }

    public void setSalx(CodeNameEntity salx) {
        this.salx = salx;
    }

    private CodeNameEntity salx;
    public String getXq() {
        return xq;
    }

    public void setXq(String xq) {
        this.xq = xq;
    }

    private String xq;// 刑期

    public List<SendFileEntity> getJds() {
        return jds;
    }

    public void setJds(List<SendFileEntity> jds) {
        this.jds = jds;
    }

    private List<SendFileEntity> jds;
//    private List<SendFileEntity> wsfj;

    public String getChrq() {
        return chrq;
    }

    public void setChrq(String chrq) {
        this.chrq = chrq;
    }

    public List<CodeNameEntity> getGkxxDply() {
        return gkxxDply;
    }

    public void setGkxxDply(List<CodeNameEntity> gkxxDply) {
        this.gkxxDply = gkxxDply;
    }

    public String getChdwDwmc() {
        return chdwDwmc;
    }

    public void setChdwDwmc(String chdwDwmc) {
        this.chdwDwmc = chdwDwmc;
    }

    public CodeNameEntity getNjjgdm() {
        return njjgdm;
    }

    public void setNjjgdm(CodeNameEntity njjgdm) {
        this.njjgdm = njjgdm;
    }

    public String getQhnxxdz() {
        return qhnxxdz;
    }

    public void setQhnxxdz(String qhnxxdz) {
        this.qhnxxdz = qhnxxdz;
    }

    public String getWfssJyaq() {
        return wfssJyaq;
    }

    public void setWfssJyaq(String wfssJyaq) {
        this.wfssJyaq = wfssJyaq;
    }

    public String getXdrychlxdm() {
        return xdrychlxdm;
    }

    public void setXdrychlxdm(String xdrychlxdm) {
        this.xdrychlxdm = xdrychlxdm;
    }

    public String getXdrychlydm() {
        return xdrychlydm;
    }

    public void setXdrychlydm(String xdrychlydm) {
        this.xdrychlydm = xdrychlydm;
    }

    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    public String getTjrxm() {
        return tjrxm;
    }

    public void setTjrxm(String tjrxm) {
        this.tjrxm = tjrxm;
    }

    public String getZxsj() {
        return zxsj;
    }

    public void setZxsj(String zxsj) {
        this.zxsj = zxsj;
    }

    public String getEjgkdQhdm() {
        return ejgkdQhdm;
    }

    public void setEjgkdQhdm(String ejgkdQhdm) {
        this.ejgkdQhdm = ejgkdQhdm;
    }

    public String getJdcs() {
        return jdcs;
    }

    public void setJdcs(String jdcs) {
        this.jdcs = jdcs;
    }

    public String getYyxq() {
        return yyxq;
    }

    public void setYyxq(String yyxq) {
        this.yyxq = yyxq;
    }

    public String getXdryfxzldm() {
        return xdryfxzldm;
    }

    public void setXdryfxzldm(String xdryfxzldm) {
        this.xdryfxzldm = xdryfxzldm;
    }

    public String getPxdd() {
        return pxdd;
    }

    public void setPxdd(String pxdd) {
        this.pxdd = pxdd;
    }

    public String getRyrq() {
        return ryrq;
    }

    public void setRyrq(String ryrq) {
        this.ryrq = ryrq;
    }

    public String getFuxdwDwmc() {
        return fuxdwDwmc;
    }

    public void setFuxdwDwmc(String fuxdwDwmc) {
        this.fuxdwDwmc = fuxdwDwmc;
    }

    public String getRyyy() {
        return ryyy;
    }

    public void setRyyy(String ryyy) {
        this.ryyy = ryyy;
    }

    public String getJdjg() {
        return jdjg;
    }

    public void setJdjg(String jdjg) {
        this.jdjg = jdjg;
    }

    public String getRsrq() {
        return rsrq;
    }

    public void setRsrq(String rsrq) {
        this.rsrq = rsrq;
    }

    public String getZhxdwDwmc() {
        return zhxdwDwmc;
    }

    public void setZhxdwDwmc(String zhxdwDwmc) {
        this.zhxdwDwmc = zhxdwDwmc;
    }

    public String getQjyy() {
        return qjyy;
    }

    public void setQjyy(String qjyy) {
        this.qjyy = qjyy;
    }

    public String getFzrbh() {
        return fzrbh;
    }

    public void setFzrbh(String fzrbh) {
        this.fzrbh = fzrbh;
    }

    public String getZhxdwDwdz() {
        return zhxdwDwdz;
    }

    public void setZhxdwDwdz(String zhxdwDwdz) {
        this.zhxdwDwdz = zhxdwDwdz;
    }

    public String getJdsj() {
        return jdsj;
    }

    public void setJdsj(String jdsj) {
        this.jdsj = jdsj;
    }

    public String getYwwczlmz() {
        return ywwczlmz;
    }

    public void setYwwczlmz(String ywwczlmz) {
        this.ywwczlmz = ywwczlmz;
    }

    public String getSfZyjd() {
        return sfZyjd;
    }

    public void setSfZyjd(String sfZyjd) {
        this.sfZyjd = sfZyjd;
    }

    public String getBdrq() {
        return bdrq;
    }

    public void setBdrq(String bdrq) {
        this.bdrq = bdrq;
    }

    public String getGkxxGrcrjb() {
        return gkxxGrcrjb;
    }

    public void setGkxxGrcrjb(String gkxxGrcrjb) {
        this.gkxxGrcrjb = gkxxGrcrjb;
    }

    public String getQjcsrq() {
        return qjcsrq;
    }

    public void setQjcsrq(String qjcsrq) {
        this.qjcsrq = qjcsrq;
    }

    public String getSqkfqGlcs() {
        return sqkfqGlcs;
    }

    public void setSqkfqGlcs(String sqkfqGlcs) {
        this.sqkfqGlcs = sqkfqGlcs;
    }

    public String getSfJrkfcs() {
        return sfJrkfcs;
    }

    public void setSfJrkfcs(String sfJrkfcs) {
        this.sfJrkfcs = sfJrkfcs;
    }

    public String getJssj() {
        return jssj;
    }

    public void setJssj(String jssj) {
        this.jssj = jssj;
    }

    public String getQrrxm() {
        return qrrxm;
    }

    public void setQrrxm(String qrrxm) {
        this.qrrxm = qrrxm;
    }

    public String getFzrxm() {
        return fzrxm;
    }

    public void setFzrxm(String fzrxm) {
        this.fzrxm = fzrxm;
    }

    public String getFlwsbh() {
        return flwsbh;
    }

    public void setFlwsbh(String flwsbh) {
        this.flwsbh = flwsbh;
    }

    public String getRyyjzt() {
        return ryyjzt;
    }

    public void setRyyjzt(String ryyjzt) {
        this.ryyjzt = ryyjzt;
    }

    public List<SendFileEntity> getXys() {
        return xys;
    }

    public void setXys(List<SendFileEntity> xys) {
        this.xys = xys;
    }

    public List<SendFileEntity> getZrs() {
        return zrs;
    }

    public void setZrs(List<SendFileEntity> zrs) {
        this.zrs = zrs;
    }

//    public List<SendFileEntity> getWsfj() {
//        return wsfj;
//    }
//
//    public void setWsfj(List<SendFileEntity> wsfj) {
//        this.wsfj = wsfj;
//    }
}
