package com.linggan.jd831.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 类  名：化学品数据总类
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2023/3/15 15:54
 * 版  权：LGKJ
 */
public class HxpAllBean implements Serializable {

    private String ckBh;//仓库编号
    private String hxpBh;//化学品编号
    private String hxpMc;//化学品编号
    private List<HxpGgBean> ggBeanList = new ArrayList<>();//规格列表

    public String getCkBh() {
        return ckBh;
    }

    public void setCkBh(String ckBh) {
        this.ckBh = ckBh;
    }

    public String getHxpMc() {
        return hxpMc;
    }

    public void setHxpMc(String hxpMc) {
        this.hxpMc = hxpMc;
    }

    public String getHxpBh() {
        return hxpBh;
    }

    public void setHxpBh(String hxpBh) {
        this.hxpBh = hxpBh;
    }

    public List<HxpGgBean> getGgBeanList() {
        return ggBeanList;
    }

    public void setGgBeanList(List<HxpGgBean> ggBeanList) {
        this.ggBeanList = ggBeanList;
    }
}
