package com.linggan.jd831.bean;

import java.util.List;

/**
 * 类  名：聊天消息实体类
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/5/26 15:43
 * 版  权：LGKJ
 */
public class GptMegEntity {


    /**
     * content : 你到家超级超级参加
     * contentType : 0
     * date : 2023-09-22 16:47:20
     * jdWd : null
     * sendImg :
     * receiveImg :
     * sender : 1379341972979712
     * receiver : 1376958849187840
     * senderName : 波合阳社工
     * receiverName : 朱睿霖二
     */

    private String content;
    private String contentType;
    private String date;
    private String jdWd;
    private String sendImg;
    private String receiveImg;
    private String sender;
    private String receiver;
    private String senderName;
    private String receiverName;
    private List<List<String>> fj_title_links;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getJdWd() {
        return jdWd;
    }

    public void setJdWd(String jdWd) {
        this.jdWd = jdWd;
    }

    public String getSendImg() {
        return sendImg;
    }

    public void setSendImg(String sendImg) {
        this.sendImg = sendImg;
    }

    public String getReceiveImg() {
        return receiveImg;
    }

    public void setReceiveImg(String receiveImg) {
        this.receiveImg = receiveImg;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }


    public List<List<String>> getFj_title_links() {
        return fj_title_links;
    }

    public void setFj_title_links(List<List<String>> fj_title_links) {
        this.fj_title_links = fj_title_links;
    }
}
