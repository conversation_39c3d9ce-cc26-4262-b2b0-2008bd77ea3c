package com.linggan.jd831.bean;

import java.io.Serializable;
import java.util.List;

/**
 * 类  名：平安关爱推荐列表
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/12/9 14:01
 * 版  权：凌感科技
 */
public class CareTuiListEntity implements Serializable {

    private String xyrbh;//毒人员编号
    private String xm;//吸毒人员姓名
    private String cyzjdm;//证件种类
    private String zjhm;//证件号码（加密）
    private CodeNameEntity ryyjzt;//一级状态类型（字典表ryyjzt）
    private CodeNameEntity bfLx;//帮扶类型（字典表bf_lx）
    private String tjbaga;//推荐平安关爱
    private String xp;//
    //详情字段
    private List<BagaTjsBean> pagaTjs;
    private String ssdq;

    public String getXyrbh() {
        return xyrbh;
    }

    public void setXyrbh(String xyrbh) {
        this.xyrbh = xyrbh;
    }

    public String getXm() {
        return xm;
    }

    public void setXm(String xm) {
        this.xm = xm;
    }

    public String getCyzjdm() {
        return cyzjdm;
    }

    public void setCyzjdm(String cyzjdm) {
        this.cyzjdm = cyzjdm;
    }

    public String getZjhm() {
        return zjhm;
    }

    public void setZjhm(String zjhm) {
        this.zjhm = zjhm;
    }

    public CodeNameEntity getRyyjzt() {
        return ryyjzt;
    }

    public void setRyyjzt(CodeNameEntity ryyjzt) {
        this.ryyjzt = ryyjzt;
    }

    public CodeNameEntity getBfLx() {
        return bfLx;
    }

    public void setBfLx(CodeNameEntity bfLx) {
        this.bfLx = bfLx;
    }

    public String getTjbaga() {
        return tjbaga;
    }

    public void setTjbaga(String tjbaga) {
        this.tjbaga = tjbaga;
    }

    public String getXp() {
        return xp;
    }

    public void setXp(String xp) {
        this.xp = xp;
    }

    public void setPagaTjs(List<BagaTjsBean> pagaTjs) {
        this.pagaTjs = pagaTjs;
    }

    public List<BagaTjsBean> getPagaTjs() {
        return pagaTjs;
    }

    public String getSsdq() {
        return ssdq;
    }

    public void setSsdq(String ssdq) {
        this.ssdq = ssdq;
    }

    public static class BagaTjsBean implements Serializable {
        private String bh;
        private String num;
        private String cjsj;
        private String tjyy;
        private ZiDianEntity bfCx;

        public String getBh() {
            return bh;
        }

        public void setBh(String bh) {
            this.bh = bh;
        }

        public String getNum() {
            return num;
        }

        public void setNum(String num) {
            this.num = num;
        }

        public String getCjsj() {
            return cjsj;
        }

        public void setCjsj(String cjsj) {
            this.cjsj = cjsj;
        }

        public String getTjyy() {
            return tjyy;
        }

        public void setTjyy(String tjyy) {
            this.tjyy = tjyy;
        }

        public ZiDianEntity getBfCx() {
            return bfCx;
        }

        public void setBfCx(ZiDianEntity bfCx) {
            this.bfCx = bfCx;
        }
    }
}
