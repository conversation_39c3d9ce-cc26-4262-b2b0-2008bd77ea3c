package com.linggan.jd831.bean;


import com.google.gson.annotations.SerializedName;

/**
 * 排场场所记录实体类
 */
public class PlaceCodeListEntity {
    /**
     * ID
     */
    private String id;

    /**
     * 涉毒场所iD
     */
    private String sdcsId;

    /**
     * 排查人ID
     */
    private String pcrId;

    /**
     * 排查人姓名
     */
    private String pcrXm;

    /**
     * 排查时间
     */
    private String pcsj;

    /**
     * 排查结果结果代码
     */
    private String pcjgJgdm;

    /**
     * 排查结果名称
     */
    private String pcjgJgmc;

    /**
     * 图片
     */
    private String tp;

    /**
     * 是否联合排查 是联合排查时需要显示为"排查确认" 且需要指定确认民警    0 否  1是
     */
    private String sflhpc;

    /**
     * 是否需要复核  0 否  1是    如果是 是且sflhpc为0时显示为"复查信息"
     */
    private String sfxyfh;

    /**
     * 是否排查任务
     */
    private String sfpcrw;

    /**
     * 排查任务ID
     */
    private String pcrwId;


    /**
     * 联合排查/ 复核民警ID
     */
    private String mjId;

    /**
     * 联合排查/ 复核民警姓名
     */
    private String mjXm;

    /**
     * 联合排查民警是否确认
     */
    private String mjSfqr;

    /**
     * 联合排查民警确认是否属实
     */
    private String mjQrSfss;

    /**
     * 联合排查 / 复核民警确认结果代码
     */
    private String mjQrJgdm;

    /**
     * 联合排查/ 复核民警确认结果名称
     */
    private String mjQrJgmc;

    /**
     * 联合排查/ 复核民警确认备注
     */
    private String mjQrBz;

    @SerializedName(value = "sflx", alternate = {"SFLX"})
    private String sflx;

    public String getSflx() {
        return sflx;
    }

    public void setSflx(String sflx) {
        this.sflx = sflx;
    }


    /**
     * 民警确认/ 复核时间
     */
    private String mjQrQrsj;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSdcsId() {
        return sdcsId;
    }

    public void setSdcsId(String sdcsId) {
        this.sdcsId = sdcsId;
    }

    public String getPcrId() {
        return pcrId;
    }

    public void setPcrId(String pcrId) {
        this.pcrId = pcrId;
    }

    public String getPcrXm() {
        return pcrXm;
    }

    public void setPcrXm(String pcrXm) {
        this.pcrXm = pcrXm;
    }

    public String getPcsj() {
        return pcsj;
    }

    public void setPcsj(String pcsj) {
        this.pcsj = pcsj;
    }

    public String getPcjgJgdm() {
        return pcjgJgdm;
    }

    public void setPcjgJgdm(String pcjgJgdm) {
        this.pcjgJgdm = pcjgJgdm;
    }

    public String getPcjgJgmc() {
        return pcjgJgmc;
    }

    public void setPcjgJgmc(String pcjgJgmc) {
        this.pcjgJgmc = pcjgJgmc;
    }

    public String getTp() {
        return tp;
    }

    public void setTp(String tp) {
        this.tp = tp;
    }

    public String getSflhpc() {
        return sflhpc;
    }

    public void setSflhpc(String sflhpc) {
        this.sflhpc = sflhpc;
    }

    public String getSfxyfh() {
        return sfxyfh;
    }

    public void setSfxyfh(String sfxyfh) {
        this.sfxyfh = sfxyfh;
    }

    public String getSfpcrw() {
        return sfpcrw;
    }

    public void setSfpcrw(String sfpcrw) {
        this.sfpcrw = sfpcrw;
    }

    public String getPcrwId() {
        return pcrwId;
    }

    public void setPcrwId(String pcrwId) {
        this.pcrwId = pcrwId;
    }

    public String getMjId() {
        return mjId;
    }

    public void setMjId(String mjId) {
        this.mjId = mjId;
    }

    public String getMjXm() {
        return mjXm;
    }

    public void setMjXm(String mjXm) {
        this.mjXm = mjXm;
    }

    public String getMjSfqr() {
        return mjSfqr;
    }

    public void setMjSfqr(String mjSfqr) {
        this.mjSfqr = mjSfqr;
    }

    public String getMjQrSfss() {
        return mjQrSfss;
    }

    public void setMjQrSfss(String mjQrSfss) {
        this.mjQrSfss = mjQrSfss;
    }

    public String getMjQrJgdm() {
        return mjQrJgdm;
    }

    public void setMjQrJgdm(String mjQrJgdm) {
        this.mjQrJgdm = mjQrJgdm;
    }

    public String getMjQrJgmc() {
        return mjQrJgmc;
    }

    public void setMjQrJgmc(String mjQrJgmc) {
        this.mjQrJgmc = mjQrJgmc;
    }

    public String getMjQrBz() {
        return mjQrBz;
    }

    public void setMjQrBz(String mjQrBz) {
        this.mjQrBz = mjQrBz;
    }

    public String getMjQrQrsj() {
        return mjQrQrsj;
    }

    public void setMjQrQrsj(String mjQrQrsj) {
        this.mjQrQrsj = mjQrQrsj;
    }
}
