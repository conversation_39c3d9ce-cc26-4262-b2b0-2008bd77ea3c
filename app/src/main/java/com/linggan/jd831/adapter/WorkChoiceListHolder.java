package com.linggan.jd831.adapter;

import android.app.Activity;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.WorksEntity;
import com.linggan.jd831.utils.StrUtils;

import org.greenrobot.eventbus.EventBus;

/**
 * 类  名：单选--数据回显
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class WorkChoiceListHolder extends IViewHolder {

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_nul_choice_list;
    }

    public class ViewHolder extends XViewHolder<WorksEntity> {
        ImageView mIvChoice;
        TextView mTvTitle;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View rootView) {
            mIvChoice = itemView.findViewById(R.id.iv_choice);
            mTvTitle = itemView.findViewById(R.id.tv_title);
            mIvChoice.setVisibility(View.GONE);
        }

        @Override
        protected void onBindData(WorksEntity itemData) {

            mTvTitle.setText(StrUtils.getDev(itemData.getYhXm(), itemData.getZrrxm()));
            itemView.setOnClickListener(v -> {
                //选中
                EventBus.getDefault().post(itemData);
                ((Activity) mContext).finish();
            });
        }
    }
}


