package com.linggan.jd831.adapter;

import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.AppUserListEntity;
import com.linggan.jd831.ui.user.PeopleInfoActivity;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XImageUtils;
import com.makeramen.roundedimageview.RoundedImageView;

/**
 * 类  名：信息完整度--考核率
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class KhXxwzlYwcListHolder extends IViewHolder {

    private String type;

    public KhXxwzlYwcListHolder(String type) {
        this.type = type;
    }

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_kh_jf_wwc;
    }


    public class ViewHolder extends XViewHolder<AppUserListEntity> {

        private RoundedImageView mIvHead;
        private TextView mTvName;
        private TextView mTvInfo;
        private TextView mTvKssj;
        private TextView mTvJssj;
        private TextView mTvGlry;
        private View mView;
        private LinearLayout mLinBo;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mIvHead = view.findViewById(R.id.iv_head);
            mTvName = view.findViewById(R.id.tv_name);
            mTvInfo = view.findViewById(R.id.tv_info);
            mTvGlry = view.findViewById(R.id.tv_glry);
            mTvKssj = view.findViewById(R.id.tv_kssj);
            mTvJssj = view.findViewById(R.id.tv_jssj);
            mView = view.findViewById(R.id.view);
            mLinBo = view.findViewById(R.id.lin_bo);
            mView.setVisibility(View.VISIBLE);
            mLinBo.setVisibility(View.GONE);
        }

        @Override
        protected void onBindData(final AppUserListEntity itemData) {
            mTvName.setText(itemData.getXm());
            XImageUtils.load(mContext, itemData.getXp(), mIvHead, R.mipmap.ic_def_head);
            mTvInfo.setText(itemData.getSex() + "\u3000" + itemData.getRylx());

            mTvKssj.setText("管理人员：" + StrUtils.getDev(itemData.getFzrxm(), "无"));
            mTvJssj.setText("信息完整度：" + itemData.getXxwzdStr());
            itemView.setOnClickListener(v -> {
                XIntentUtil.redirectToNextActivity(mContext, PeopleInfoActivity.class, "id", itemData.getXyrbh() + "");
            });
        }
    }
}


