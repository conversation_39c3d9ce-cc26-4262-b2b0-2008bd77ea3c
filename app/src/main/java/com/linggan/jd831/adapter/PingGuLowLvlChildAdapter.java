package com.linggan.jd831.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.PgLowLvlEntity;

import java.util.List;

/**
 * 季度评估-低风险验证-答题-选项
 */
public class PingGuLowLvlChildAdapter extends RecyclerView.Adapter<PingGuLowLvlChildAdapter.Holder> {

    private List<PgLowLvlEntity.SubItemListBean> list;
    private Context context;

    public PingGuLowLvlChildAdapter(Context context, List<PgLowLvlEntity.SubItemListBean> list) {
        this.context = context;
        this.list = list;
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_pinggu_low_lvl_child, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        PgLowLvlEntity.SubItemListBean subItemListBean = list.get(position);
        holder.mTitle.setText(subItemListBean.getMc());

        //是否通过（1未通过；2通过）
        holder.mTvNo.setOnClickListener(v -> {
            subItemListBean.setChoice(true);
            subItemListBean.setChoiceSFtg("1");
            holder.mLin.setBackgroundResource(R.drawable.bg_red_gen);
            holder.mTvNo.setTextColor(Color.parseColor("#DD392F"));
            holder.mTvYes.setTextColor(Color.parseColor("#8C8C8C"));
        });
        holder.mTvYes.setOnClickListener(v -> {
            subItemListBean.setChoice(true);
            subItemListBean.setSfTg(2);
            subItemListBean.setChoiceSFtg("2");
            holder.mLin.setBackgroundResource(R.drawable.bg_green_gen);
            holder.mTvYes.setTextColor(Color.parseColor("#1CBA49"));
            holder.mTvNo.setTextColor(Color.parseColor("#8C8C8C"));
        });
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    public static class Holder extends RecyclerView.ViewHolder {
        TextView mTitle;
        TextView mTvNo;
        TextView mTvYes;
        LinearLayout mLin;

        Holder(@NonNull View itemView) {
            super(itemView);
            mTvNo = itemView.findViewById(R.id.tv_no);
            mTvYes = itemView.findViewById(R.id.tv_yes);
            mLin = itemView.findViewById(R.id.lin);
            mTitle = itemView.findViewById(R.id.tv_title);
        }
    }

}
