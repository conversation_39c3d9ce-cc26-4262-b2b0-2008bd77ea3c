package com.linggan.jd831.adapter;

import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.CangLsjlListEntity;
import com.linggan.jd831.bean.LoginEntity;
import com.linggan.jd831.ui.works.qiye.CangKuInfoActivity;
import com.linggan.jd831.ui.works.qiye.OutRuKuSureActivity;
import com.linggan.jd831.utils.UserInfoUtils;

/**
 * 管理员页面--适配器
 */
public class CangLsCodeListHolder extends IViewHolder {

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_mag_index_list;
    }

    public class ViewHolder extends XViewHolder<CangLsjlListEntity> {
        TextView mTvTitle;
        TextView mTvTime;
        TextView mTvZt;
        RecyclerView mRecycle;
        LinearLayout linZt;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View itemView) {
            this.mTvTitle = itemView.findViewById(R.id.tv_title);
            this.mTvTime = itemView.findViewById(R.id.tv_time);
            this.mTvZt = itemView.findViewById(R.id.tv_zt);
            this.mRecycle = itemView.findViewById(R.id.mRecycle);
            this.linZt = itemView.findViewById(R.id.lin_zt);
            mRecycle.setLayoutManager(new LinearLayoutManager(mContext));
        }

        @Override
        protected void onBindData(final CangLsjlListEntity itemData) {
            //0已确认；1已驳回；2未确认
            if (itemData.getZtEnum() != null) {
                mTvZt.setText(itemData.getZtEnum().getName());
                if (!TextUtils.isEmpty(itemData.getZtEnum().getValue())) {
                    switch (itemData.getZtEnum().getValue()) {
                        case "0":
                            linZt.setBackgroundResource(R.drawable.bg_green_gen);
                            mTvZt.setTextColor(Color.parseColor("#1CBA49"));
                            itemView.setOnClickListener(v -> {
                                Bundle bundle = new Bundle();
                                bundle.putString("bh", itemData.getBh());
                                if (itemData.getFsEnum() != null) {
                                    bundle.putString("type", itemData.getFsEnum().getValue());
                                }
                                XIntentUtil.redirectToNextActivity(mContext, CangKuInfoActivity.class, bundle);
                            });
                            break;
                        case "1":
                            linZt.setBackgroundResource(R.drawable.bg_red_gen);
                            mTvZt.setTextColor(Color.parseColor("#DD392F"));
                            itemView.setOnClickListener(v -> {
                                Bundle bundle = new Bundle();
                                bundle.putString("bh", itemData.getBh());
                                if (itemData.getFsEnum() != null) {
                                    bundle.putString("type", itemData.getFsEnum().getValue());
                                }
                                XIntentUtil.redirectToNextActivity(mContext, CangKuInfoActivity.class, bundle);
                            });
                            break;
                        case "2":
                            linZt.setBackgroundResource(R.drawable.bg_blue_gen);
                            mTvZt.setTextColor(Color.parseColor("#0053E2"));
                            itemView.setOnClickListener(v -> {
                                Bundle bundle = new Bundle();
                                bundle.putString("bh", itemData.getBh());
                                if (itemData.getFsEnum() != null) {
                                    bundle.putString("type", itemData.getFsEnum().getValue());
                                }

                                LoginEntity loginEntity = UserInfoUtils.getUserInfo();
                                if (loginEntity != null) {
                                    if (loginEntity.getRysflxdm().equals("yzdqyxxy")) {
                                        //易制毒企业信息员-信息员：登记确认、报损确认、注销申请
                                        XIntentUtil.redirectToNextActivity(mContext, OutRuKuSureActivity.class, bundle);
                                    } else if (loginEntity.getRysflxdm().equals("yzdqycgy")) {
                                        //易制毒企业仓管员-仓管员：出库登记、入库登记、报损申请
                                        XIntentUtil.redirectToNextActivity(mContext, CangKuInfoActivity.class, bundle);
                                    }
                                }
                            });
                            break;
                    }
                }
            }
            mTvTitle.setText(itemData.getCkMc());
            mTvTime.setText(itemData.getOperateTime());
            CangKuLsjlListAdapter cangKuLsjlListAdapter = new CangKuLsjlListAdapter(mContext, itemData.getNumList());
            mRecycle.setAdapter(cangKuLsjlListAdapter);
            cangKuLsjlListAdapter.setOnItemClickListener((data, position) -> {
                if (!TextUtils.isEmpty(itemData.getZtEnum().getValue())) {
                    Bundle bundle = new Bundle();
                    bundle.putString("bh", itemData.getBh());
                    if (itemData.getFsEnum() != null) {
                        bundle.putString("type", itemData.getFsEnum().getValue());
                    }
                    switch (itemData.getZtEnum().getValue()) {
                        case "0":
                        case "1":
                            XIntentUtil.redirectToNextActivity(mContext, CangKuInfoActivity.class, bundle);
                            break;
                        case "2":
                            LoginEntity loginEntity = UserInfoUtils.getUserInfo();
                            if (loginEntity != null) {
                                if (loginEntity.getRysflxdm().equals("yzdqyxxy")) {
                                    //易制毒企业信息员-信息员：登记确认、报损确认、注销申请
                                    XIntentUtil.redirectToNextActivity(mContext, OutRuKuSureActivity.class, bundle);
                                } else if (loginEntity.getRysflxdm().equals("yzdqycgy")) {
                                    //易制毒企业仓管员-仓管员：出库登记、入库登记、报损申请
                                    XIntentUtil.redirectToNextActivity(mContext, CangKuInfoActivity.class, bundle);
                                }
                            }
                            break;
                    }
                }
            });
        }
    }
}
