package com.linggan.jd831.adapter;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.TmsBean;

import java.util.List;

/**
 * 问卷答题--数据回显
 */
public class WjDtXmHxAdapter extends RecyclerView.Adapter<WjDtXmHxAdapter.Holder> {
    private List<TmsBean> list;
    private Context context;

    public WjDtXmHxAdapter(Context context, List<TmsBean> list) {
        this.list = list;
        this.context = context;
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_wjdt_xm_list, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        TmsBean item = list.get(position);
        String titles = item.getXh() + "、" + item.getNr();
        holder.tvTitle.setText(titles);
        if (item.getLx() != null) {
            if (TextUtils.equals("1", item.getLx().getCode())) {//单选
                holder.tvTag.setVisibility(View.VISIBLE);
                holder.tvTag.setText(getQuesType(item.getLx().getCode()));
                holder.tvTag.setBackgroundResource(R.mipmap.ic_dxt);
                holder.tvTag.setTextColor(Color.parseColor("#08CC7A"));
                holder.mRecycleChoice.setVisibility(View.VISIBLE);
                holder.mEdit.setVisibility(View.GONE);
            } else if (TextUtils.equals("2", item.getLx().getCode())) {//多选
                holder.tvTag.setVisibility(View.VISIBLE);
                holder.tvTag.setText(getQuesType(item.getLx().getCode()));
                holder.tvTag.setBackgroundResource(R.mipmap.ic_ddxt);
                holder.tvTag.setTextColor(Color.parseColor("#FF8B30"));
                holder.mRecycleChoice.setVisibility(View.VISIBLE);
                holder.mEdit.setVisibility(View.GONE);
            } else if (TextUtils.equals("3", item.getLx().getCode())) {   //是填空
                holder.tvTag.setVisibility(View.GONE);
                holder.mRecycleChoice.setVisibility(View.GONE);
                holder.mEdit.setVisibility(View.VISIBLE);
                holder.mEdit.setText(item.getDas());
                Log.i("ttt", "onBindViewHolder: " + item.getDas());
            } else {
                holder.tvTag.setVisibility(View.GONE);
                holder.mEdit.setVisibility(View.GONE);
            }
        } else {
            holder.tvTag.setVisibility(View.GONE);
            holder.mEdit.setVisibility(View.GONE);
        }
        WJDtXmHuiXianAdapter huiXianAdapter = new WJDtXmHuiXianAdapter(item.getXzs(), item.getLx() != null ? (item.getLx().getCode()) : "", item.getDas());
        holder.mRecycleChoice.setAdapter(huiXianAdapter);
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    class Holder extends RecyclerView.ViewHolder {
        protected TextView tvTitle, tvTag;
        protected RecyclerView mRecycleChoice;
        protected EditText mEdit;

        Holder(@NonNull View itemView) {
            super(itemView);
            tvTitle = (TextView) itemView.findViewById(R.id.tv_title);
            tvTag = (TextView) itemView.findViewById(R.id.tv_tag);
            mRecycleChoice = (RecyclerView) itemView.findViewById(R.id.mRecycle_choice);
            mRecycleChoice.setLayoutManager(new LinearLayoutManager(context));
            mEdit = (EditText) itemView.findViewById(R.id.mEdit);
            mEdit.setEnabled(false);
        }
    }

    private String getQuesType(String type) {
        String name = "";
        //0 否  1 是
        switch (type) {
            case "1":
                name = "单选";
                break;
            case "2":
                name = "多选";
                break;
            default:
                name = "";
                break;
        }
        return name;
    }

    public List<TmsBean> getList() {
        return list;
    }
}
