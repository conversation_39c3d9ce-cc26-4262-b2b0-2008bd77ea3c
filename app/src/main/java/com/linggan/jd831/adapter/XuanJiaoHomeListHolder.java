package com.linggan.jd831.adapter;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.WxDyListEntity;
import com.linggan.jd831.ui.common.WebUrlActivity;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.utils.XImageUtils;
import com.makeramen.roundedimageview.RoundedImageView;

import org.greenrobot.eventbus.EventBus;
import org.xutils.http.RequestParams;

import java.util.HashMap;
import java.util.Map;

/**
 * 类  名：宣教首页
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class XuanJiaoHomeListHolder extends IViewHolder {


    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_xuan_jiao_home;
    }


    public class ViewHolder extends XViewHolder<WxDyListEntity> {

        private RoundedImageView mIvHead;
        private TextView mTvInfo;
        private TextView mBtZan;
        private TextView mTvLookNum;
        private LinearLayout mLin;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mIvHead = view.findViewById(R.id.iv_head);
            mTvInfo = view.findViewById(R.id.tv_title);
            mBtZan = view.findViewById(R.id.bt_zan);
            mTvLookNum = view.findViewById(R.id.tv_look_num);
            mBtZan = view.findViewById(R.id.bt_zan);
            mTvLookNum = view.findViewById(R.id.tv_look_num);
            mLin = view.findViewById(R.id.lin);
            String areaIds = UserInfoUtils.getUserInfo().getYhXzqhdm();
            if (!TextUtils.isEmpty(areaIds) && !areaIds.startsWith("43")) {
                mLin.setVisibility(View.VISIBLE);
            } else {
                mLin.setVisibility(View.GONE);
            }
        }

        @Override
        protected void onBindData(final WxDyListEntity itemData) {
            XImageUtils.load(mContext, itemData.getImgLinks(), mIvHead);
            mTvInfo.setText(itemData.getArticleTitle());
            if (itemData.getCydzsl() != null) {
                if (itemData.getCydzsl().getCysl() > 9999) {
                    mTvLookNum.setText("查看：" + StrUtils.getWanNum(itemData.getCydzsl().getCysl() + "") + "万+");
                } else {
                    mTvLookNum.setText("查看：" + itemData.getCydzsl().getCysl() + "");
                }
                if (itemData.getCydzsl().getDzsl() > 9999) {
                    mBtZan.setText("点赞：" + StrUtils.getWanNum(itemData.getCydzsl().getDzsl() + "") + "万+");
                } else {
                    mBtZan.setText("点赞：" + itemData.getCydzsl().getDzsl() + "");
                }
            } else {
                mTvLookNum.setText("查看：0");
                mBtZan.setText("点赞：0");
            }
            if (itemData.isSfdz()) {
                Drawable drawable = mContext.getResources().getDrawable(R.mipmap.ic_zan_x_t);
                mBtZan.setCompoundDrawablesWithIntrinsicBounds(drawable, null, null, null);
            } else {
                Drawable drawable = mContext.getResources().getDrawable(R.mipmap.ic_zan_x);
                mBtZan.setCompoundDrawablesWithIntrinsicBounds(drawable, null, null, null);
            }
            itemView.setOnClickListener(v -> {
                Bundle bundle = new Bundle();
                bundle.putString("url", itemData.getArticleLink());
                bundle.putString("bh", itemData.getLinkId());
                if (itemData.getCydzsl() != null) {
                    bundle.putInt("num", itemData.getCydzsl().getDzsl());
                }
                bundle.putBoolean("dz", itemData.isSfdz());
                XIntentUtil.redirectToNextActivity(mContext, WebUrlActivity.class, bundle);
            });
            mBtZan.setOnClickListener(v -> {
                if (itemData.isSfdz()) {
                    postDzData(itemData.getLinkId(), "1", (code, id) -> {
                    });
                } else {
                    postDzData(itemData.getLinkId(), "0", (code, id) -> {
                    });
                }
            });
        }
    }

    private void postDzData(String bh, String dzzt, DialogUtils.OnResult onResult) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.XJ_DZ);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("nrbh", bh);
        objectMap.put("nrlx", "0");
        objectMap.put("dzzt", dzzt);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJson(mContext, requestParams, jiaMiString, DialogUtils.showLoadDialog(mContext, ""), true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    onResult.onSuccess("", "");
                    EventBus.getDefault().post(new WxDyListEntity());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }
}


