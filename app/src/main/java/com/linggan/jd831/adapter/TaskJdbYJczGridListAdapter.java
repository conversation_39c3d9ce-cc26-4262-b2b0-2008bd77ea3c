package com.linggan.jd831.adapter;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.TaskDataNewEntity;
import com.linggan.jd831.ui.works.task.TaskRyXxListActivity;
import com.linggan.jd831.ui.works.yujing.TaskYjczListActivity;

import java.util.List;


/**
 * 任务中心-禁毒办-预警处置-适配器
 */
public class TaskJdbYJczGridListAdapter extends RecyclerView.Adapter<TaskJdbYJczGridListAdapter.Holder> {

    private List<TaskDataNewEntity.FldxBean> list;
    private Context context;
    private String xzqhdm, xzqhdj;

    public TaskJdbYJczGridListAdapter(Context context, List<TaskDataNewEntity.FldxBean> list, String xzqhdm, String xzqhdj) {
        this.context = context;
        this.list = list;
        this.xzqhdm = xzqhdm;
        this.xzqhdj = xzqhdj;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_jdb_city_grid, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        TaskDataNewEntity.FldxBean liuList = list.get(position);
        if (liuList.getYjlx() != null) {
            holder.mTvGTitle.setText(liuList.getYjlx().getName());
        } else {
            holder.mTvGTitle.setText("");
        }
        holder.mTvGNum.setText(liuList.getNum() + "");

        holder.itemView.setOnClickListener(v -> {
            Bundle bundle = new Bundle();
            bundle.putString("dwDms", xzqhdm);
            bundle.putString("xzqhdj", xzqhdj);
            if (liuList.getYjlx() != null) {
                bundle.putString("lxid", liuList.getYjlx().getCode());
                XIntentUtil.redirectToNextActivity(context, TaskYjczListActivity.class, bundle);
            } else {
                XIntentUtil.redirectToNextActivity(context, TaskYjczListActivity.class, bundle);
            }
        });
    }

    class Holder extends RecyclerView.ViewHolder {
        TextView mTvGTitle;
        TextView mTvGNum;

        Holder(@NonNull View itemView) {
            super(itemView);
            this.mTvGTitle = itemView.findViewById(R.id.tv_g_title);
            this.mTvGNum = itemView.findViewById(R.id.tv_g_num);
        }
    }
}
