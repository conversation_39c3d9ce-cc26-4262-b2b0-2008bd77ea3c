package com.linggan.jd831.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.CodeNameEntity;

import java.util.List;


/**
 * 聊天消息里面--督办/考核数据填充类
 */
public class DbChatListAdapter extends RecyclerView.Adapter<DbChatListAdapter.Holder> {

    private List<CodeNameEntity> list;
    private Context context;

    public DbChatListAdapter(Context context, List<CodeNameEntity> list) {
        this.context = context;
        this.list = list;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_chat_db, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        CodeNameEntity codeNameEntity = list.get(position);
        holder.mTvBtTs.setText(codeNameEntity.getName());
        holder.mTvNr.setText(codeNameEntity.getCode());
    }

    public class Holder extends RecyclerView.ViewHolder {
        TextView mTvBtTs;
        TextView mTvNr;

        Holder(@NonNull View itemView) {
            super(itemView);
            this.mTvBtTs = itemView.findViewById(R.id.tv_bt_ts);
            this.mTvNr = itemView.findViewById(R.id.tv_nr);
        }
    }
}
