package com.linggan.jd831.adapter;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.CarHcNoDataBean;
import com.linggan.jd831.bean.JzxxBean;
import com.linggan.jd831.ui.works.task.CarJzAddActivity;
import com.linggan.jd831.utils.StrUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.List;


/**
 * 车辆驾照核查--驾照信息核查
 */

public class CarJzListAdapter extends RecyclerView.Adapter<CarJzListAdapter.Holder> {

    private List<JzxxBean> list;
    private Context context;

    public CarJzListAdapter(Context context, List<JzxxBean> list) {
        this.context = context;
        this.list = list;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.itme_car_jz_list_add, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        JzxxBean hxpGgBean = list.get(position);
        int num = position + 1;
        holder.mTvPo.setText("驾照" + num);
        holder.mTvJzlx.setText(hxpGgBean.getJzlx());
        holder.mTvJzTime.setText(StrUtils.jieQuTime(hxpGgBean.getFzsj()));
        if (!TextUtils.isEmpty(hxpGgBean.getSfBrzj())) {
            //1是本人证件2不是本人证件
            if (hxpGgBean.getSfBrzj().equals("2")) {
                holder.mRadioGroup.check(R.id.rb_no);
                holder.linZx.setVisibility(View.GONE);
                holder.mRadioGroupZx.clearCheck();
            } else {
                holder.mRadioGroup.check(R.id.rb_yes);
                holder.linZx.setVisibility(View.VISIBLE);
            }
        } else {
            holder.mRadioGroup.clearCheck();
            holder.linZx.setVisibility(View.GONE);
        }
        if (!TextUtils.isEmpty(hxpGgBean.getSfZx())) {
            holder.linZx.setVisibility(View.VISIBLE);
            //1已注销2未注销
            if (hxpGgBean.getSfBrzj().equals("2")) {
                holder.mRadioGroupZx.check(R.id.rb_zx_no);
            } else {
                holder.mRadioGroupZx.check(R.id.rb_zx_yes);
            }
        } else {
            holder.mRadioGroupZx.clearCheck();
            holder.linZx.setVisibility(View.GONE);
        }
        if (hxpGgBean.getSjly() == 2) {
            holder.linBr.setVisibility(View.GONE);
            //自己新增
            holder.mIvDel.setVisibility(View.VISIBLE);
            holder.mBtDel.setOnClickListener(v -> {
                list.remove(position);
                notifyDataSetChanged();
                EventBus.getDefault().post(new CarHcNoDataBean("1"));
            });
            holder.itemView.setOnClickListener(v -> {
                Bundle bundle = new Bundle();
                bundle.putInt("pos", position);
                bundle.putSerializable("jz", hxpGgBean);
                XIntentUtil.redirectToNextActivity(context, CarJzAddActivity.class, bundle);
            });
        } else {
            holder.linBr.setVisibility(View.VISIBLE);
            holder.mIvDel.setVisibility(View.GONE);
        }

        holder.mRadioGroup.setOnCheckedChangeListener((group, checkedId) -> {
            switch (checkedId) {
                case R.id.rb_no:
                    hxpGgBean.setSfBrzj("2");
                    holder.linZx.setVisibility(View.GONE);
                    holder.mRadioGroupZx.clearCheck();
                    hxpGgBean.setSfZx("");
                    break;
                case R.id.rb_yes:
                    hxpGgBean.setSfBrzj("1");
                    holder.linZx.setVisibility(View.VISIBLE);
                    break;
            }
        });
        holder.mRadioGroupZx.setOnCheckedChangeListener((group, checkedId) -> {
            switch (checkedId) {
                case R.id.rb_zx_no:
                    hxpGgBean.setSfZx("2");
                    break;
                case R.id.rb_zx_yes:
                    hxpGgBean.setSfZx("1");
                    break;
            }
        });
        if (position == list.size() - 1) {
            holder.viewLine.setVisibility(View.GONE);
        } else {
            holder.viewLine.setVisibility(View.VISIBLE);
        }
    }

    class Holder extends RecyclerView.ViewHolder {
        TextView mTvPo;
        TextView mTvJzlx;
        TextView mTvJzTime;
        RadioGroup mRadioGroup;
        RadioGroup mRadioGroupZx;
        ImageView mIvDel;
        LinearLayout mBtDel;
        private LinearLayout linZx, linBr;
        private View viewLine;

        Holder(@NonNull View itemView) {
            super(itemView);
            this.mTvPo = itemView.findViewById(R.id.tv_po);
            this.mTvJzlx = itemView.findViewById(R.id.tv_jzlx);
            this.mTvJzTime = itemView.findViewById(R.id.tv_jz_time);
            this.mRadioGroup = itemView.findViewById(R.id.radio_group);
            this.mRadioGroupZx = itemView.findViewById(R.id.radio_group_zx);
            this.linZx = itemView.findViewById(R.id.lin_zx);
            this.linBr = itemView.findViewById(R.id.lin_br);
            this.mIvDel = itemView.findViewById(R.id.iv_del);
            this.mBtDel = itemView.findViewById(R.id.bt_del);
            this.viewLine = itemView.findViewById(R.id.view_line);
        }
    }
}
