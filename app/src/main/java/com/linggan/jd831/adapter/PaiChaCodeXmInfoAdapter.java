package com.linggan.jd831.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.PlaceCodeInfoEntity;
import com.linggan.jd831.bean.PlaceTypeXmEntity;
import com.linggan.jd831.utils.StrUtils;

import java.util.List;

/**
 * 踏查的查询配置-详情
 */
public class PaiChaCodeXmInfoAdapter extends RecyclerView.Adapter<PaiChaCodeXmInfoAdapter.Holder> {
    private List<PlaceCodeInfoEntity.PcxmVOsBean> list;
    private Context context;

    public PaiChaCodeXmInfoAdapter(Context context, List<PlaceCodeInfoEntity.PcxmVOsBean> list) {
        this.list = list;
        this.context = context;
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_place_code_xm_info_list, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        PlaceCodeInfoEntity.PcxmVOsBean item = list.get(position);
        holder.tvTitle.setText(item.getPcxm());
        holder.tvInfo.setText(StrUtils.listToString(item.getPcnr()));
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    class Holder extends RecyclerView.ViewHolder {
        protected TextView tvTitle, tvInfo;
        View view;

        Holder(@NonNull View itemView) {
            super(itemView);
            view = itemView;
            tvTitle = (TextView) itemView.findViewById(R.id.tv_title);
            tvInfo = (TextView) itemView.findViewById(R.id.tv_info);
        }
    }

}
