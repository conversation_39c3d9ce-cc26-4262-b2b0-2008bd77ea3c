package com.linggan.jd831.adapter;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.VisitListEntity;
import com.linggan.jd831.ui.works.hecha.HeChaGkInfoActivity;
import com.linggan.jd831.utils.StrUtils;

/**
 * 类  名：核查管控
 * 作  者：ZXB
 * 说  明：
 * 时  间：2024/6/3 11:51
 * 版  权：LGKJ
 */
public class HeChaGkListHolder extends IViewHolder {


    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_hecha_gk_list;
    }


    public class ViewHolder extends XViewHolder<VisitListEntity> {
        private TextView mTvTitle;
        private TextView mTvTime;
        private TextView mTvInfo;
        private TextView mTvPeople;
        private ImageView mIvSpTag;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mTvTitle = view.findViewById(R.id.tv_title);
            mTvTime = view.findViewById(R.id.tv_time);
            mTvInfo = view.findViewById(R.id.tv_info);
            mTvPeople = view.findViewById(R.id.tv_people);
            mIvSpTag = view.findViewById(R.id.iv_sp_tag);
        }

        @Override
        protected void onBindData(final VisitListEntity itemData) {
            if (itemData.getLx() != null) {
                mTvTitle.setText(itemData.getLx().getName());
            }
            if (!TextUtils.isEmpty(itemData.getZqKssj())) {
                mTvTime.setText(itemData.getZqKssj() + " 至 " + itemData.getZqJssj());
            } else {
                mTvTime.setText(itemData.getZq());
            }
            mTvInfo.setText(itemData.getHcsj());
            mTvPeople.setText(itemData.getShengQhmc() + itemData.getShiQhmc() + itemData.getQuQhmc() + itemData.getXzQhmc()
                    + itemData.getDz());

            //0审批通过；2审批中；1审批不通过
            StrUtils.showSpStatusTag(itemData.getApproval(), mIvSpTag);

            itemView.setOnClickListener(v -> {
                Bundle bundle = new Bundle();
                bundle.putString("id", itemData.getBh() + "");
                bundle.putString("pid", itemData.getXyrbh() + "");
                XIntentUtil.redirectToNextActivity(mContext, HeChaGkInfoActivity.class, bundle);
            });
        }
    }
}


