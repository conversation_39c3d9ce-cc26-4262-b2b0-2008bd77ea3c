package com.linggan.jd831.adapter;

import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.TaskDataNewEntity;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

/**
 * 类  名：任务中心(辖区)--预警处置
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2023/3/6 10:57
 * 版  权：LGKJ
 */
public class TaskJdbYJczCityHolder extends IViewHolder {

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_task_jbd_city;
    }

    public class ViewHolder extends XViewHolder<TaskDataNewEntity> {

        private TextView tvName;
        private RecyclerView recycler;
        private TextView mTvBtMore;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            tvName = view.findViewById(R.id.tv_title);
            recycler = view.findViewById(R.id.recycle_grid_city);
            recycler.setLayoutManager(new GridLayoutManager(mContext, 3));
            mTvBtMore = view.findViewById(R.id.tv_bt_more);
        }

        @Override
        protected void onBindData(final TaskDataNewEntity itemData) {
            tvName.setText(itemData.getXzqhmc() + "(" + itemData.getSl() + "条)");
            if (itemData.getFldx() != null && itemData.getFldx().size() > 0) {
                List<TaskDataNewEntity.FldxBean> listOne = new ArrayList<>();
                if (itemData.getFldx().size() > 3) {
                    listOne.add(itemData.getFldx().get(0));
                    listOne.add(itemData.getFldx().get(1));
                    listOne.add(itemData.getFldx().get(2));
                    mTvBtMore.setVisibility(View.VISIBLE);
                    mTvBtMore.setText("查看更多");
                } else {
                    listOne.addAll(itemData.getFldx());
                    mTvBtMore.setVisibility(View.GONE);
                }
                recycler.setVisibility(View.VISIBLE);
                recycler.setAdapter(new TaskJdbYJczGridListAdapter(mContext, listOne, itemData.getXzqhdm(), itemData.getDqdj()));
                final boolean[] isOpen = {true};
                mTvBtMore.setOnClickListener(v -> {
                    if (isOpen[0]) {
                        recycler.setAdapter(new TaskJdbYJczGridListAdapter(mContext, itemData.getFldx(), itemData.getXzqhdm(), itemData.getDqdj()));
                        mTvBtMore.setText("收起");
                    } else {
                        recycler.setAdapter(new TaskJdbYJczGridListAdapter(mContext, listOne, itemData.getXzqhdm(), itemData.getDqdj()));
                        mTvBtMore.setText("查看更多");
                    }
                    isOpen[0] = !isOpen[0];
                });
            } else {
                mTvBtMore.setVisibility(View.INVISIBLE);
                recycler.setVisibility(View.GONE);
            }
            tvName.setOnClickListener(v -> {
                EventBus.getDefault().post(itemData);
            });
        }
    }
}
