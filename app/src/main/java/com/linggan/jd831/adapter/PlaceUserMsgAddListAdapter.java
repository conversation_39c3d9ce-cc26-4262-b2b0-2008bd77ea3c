package com.linggan.jd831.adapter;

import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.PlaceUserEntity;

import java.util.ArrayList;
import java.util.List;


/**
 * 场所排查-使用人信息-适配器
 */

public class PlaceUserMsgAddListAdapter extends RecyclerView.Adapter<PlaceUserMsgAddListAdapter.Holder> {

    private List<PlaceUserEntity> list;
    private Context context;
    private List<String> listDelId = new ArrayList<>();

    public PlaceUserMsgAddListAdapter(Context context, List<PlaceUserEntity> list) {
        this.context = context;
        this.list = list;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_place_user_msg_add, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        PlaceUserEntity placeUser = list.get(position);
        //
        holder.mEtName.removeTextChangedListener(holder.textWatcherXm);
        holder.textWatcherXm = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                placeUser.setSyrXm(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        };
        holder.mEtName.setText(placeUser.getSyrXm());
        holder.mEtName.addTextChangedListener(holder.textWatcherXm);
        //
        holder.mEtIdCard.removeTextChangedListener(holder.textWatcherId);
        holder.textWatcherId = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                placeUser.setSyrSfhm(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        };
        holder.mEtIdCard.setText(placeUser.getSyrSfhm());
        holder.mEtIdCard.addTextChangedListener(holder.textWatcherId);
        //
        holder.mEtPhone.removeTextChangedListener(holder.textWatcherDh);
        holder.textWatcherDh = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                placeUser.setSyrLxdh(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        };
        holder.mEtPhone.setText(placeUser.getSyrLxdh());
        holder.mEtPhone.addTextChangedListener(holder.textWatcherDh);
        //
        holder.mIvCha.setOnClickListener(v -> {
            listDelId.add(placeUser.getId());
            list.remove(position);
            notifyDataSetChanged();
        });
    }

    class Holder extends RecyclerView.ViewHolder {
        EditText mEtName;
        EditText mEtIdCard;
        EditText mEtPhone;
        ImageView mIvCha;
        private TextWatcher textWatcherXm, textWatcherDh, textWatcherId;

        Holder(@NonNull View itemView) {
            super(itemView);
            this.mEtName = itemView.findViewById(R.id.et_name);
            this.mEtIdCard = itemView.findViewById(R.id.et_id_card);
            this.mEtPhone = itemView.findViewById(R.id.et_phone);
            this.mIvCha = itemView.findViewById(R.id.iv_cha);
        }
    }

    public List<String> getListDelId() {
        return listDelId;
    }
}
