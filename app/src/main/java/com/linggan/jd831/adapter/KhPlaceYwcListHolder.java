package com.linggan.jd831.adapter;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.MapUtils;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.KhlvPlaceEntity;
import com.linggan.jd831.ui.works.place.PaiChaPlaceCodeInfoActivity;
import com.linggan.jd831.utils.DialogUtils;

/**
 * 类  名：周期排查
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class KhPlaceYwcListHolder extends IViewHolder {

    private String type;

    public KhPlaceYwcListHolder(String type) {
        this.type = type;
    }

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_kh_pccs;
    }

    public class ViewHolder extends XViewHolder<KhlvPlaceEntity> {
        private TextView mTvTitle;
        private TextView mTvTag;
        private TextView mTvDay;
        private TextView mTvName;
        private TextView mTvKsTime;
        private TextView mTvEndTime, mTvEndTitle;
        private TextView mTvZrr;
        private View mView;
        private TextView mBtPost;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mTvTitle = view.findViewById(R.id.tv_title);
            mTvTag = view.findViewById(R.id.tv_tag);
            mTvDay = view.findViewById(R.id.tv_day);
            mTvName = view.findViewById(R.id.tv_name);
            mTvKsTime = view.findViewById(R.id.tv_ks_time);
            mTvEndTime = view.findViewById(R.id.tv_end_time);
            mTvZrr = view.findViewById(R.id.tv_zrr);
            mTvEndTitle = view.findViewById(R.id.tv_end_title);
            mView = view.findViewById(R.id.view);
            mBtPost = view.findViewById(R.id.bt_post);
            mView.setVisibility(View.GONE);
            mBtPost.setVisibility(View.GONE);
        }

        @Override
        protected void onBindData(final KhlvPlaceEntity itemData) {

            mTvTitle.setText(itemData.getCsmc());
            mTvName.setText(itemData.getCsdz());
            mTvKsTime.setText("场所类型：\u3000" + itemData.getCslxLxmc());
            mTvEndTitle.setText("排查时间：\u3000");
            mTvEndTime.setText(itemData.getPcsj());
            mTvZrr.setText("责任人：\u3000\u3000" + itemData.getFzrmc());


            mTvName.setOnClickListener(v -> {
                DialogUtils.mapDialog(mContext, (code, va) -> {
                    if (code.equals("1")) {
                        MapUtils.openGaoDe(mContext, itemData.getCsdz());
                    } else {
                        MapUtils.goToBaiduMap(mContext, itemData.getCsdz());
                    }
                });
            });

            itemView.setOnClickListener(v -> {
                Bundle bundle = new Bundle();
                bundle.putString("id", itemData.getRwId() + "");
                XIntentUtil.redirectToNextActivity(mContext, PaiChaPlaceCodeInfoActivity.class, bundle);
            });
        }
    }
}


