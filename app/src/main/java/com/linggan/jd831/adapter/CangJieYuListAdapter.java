package com.linggan.jd831.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.HxpGgIntBean;
import com.linggan.jd831.utils.StrUtils;

import java.util.List;


/**
 * 仓库历史详情-结余展示
 */

public class CangJieYuListAdapter extends RecyclerView.Adapter<CangJieYuListAdapter.Holder> {

    private List<HxpGgIntBean> list;
    private Context context;

    public CangJieYuListAdapter(Context context, List<HxpGgIntBean> list) {
        this.context = context;
        this.list = list;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {

        View view = LayoutInflater.from(context).inflate(R.layout.item_cang_jieyu_list, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        HxpGgIntBean hxpGgBean = list.get(position);
        double jy = Double.parseDouble(StrUtils.getDev(hxpGgBean.getJy(), "0"));
        holder.mTvCkName.setText(jy + hxpGgBean.getDw() + "\u3000\u3000" + hxpGgBean.getGgSl() + hxpGgBean.getGgDw() + "/" + hxpGgBean.getDw());
    }

    class Holder extends RecyclerView.ViewHolder {
        TextView mTvCkName;

        Holder(@NonNull View itemView) {
            super(itemView);
            this.mTvCkName = itemView.findViewById(R.id.tv_ck_name);
        }
    }
}
