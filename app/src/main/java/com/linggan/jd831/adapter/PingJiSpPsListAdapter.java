package com.linggan.jd831.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.PsxxBean;
import com.linggan.jd831.utils.XImageUtils;
import com.linggan.jd831.widget.XGridViewForScrollView;

import java.util.List;


/**
 * 易制毒-企业评级-详情--评审信息
 */

public class PingJiSpPsListAdapter extends RecyclerView.Adapter<PingJiSpPsListAdapter.Holder> {

    private List<PsxxBean> list;
    private Context context;

    public PingJiSpPsListAdapter(Context context, List<PsxxBean> list) {
        this.context = context;
        this.list = list;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.itme_pj_sp_pdxx, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        PsxxBean bean = list.get(position);
        holder.mTvPsdf.setText(bean.getPsdf());
        holder.mTvPssm.setText(bean.getPssm());
        holder.mTvPsdw.setText(bean.getPsDwmc());
        holder.mGridFj.setAdapter(new FileAllTypeAdapter(context, bean.getSpfj()));
    }

    public static class Holder extends RecyclerView.ViewHolder {
        TextView mTvPsdf;
        TextView mTvPssm;
        TextView mTvPsdw;
        XGridViewForScrollView mGridFj;

        Holder(@NonNull View itemView) {
            super(itemView);
            this.mTvPsdf = itemView.findViewById(R.id.tv_psdf);
            this.mTvPssm = itemView.findViewById(R.id.tv_pssm);
            this.mTvPsdw = itemView.findViewById(R.id.tv_psdw);
            this.mGridFj = itemView.findViewById(R.id.grid_fj);
        }
    }
}
