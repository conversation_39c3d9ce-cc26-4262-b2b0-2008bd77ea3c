package com.linggan.jd831.adapter;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.TaskLiuListEntity;

import java.util.List;


/**
 * 任务中中心-顶部5个菜单-适配器
 */
public class TaskTopMenuAdapter extends RecyclerView.Adapter<TaskTopMenuAdapter.Holder> {

    private List<TaskLiuListEntity> list;
    private Context context;

    public TaskTopMenuAdapter(Context context, List<TaskLiuListEntity> list) {
        this.context = context;
        this.list = list;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_approval_name_list, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        TaskLiuListEntity liuList = list.get(position);
        holder.tvName.setText(liuList.getName());
        if (!TextUtils.isEmpty(liuList.getRemark())) {
            holder.mViewRemark.setVisibility(View.VISIBLE);
        } else {
            holder.mViewRemark.setVisibility(View.GONE);
        }
        //-1 发起审批    0  审批通过   1审批不通过   2正在审批中
        if (position == list.size() - 1) {
            holder.mViewTwo.setVisibility(View.INVISIBLE);
        } else {
            holder.mViewTwo.setVisibility(View.VISIBLE);
        }
        if (!TextUtils.isEmpty(liuList.getResult())) {
            if (liuList.getResult().equals("-1") || liuList.getResult().equals("0")) {
                holder.mIvTag.setImageResource(R.mipmap.ic_task_fin);
                holder.mViewTwo.setBackgroundColor(Color.parseColor("#0053E2"));
            } else if (liuList.getResult().equals("1")) {
                holder.mIvTag.setImageResource(R.mipmap.ic_task_jj);
                holder.mViewTwo.setBackgroundColor(Color.parseColor("#0053E2"));
            } else if (liuList.getResult().equals("2")) {
                holder.mIvTag.setImageResource(R.mipmap.ic_task_jx);
                holder.mViewTwo.setBackgroundColor(Color.parseColor("#0053E2"));
            }
        } else {
            holder.mViewTwo.setBackgroundColor(Color.parseColor("#D9D9D9"));
            holder.mIvTag.setImageResource(R.mipmap.ic_task_ing);
        }
    }

    class Holder extends RecyclerView.ViewHolder {
        ImageView mIvTag;
        View mViewTwo, mViewRemark;
        TextView tvName;

        Holder(@NonNull View itemView) {
            super(itemView);
            this.mIvTag = itemView.findViewById(R.id.iv_tag);
            this.mViewTwo = itemView.findViewById(R.id.view_two);
            tvName = itemView.findViewById(R.id.tv_name);
            mViewRemark = itemView.findViewById(R.id.view_remark);
        }
    }
}
