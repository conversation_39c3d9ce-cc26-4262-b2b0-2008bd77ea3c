package com.linggan.jd831.adapter;

import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.SjsjNrBean;

import java.util.List;


/**
 * 任务中心-目标任务-数据采集-适配器
 */

public class TaskMuBiaoInfoMsgListAdapter extends RecyclerView.Adapter<TaskMuBiaoInfoMsgListAdapter.Holder> {

    private List<SjsjNrBean> list;
    private Context context;

    public TaskMuBiaoInfoMsgListAdapter(Context context, List<SjsjNrBean> list) {
        this.context = context;
        this.list = list;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_task_mbrw_lv_show, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        SjsjNrBean sjsjNrBean = list.get(position);
        if (!TextUtils.isEmpty(sjsjNrBean.getZlmc())) {
            if (sjsjNrBean.getZlmc().length() < 8) {
                int num = 7 - sjsjNrBean.getZlmc().length();
                String space = "";
                for (int i = 0; i < num; i++) {
                    space += "\u3000";
                }
                holder.tvTitle.setText(sjsjNrBean.getZlmc() + space);
            } else {
                holder.tvTitle.setText(sjsjNrBean.getZlmc() + "\u3000");
            }
        } else {
            holder.tvTitle.setText(sjsjNrBean.getZlmc());
        }
        holder.tvInfo.setText(sjsjNrBean.getSjsjnr());
    }

    public static class Holder extends RecyclerView.ViewHolder {
        TextView tvTitle;
        TextView tvInfo;

        Holder(@NonNull View itemView) {
            super(itemView);
            this.tvTitle = itemView.findViewById(R.id.tv_title);
            this.tvInfo = itemView.findViewById(R.id.tv_info);
        }
    }


}
