package com.linggan.jd831.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.HxpGgBean;
import com.linggan.jd831.bean.HxpGgIntBean;

import java.util.List;


/**
 * 仓库历史记录-适配器
 */

public class CangKuLsjlListAdapter extends RecyclerView.Adapter<CangKuLsjlListAdapter.Holder> {

    private List<HxpGgIntBean> list;
    private Context context;
    private OnItemClickListener onItemClickListener;

    public CangKuLsjlListAdapter(Context context, List<HxpGgIntBean> list) {
        this.context = context;
        this.list = list;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.itme_cang_lsjl_code_list, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        HxpGgIntBean hxpGgBean = list.get(position);
        holder.mTvCrk.setText(hxpGgBean.getHxpMc());
        holder.mTvRl.setText(hxpGgBean.getGgSl() + hxpGgBean.getGgDw());
        holder.mTvSl.setText(hxpGgBean.getSl() + hxpGgBean.getDw());

        holder.itemView.setOnClickListener(v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(hxpGgBean, position);
            }
        });
    }

    class Holder extends RecyclerView.ViewHolder {
        TextView mTvCrk;
        TextView mTvRl;
        TextView mTvSl;

        Holder(@NonNull View itemView) {
            super(itemView);
            this.mTvCrk = itemView.findViewById(R.id.tv_crk);
            this.mTvRl = itemView.findViewById(R.id.tv_rl);
            this.mTvSl = itemView.findViewById(R.id.tv_sl);
        }
    }

    public interface OnItemClickListener {
        void onItemClick(HxpGgIntBean data, int position);
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }
}
