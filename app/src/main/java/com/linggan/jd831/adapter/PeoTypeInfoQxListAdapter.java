package com.linggan.jd831.adapter;


import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.YjztbhListBean;

import java.util.List;


/**
 * 类  名：人员类型--人员详情里面展示
 * 描   述：
 * 作  者：ZXB
 * 时  间：2020/4/28 1:00
 * 版  权：凌感科技
 */
public class PeoTypeInfoQxListAdapter extends RecyclerView.Adapter<PeoTypeInfoQxListAdapter.BaseHolder> {

    private Context mContext;
    private List<YjztbhListBean> data;

    public PeoTypeInfoQxListAdapter(Context mContext, List<YjztbhListBean> data) {
        this.mContext = mContext;
        this.data = data;
    }

    @NonNull
    @Override
    public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new BaseHolder(LayoutInflater.from(mContext).inflate(R.layout.item_people_type_info1, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull BaseHolder holder, int position) {
        YjztbhListBean yjztbhListBean = data.get(position);
        if (yjztbhListBean.getRyyjzt().getSjName().length() > 2) {
            holder.mTvDjTitle.setText(yjztbhListBean.getRyyjzt().getSjName().substring(0, 2));
        } else {
            holder.mTvDjTitle.setText(yjztbhListBean.getRyyjzt().getSjName());
        }
        holder.mTvInfo.setText(yjztbhListBean.getRyyjzt().getName());
    }

    @Override
    public int getItemCount() {
        return data == null ? 0 : data.size();
    }

    class BaseHolder extends RecyclerView.ViewHolder {
        TextView mTvDjTitle;
        TextView mTvInfo;

        public BaseHolder(View rootView) {
            super(rootView);
            this.mTvDjTitle = rootView.findViewById(R.id.tv_dj_title);
            this.mTvInfo = rootView.findViewById(R.id.tv_info);
        }
    }

}

































