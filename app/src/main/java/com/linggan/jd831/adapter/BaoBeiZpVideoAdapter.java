package com.linggan.jd831.adapter;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.ZpBean;
import com.linggan.jd831.ui.common.PhotoActivity;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XImageUtils;
import com.makeramen.roundedimageview.RoundedImageView;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 易制毒-报备-抓拍--预警照片
 */

public class BaoBeiZpVideoAdapter extends RecyclerView.Adapter<BaoBeiZpVideoAdapter.Holder> {

    private List<ZpBean> list;
    private Context mContext;

    public BaoBeiZpVideoAdapter(Context context, List<ZpBean> list) {
        this.mContext = context;
        this.list = list;
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.itme_bb_zp, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        ZpBean itemData = list.get(position);
        XImageUtils.load(mContext, itemData.getZpRlUrl(), holder.mIvhead);
        holder.mTvTime.setText(StrUtils.jieQuTime(itemData.getZpSj(), 1));
        holder.mIvhead.setOnClickListener(v -> {
            if (list != null && list.size() > 0) {
                List<String> imageList = new ArrayList<String>();
                for (int k = 0; k < list.size(); k++) {
                    imageList.add(list.get(k).getZpRlUrl());
                }
                Bundle bundle = new Bundle();
                bundle.putSerializable("info", (Serializable) imageList);
                bundle.putInt("pos", position);
                XIntentUtil.redirectToNextActivity(mContext, PhotoActivity.class, bundle);
            }
        });
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    class Holder extends RecyclerView.ViewHolder {
        RoundedImageView mIvhead;
        TextView mTvTime;

        Holder(@NonNull View itemView) {
            super(itemView);
            this.mIvhead = itemView.findViewById(R.id.ivHead);
            this.mTvTime = itemView.findViewById(R.id.tv_time);
        }
    }


}
