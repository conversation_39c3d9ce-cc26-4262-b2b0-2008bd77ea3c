package com.linggan.jd831.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.utils.XImageUtils;
import com.makeramen.roundedimageview.RoundedImageView;

import java.util.List;

/**
 * 图片添加适配器
 */
public class FileAllAddAdapter extends BaseAdapter {
    private List<OssFileEntity> list;
    private boolean isDel;//是否可以删除
    private int max = 8;//最大添加数量
    private OnImageDeleteListener onImageDeleteListener;//图片删除控件

    public FileAllAddAdapter(List<OssFileEntity> list) {
        this.list = list;
    }

    @Override
    public int getCount() {
        if (isDel) {
            if (list.size() < max) {
                return list.size() + 1;
            } else {
                return max;
            }
        } else {
            return list.size();
        }
    }

    @Override
    public OssFileEntity getItem(int i) {
        return list.get(i);
    }

    @Override
    public long getItemId(int i) {
        return i;
    }

    @Override
    public View getView(int i, View view, ViewGroup viewGroup) {
        Holder holder;
        if (view == null) {
            holder = new Holder();
            view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.item_image_add, null);
            holder.image = view.findViewById(R.id.image_add);
            holder.delete = view.findViewById(R.id.image_del);
            view.setTag(holder);
        } else {
            holder = (Holder) view.getTag();
        }
        if (isDel) {
            if (list.size() < 8) {
                if (i < list.size()) {
                    holder.delete.setVisibility(View.VISIBLE);
                    holder.image.setVisibility(View.VISIBLE);
                    if (list.get(i).getSavePath().endsWith(".pdf")) {
                        holder.image.setImageResource(R.mipmap.ic_pdf);
                    } else if (list.get(i).getSavePath().endsWith(".ppt") || list.get(i).getSavePath().endsWith(".pptx")) {
                        holder.image.setImageResource(R.mipmap.ic_file_ppt);
                    } else if (list.get(i).getSavePath().endsWith(".txt")) {
                        holder.image.setImageResource(R.mipmap.ic_file_txt);
                    } else if (list.get(i).getSavePath().endsWith(".zip") || list.get(i).getSavePath().endsWith(".z7") || list.get(i).getSavePath().endsWith(".rar")) {
                        holder.image.setImageResource(R.mipmap.ic_file_zip);
                    } else if (list.get(i).getSavePath().endsWith(".xls") || list.get(i).getSavePath().endsWith(".xlsx")) {
                        holder.image.setImageResource(R.mipmap.ic_file_exe);
                    } else if (list.get(i).getSavePath().endsWith(".img") || list.get(i).getSavePath().endsWith(".png") || list.get(i).getSavePath().endsWith(".jpg") || list.get(i).getSavePath().endsWith(".jpeg")) {
                        XImageUtils.loadNoCache1(viewGroup.getContext(), list.get(i).getSavePath(), holder.image);
                    } else if (list.get(i).getSavePath().endsWith(".mp4") || list.get(i).getSavePath().endsWith(".wmv") || list.get(i).getSavePath().endsWith(".avi") || list.get(i).getSavePath().endsWith(".rm")
                            || list.get(i).getSavePath().endsWith(".ram")) {
                        XImageUtils.displayVideoThumbnailOld(viewGroup.getContext(), list.get(i).getSavePath(), holder.image);
                    } else if (list.get(i).getSavePath().endsWith(".mp3") || list.get(i).getSavePath().endsWith(".wma") || list.get(i).getSavePath().endsWith(".aac")) {
                        holder.image.setImageResource(R.mipmap.ic_file_mp3);
                    } else if (list.get(i).getSavePath().endsWith(".doc") || list.get(i).getSavePath().endsWith(".docx")) {
                        holder.image.setImageResource(R.mipmap.ic_file_word);
                    } else {
                        holder.image.setImageResource(R.mipmap.ic_file_wz);
                    }
                    if (onImageDeleteListener != null) {
                        holder.delete.setOnClickListener(view1 -> onImageDeleteListener.onDelete(i));
                    }
                } else {
                    holder.delete.setVisibility(View.GONE);
                    holder.image.setVisibility(View.INVISIBLE);
                }
            } else {
                holder.delete.setVisibility(View.VISIBLE);
                holder.image.setVisibility(View.VISIBLE);
                if (list.get(i).getSavePath().endsWith(".pdf")) {
                    holder.image.setImageResource(R.mipmap.ic_pdf);
                } else if (list.get(i).getSavePath().endsWith(".ppt") || list.get(i).getSavePath().endsWith(".pptx")) {
                    holder.image.setImageResource(R.mipmap.ic_file_ppt);
                } else if (list.get(i).getSavePath().endsWith(".txt")) {
                    holder.image.setImageResource(R.mipmap.ic_file_txt);
                } else if (list.get(i).getSavePath().endsWith(".zip") || list.get(i).getSavePath().endsWith(".z7") || list.get(i).getSavePath().endsWith(".rar")) {
                    holder.image.setImageResource(R.mipmap.ic_file_zip);
                } else if (list.get(i).getSavePath().endsWith(".xls") || list.get(i).getSavePath().endsWith(".xlsx")) {
                    holder.image.setImageResource(R.mipmap.ic_file_exe);
                } else if (list.get(i).getSavePath().endsWith(".img") || list.get(i).getSavePath().endsWith(".png") || list.get(i).getSavePath().endsWith(".jpg") || list.get(i).getSavePath().endsWith(".jpeg")) {
//                    holder.image.setImageResource(R.mipmap.ic_file_img);
                    XImageUtils.loadNoCache1(viewGroup.getContext(), list.get(i).getSavePath(), holder.image);
                } else if (list.get(i).getSavePath().endsWith(".mp4") || list.get(i).getSavePath().endsWith(".wmv") || list.get(i).getSavePath().endsWith(".avi") || list.get(i).getSavePath().endsWith(".rm")
                        || list.get(i).getSavePath().endsWith(".ram")) {
//                    holder.image.setImageResource(R.mipmap.ic_file_video);
                    XImageUtils.displayVideoThumbnailOld(viewGroup.getContext(), list.get(i).getSavePath(), holder.image);
                } else if (list.get(i).getSavePath().endsWith(".mp3") || list.get(i).getSavePath().endsWith(".wma") || list.get(i).getSavePath().endsWith(".aac")) {
                    holder.image.setImageResource(R.mipmap.ic_file_mp3);
                } else if (list.get(i).getSavePath().endsWith(".doc") || list.get(i).getSavePath().endsWith(".docx")) {
                    holder.image.setImageResource(R.mipmap.ic_file_word);
                } else {
                    holder.image.setImageResource(R.mipmap.ic_file_wz);
                }
                if (onImageDeleteListener != null) {
                    holder.delete.setOnClickListener(view1 -> onImageDeleteListener.onDelete(i));
                }
            }
        } else {
            holder.delete.setVisibility(View.GONE);
            holder.image.setVisibility(View.VISIBLE);
            if (list.get(i).getSavePath().endsWith(".pdf")) {
                holder.image.setImageResource(R.mipmap.ic_pdf);
            } else if (list.get(i).getSavePath().endsWith(".ppt") || list.get(i).getSavePath().endsWith(".pptx")) {
                holder.image.setImageResource(R.mipmap.ic_file_ppt);
            } else if (list.get(i).getSavePath().endsWith(".txt")) {
                holder.image.setImageResource(R.mipmap.ic_file_txt);
            } else if (list.get(i).getSavePath().endsWith(".zip") || list.get(i).getSavePath().endsWith(".z7") || list.get(i).getSavePath().endsWith(".rar")) {
                holder.image.setImageResource(R.mipmap.ic_file_zip);
            } else if (list.get(i).getSavePath().endsWith(".xls") || list.get(i).getSavePath().endsWith(".xlsx")) {
                holder.image.setImageResource(R.mipmap.ic_file_exe);
            } else if (list.get(i).getSavePath().endsWith(".img") || list.get(i).getSavePath().endsWith(".png") || list.get(i).getSavePath().endsWith(".jpg") || list.get(i).getSavePath().endsWith(".jpeg")) {
//                holder.image.setImageResource(R.mipmap.ic_file_img);
                XImageUtils.loadNoCache1(viewGroup.getContext(), list.get(i).getSavePath(), holder.image);
            } else if (list.get(i).getSavePath().endsWith(".mp4") || list.get(i).getSavePath().endsWith(".wmv") || list.get(i).getSavePath().endsWith(".avi") || list.get(i).getSavePath().endsWith(".rm")
                    || list.get(i).getSavePath().endsWith(".ram")) {
//                holder.image.setImageResource(R.mipmap.ic_file_video);
                XImageUtils.displayVideoThumbnailOld(viewGroup.getContext(), list.get(i).getSavePath(), holder.image);
            } else if (list.get(i).getSavePath().endsWith(".mp3") || list.get(i).getSavePath().endsWith(".wma") || list.get(i).getSavePath().endsWith(".aac")) {
                holder.image.setImageResource(R.mipmap.ic_file_mp3);
            } else if (list.get(i).getSavePath().endsWith(".doc") || list.get(i).getSavePath().endsWith(".docx")) {
                holder.image.setImageResource(R.mipmap.ic_file_word);
            } else {
                holder.image.setImageResource(R.mipmap.ic_file_wz);
            }
        }
        return view;
    }

    public void setDel(boolean del) {
        isDel = del;
    }

    /**
     * 设置图片删除监听
     *
     * @param onImageDeleteListener 删除监听接口
     */
    public void setOnImageDeleteListener(OnImageDeleteListener onImageDeleteListener) {
        this.onImageDeleteListener = onImageDeleteListener;
    }

    public void setMax(int max) {
        this.max = max;
    }


    private class Holder {
        RoundedImageView image;
        ImageView delete;
    }

    public interface OnImageDeleteListener {
        void onDelete(int i);
    }
}
