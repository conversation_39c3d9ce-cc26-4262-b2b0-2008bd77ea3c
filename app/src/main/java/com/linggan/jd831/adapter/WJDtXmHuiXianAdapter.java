package com.linggan.jd831.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.XzsBean;

import java.util.List;

/**
 * 类  名：配置多选--数据绑定适配器--数据回显
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2020/8/13  17:25
 * 版  权：LGKJ
 */
public class WJDtXmHuiXianAdapter extends RecyclerView.Adapter<WJDtXmHuiXianAdapter.Holder> {

    private List<XzsBean> list;
    private Context context;
    private String answerType, das;
    private String[] ZiMuArr = {"", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

    public WJDtXmHuiXianAdapter(List<XzsBean> list, String answerType, String das) {
        this.list = list;
        this.answerType = answerType;
        this.das = das;
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        this.context = parent.getContext();
        View view = LayoutInflater.from(context).inflate(R.layout.item_wjdt_xm_huixian_list, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, @SuppressLint("RecyclerView") int position) {
        XzsBean item = list.get(position);
        if (item != null && !TextUtils.isEmpty(item.getNr())) {
            holder.mText.setText(ZiMuArr[item.getXh()] + ". " + item.getNr());
            holder.linText.setVisibility(View.VISIBLE);
        } else {
            holder.linText.setVisibility(View.GONE);
        }
        if (!TextUtils.isEmpty(answerType)) {
            if (answerType.equals("1")) {
                //单选
                holder.mEdit.setVisibility(View.GONE);
                if (TextUtils.equals(item.getXh() + "", das)) {
                    holder.mImgCheck.setImageResource(R.mipmap.ic_hc_yuan_t);
                    if (TextUtils.equals("1", item.getLx())) {
                        holder.mEdit.setVisibility(View.VISIBLE);
                        holder.mEdit.setText(item.getQthd());
                    } else {
                        holder.mEdit.setVisibility(View.GONE);
                    }
                } else {
                    holder.mImgCheck.setImageResource(R.mipmap.ic_hc_yuan);
                }

            } else if (answerType.equals("2")) {
                holder.mEdit.setVisibility(View.GONE);
                holder.mImgCheck.setImageResource(R.mipmap.ic_ddx_t);
                //多选
                if (!TextUtils.isEmpty(das) && das.contains(item.getXh() + "")) {
                    holder.mImgCheck.setImageResource(R.mipmap.ic_hc_gou_t);
                    if (TextUtils.equals("1", item.getLx())) {
                        holder.mEdit.setVisibility(View.VISIBLE);
                        holder.mEdit.setText(item.getQthd());
                    } else {
                        holder.mEdit.setVisibility(View.GONE);
                    }
                } else {
                    holder.mImgCheck.setImageResource(R.mipmap.ic_hc_gou);
                    holder.mEdit.setVisibility(View.GONE);
                }
            } else {
                holder.mEdit.setVisibility(View.GONE);
            }
        } else {
            holder.mEdit.setVisibility(View.GONE);
        }

    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    class Holder extends RecyclerView.ViewHolder {
        protected ImageView mImgCheck;
        protected TextView mText, tv_title;
        protected TextView mEdit;
        private LinearLayout linText;

        Holder(@NonNull View itemView) {
            super(itemView);
            mImgCheck = (ImageView) itemView.findViewById(R.id.mCheck);
            mText = (TextView) itemView.findViewById(R.id.mText);
            mEdit = (TextView) itemView.findViewById(R.id.mEdit);
            tv_title = (TextView) itemView.findViewById(R.id.tv_title);
            linText = (LinearLayout) itemView.findViewById(R.id.lin_text);
        }
    }

}
