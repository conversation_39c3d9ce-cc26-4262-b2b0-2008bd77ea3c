package com.linggan.jd831.adapter;

import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.CarListEntity;
import com.linggan.jd831.ui.works.car.CarInfoActivity;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XImageUtils;
import com.makeramen.roundedimageview.RoundedImageView;

/**
 * 类  名：车辆列表
 * 作  者：ZXB
 * 说  明：
 * 时  间：2024/4/8 13:11
 * 版  权：LGKJ
 */
public class CarListHolder extends IViewHolder {

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_car_list;
    }


    public class ViewHolder extends XViewHolder<CarListEntity> {
        private RoundedImageView mIvCar;
        private TextView mTvCph;
        private TextView mTvCllx;
        private TextView mTvYyzt;
        private LinearLayout lin1;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mIvCar = view.findViewById(R.id.iv_car);
            mTvCph = view.findViewById(R.id.tv_cph);
            mTvCllx = view.findViewById(R.id.tv_cllx);
            mTvYyzt = view.findViewById(R.id.tv_yyzt);
            lin1 = view.findViewById(R.id.lin1);
        }

        @Override
        protected void onBindData(final CarListEntity itemData) {
            if (!TextUtils.isEmpty(itemData.getClZp())) {
                XImageUtils.load(mContext, itemData.getClZp(), mIvCar, R.mipmap.ic_car_head);
            } else {
                mIvCar.setImageResource(R.mipmap.ic_car_head);
            }
            mTvCph.setText(itemData.getCph());
            mTvCllx.setText(StrUtils.getDev(itemData.getLx(), "-"));
            if (itemData.getYyzZt() != null) {//0正常 1即将到期 2已过期
                lin1.setVisibility(View.VISIBLE);
                StrUtils.setYyZtUI(itemData.getYyzZt().getCode(), itemData.getYyzZt().getName(), mTvYyzt);
            } else {
                lin1.setVisibility(View.GONE);
            }
            itemView.setOnClickListener(v -> {
                Bundle bundle = new Bundle();
                bundle.putString("bh", itemData.getBh() + "");
                XIntentUtil.redirectToNextActivity(mContext, CarInfoActivity.class, bundle);
            });
        }
    }
}


