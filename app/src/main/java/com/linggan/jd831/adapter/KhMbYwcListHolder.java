package com.linggan.jd831.adapter;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.KhMbEntity;
import com.linggan.jd831.bean.ZiDianEntity;
import com.linggan.jd831.ui.works.chat.ChatActivity;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.widget.BaseZiDianDialog;

import java.util.ArrayList;

/**
 * 类  名：考核--目标任务
 * 作  者：ZXB
 * 说  明：
 * 时  间：2024/4/18 11:51
 * 版  权：LGKJ
 */
public class KhMbYwcListHolder extends IViewHolder {

    private String dwdm;

    public KhMbYwcListHolder(String dwdm) {
        this.dwdm = dwdm;
    }

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_kh_mbrw_paga;
    }

    public class ViewHolder extends XViewHolder<KhMbEntity> {
        private TextView mTvTitle;
        private TextView mTvZq;
        private TextView mTvMbmc;
        private TextView mBtPost;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mTvTitle = view.findViewById(R.id.tv_title);
            mTvZq = view.findViewById(R.id.tv_zq);
            mTvMbmc = view.findViewById(R.id.tv_mbmc);
            mBtPost = view.findViewById(R.id.bt_post);
        }

        @Override
        protected void onBindData(final KhMbEntity itemData) {

            mTvTitle.setText(itemData.getRwmc());
            mTvZq.setText(itemData.getRwzq());
            mTvMbmc.setText(itemData.getMbmc());
            mBtPost.setOnClickListener(v -> {
                FactoryUtils.getKhGaAndMbLxrList(mContext, dwdm, result -> {
                    BaseZiDianDialog baseZiDianDialog = new BaseZiDianDialog((Activity) mContext, (ArrayList<ZiDianEntity>) result);
                    baseZiDianDialog.setOnClickDataListener(() -> {
                        Bundle bundle = new Bundle();
                        bundle.putString("bh", baseZiDianDialog.getData().getId());
                        //0工作人员发给吸毒人员；1吸毒人员发给工作人员；2工作人员发给工作人员
                        bundle.putString("fsLx", "0");
                        bundle.putString("name", baseZiDianDialog.getData().getYhXm());
//                        bundle.putString("img", baseZiDianDialog.getXp());
                        XIntentUtil.redirectToNextActivity(mContext, ChatActivity.class, bundle);
                    });
                    baseZiDianDialog.show();
                });
            });
        }
    }
}


