package com.linggan.jd831.adapter;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.TalkListEntity;
import com.linggan.jd831.bean.XysListEntity;
import com.linggan.jd831.ui.works.talk.TalkInfoActivity;
import com.linggan.jd831.ui.works.xieyi.XieYiShuInfoActivity;
import com.linggan.jd831.utils.StrUtils;

/**
 * 类  名：协议书列表
 * 作  者：ZXB
 * 说  明：
 * 时  间：2024/4/28 11:51
 * 版  权：LGKJ
 */
public class XieYiShuListHolder extends IViewHolder {


    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_xieyishu_list;
    }

    public class ViewHolder extends XViewHolder<XysListEntity> {
        private TextView mTvTitle;
        private ImageView mIvSpTag;
        private TextView mTvTime;
        private TextView mTvScr;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mTvTitle = view.findViewById(R.id.tv_title);
            mIvSpTag = view.findViewById(R.id.iv_sp_tag);
            mTvTime = view.findViewById(R.id.tv_time);
            mTvScr = view.findViewById(R.id.tv_scr);
        }

        @Override
        protected void onBindData(final XysListEntity itemData) {

            mTvTitle.setText(itemData.getXysmc());
            mTvTime.setText(itemData.getLrsj());
            mTvScr.setText(itemData.getBjrxm());
            //0审批通过；2审批中；1审批不通过
            StrUtils.showSpStatusTag(itemData.getApproval(), mIvSpTag);

            itemView.setOnClickListener(v -> {
                Bundle bundle = new Bundle();
                bundle.putString("bh", itemData.getBh() + "");
                bundle.putString("pid", itemData.getXyrbh() + "");
                XIntentUtil.redirectToNextActivity(mContext, XieYiShuInfoActivity.class, bundle);
            });
        }
    }
}


