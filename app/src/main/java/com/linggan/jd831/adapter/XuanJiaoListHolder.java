package com.linggan.jd831.adapter;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.XuanJiaoListEntity;
import com.linggan.jd831.ui.works.visit.XuanJiaoInfoActivity;
import com.linggan.jd831.ui.works.visit.XuanJiaoInfoNewActivity;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.UserInfoUtils;

/**
 * 类  名：宣教列表
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class XuanJiaoListHolder extends IViewHolder {

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_xuan_jiao_list;
    }


    public class ViewHolder extends XViewHolder<XuanJiaoListEntity> {

        private TextView mTvTitle;
        private ImageView mIvSpTag;
        private TextView mTvNum;
        private TextView mTvLx;
        private TextView mTvTime;
        private TextView mTvAddress;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mTvTitle = view.findViewById(R.id.tv_title);
            mIvSpTag = view.findViewById(R.id.iv_sp_tag);
            mTvNum = view.findViewById(R.id.tv_num);
            mTvLx = view.findViewById(R.id.tv_lx);
            mTvTime = view.findViewById(R.id.tv_time);
            mTvAddress = view.findViewById(R.id.tv_address);
        }

        @Override
        protected void onBindData(final XuanJiaoListEntity itemData) {
            mTvTitle.setText(itemData.getZt());
            mTvNum.setText(StrUtils.getDev(itemData.getCyrs(), "0"));
            if (itemData.getLx() != null) {
                mTvLx.setText(itemData.getLx().getName());
            } else {
                mTvLx.setText("");
            }
            mTvAddress.setText(StrUtils.getDev(itemData.getDz(), "无"));
            mTvTime.setText(itemData.getKssj() + " 至 " + itemData.getJssj());

            //0审批通过；2审批中；1审批不通过
//            StrUtils.showSpStatusTag(itemData.getApproval(),mIvSpTag);
            itemView.setOnClickListener(v -> {
                Bundle bundle = new Bundle();
                bundle.putString("bh", itemData.getBh() + "");
                bundle.putString("zt", itemData.getZt() + "");
                bundle.putString("ewm", itemData.getEwmlj() + "");
                bundle.putString("num", itemData.getCyrs() + "");
                String xzqhdm = UserInfoUtils.getUserInfo().getYhXzqhdm();
                if (!TextUtils.isEmpty(xzqhdm) && xzqhdm.startsWith("510121")) {
                    XIntentUtil.redirectToNextActivity(mContext, XuanJiaoInfoNewActivity.class, bundle);
                } else {
                    XIntentUtil.redirectToNextActivity(mContext, XuanJiaoInfoActivity.class, bundle);
                }
            });
        }
    }
}


