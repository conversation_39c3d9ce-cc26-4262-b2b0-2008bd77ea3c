package com.linggan.jd831.adapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.net.XResultPageData;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;

/**
 * 类  名：周期展示通过holder
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class HcTimeTopHolder extends IViewHolder {

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_time_top;
    }

    public class ViewHolder extends XViewHolder<XResultPageData.ExtraBean> {
        private TextView mTvTimeTitle;
        private TextView mTvTime;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mTvTimeTitle = view.findViewById(R.id.tv_time_title);
            mTvTime = view.findViewById(R.id.tv_time);
        }

        @Override
        protected void onBindData(final XResultPageData.ExtraBean itemData) {
            if (itemData != null && !TextUtils.isEmpty(itemData.getStartTime())) {
                mTvTime.setText(itemData.getStartTime() + " 至 " + itemData.getEndTime());
                mTvTimeTitle.setText("应核查周期：");
            } else {
                mTvTimeTitle.setText("当前无核查周期");
            }
        }
    }
}


