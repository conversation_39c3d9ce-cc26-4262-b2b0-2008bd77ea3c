package com.linggan.jd831.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.PagaListEntity;
import com.linggan.jd831.ui.works.task.TaskCareTransferActivity;

/**
 * 类  名：任务中心
 * 作  者：ZXB
 * 说  明：平安关爱
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class TaskCenPagaHolder extends IViewHolder {


    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_task_cen_paga_list;
    }

    public class ViewHolder extends XViewHolder<PagaListEntity> {
        private ImageView mIvTag;
        private TextView mTvTitle;
        private TextView mTvTag;
        private TextView mTvDay;
        private TextView mTvName;
        private TextView mTvType;
        private TextView mTvKsTime;
        private TextView mTvEndTime;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mIvTag = view.findViewById(R.id.iv_tag);
            mTvTitle = view.findViewById(R.id.tv_title);
            mTvTag = view.findViewById(R.id.tv_tag);
            mTvDay = view.findViewById(R.id.tv_day);
            mTvName = view.findViewById(R.id.tv_name);
            mTvType = view.findViewById(R.id.tv_type);
            mTvKsTime = view.findViewById(R.id.tv_ks_time);
            mTvEndTime = view.findViewById(R.id.tv_end_time);
        }

        @Override
        protected void onBindData(final PagaListEntity itemData) {
            mTvTitle.setText(itemData.getRwmc());
            if (itemData.getBfCx() != null) {
                mTvName.setText(itemData.getBfCx().getBfCxMc());
            } else {
                mTvName.setText("");
            }
            mTvKsTime.setText(itemData.getZbsj());
            mTvEndTime.setText(itemData.getTjrdw());

            mTvDay.setText("已进行：" + itemData.getYjxts() + "天");
            itemView.setOnClickListener(v -> {
                XIntentUtil.redirectToNextActivity(mContext, TaskCareTransferActivity.class, "gaBh", itemData.getGaBh());
            });
        }
    }
}


