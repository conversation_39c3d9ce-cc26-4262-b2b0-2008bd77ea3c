package com.linggan.jd831.adapter;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.PunishListEntity;
import com.linggan.jd831.bean.ViolationListEntity;
import com.linggan.jd831.ui.works.punish.PunishInfoActivity;
import com.linggan.jd831.ui.works.punish.ViolationInfoActivity;
import com.linggan.jd831.utils.StrUtils;

/**
 * 类  名：管控措施列表
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class ViolationListHolder extends IViewHolder {


    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_violation_list;
    }


    class ViewHolder extends XViewHolder<ViolationListEntity> {
        private TextView mTvTitle;
        private ImageView mIvSpTag;
        private TextView mTvInfo;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mTvTitle = view.findViewById(R.id.tv_title);
            mIvSpTag = view.findViewById(R.id.iv_sp_tag);
            mTvInfo = view.findViewById(R.id.tv_info);
        }

        @Override
        protected void onBindData(final ViolationListEntity itemData) {
            //0审批通过；2审批中；1审批不通过
            StrUtils.showSpStatusTag(itemData.getApproval(), mIvSpTag);
            if (itemData.getLxqf() != null) {
                if (itemData.getLxqf().equals("1")) {
                    mTvTitle.setText("风险管控措施");
                } else if (itemData.getLxqf().equals("2")) {
                    mTvTitle.setText("脱失脱管措施");
                }
            }
            mTvInfo.setText(itemData.getGksj());
            itemView.setOnClickListener(v -> {
                Bundle bundle = new Bundle();
                bundle.putString("id", itemData.getBh() + "");
                bundle.putString("pid", itemData.getXyrbh() + "");
                XIntentUtil.redirectToNextActivity(mContext, ViolationInfoActivity.class, bundle);
            });
        }
    }
}


