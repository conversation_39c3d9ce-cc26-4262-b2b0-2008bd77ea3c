package com.linggan.jd831.adapter;

import android.graphics.Color;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.PeoStatusEntity;
import com.linggan.jd831.ui.user.edit.PeopleStatusInfoActivity;
import com.linggan.jd831.utils.StrUtils;

/**
 * 类  名：人员状态列表
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class PeoStatusListHolder extends IViewHolder {


    private String peoID;

    public PeoStatusListHolder(String peoID) {
        this.peoID = peoID;
    }

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_peo_status_list;
    }


    public class ViewHolder extends XViewHolder<PeoStatusEntity> {

        private TextView mTvTitle;
        private TextView mTvTime;
        private LinearLayout mLin;
        private TextView mTvInfo;
        private LinearLayout mLin1;
        private CardView mCardview;
        private TextView mTvJsTitle;
        private ImageView mIvSpTag;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mTvTitle = view.findViewById(R.id.tv_title);
            mTvTime = view.findViewById(R.id.tv_time);
            mLin = view.findViewById(R.id.lin);
            mTvInfo = view.findViewById(R.id.tv_info);
            mLin1 = view.findViewById(R.id.lin1);
            mCardview = view.findViewById(R.id.cardView);
            mTvJsTitle = view.findViewById(R.id.tv_js_title);
            mIvSpTag = view.findViewById(R.id.iv_sp_tag);
        }

        @Override
        protected void onBindData(final PeoStatusEntity itemData) {
            if (itemData.getEjztlx() != null) {
                mTvTitle.setText(itemData.getEjztlx().getName());
                if (itemData.getEjztlx().getName().equals("死亡")) {
                    mTvTitle.setTextColor(Color.parseColor("#DD392F"));
                    mLin.setVisibility(View.GONE);
                    mTvJsTitle.setTextColor(Color.parseColor("#262626"));
                    mTvInfo.setTextColor(Color.parseColor("#262626"));
                } else {
                    mTvTitle.setTextColor(Color.parseColor("#262626"));
                    mTvInfo.setTextColor(Color.parseColor("#8C8C8C"));
                    mTvJsTitle.setTextColor(Color.parseColor("#8C8C8C"));
                    mLin.setVisibility(View.VISIBLE);
                }
            }
            mTvTime.setText(itemData.getKssj());
            mTvInfo.setText(StrUtils.getDev(itemData.getJssj(), "暂未结束"));

            //0审批通过；2审批中；1审批不通过
            StrUtils.showSpStatusTag(itemData.getApproval(), mIvSpTag);

            itemView.setOnClickListener(v -> {
                Bundle bundle = new Bundle();
                bundle.putString("id", itemData.getEjztbh() + "");
                bundle.putString("approval", itemData.getApproval() + "");
                bundle.putString("kssj", itemData.getKssj());
                bundle.putString("peoId", peoID);
                if (itemData.getEjztlx() != null) {
                    bundle.putString("code", itemData.getEjztlx().getCode() + "");
                    bundle.putString("title", itemData.getEjztlx().getName() + "");
                    if (!itemData.getEjztlx().getCode().equals("14")) {
                        XIntentUtil.redirectToNextActivity(mContext, PeopleStatusInfoActivity.class, bundle);
                    }
                } else {
                    XIntentUtil.redirectToNextActivity(mContext, PeopleStatusInfoActivity.class, bundle);
                }
            });
        }
    }
}


