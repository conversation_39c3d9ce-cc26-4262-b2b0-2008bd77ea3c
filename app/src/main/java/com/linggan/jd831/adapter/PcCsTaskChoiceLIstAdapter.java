package com.linggan.jd831.adapter;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.PlaceAreaTaskEntity;
import com.linggan.jd831.ui.works.place.PaiChaPlaceTaskInfoActivity;

import java.util.List;

/**
 * 类  名：排查场所列表
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class PcCsTaskChoiceLIstAdapter extends RecyclerView.Adapter<PcCsTaskChoiceLIstAdapter.Holder> {


    private int last = -1;
    private List<PlaceAreaTaskEntity> list;
    private Context context;
    private OnItemClickListener onItemClickListener;
    private String infoHui;

    public PcCsTaskChoiceLIstAdapter(Context context, List<PlaceAreaTaskEntity> list, String infoHui) {
        this.list = list;
        this.context = context;
        this.infoHui = infoHui;
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_pccf_task, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        PlaceAreaTaskEntity itemData = list.get(position);

        holder.mTvTitle.setText(itemData.getRwmc());
        holder.mTvInfo.setText("任务描述：" + itemData.getRwms());
        holder.mTvTime.setText("任务时间：" + itemData.getRwkssj() + "-" + itemData.getRwjssj());

        if (last == position) {
            holder.mIvChoice.setImageResource(R.mipmap.ic_choice);
        } else {
            holder.mIvChoice.setImageResource(R.mipmap.ic_kong);
        }
        holder.mIvChoice.setOnClickListener(v -> {
            onItemClickListener.onItemClick(itemData, position);
        });
        holder.mIvChoice.setOnClickListener(v -> {
            onItemClickListener.onItemClick(itemData, position);
        });

        holder.linInfo.setOnClickListener(v -> {
            //详情。
            Bundle bundle = new Bundle();
            bundle.putSerializable("rwms", itemData);
            XIntentUtil.redirectToNextActivity(context, PaiChaPlaceTaskInfoActivity.class, bundle);
        });
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    class Holder extends RecyclerView.ViewHolder {
        ImageView mIvChoice;
        TextView mTvTitle, mTvInfo, mTvTime;
        View view;
        private LinearLayout linInfo;

        Holder(@NonNull View itemView) {
            super(itemView);
            view = itemView;
            mIvChoice = itemView.findViewById(R.id.iv_choice);
            mTvTitle = itemView.findViewById(R.id.tv_title);
            mTvInfo = itemView.findViewById(R.id.tv_info);
            mTvTime = itemView.findViewById(R.id.tv_time);
            linInfo = itemView.findViewById(R.id.lin_info);
        }
    }

    public void setLast(int last) {
        this.last = last;
        notifyDataSetChanged();
    }

    public interface OnItemClickListener {
        void onItemClick(PlaceAreaTaskEntity menu, int position);
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }
}


