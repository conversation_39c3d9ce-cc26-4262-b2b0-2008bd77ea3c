package com.linggan.jd831.adapter;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.PingGuListEntity;
import com.linggan.jd831.ui.works.jidu.PingGuInfoActivity;
import com.linggan.jd831.utils.StrUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 类  名：评估
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class PingGuListHolder extends IViewHolder {

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_pinggu_list;
    }

    public class ViewHolder extends XViewHolder<PingGuListEntity> {

        private TextView mTvTitle;
        private TextView mTvTime;
        private TextView mTvInfo;
        private ImageView mIvTag, mIvSpTag;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mTvTitle = view.findViewById(R.id.tv_title);
            mTvTime = view.findViewById(R.id.tv_time);
            mTvInfo = view.findViewById(R.id.tv_info);
            mIvTag = view.findViewById(R.id.iv_tag);
            mIvSpTag = view.findViewById(R.id.iv_sp_tag);
        }

        @Override
        protected void onBindData(final PingGuListEntity itemData) {
            if (itemData.getFxdjList() != null && itemData.getFxdjList().size() > 0) {
                List<String> stringList = new ArrayList<>();
                for (int i = 0; i < itemData.getFxdjList().size(); i++) {
                    stringList.add(itemData.getFxdjList().get(i).getMc());
                }
                mTvTitle.setText(StrUtils.listToString(stringList));
            } else {
                mTvTitle.setText("");
            }
            if (!TextUtils.isEmpty(itemData.getFxdj())) {
                if (itemData.getFxdj().startsWith("高")) {
                    mIvTag.setImageResource(R.mipmap.ic_tag_gao);
                } else if (itemData.getFxdj().startsWith("特高")) {
                    mIvTag.setImageResource(R.mipmap.ic_tag_te);
                } else if (itemData.getFxdj().startsWith("中")) {
                    mIvTag.setImageResource(R.mipmap.ic_tag_zhong);
                } else {
                    mIvTag.setImageResource(R.mipmap.ic_tag_low);
                }
            }
            //0审批通过；2审批中；1审批不通过
            StrUtils.showSpStatusTag(itemData.getApproval(), mIvSpTag);

            if (!TextUtils.isEmpty(itemData.getPgQsrq())) {
                mTvTime.setText(itemData.getPgQsrq() + " 至 " + itemData.getPgZzrq());
            } else {
                mTvTime.setText("非周期评估");
            }
            mTvInfo.setText(itemData.getPgsj());
            itemView.setOnClickListener(v -> {
                Bundle bundle = new Bundle();
                bundle.putString("id", itemData.getBh() + "");
                bundle.putString("pid", itemData.getXyrbh() + "");
                XIntentUtil.redirectToNextActivity(mContext, PingGuInfoActivity.class, bundle);
            });
        }
    }
}


