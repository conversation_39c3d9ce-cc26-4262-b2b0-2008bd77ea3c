package com.linggan.jd831.adapter;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;

import com.lgfzd.base.utils.XIntentUtil;
import com.linggan.jd831.R;
import com.linggan.jd831.ui.common.PhotoActivity;
import com.linggan.jd831.ui.common.VideoActivity;
import com.linggan.jd831.utils.XImageUtils;
import com.makeramen.roundedimageview.RoundedImageView;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 图片添加适配器
 */
public class ImaVideoShowStringAdapter extends BaseAdapter {

    private List<String> list;

    public ImaVideoShowStringAdapter(List<String> list) {
        this.list = list;
    }

    @Override
    public int getCount() {
        return list == null ? 0 : list.size();
    }

    @Override
    public Object getItem(int i) {
        return list.get(i);
    }

    @Override
    public long getItemId(int i) {
        return i;
    }

    @Override
    public View getView(int position, View view, ViewGroup viewGroup) {
        Holder holder;
        if (view == null) {
            holder = new Holder();
            view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.item_image_show, viewGroup, false);
            holder.image = view.findViewById(R.id.image_add);
            holder.imagePlay = view.findViewById(R.id.iv_play);
            view.setTag(holder);
        } else {
            holder = (Holder) view.getTag();
        }
        if (!list.get(position).endsWith(".mp4")) {
            XImageUtils.load(viewGroup.getContext(), list.get(position), holder.image);
            holder.imagePlay.setVisibility(View.GONE);
            view.setOnClickListener(v -> {
                if (list != null && !list.isEmpty()) {
                    List<String> imageList = new ArrayList<String>();
                    for (int k = 0; k < list.size(); k++) {
                        imageList.add(list.get(k));
                    }
                    Bundle bundle = new Bundle();
                    bundle.putSerializable("info", (Serializable) imageList);
                    bundle.putInt("pos", position);
                    XIntentUtil.redirectToNextActivity(viewGroup.getContext(), PhotoActivity.class, bundle);
                }
            });
        } else {
            XImageUtils.displayVideoThumbnailOld(viewGroup.getContext(), list.get(position), holder.image);
            holder.imagePlay.setVisibility(View.VISIBLE);
            view.setOnClickListener(v -> {
                XIntentUtil.redirectToNextActivity(viewGroup.getContext(), VideoActivity.class, "path", list.get(position));
            });
        }
        return view;
    }

    private class Holder {
        RoundedImageView image;
        ImageView imagePlay;
    }
}
