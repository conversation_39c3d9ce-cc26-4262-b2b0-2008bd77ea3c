package com.linggan.jd831.adapter;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.lgfzd.base.utils.XDateUtil;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.MessageEntity;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.ui.common.FileShowActivity;
import com.linggan.jd831.ui.common.PhotoActivity;
import com.linggan.jd831.ui.common.VideoActivity;
import com.linggan.jd831.ui.works.chat.VideoChatActivity;
import com.linggan.jd831.ui.works.chat.VoiceChatActivity;
import com.linggan.jd831.ui.works.chat.ZiLiaoInfoActivity;
import com.linggan.jd831.utils.FileTypeConstants;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XImageUtils;
import com.makeramen.roundedimageview.RoundedImageView;

import java.util.List;

/**
 * 聊天信息
 */
public class MessageListAdapter extends RecyclerView.Adapter<MessageListAdapter.ViewHolder> {

    private List<MessageEntity> list;
    private Context context;
    private String name, headImg, bh;

    public MessageListAdapter(Context context, List<MessageEntity> list, String bh, String name, String headImg) {
        this.context = context;
        this.list = list;
        this.name = name;
        this.bh = bh;
        this.headImg = headImg;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(context).inflate(R.layout.item_message_chat_ls, parent, false));
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        MessageEntity message = list.get(position);
        if (message != null) {
            if (position > 0) {
                String timeS = XDateUtil.getIntervalTime(list.get(position - 1).getDate(), message.getDate());
                if (!TextUtils.isEmpty(timeS)) {
                    holder.mTvTime.setText(timeS);
                    holder.mTvTime.setVisibility(View.VISIBLE);
                } else {
                    holder.mTvTime.setVisibility(View.GONE);
                }
            } else {
                holder.mTvTime.setVisibility(View.GONE);
            }
        } else {
            holder.mTvTime.setVisibility(View.GONE);
        }
        //工作人员发的消息
        String userId = UserInfoUtils.getUserInfo().getUserId();
        if (!TextUtils.isEmpty(message.getSender()) && userId.equals(message.getSender())) {
            //当前账号自己
            if (!TextUtils.isEmpty(UserInfoUtils.getUserInfo().getUserName())) {
                if (UserInfoUtils.getUserInfo().getUserName().length() > 2) {
                    holder.mTvPicNameRight.setText(UserInfoUtils.getUserInfo().getUserName().substring(0, 2));
                } else {
                    holder.mTvPicNameRight.setText(UserInfoUtils.getUserInfo().getUserName());
                }
            } else {
                holder.mTvPicNameRight.setText("");
            }
            holder.rightImage.setVisibility(View.GONE);
            holder.linFileLeft.setVisibility(View.GONE);
            setInfo(context, message.getContentType(), message.getContent(), message.getJdWd(), message.getRecordZl(), holder.rightContent, holder.right_image_con, holder.mIvPlayRight, holder.mLinRightDw, holder.mRightContentDw, holder.mRightImageDw, holder.mFeImgVideoRight, holder.ivRightContent, 2, bh, headImg, name, holder.tvLxkf, holder.rightContent1, holder.linFile, holder.tvFileName, holder.tvFileSize, holder.ivFileType);
            holder.rightBubble.setVisibility(View.VISIBLE);
            holder.leftBubble.setVisibility(View.GONE);
            
            // 为发送的消息添加已读/未读状态和时间显示
            if (holder.tvMessageStatus != null) {
                // 将原来使用文本的方式改为使用drawable方式
                if (!TextUtils.isEmpty(message.getSfYd())) {
                    holder.tvMessageStatus.setText(""); // 清空文本
                    if ("0".equals(message.getSfYd())) {
                        // 显示已读图标
                        holder.tvMessageStatus.setCompoundDrawablesWithIntrinsicBounds(
                                ContextCompat.getDrawable(context, R.drawable.message_read), null, null, null);
                    } else if ("1".equals(message.getSfYd())) {
                        // 显示未读图标
                        holder.tvMessageStatus.setCompoundDrawablesWithIntrinsicBounds(
                                ContextCompat.getDrawable(context, R.drawable.message_unread), null, null, null);
                    }
                    holder.tvMessageStatus.setCompoundDrawablePadding(5); // 设置图标间距
                    holder.tvMessageStatus.setVisibility(View.VISIBLE);
                } else {
                    holder.tvMessageStatus.setVisibility(View.GONE);
                }
            }
        } else {
            //聊天好友
            holder.linFile.setVisibility(View.GONE);
            setInfo(context, message.getContentType(), message.getContent(), message.getJdWd(), message.getRecordZl(), holder.leftContent, holder.left_image_con, holder.mIvPlayLeft, holder.mLinLeftDw, holder.mLeftContentDw, holder.mLeftImageDw, holder.mFeImgVideoLeft, holder.ivLeftContent, 1, bh, headImg, name, holder.tvLxkf, holder.rightContent1, holder.linFileLeft, holder.tvFileNameLeft, holder.tvFileSizeLeft, holder.ivFileTypeLeft);
            holder.rightBubble.setVisibility(View.GONE);
            holder.leftBubble.setVisibility(View.VISIBLE);
            if (TextUtils.isEmpty(headImg)) {
                holder.leftImage.setVisibility(View.GONE);
                holder.mLinHeadLeft.setVisibility(View.VISIBLE);
                if (!TextUtils.isEmpty(name)) {
                    if (name.length() > 2) {
                        holder.mTvPicNameLeft.setText(name.substring(0, 2));
                    } else {
                        holder.mTvPicNameLeft.setText(name);
                    }
                } else {
                    holder.mTvPicNameLeft.setText("");
                }
            } else {
                holder.leftImage.setVisibility(View.VISIBLE);
                holder.mLinHeadLeft.setVisibility(View.GONE);
                XImageUtils.load(context, headImg, holder.leftImage);
            }
            
            // 接收的消息不显示状态
            if (holder.tvMessageStatus != null) {
                holder.tvMessageStatus.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }


    public static class ViewHolder extends RecyclerView.ViewHolder {
        RoundedImageView leftImage, rightImage;
        TextView leftName;
        TextView leftContent;
        LinearLayout leftBubble;
        TextView rightName;
        TextView rightContent, rightContent1;
        ImageView left_image_con, right_image_con;
        LinearLayout rightBubble;
        ImageView mIvPlayLeft;
        ImageView mIvPlayRight, ivRightContent, ivLeftContent;
        TextView mTvPicNameLeft;
        LinearLayout mLinHeadLeft;
        TextView mTvPicNameRight;
        LinearLayout mLinHeadRight;
        TextView mLeftContentDw;
        ImageView mLeftImageDw;
        LinearLayout mLinLeftDw;
        TextView mRightContentDw, mTvTime;
        ImageView mRightImageDw;
        LinearLayout mLinRightDw;
        FrameLayout mFeImgVideoLeft;
        FrameLayout mFeImgVideoRight;
        //
        LinearLayout linText, linTextRight;
        TextView tvDbBt;
        ImageView ivDbZt;
        RecyclerView recycle;
        View views;
        TextView btCancel;
        TextView btSure;
        LinearLayout linDi;
        LinearLayout linDb;
        TextView tvDbBtWc;
        TextView tvWc;
        RecyclerView recycleWc;
        TextView tvDbWcDb;
        TextView tvDbWcDbNr;
        LinearLayout linDbWc;
        private TextView tvLxkf;
        LinearLayout linFile;
        TextView tvFileName;
        TextView tvFileSize;
        ImageView ivFileType;
        LinearLayout linFileLeft;
        TextView tvFileNameLeft;
        TextView tvFileSizeLeft;
        ImageView ivFileTypeLeft;
        // 添加消息状态文本控件
        TextView tvMessageStatus;

        public ViewHolder(View itemView) {
            super(itemView);
            leftImage = itemView.findViewById(R.id.left_image);
            left_image_con = itemView.findViewById(R.id.left_image_con);
            leftName = itemView.findViewById(R.id.left_name);
            leftContent = itemView.findViewById(R.id.left_content);
            leftBubble = itemView.findViewById(R.id.left_bubble);
            rightName = itemView.findViewById(R.id.right_name);
            rightContent = itemView.findViewById(R.id.right_content);
            rightContent1 = itemView.findViewById(R.id.right_content1);
            rightImage = itemView.findViewById(R.id.right_image);
            rightBubble = itemView.findViewById(R.id.right_bubble);
            right_image_con = itemView.findViewById(R.id.right_image_con);
            mIvPlayLeft = itemView.findViewById(R.id.iv_play_left);
            mIvPlayRight = itemView.findViewById(R.id.iv_play_right);
            this.mTvPicNameLeft = itemView.findViewById(R.id.tv_pic_name_left);
            this.mLinHeadLeft = itemView.findViewById(R.id.lin_head_left);
            this.mTvPicNameRight = itemView.findViewById(R.id.tv_pic_name_right);
            this.mLinHeadRight = itemView.findViewById(R.id.lin_head_right);
            this.mLeftContentDw = itemView.findViewById(R.id.left_content_dw);
            this.mLeftImageDw = itemView.findViewById(R.id.left_image_dw);
            this.mLinLeftDw = itemView.findViewById(R.id.lin_left_dw);
            this.mRightContentDw = itemView.findViewById(R.id.right_content_dw);
            this.mRightImageDw = itemView.findViewById(R.id.right_image_dw);
            this.mLinRightDw = itemView.findViewById(R.id.lin_right_dw);
            this.mFeImgVideoLeft = itemView.findViewById(R.id.fe_img_video_left);
            this.mFeImgVideoRight = itemView.findViewById(R.id.fe_img_video_right);
            this.mTvTime = itemView.findViewById(R.id.tv_time);
            this.ivLeftContent = itemView.findViewById(R.id.iv_left_content);
            this.ivRightContent = itemView.findViewById(R.id.iv_right_content);
            this.tvLxkf = itemView.findViewById(R.id.tv_lxkf);
            //
            this.linText = itemView.findViewById(R.id.lin_text);
            this.linTextRight = itemView.findViewById(R.id.lin_text_right);
            this.tvDbBt = itemView.findViewById(R.id.tv_db_bt);
            this.ivDbZt = itemView.findViewById(R.id.iv_db_zt);
            this.recycle = itemView.findViewById(R.id.recycle);
            this.views = itemView.findViewById(R.id.views);
            this.btCancel = itemView.findViewById(R.id.bt_cancel);
            this.btSure = itemView.findViewById(R.id.bt_sure);
            this.linDi = itemView.findViewById(R.id.lin_di);
            this.linDb = itemView.findViewById(R.id.lin_db);
            this.tvDbBtWc = itemView.findViewById(R.id.tv_db_bt_wc);
            this.tvWc = itemView.findViewById(R.id.tv_wc);
            this.recycleWc = itemView.findViewById(R.id.recycle_wc);
            this.tvDbWcDb = itemView.findViewById(R.id.tv_db_wc_db);
            this.tvDbWcDbNr = itemView.findViewById(R.id.tv_db_wc_db_nr);
            this.linDbWc = itemView.findViewById(R.id.lin_db_wc);
            this.linFile = itemView.findViewById(R.id.lin_file);
            this.tvFileName = itemView.findViewById(R.id.tv_file_name);
            this.tvFileSize = itemView.findViewById(R.id.tv_file_size);
            this.ivFileType = itemView.findViewById(R.id.iv_file_type);
            this.linFileLeft = itemView.findViewById(R.id.lin_file_left);
            this.tvFileNameLeft = itemView.findViewById(R.id.tv_file_name_left);
            this.tvFileSizeLeft = itemView.findViewById(R.id.tv_file_size_left);
            this.ivFileTypeLeft = itemView.findViewById(R.id.iv_file_type_left);
            // 添加消息状态文本控件
            this.tvMessageStatus = itemView.findViewById(R.id.tv_message_status);
        }
    }

    private void setInfo(Context context, String contentType, String info, String la, List<MessageEntity.RecordZlBean> zlBh,
                         TextView textView, ImageView imageView, ImageView playImg, LinearLayout linDw, TextView tvDw,
                         ImageView ivDw, FrameLayout feImgVideo, ImageView ivContent, int from, String bh, String receiverImg,
                         String receiverName, TextView tvLxkf, TextView rightContent1,
                         LinearLayout fileLayout, TextView fileName, TextView fileSize, ImageView fileTypeIcon) {
        //0文本；1图片；2视频；3定位；4RTC
        if (contentType.equals("0")) {
            textView.setVisibility(View.VISIBLE);
            rightContent1.setVisibility(View.GONE);
            ivContent.setVisibility(View.VISIBLE);
            feImgVideo.setVisibility(View.GONE);
            linDw.setVisibility(View.GONE);
            tvLxkf.setVisibility(View.GONE);
            fileLayout.setVisibility(View.GONE);
            textView.setText(info);
            textView.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
            textView.setOnLongClickListener(v -> {
                ClipboardManager clipboardManager = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
                ClipData clipData = ClipData.newPlainText("label", info);
                clipboardManager.setPrimaryClip(clipData);
                XToastUtil.showToast(context, "复制成功");
                return false;
            });
        } else if (contentType.equals("1")) {
            //1图片
            feImgVideo.setVisibility(View.VISIBLE);
            imageView.setVisibility(View.VISIBLE);
            textView.setVisibility(View.GONE);
            rightContent1.setVisibility(View.GONE);
            ivContent.setVisibility(View.GONE);
            playImg.setVisibility(View.GONE);
            linDw.setVisibility(View.GONE);
            tvLxkf.setVisibility(View.GONE);
            fileLayout.setVisibility(View.GONE);
            XImageUtils.load(context, info, imageView);
            imageView.setOnClickListener(v -> {
                context.startActivity(new Intent(context, PhotoActivity.class).putExtra("path", info));
            });
        } else if (contentType.equals("2")) {
            //2视频
            feImgVideo.setVisibility(View.VISIBLE);
            imageView.setVisibility(View.VISIBLE);
            textView.setVisibility(View.GONE);
            rightContent1.setVisibility(View.GONE);
            ivContent.setVisibility(View.GONE);
            playImg.setVisibility(View.VISIBLE);
            linDw.setVisibility(View.GONE);
            tvLxkf.setVisibility(View.GONE);
            fileLayout.setVisibility(View.GONE);
            XImageUtils.displayVideoThumbnailOld(context, info, imageView);
            imageView.setOnClickListener(v -> {
                context.startActivity(new Intent(context, VideoActivity.class).putExtra("path", info));
            });
        } else if (contentType.equals("3")) {
            //3定位
            textView.setVisibility(View.GONE);
            ivContent.setVisibility(View.GONE);
            rightContent1.setVisibility(View.GONE);
            feImgVideo.setVisibility(View.GONE);
            linDw.setVisibility(View.VISIBLE);
            tvLxkf.setVisibility(View.GONE);
            fileLayout.setVisibility(View.GONE);
            tvDw.setText(info);
            textView.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
            String[] jsdArr = la.split(",");
            if (jsdArr != null) {
                XImageUtils.load(context, StrUtils.imgMapUrl(jsdArr[1], jsdArr[0]), ivDw);
            }
        } else if (contentType.equals("4")) {//4Rct
            textView.setVisibility(View.VISIBLE);
            ivContent.setVisibility(View.VISIBLE);
            rightContent1.setVisibility(View.GONE);
            feImgVideo.setVisibility(View.GONE);
            linDw.setVisibility(View.GONE);
            tvLxkf.setVisibility(View.GONE);
            fileLayout.setVisibility(View.GONE);
            textView.setText(info);
            textView.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
        } else if (contentType.equals("5")) {//音频挂断
            textView.setVisibility(View.VISIBLE);
            ivContent.setVisibility(View.VISIBLE);
            rightContent1.setVisibility(View.GONE);
            feImgVideo.setVisibility(View.GONE);
            linDw.setVisibility(View.GONE);
            tvLxkf.setVisibility(View.GONE);
            fileLayout.setVisibility(View.GONE);
            textView.setText(info);
            Drawable drawable = context.getResources().getDrawable(R.mipmap.ic_call_jj);
            if (from == 1) {
                textView.setCompoundDrawablesWithIntrinsicBounds(null, null, drawable, null);
                textView.setOnClickListener(v -> {
                    Bundle bundle = new Bundle();
                    bundle.putString("bh", bh);
                    bundle.putString("name", receiverName);
                    bundle.putString("img", receiverImg);
                    XIntentUtil.redirectToNextActivity(context, VoiceChatActivity.class, bundle);
                });
            } else {
                textView.setCompoundDrawablesWithIntrinsicBounds(drawable, null, null, null);
                textView.setOnClickListener(v -> {
                    Bundle bundle = new Bundle();
                    bundle.putString("bh", bh);
                    bundle.putString("name", receiverName);
                    bundle.putString("img", receiverImg);
                    XIntentUtil.redirectToNextActivity(context, VoiceChatActivity.class, bundle);
                });
            }
        } else if (contentType.equals("6")) {//视频挂断
            textView.setVisibility(View.VISIBLE);
            ivContent.setVisibility(View.VISIBLE);
            rightContent1.setVisibility(View.GONE);
            feImgVideo.setVisibility(View.GONE);
            linDw.setVisibility(View.GONE);
            tvLxkf.setVisibility(View.GONE);
            fileLayout.setVisibility(View.GONE);
            textView.setText(info);
            Drawable drawable = context.getResources().getDrawable(R.mipmap.ic_video_jj);
            if (from == 1) {
                textView.setCompoundDrawablesWithIntrinsicBounds(null, null, drawable, null);
                textView.setOnClickListener(v -> {
                    Bundle bundle = new Bundle();
                    bundle.putString("bh", bh);
                    bundle.putString("name", receiverName);
                    bundle.putString("img", receiverImg);
                    XIntentUtil.redirectToNextActivity(context, VideoChatActivity.class, bundle);
                });
            } else {
                textView.setCompoundDrawablesWithIntrinsicBounds(drawable, null, null, null);
                textView.setOnClickListener(v -> {
                    Bundle bundle = new Bundle();
                    bundle.putString("bh", bh);
                    bundle.putString("name", receiverName);
                    bundle.putString("img", receiverImg);
                    XIntentUtil.redirectToNextActivity(context, VideoChatActivity.class, bundle);
                });
            }
        } else if (contentType.equals("8")) {//联系客服
            textView.setVisibility(View.GONE);
            rightContent1.setVisibility(View.GONE);
            ivContent.setVisibility(View.GONE);
            feImgVideo.setVisibility(View.GONE);
            linDw.setVisibility(View.GONE);
            feImgVideo.setVisibility(View.GONE);
            imageView.setVisibility(View.GONE);
            tvLxkf.setVisibility(View.VISIBLE);
            fileLayout.setVisibility(View.GONE);
        } else if (contentType.equals("7")) {//资料库消息
            textView.setVisibility(View.GONE);
            rightContent1.setVisibility(View.VISIBLE);
            ivContent.setVisibility(View.VISIBLE);
            feImgVideo.setVisibility(View.GONE);
            linDw.setVisibility(View.GONE);
            tvLxkf.setVisibility(View.GONE);
            fileLayout.setVisibility(View.GONE);
            if (zlBh != null && zlBh.size() > 0) {
                rightContent1.setText(zlBh.get(0).getZlkZsBt());
                rightContent1.setOnClickListener(v -> {
                    XIntentUtil.redirectToNextActivity(context, ZiLiaoInfoActivity.class, "bh", zlBh.get(0).getZlkBh());
                });
            }
        } else if (contentType.equals(String.valueOf(FileTypeConstants.FILE_TYPE_PDF)) ||
                contentType.equals(String.valueOf(FileTypeConstants.FILE_TYPE_EXCEL)) ||
                contentType.equals(String.valueOf(FileTypeConstants.FILE_TYPE_WORD)) ||
                contentType.equals(String.valueOf(FileTypeConstants.FILE_TYPE_RAR))) {
            // 文件类型
            textView.setVisibility(View.GONE);
            rightContent1.setVisibility(View.GONE);
            ivContent.setVisibility(View.GONE);
            feImgVideo.setVisibility(View.GONE);
            linDw.setVisibility(View.GONE);
            tvLxkf.setVisibility(View.GONE);
            // 解析文件信息
            OssFileEntity fileInfo = new Gson().fromJson(info, OssFileEntity.class);
            if (fileInfo != null) {
                fileLayout.setVisibility(View.VISIBLE);
                // 根据文件类型设置图标
                int fileIcon = R.drawable.chat_file_word; // 默认图标
                if (contentType.equals(String.valueOf(FileTypeConstants.FILE_TYPE_PDF))) {
                    fileIcon = R.drawable.chat_file_pdf;
                } else if (contentType.equals(String.valueOf(FileTypeConstants.FILE_TYPE_EXCEL))) {
                    fileIcon = R.drawable.chat_file_excel;
                } else if (contentType.equals(String.valueOf(FileTypeConstants.FILE_TYPE_WORD))) {
                    fileIcon = R.drawable.chat_file_word;
                } else if (contentType.equals(String.valueOf(FileTypeConstants.FILE_TYPE_RAR))) {
                    fileIcon = R.drawable.chat_file_rar;
                }

                // 设置文件信息
//                fileName.setMaxLines(2);
//                fileName.setEllipsize(TextUtils.TruncateAt.MIDDLE);
                fileName.setText(fileInfo.getOriginName());
                fileSize.setText(formatFileSize(Long.parseLong(fileInfo.getFileSize())));
                fileTypeIcon.setImageResource(fileIcon);

                // 添加点击事件
                fileLayout.setOnClickListener(v -> {
                    String fileUrl = fileInfo.getSavePath();
                    if (contentType.equals(String.valueOf(FileTypeConstants.FILE_TYPE_RAR))) {
                        XToastUtil.showToast(context, "请前往电脑端查看");
//                        // RAR文件直接用浏览器打开
//                        try {
//                            Intent intent = new Intent(Intent.ACTION_VIEW);
//                            intent.setData(Uri.parse(fileUrl));
//                            context.startActivity(intent);
//                        } catch (Exception e) {
//                            XToastUtil.showToast(context, "打开失败，请检查是否安装浏览器");
//                        }
                    } else {
                        // 使用TBS预览文件
                        Bundle bundle = new Bundle();
                        bundle.putString("title", fileInfo.getOriginName());
                        bundle.putString("url", fileUrl);
                        XIntentUtil.redirectToNextActivity(context, FileShowActivity.class, bundle);
                    }
                });
            }

        } else {
            textView.setVisibility(View.GONE);
            ivContent.setVisibility(View.GONE);
            feImgVideo.setVisibility(View.GONE);
            rightContent1.setVisibility(View.GONE);
            linDw.setVisibility(View.GONE);
            tvLxkf.setVisibility(View.GONE);
            textView.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
            fileLayout.setVisibility(View.GONE);
        }
    }

    // 格式化文件大小
    private String formatFileSize(long fileSize) {
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return fileSize / 1024 + " KB";
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
