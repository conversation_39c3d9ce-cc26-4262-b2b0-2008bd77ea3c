package com.linggan.jd831.adapter;

import android.content.Context;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.CanKuMsgEntity;
import com.linggan.jd831.bean.SjsjNrBean;

import java.util.List;


/**
 * 任务中心-目标任务-数据采集-适配器
 */

public class TaskMuBiaoAddListAdapter extends RecyclerView.Adapter<TaskMuBiaoAddListAdapter.Holder> {

    private List<SjsjNrBean> list;
    private Context context;

    public TaskMuBiaoAddListAdapter(Context context, List<SjsjNrBean> list) {
        this.context = context;
        this.list = list;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_task_mbrw_lv_input, parent, false);
        return new Holder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        SjsjNrBean sjsjNrBean = list.get(position);
        holder.tvTitle.setText(sjsjNrBean.getZlmc());

        if (!TextUtils.isEmpty(sjsjNrBean.getSjsjnr())) {
            holder.mEtName.setText(sjsjNrBean.getSjsjnr());
        } else {
            holder.mEtName.setText("");
        }
        TextWatcher textWatcherName = new TextWatcher() {
            private CharSequence temp;
            private int editStart;
            private int editEnd;

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                temp = s;
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                editStart = holder.mEtName.getSelectionStart();
                editEnd = holder.mEtName.getSelectionEnd();
                String input = s.toString();
                if (!TextUtils.isEmpty(input)) {
                    if (!input.contains(".")) {
                        if (input.length() > 9) {
                            holder.mEtName.setText(input.substring(0, 9));
                            holder.mEtName.setSelection(9);
                        }
                        sjsjNrBean.setSjsjnr(holder.mEtName.getText().toString());
                    } else {
                        int decimalIndex = input.indexOf(".");
                        int decimalPlaces = input.length() - decimalIndex - 1;
                        if (decimalPlaces > 4) {
                            s.delete(editStart - 1, editEnd);//删除限制外的内容
                            int tempSelection = editStart;
                            holder.mEtName.setText(s);//显示限制内的内容
                            holder.mEtName.setSelection(tempSelection);//光标焦点设置在行末
                        }
                        sjsjNrBean.setSjsjnr(holder.mEtName.getText().toString());
                    }
                } else {
                    sjsjNrBean.setSjsjnr("");
                }
            }
        };
        holder.mEtName.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                holder.mEtName.addTextChangedListener(textWatcherName);
            } else {
                holder.mEtName.removeTextChangedListener(textWatcherName);
            }
        });
    }

    public static class Holder extends RecyclerView.ViewHolder {
        TextView tvTitle;
        EditText mEtName;

        Holder(@NonNull View itemView) {
            super(itemView);
            this.tvTitle = itemView.findViewById(R.id.tv_title);
            this.mEtName = itemView.findViewById(R.id.et_xzl);
        }
    }

    public List<SjsjNrBean> getList() {
        return list;
    }


}
