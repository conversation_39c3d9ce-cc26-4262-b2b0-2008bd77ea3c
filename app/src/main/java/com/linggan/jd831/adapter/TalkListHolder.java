package com.linggan.jd831.adapter;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XToastUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.TalkListEntity;
import com.linggan.jd831.ui.works.talk.TalkInfoActivity;
import com.linggan.jd831.utils.StrUtils;

/**
 * 类 名：谈话列表
 * 作 者：ZXB
 * 说 明：
 * 时 间：2021/6/28 11:51
 * 版 权：LGKJ
 */
public class TalkListHolder extends IViewHolder {

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_talk_list;
    }

    public class ViewHolder extends XViewHolder<TalkListEntity> {
        private TextView mTvTitle;
        private TextView mTvTime;
        private TextView mTvInfo;
        private TextView mTvPeople;
        private ImageView mIvSpTag;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mTvTitle = view.findViewById(R.id.tv_title);
            mTvTime = view.findViewById(R.id.tv_time);
            mTvInfo = view.findViewById(R.id.tv_info);
            mTvPeople = view.findViewById(R.id.tv_people);
            mIvSpTag = view.findViewById(R.id.iv_sp_tag);
        }

        @Override
        protected void onBindData(final TalkListEntity itemData) {
            if (itemData.getThlx() != null) {
                mTvTitle.setText(itemData.getThlx().getName());
            }
            if (!TextUtils.isEmpty(itemData.getZqKssj())) {
                mTvTime.setText(itemData.getZqKssj() + " 至 " + itemData.getZqJssj());
            } else {
                mTvTime.setText(itemData.getZq());
            }
            mTvInfo.setText(itemData.getThsj());
            // 处理地址显示逻辑
            String sheng = itemData.getShengQhmc();
            String shi = itemData.getShiQhmc();
            String qu = itemData.getQuQhmc();
            String xz = itemData.getXzQhmc();
            String thdd = itemData.getThdd();

            // 检查第一组（省市区县）是否都为空
            boolean isFirstGroupEmpty = TextUtils.isEmpty(sheng) &&
                    TextUtils.isEmpty(shi) &&
                    TextUtils.isEmpty(qu) &&
                    TextUtils.isEmpty(xz);

            // 如果第一组都为空但谈话地点不为空，只显示谈话地点
            if (isFirstGroupEmpty && !TextUtils.isEmpty(thdd)) {
                mTvPeople.setText(thdd);
            }
            // 如果所有都为空，显示 "-"
            else if (isFirstGroupEmpty && TextUtils.isEmpty(thdd)) {
                mTvPeople.setText("-");
            }
            // 否则拼接所有非空值
            else {
                StringBuilder sb = new StringBuilder();
                if (!TextUtils.isEmpty(sheng))
                    sb.append(sheng);
                if (!TextUtils.isEmpty(shi))
                    sb.append(shi);
                if (!TextUtils.isEmpty(qu))
                    sb.append(qu);
                if (!TextUtils.isEmpty(xz))
                    sb.append(xz);
                if (!TextUtils.isEmpty(thdd))
                    sb.append(thdd);
                mTvPeople.setText(sb.toString());
            }

            // 0审批通过；2审批中；1审批不通过
            StrUtils.showSpStatusTag(itemData.getApproval(), mIvSpTag);

            itemView.setOnClickListener(v -> {
                try {
                    if (itemData.getZjwc().equals("1")) {
                        XToastUtil.showToast(mContext, "直接完成类任务无法查看详情");
                        return;
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }

                Bundle bundle = new Bundle();
                bundle.putString("id", itemData.getBh() + "");
                bundle.putString("pid", itemData.getXyrbh() + "");
                XIntentUtil.redirectToNextActivity(mContext, TalkInfoActivity.class, bundle);
            });
        }
    }
}
