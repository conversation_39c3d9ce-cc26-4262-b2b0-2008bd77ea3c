
package com.linggan.jd831.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.GuanKongListItemSJGKLEntity;
import com.linggan.jd831.constants.CompletionRateType;
import com.linggan.jd831.utils.StrUtils;

import java.util.ArrayList;
import java.util.List;

public class GuanKongSJGKLItemListHolder extends IViewHolder {

    private CompletionRateType code;

    public GuanKongSJGKLItemListHolder() {
        this(null);
    }

    // New constructor that accepts extra data
    public GuanKongSJGKLItemListHolder(CompletionRateType code) {
        this.code = code;
    }

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter, code);
    }

    @Override
    public int getLayout() {
        return R.layout.item_guan_kong_item_list;
    }

    // Method to get all data
    public static List<GuanKongListItemSJGKLEntity> getAllData(RecyclerView recyclerView) {
        List<GuanKongListItemSJGKLEntity> allData = new ArrayList<>();
        if (recyclerView != null && recyclerView.getAdapter() != null) {
            RecyclerView.Adapter adapter = recyclerView.getAdapter();
            int itemCount = adapter.getItemCount();
            for (int i = 0; i < itemCount; i++) {
                RecyclerView.ViewHolder viewHolder = adapter.createViewHolder(recyclerView, adapter.getItemViewType(i));
                adapter.onBindViewHolder(viewHolder, i);
                if (viewHolder instanceof ViewHolder) {
                    GuanKongListItemSJGKLEntity item = ((ViewHolder) viewHolder).getItem();
                    if (item != null) {
                        allData.add(item);
                    }
                }
            }
        }
        return allData;
    }

    public class ViewHolder extends XViewHolder<GuanKongListItemSJGKLEntity> {
        private TextView tvTitle;
        private ImageView ivTips;
        private TextView tvTips;
        private TextView tvSfzTitle;
        private TextView tvSfz;
        private TextView tvPhoneTitle;
        private TextView tvPhone;
        private TextView tvSsxqTitle;
        private TextView tvSsxq;
        private TextView tvZrsgTitle;
        private TextView tvZrsg;

        private GuanKongListItemSJGKLEntity item;
        private CompletionRateType extraData;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter, CompletionRateType type) {
            super(itemView, adapter);
            this.extraData = type;
        }

        @Override
        protected void initView(View view) {
            tvTitle = view.findViewById(R.id.tv_title);
            ivTips = view.findViewById(R.id.iv_tips);
            tvTips = view.findViewById(R.id.tv_tips);
            tvSfzTitle = view.findViewById(R.id.tv_sfz_title);
            tvSfz = view.findViewById(R.id.tv_sfz);
            tvPhoneTitle = view.findViewById(R.id.tv_phone_title);
            tvPhone = view.findViewById(R.id.tv_phone);
            tvSsxqTitle = view.findViewById(R.id.tv_ssxq_title);
            tvSsxq = view.findViewById(R.id.tv_ssxq);
            tvZrsgTitle = view.findViewById(R.id.tv_zrsg_title);
            tvZrsg = view.findViewById(R.id.tv_zrsg);
        }

        @Override
        protected void onBindData(final GuanKongListItemSJGKLEntity itemData) {
            this.item = itemData;
            if (itemData == null) {
                return;
            }

            tvTitle.setText(itemData.getXm());

            try {
                tvTips.setVisibility(View.VISIBLE);
                tvTips.setText(itemData.getGkzt());
            } catch (Exception e) {
                ivTips.setVisibility(View.GONE);
                tvTips.setVisibility(View.GONE);
            }


            tvSfz.setText(StrUtils.getDev(itemData.getZjhm(), ""));

            String phoneNumber = itemData.getLxdh();
            String maskedPhone = "";
            if (phoneNumber != null && phoneNumber.length() >= 7) {
                maskedPhone = phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(phoneNumber.length() - 4);
            } else {
                maskedPhone = phoneNumber;
            }
            tvPhone.setText(maskedPhone);
            tvSsxq.setText(StrUtils.getDev(itemData.getSsxq(), "-"));
            // 负责社工
            tvZrsg.setText(StrUtils.getDev(itemData.getSg(), "-"));
        }

        private void setImageBasedOnCondition(boolean condition) {
            ivTips.setImageResource(condition ? R.drawable.done : R.drawable.incomplete);
            tvTips.setVisibility(View.GONE);
        }

        public GuanKongListItemSJGKLEntity getItem() {
            return item;
        }
    }

}
