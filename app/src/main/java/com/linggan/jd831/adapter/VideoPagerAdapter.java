package com.linggan.jd831.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.WxDyListEntity;

import java.util.List;

/**
 * 视频翻页
 */
public class VideoPagerAdapter extends RecyclerView.Adapter<VideoPagerAdapter.ViewHolder> {

    private Context mContext;
    private int mCurrentPosition = 0;
    private List<WxDyListEntity> videoList;

    public VideoPagerAdapter(Context mContext, List<WxDyListEntity> videoList) {
        this.mContext = mContext;
        this.videoList = videoList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_video_pager, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        WxDyListEntity wxDyListEntity = videoList.get(position);
//        XImageUtils.loadNoCacheFit(mContext, wxDyListEntity.getImgLinks(), holder.ivCover);
//        holder.tvContent.setText(wxDyListEntity.getArticleTitle());
    }

    @Override
    public int getItemCount() {
        return videoList == null ? 0 : videoList.size();
    }


    class ViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivCover;
        private TextView tvContent;

        ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivCover = itemView.findViewById(R.id.ivCover);
            tvContent = itemView.findViewById(R.id.tvContent);
        }
    }

    public void setVideoList(List<WxDyListEntity> videoList, int mCurrentPosition) {
        this.mCurrentPosition = mCurrentPosition;
        this.videoList = videoList;
        notifyDataSetChanged();
    }
}
