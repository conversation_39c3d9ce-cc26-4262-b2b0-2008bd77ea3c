package com.linggan.jd831.adapter;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.MapUtils;
import com.lgfzd.base.utils.XDateUtil;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XPermissionUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.WsCyRwListEntity;
import com.linggan.jd831.ui.works.sewage.TaskWsAddActivity;
import com.linggan.jd831.ui.works.sewage.TaskWsDdAddActivity;
import com.linggan.jd831.ui.works.sewage.TaskWsHhAddActivity;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.StrUtils;

/**
 * 类  名：任务中心--污水列表（机构）
 * 作  者：ZXB
 * 说  明：
 * 时  间：2023/5/4 11:51
 * 版  权：LGKJ
 */
public class TaskWSJgListHolder extends IViewHolder {

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_task_wsjc_jg_list;
    }

    public class ViewHolder extends XViewHolder<WsCyRwListEntity> {
        private ImageView mIvTag;
        private TextView mTvTitle;
        private TextView mTvTag;
        private TextView mTvDay;
        private TextView mTvInfo;
        private TextView mTvKsTime;
        private TextView mTvCyDate, tvSfDd, tvSfjc;
        private LinearLayout mBtDaoHang;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mIvTag = view.findViewById(R.id.iv_tag);
            mTvTitle = view.findViewById(R.id.tv_title);
            mTvTag = view.findViewById(R.id.tv_tag);
            mTvDay = view.findViewById(R.id.tv_day);
            mTvInfo = view.findViewById(R.id.tv_info);
            mTvKsTime = view.findViewById(R.id.tv_ks_time);
            mTvCyDate = view.findViewById(R.id.tv_cy_date);
            mBtDaoHang = view.findViewById(R.id.bt_dao_hang);
            tvSfDd = view.findViewById(R.id.tv_sf_dd);
            tvSfjc = view.findViewById(R.id.tv_sfjc);
        }

        @Override
        protected void onBindData(final WsCyRwListEntity itemData) {
            String mc = StrUtils.getDev(itemData.getCsMc(), itemData.getCydMc());
            if (mc.length() > 13) {
                mTvTitle.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f));
            } else {
                mTvTitle.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT));
            }
            mTvTitle.setText(StrUtils.getDev(itemData.getCsMc(), itemData.getCydMc()));
            mTvInfo.setText("" + StrUtils.getDev(itemData.getCsDz(), itemData.getCydDz()));
            if (itemData.getSfJccy() == 1) {//是否交叉采水（1是；0否）
                tvSfjc.setVisibility(View.VISIBLE);
            } else {
                tvSfjc.setVisibility(View.GONE);
            }
            mTvKsTime.setText("取样日期：" + itemData.getCyRqKssj() + " 至 " + itemData.getCyRqJssj());

//            if (!TextUtils.isEmpty(itemData.getCyRqJssj())) {
//                if (itemData.getWsCyLx() != null) {
//                    //混合采样 --连续采样
//                    if (itemData.getWsCyLx().getCode().equals("2") || itemData.getWsCyLx().getCode().equals("3")) {
//                        mTvKsTime.setText("取样日期：" + itemData.getCyRqKssj());
//                    } else {
//                        mTvKsTime.setText("取样日期：" + itemData.getCyRqKssj() + " 至 " + itemData.getCyRqJssj());
//                    }
//                } else {
//                    mTvKsTime.setText("取样日期：" + itemData.getCyRqKssj() + " 至 " + itemData.getCyRqJssj());
//                }
//            } else {
//                mTvKsTime.setText("取样日期：" + itemData.getCyRqKssj());
//            }

            if (XShareCacheUtils.getInstance().getString("taskws").equals("1")) {
                if (itemData.getJlzxts() != 0) {
                    mTvDay.setText("距离结束：" + itemData.getJlzxts() + "天");
                } else {
                    mTvDay.setText("今日执行");
                }
            } else {
                mTvDay.setText("");
            }
            //是否定点 wzXz: 1，是定点    0 非定点
            if (itemData.getWzXz() == 1) {
                tvSfDd.setText("是否定点：是");
            } else if (itemData.getWzXz() == 0) {
                tvSfDd.setText("是否定点：否");
            } else {
                tvSfDd.setText("是否定点：");
            }
            //采样类型
            if (itemData.getWsCyLx() != null) {
                mTvTag.setVisibility(View.VISIBLE);
                mTvTag.setText(TextUtils.isEmpty(itemData.getWsCyLx().getName()) ? "" : itemData.getWsCyLx().getName().substring(0, 2));
                if (itemData.getWsCyLx().getCode().equals("1")) { //1瞬时采样-1.瞬时 时段取第一项日期截取 时分秒显示   可以多天
                    if (itemData.getQysjd() != null && itemData.getQysjd().size() > 0) {
                        String[] ks = itemData.getQysjd().get(0).getKsSj().split(" ");
                        String[] js = itemData.getQysjd().get(0).getJsSj().split(" ");
                        mTvCyDate.setText(ks[1] + " 至 " + js[1]);
                    } else {
                        mTvCyDate.setText("-");
                    }
                } else if (itemData.getWsCyLx().getCode().equals("2")) {//2混合采样-2.混合 时段取每一项 日期截取 时分秒显示 必定在同一天
                    if (itemData.getQysjd() != null && itemData.getQysjd().size() > 0) {
                        StringBuffer stringBuffer = new StringBuffer();
                        for (int k = 0; k < itemData.getQysjd().size(); k++) {
                            String[] ks = itemData.getQysjd().get(k).getKsSj().split(" ");
                            String[] js = itemData.getQysjd().get(k).getJsSj().split(" ");
                            stringBuffer.append(ks[1] + " 至 " + js[1] + "\n");
                        }
                        if (stringBuffer.length() > 0) {
                            mTvCyDate.setText(stringBuffer.substring(0, stringBuffer.length() - 1));
                        } else {
                            mTvCyDate.setText("-");
                        }
                    } else {
                        mTvCyDate.setText("-");
                    }
                } else if (itemData.getWsCyLx().getCode().equals("3")) {//3连续采样-3连续 无时段 显示“-” 必定在同一天
                    mTvCyDate.setText("-");
                } else {
                    mTvCyDate.setText("-");
                }
            } else {
                mTvTag.setText("");
                mTvTag.setVisibility(View.GONE);
            }
            itemView.setOnClickListener(v ->

            {
                //1瞬时采样；2混合采样；3连续采样；4混合采样中的单次采样
                if (itemData.getWsCyLx() != null) {
                    if (itemData.getWsCyLx().getCode().equals("1")) {//瞬时需要判断是否在当前时刻
                        long timeDq = System.currentTimeMillis();
                        String ssBh = "", qyKssj = "", qyJssj = "";
                        //当前日期年月日时间戳
                        long dqNyr = XDateUtil.getDateByFormat(XDateUtil.getStringByFormat(timeDq, XDateUtil.dateFormatYMD), XDateUtil.dateFormatYMD).getTime();
                        long dqSfm = XDateUtil.getDateByFormat(XDateUtil.getStringByFormat(timeDq, XDateUtil.dateFormatHMS), XDateUtil.dateFormatHMS).getTime();
                        //任务日期年月日时间戳
                        long rwKssj = XDateUtil.getDateByFormat(itemData.getCyRqKssj(), XDateUtil.dateFormatYMD).getTime();
                        long rwJssj = XDateUtil.getDateByFormat(itemData.getCyRqJssj(), XDateUtil.dateFormatYMD).getTime();
                        Log.i("sss", "onBindData: " + dqNyr + "---" + rwKssj + "---" + rwJssj);
                        if (dqNyr < rwKssj || dqNyr > rwJssj) {
                            XToastUtil.showToast(mContext, "请在指定时间段内进行采样");
                            return;
                        }
                        for (int k = 0; k < itemData.getQysjd().size(); k++) {
                            long cyKsTime = XDateUtil.getDateByFormat(XDateUtil.getStringByFormat(itemData.getQysjd().get(k).getKsSj(), XDateUtil.dateFormatHMS), XDateUtil.dateFormatHMS).getTime();
                            long cyJsTime = XDateUtil.getDateByFormat(XDateUtil.getStringByFormat(itemData.getQysjd().get(k).getJsSj(), XDateUtil.dateFormatHMS), XDateUtil.dateFormatHMS).getTime();
                            if (dqSfm >= cyKsTime && dqSfm <= cyJsTime) {
                                //表示再当前时间段
                                ssBh = itemData.getQysjd().get(k).getBh();
                                qyKssj = itemData.getQysjd().get(k).getKsSj();
                                qyJssj = itemData.getQysjd().get(k).getJsSj();
                                break;
                            }
                        }
                        if (XShareCacheUtils.getInstance().getString("taskws").equals("2")) {
                            //逾期不能点击
                            return;
                        }
                        if (TextUtils.isEmpty(ssBh)) {
                            XToastUtil.showToast(mContext, "请在指定时间段内进行采样");
                            return;
                        }
                        Bundle bundle = new Bundle();
                        bundle.putString("bh", itemData.getBh());
                        bundle.putString("qybh", ssBh);
                        bundle.putString("qyKssj", qyKssj);
                        bundle.putString("qyJssj", qyJssj);
                        bundle.putString("cylx", itemData.getWsCyLx().getCode());
                        bundle.putString("from", "jg");
                        //距离满足才能进去-单位为米(0:否1是)
                        if (itemData.getWzXz() == 1 && !XPermissionUtil.isLocServiceEnable(mContext)) {
                            XToastUtil.showToast(mContext, "您尚未开启手机中的《位置信息》功能，请打开位置服务功能");
                            return;
                        }
                        if (itemData.getWzXz() == 1 && ActivityCompat.checkSelfPermission(mContext, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                            XToastUtil.showToast(mContext, "系统无法获取您的定位，请在设置-应用管理-禁毒行动，权限里面打开定位权限");
                            return;
                        }
                        if (itemData.getWzXz() == 1 && itemData.getWsCyFs().getCode().equals("1") && StrUtils.jumpMapJL(itemData.getWd(), itemData.getJd()) > 1000) {
                            XToastUtil.showToast(mContext, "您当前位置超出采样范围，请前往指定采样点进行采样");
                            return;
                        }
                        if (itemData.getWsCyFs() != null) {
                            if (itemData.getWsCyFs().getCode().equals("1")) {
                                //指定点
                                XIntentUtil.redirectToNextActivity(mContext, TaskWsAddActivity.class, bundle);
                            } else {
                                XIntentUtil.redirectToNextActivity(mContext, TaskWsDdAddActivity.class, bundle);
                            }
                        }
                    } else if (itemData.getWsCyLx().getCode().equals("3")) {
                        //连续
                        Bundle bundle = new Bundle();
                        bundle.putString("bh", itemData.getBh());
                        bundle.putString("cylx", itemData.getWsCyLx().getCode());
                        bundle.putString("from", "jg");
                        if (XShareCacheUtils.getInstance().getString("taskws").equals("2")) {
                            //逾期不能点击
                            return;
                        }
                        //距离满足才能进去-单位为米(0:否1是)
                        if (itemData.getWzXz() == 1 && !XPermissionUtil.isLocServiceEnable(mContext)) {
                            XToastUtil.showToast(mContext, "您尚未开启手机中的《位置信息》功能，请打开位置服务功能");
                            return;
                        }
                        if (itemData.getWzXz() == 1 && ActivityCompat.checkSelfPermission(mContext, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                            XToastUtil.showToast(mContext, "系统无法获取您的定位，请在设置-应用管理-禁毒行动，权限里面打开定位权限");
                            return;
                        }
                        if (itemData.getWzXz() == 1 && itemData.getWsCyFs().getCode().equals("1") && StrUtils.jumpMapJL(itemData.getWd(), itemData.getJd()) > 1000) {
                            XToastUtil.showToast(mContext, "您当前位置超出采样范围，请前往指定采样点进行采样");
                            return;
                        }
                        if (itemData.getWsCyFs() != null) {
                            if (itemData.getWsCyFs().getCode().equals("1")) {
                                //指定点
                                XIntentUtil.redirectToNextActivity(mContext, TaskWsAddActivity.class, bundle);
                            } else {
                                XIntentUtil.redirectToNextActivity(mContext, TaskWsDdAddActivity.class, bundle);
                            }
                        }
                    } else {
                        //混合
                        Bundle bundle = new Bundle();
                        bundle.putString("bh", itemData.getBh());
                        bundle.putString("cylx", itemData.getWsCyLx().getCode());
                        bundle.putString("from", "jg");
                        if (XShareCacheUtils.getInstance().getString("taskws").equals("2")) {
                            //逾期不能点击
                            return;
                        }
                        //距离满足才能进去-单位为米(0:否1是)
                        if (itemData.getWzXz() == 1 && !XPermissionUtil.isLocServiceEnable(mContext)) {
                            XToastUtil.showToast(mContext, "您尚未开启手机中的《位置信息》功能，请打开位置服务功能");
                            return;
                        }
                        if (itemData.getWzXz() == 1 && ActivityCompat.checkSelfPermission(mContext, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                            XToastUtil.showToast(mContext, "系统无法获取您的定位，请在设置-应用管理-禁毒行动，权限里面打开定位权限");
                            return;
                        }
                        if (itemData.getWzXz() == 1 && itemData.getWsCyFs().getCode().equals("1") && StrUtils.jumpMapJL(itemData.getWd(), itemData.getJd()) > 1000) {
                            XToastUtil.showToast(mContext, "您当前位置超出采样范围，请前往指定采样点进行采样");
                            return;
                        }
                        if (itemData.getWsCyFs() != null) {
                            if (itemData.getWsCyFs().getCode().equals("1")) {
                                //指定点
                                XIntentUtil.redirectToNextActivity(mContext, TaskWsHhAddActivity.class, bundle);
                            } else {
                                XIntentUtil.redirectToNextActivity(mContext, TaskWsDdAddActivity.class, bundle);
                            }
                        }
                    }
                }
            });
            mBtDaoHang.setOnClickListener(v -> {
                //导航
                DialogUtils.mapDialog(mContext, (code, va) -> {
                    if (code.equals("1")) {
                        MapUtils.openGaoDe(mContext, StrUtils.getDev(itemData.getCsDz(), itemData.getCydDz()));
                    } else {
                        MapUtils.goToBaiduMap(mContext, StrUtils.getDev(itemData.getCsDz(), itemData.getCydDz()));
                    }
                });
            });
        }
    }
}


