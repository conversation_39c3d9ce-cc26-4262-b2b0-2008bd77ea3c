package com.linggan.jd831.adapter;

import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.YbbhZuEntity;
import com.linggan.jd831.bean.ZiDianEntity;
import com.linggan.jd831.utils.StrUtils;

import java.util.List;

/**
 * 类  名：样本编号选择多选--数据回显
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class YbBhChoiceListHolder extends IViewHolder {

    private String dwdmArr;

    public YbBhChoiceListHolder(String dwdmArr) {
        this.dwdmArr = dwdmArr;
    }

    @Override
    public int getLayout() {
        return R.layout.item_nul_choice_list;
    }

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    class ViewHolder extends XViewHolder<YbbhZuEntity.YbbhvosBean> {
        ImageView mIvChoice;
        TextView mTvTitle;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mIvChoice = itemView.findViewById(R.id.iv_choice);
            mTvTitle = itemView.findViewById(R.id.tv_title);
        }

        @Override
        protected void onBindData(final YbbhZuEntity.YbbhvosBean itemData) {

            mTvTitle.setText(itemData.getYbbh());

            if (!TextUtils.isEmpty(dwdmArr)) {
                String code = itemData.getYbbh();
                if (!TextUtils.isEmpty(code) && dwdmArr.contains(code)) {
                    itemData.setChoice(true);
                    mIvChoice.setSelected(true);
                } else {
                    itemData.setChoice(itemData.isChoice());
                    mIvChoice.setSelected(itemData.isChoice());
                }
            } else {
                mIvChoice.setSelected(itemData.isChoice());
            }
            itemView.setOnClickListener(v -> {
                //选中
                itemData.setChoice(!itemData.isChoice());
                mIvChoice.setSelected(itemData.isChoice());
            });
        }
    }
}


