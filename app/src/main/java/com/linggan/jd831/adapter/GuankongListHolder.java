package com.linggan.jd831.adapter;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.GuanKongListEntity;
import com.linggan.jd831.constants.CompletionRateType;
import com.linggan.jd831.ui.works.guankong.GuanKongListItemActivity;
import com.linggan.jd831.utils.StrUtils;

/**
 * 类  名：宣教列表
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class GuankongListHolder extends IViewHolder {

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_guan_kong_list;
    }


    public class ViewHolder extends XViewHolder<GuanKongListEntity> {

        private TextView mTvTitle;
        private ImageView mIvSpTag;
        private TextView mTvNum;
        private TextView mTvLx;
        private TextView mTvTime;
        private TextView mTvRate;
        private ImageView mIVIcon;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mTvTitle = view.findViewById(R.id.tv_title);
            mTvRate = view.findViewById(R.id.tv_rate);
            mIVIcon = view.findViewById(R.id.iv_icon);
//            mIvSpTag = view.findViewById(R.id.iv_sp_tag);
//            mIvSpTag = view.findViewById(R.id.iv_sp_tag);
//            mTvNum = view.findViewById(R.id.tv_num);
//            mTvLx = view.findViewById(R.id.tv_lx);
//            mTvTime = view.findViewById(R.id.tv_time);
//            mTvAddress = view.findViewById(R.id.tv_address);
        }

        @Override
        protected void onBindData(final GuanKongListEntity itemData) {
            if (itemData == null) {
                return;
            }
            mTvTitle.setText(itemData.getWcllx().getName());
            if (itemData.getWcl() != null && !itemData.getWcl().contains("%")) {
                mTvRate.setText(StrUtils.getDev(itemData.getWcl(), "0") + "%");
            } else {
                mTvRate.setText(StrUtils.getDev(itemData.getWcl(), "0"));
            }

            CompletionRateType type = CompletionRateType.fromCode(itemData.getWcllx().getCode());
            mIVIcon.setImageResource(type.getIconResId());

            itemView.setOnClickListener(v -> {
                Bundle bundle = new Bundle();
                bundle.putString("code", itemData.getWcllx().getCode() + "");
                bundle.putString("name", itemData.getWcllx().getName() + "");
                XIntentUtil.redirectToNextActivity(mContext, GuanKongListItemActivity.class, bundle);
            });
        }
    }
}


