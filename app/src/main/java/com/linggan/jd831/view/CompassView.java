package com.linggan.jd831.view;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;

/**
 * 指南针视图
 */
public class CompassView extends View {
    private Paint mPaint;
    private Paint mTextPaint;
    private float direction = 0f; // 方向角度
    private int mWidth;
    private int mHeight;
    private static final int CIRCLE_RADIUS = 200; // 圆的半径
    private static final float ARROW_LENGTH = 160f; // 箭头长度
    private ValueAnimator mRotateAnimator;

    public CompassView(Context context) {
        super(context);
        init();
    }

    public CompassView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        // 初始化画笔
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setStyle(Paint.Style.STROKE); // 改为描边样式
        mPaint.setStrokeWidth(2);  // 减小线条宽度
        mPaint.setColor(Color.GRAY); // 改为灰色

        // 文字画笔
        mTextPaint = new Paint();
        mTextPaint.setAntiAlias(true);
        mTextPaint.setTextSize(40);
        mTextPaint.setColor(Color.GRAY); // 改为灰色
        mTextPaint.setTextAlign(Paint.Align.CENTER);

        // 初始化旋转动画
        mRotateAnimator = ValueAnimator.ofFloat(0, 360);
        mRotateAnimator.setDuration(1000);
        mRotateAnimator.setRepeatCount(ValueAnimator.INFINITE);
        mRotateAnimator.setRepeatMode(ValueAnimator.RESTART);
        mRotateAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
        mRotateAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float value = (float) animation.getAnimatedValue();
                // 使用动画值来平滑地改变方向
                // 这里可以根据需要调整动画的逻辑
                // 例如，可以根据当前方向和目标方向的差值来调整动画的速度
                // 这里为了简化，直接使用动画值
                setDirection(value);
            }
        });
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mWidth = w;
        mHeight = h;
        // 移除渐变效果
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        canvas.drawColor(Color.WHITE);

        // 第一部分：画固定不动的长线段（当前方向指示器）
        canvas.save();
        canvas.translate(mWidth / 2f, mHeight / 2f);
        mPaint.setColor(Color.RED);
        mPaint.setStrokeWidth(4);
        canvas.drawLine(0, -CIRCLE_RADIUS - 40, 0, -CIRCLE_RADIUS + 40, mPaint);
        canvas.restore();

        // 第二部分：画会旋转的指南针部分
        canvas.save();
        canvas.translate(mWidth / 2f, mHeight / 2f);
        canvas.rotate(-direction);

        // 恢复画笔设置
        mPaint.setStrokeWidth(2);
        mPaint.setColor(Color.LTGRAY);

        // 绘制外圈短线段
        int segments = 180;
        float anglePerSegment = 360f / segments;
        float segmentLength = 4;

        for (int i = 0; i < segments; i++) {
            canvas.save();
            canvas.rotate(i * anglePerSegment);
            float startX = -CIRCLE_RADIUS;
            float startY = 0;
            float endX = startX + segmentLength;
            float endY = 0;
            canvas.drawLine(startX, startY, endX, endY, mPaint);
            canvas.restore();
        }

        // 画刻度
        mPaint.setColor(Color.GRAY);
        for (int i = 0; i < 360; i += 3) {
            canvas.save();
            canvas.rotate(i);
            if (i % 30 == 0) {
                // 长刻度从圆圈外部开始
                canvas.drawLine(-CIRCLE_RADIUS - 20, 0, -CIRCLE_RADIUS, 0, mPaint);
            } else {
                // 短刻度从圆圈外部开始
                canvas.drawLine(-CIRCLE_RADIUS - 10, 0, -CIRCLE_RADIUS, 0, mPaint);
            }
            canvas.restore();
        }

        // 画角度数字 - 移到外部
        mTextPaint.setTextSize(30);
        mTextPaint.setColor(Color.GRAY);
        for (int i = 0; i < 360; i += 30) {
            canvas.save();
            canvas.rotate(i);
            // 将数字放在刻度外部
            canvas.drawText(String.valueOf(i), 0, -CIRCLE_RADIUS - 40, mTextPaint);
            canvas.restore();
        }

        // 画方向文字 - 移到外部
        String[] directions = {"北", "东", "南", "西"};
        mTextPaint.setTextSize(50);
        // 计算内圈半径
        float innerRadius = CIRCLE_RADIUS / 2f;
        for (int i = 0; i < 4; i++) {
            canvas.save();
            canvas.rotate(i * 90);
            if (i == 0) {
                mTextPaint.setColor(Color.RED);
            } else {
                mTextPaint.setColor(Color.GRAY);
            }
            // 将方向文字放在数字外部
            // 调整文字位置到内圈
            canvas.drawText(directions[i], 0, -innerRadius, mTextPaint);
            canvas.restore();
        }

        // 画中心十字
        canvas.drawLine(-10, 0, 10, 0, mPaint);
        canvas.drawLine(0, -10, 0, 10, mPaint);

        // 画指针 - 调整红色指针到外部
        mPaint.setStyle(Paint.Style.FILL);

        // 画红色三角形指针 - 移到外部
        mPaint.setColor(Color.RED);
        Path redTriangle = new Path();
        redTriangle.moveTo(0, -CIRCLE_RADIUS - 40); // 起点移到圆圈外部
        redTriangle.lineTo(-15, -CIRCLE_RADIUS);
        redTriangle.lineTo(15, -CIRCLE_RADIUS);
        redTriangle.close();
        canvas.drawPath(redTriangle, mPaint);

        // 画灰色三角形指针
        mPaint.setColor(Color.GRAY);
        Path grayTriangle = new Path();
        grayTriangle.moveTo(0, CIRCLE_RADIUS + 40); // 同样移到外部
        grayTriangle.lineTo(-15, CIRCLE_RADIUS);
        grayTriangle.lineTo(15, CIRCLE_RADIUS);
        grayTriangle.close();
        canvas.drawPath(grayTriangle, mPaint);

        canvas.restore();
    }

    /**
     * 设置方向角度
     */
    public void setDirection(float direction) {
        this.direction = direction;
        invalidate();
    }
} 