package com.linggan.jd831.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class QuarterCalculator {
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    public static Map<String, Object> getQuarter(int quarter) {
        return getQuarter(quarter, Calendar.getInstance().get(Calendar.YEAR));
    }

    public static Map<String, Object> getQuarter(int quarter, int year) {
        if (quarter < 1 || quarter > 4) {
            throw new IllegalArgumentException("Quarter must be between 1 and 4");
        }
        Date startDate = getQuarterStartDate(year, quarter);
        Date endDate = getQuarterEndDate(year, quarter);

        return createQuarterMap(year, quarter, startDate, endDate);
    }

    public static Map<String, Object> getQuarterFromDate(String dateString) {
        try {
            Date date = DATE_FORMAT.parse(dateString);
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);

            int year = cal.get(Calendar.YEAR);
            int quarter = getQuarterFromMonth(cal.get(Calendar.MONTH) + 1);

            return getQuarter(quarter, year);
        } catch (ParseException e) {
            throw new IllegalArgumentException("Invalid date format. Expected format: yyyy-MM-dd", e);
        }
    }

    private static int getQuarterFromMonth(int month) {
        return (month - 1) / 3 + 1;
    }

    private static Date getQuarterStartDate(int year, int quarter) {
        Calendar cal = Calendar.getInstance();
        cal.set(year, (quarter - 1) * 3, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    private static Date getQuarterEndDate(int year, int quarter) {
        Calendar cal = Calendar.getInstance();
        cal.set(year, quarter * 3 - 1, 1, 23, 59, 59);
        cal.set(Calendar.MILLISECOND, 999);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        return cal.getTime();
    }

    private static Map<String, Object> createQuarterMap(int year, int quarter, Date startDate, Date endDate) {
        Map<String, Object> result = new HashMap<>();
        result.put("year", year);
        result.put("quarter", quarter);
        result.put("start_date", DATE_FORMAT.format(startDate));
        result.put("end_date", DATE_FORMAT.format(endDate));
        return result;
    }

    // 返回季度列表
    public static ArrayList<String> getQuarterList() {
        return new ArrayList<>(Arrays.asList("第1季度", "第2季度", "第3季度", "第4季度"));
    }

    // 返回季度数字
    public static int getJdCode(String jd) {
        if (jd == null || jd.isEmpty()) {
            return 0;
        }
        ArrayList<String> quarter = new ArrayList<>(Arrays.asList("第1季度", "第2季度", "第3季度", "第4季度"));
        return quarter.indexOf(jd) + 1;
    }

    public static String getJdString(String jd) {
        if (jd == null || jd.isEmpty()) {
            return "";
        }
        int quarter;
        try {
            quarter = (int) Math.round(Double.parseDouble(jd));
        } catch (NumberFormatException e) {
            return "";
        }
        return "第" + quarter + "季度";
    }


    /**
     * 将数字季度直接转换为中文季度表示
     * @param jd 季度数字，如"1"、"2"、"3"、"4"
     * @return 格式为"第一季度"、"第二季度"等的字符串
     */
    public static String convertToChineseQuarter(String jd) {
        if (jd == null || jd.isEmpty()) {
            return "";
        }

        try {
            int quarter = Integer.parseInt(jd);
            String chineseDigit;
            switch (quarter) {
                case 1:
                    chineseDigit = "一";
                    break;
                case 2:
                    chineseDigit = "二";
                    break;
                case 3:
                    chineseDigit = "三";
                    break;
                case 4:
                    chineseDigit = "四";
                    break;
                default:
                    return "第" + quarter + "季度";
            }

            return "第" + chineseDigit + "季度";
        } catch (NumberFormatException e) {
            return "第" + jd + "季度";
        }
    }
}