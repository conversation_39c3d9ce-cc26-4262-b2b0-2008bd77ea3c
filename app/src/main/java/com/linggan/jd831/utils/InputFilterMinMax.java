package com.linggan.jd831.utils;

import android.text.InputFilter;
import android.text.Spanned;

/**
 * 0.0到100.0
 */
public class InputFilterMinMax implements InputFilter {


    private double min, max;

    public InputFilterMinMax(double min, double max) {
        this.min = min;
        this.max = max;
    }

    @Override
    public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
        try {
            // Prepare the resulting text to check if it matches the constraints
            String result = dest.subSequence(0, dstart) + source.subSequence(start, end).toString() + dest.subSequence(dend, dest.length());

            // Check for multiple leading zeroes
            if (result.matches("00+")) {
                return "";
            }

            // Check for more than 1 decimal digit
            if (result.contains(".")) {
                int dotIndex = result.indexOf(".");
                String decimalPart = result.substring(dotIndex + 1);
                if (decimalPart.length() > 1) {
                    return "";
                }
            }

            // Parse the result number and return null if it's in the range
            double input = Double.parseDouble(result);
            if (isInRange(min, max, input)) {
                return null;
            }
        } catch (NumberFormatException e) {
            // Not a valid number
        }

        // For invalid inputs
        return "";
    }

    private boolean isInRange(double a, double b, double c) {
        return b > a ? c >= a && c <= b : c >= b && c <= a;
    }
}