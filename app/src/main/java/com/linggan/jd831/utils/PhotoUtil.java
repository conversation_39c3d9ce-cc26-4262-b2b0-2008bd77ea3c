package com.linggan.jd831.utils;

import android.Manifest;
import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.net.Uri;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.TextUtils;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.hjq.permissions.XXPermissions;
import com.lgfzd.base.utils.XAppUtil;
import com.lgfzd.base.utils.XDialogUtils;
import com.lgfzd.base.utils.XPermissionUtil;
import com.lgfzd.base.utils.XViewUtil;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 图片的工具类
 */
public class PhotoUtil {

    /**
     * 获取上传的路径
     *
     * @return 路径
     */
    public static String createPath(Activity activity) {
        //先判断外设SD卡是不是可用
        if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            //判断是否存在同文件
            File file = new File(activity.getExternalCacheDir().getPath() + File.separator + System.currentTimeMillis() + ".png");
            if (file.exists()) {
                file.delete();
            }
            return activity.getExternalCacheDir().getPath() + File.separator + System.currentTimeMillis() + ".png";
        } else {
            File file = new File(activity.getFilesDir() + File.separator + System.currentTimeMillis() + ".png");
            if (file.exists()) {
                file.delete();
            }
            return activity.getFilesDir() + File.separator + System.currentTimeMillis() + ".png";
        }
    }


    /**
     * 获取上传的路径
     *
     * @return 路径
     */
    public static String createPath(Activity getActivity, String fileName) {
        //先判断外设SD卡是不是可用
        if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            //判断是否存在同文件
            File file = new File(getActivity.getExternalCacheDir().getPath() + File.separator + fileName);
            if (file.exists()) {
                file.delete();
            }
            return getActivity.getExternalCacheDir().getPath() + File.separator + fileName;
        } else {
            File file = new File(getActivity.getFilesDir() + File.separator + fileName);
            if (file.exists()) {
                file.delete();
            }
            return getActivity.getFilesDir() + File.separator + fileName;
        }
    }


    /**
     * 添加水印
     *
     * @param context    上下文
     * @param src        需要添加水印的图片
     * @param watermarks 水印字符
     * @return 添加了水印的图片
     */
    public static Bitmap addWatermark1(Activity context, Bitmap src, String[] watermarks, Boolean offline) {
        //原图宽高
        int w = src.getWidth();
        int h = src.getHeight();
//        预想图宽高
        float newW = XAppUtil.screenWidth(context);
        float newH = h * (newW / w);
//        缩放比例
        float scaleW = newW / w;
        float scaleH = newH / h;
        Matrix matrix = new Matrix();
        matrix.postScale(scaleW, scaleH);
        src = Bitmap.createBitmap(src, 0, 0, w, h, matrix, true);
        //重新画一个图添加水印
        Bitmap newBitmap = Bitmap.createBitmap(src.getWidth(), src.getHeight(), Bitmap.Config.RGB_565);
        Canvas mCanvas = new Canvas(newBitmap);
        // 往位图中开始画入src原始图片
        mCanvas.drawBitmap(src, 0, 0, null);
        //文字画笔
        TextPaint textPaint = new TextPaint();
        textPaint.reset();
        textPaint.clearShadowLayer();
        textPaint.setColor(Color.RED);
        textPaint.setTextSize(XViewUtil.sp2px(context, 12));
        //判断是否有读取手机状态的权限
        StringBuilder builder = new StringBuilder();
        //
        String num = "";
        try {
            num = XAppUtil.getIMEI(context);
        } catch (Exception e) {
        }
        builder.append("设备号:").append(num).append("\n");
        //添加时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
        builder.append("时间:").append(sdf.format(new Date(System.currentTimeMillis()))).append("\n");
        //添加地址坐标
        if (watermarks != null) {
            if (watermarks.length > 0) builder.append("经度:").append(watermarks[0]).append("\n");
            if (watermarks.length > 1) builder.append("纬度:").append(watermarks[1]).append("\n");
            if (offline) {
                builder.append("地址:").append("离线排查上传");
            } else {
                if (watermarks.length > 2) builder.append("地址:").append(watermarks[2]);
            }
        } else {
            if (offline) {
                builder.append("地址:").append("离线排查上传");
            }
        }
        StaticLayout layout = new StaticLayout(builder.toString(), textPaint, src.getWidth() - 20, Layout.Alignment.ALIGN_NORMAL, 1.0f, 0.0f, false);
        //移动绘制起点
        mCanvas.translate(20f, (float) (src.getHeight() - layout.getHeight() - 30));
        layout.draw(mCanvas);
        return newBitmap;
    }

    /**
     * 把Bitmap转Byte
     */
    public static byte[] bitmap2Bytes(Bitmap bm) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        bm.compress(Bitmap.CompressFormat.PNG, 95, baos);
        return baos.toByteArray();
    }

    /**
     * 添加水印--将自定义view的bitmap加到图片上面
     *
     * @param context   上下文
     * @param src       需要添加水印的图片原始图片
     * @param watermark 水印图片
     * @return 添加了水印的图片
     */
    public static Bitmap addWatermark(Activity context, Bitmap src, Bitmap watermark) {
        if (src == null) {
            return null;
        }
        int width = src.getWidth();
        int height = src.getHeight();
        float be = width / height;
//        Log.i("vvv", "addWatermark: " + width + "--" + height + "---" + be);
        if ((float) 16 / 9 >= be && be >= (float) 4 / 3) {
            //在图片比例区间内16;9~4：3内，将生成的水印bitmap设置为原图宽高各自的1/5
            watermark = zoomBitmap(watermark, (int) width / 4, (int) height / 5);
//            Log.i("vvv", "addWatermark11: " + be);
        } else if (be > (float) 16 / 9) {
            //生成4：3的水印
            watermark = zoomBitmap(watermark, (int) width / 3, (int) width * 3 / 15);
//            Log.i("vvv", "addWatermark22: " + be);
        } else if (be < (float) 4 / 3) {
            //生成4：3的水印
            watermark = zoomBitmap(watermark, (int) height * 4 / 10, (int) height / 5);
//            Log.i("vvv", "addWatermark33: " + be);
        }
//        Log.i("vvv", "addWatermark44: " + watermark.getWidth() + "--" + watermark.getHeight());
        //创建一个bitmap
        Bitmap newb = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);// 创建一个新的和SRC长度宽度一样的位图
        //将该图片作为画布
        Canvas canvas = new Canvas(newb);
        //在画布 0，0坐标上开始绘制原始图片
        canvas.drawBitmap(src, 0, 0, null);
        //在画布上绘制水印图片
        int h = src.getHeight() - watermark.getHeight() - 20;
        canvas.drawBitmap(watermark, 10, h, null);
        // 保存
        canvas.save();
        // 存储
        canvas.restore();
        return newb;
    }

    /**
     * 打开相机拍照
     *
     * @param activity
     * @param permissionResultCallback
     */
    public static void openCameraAndToPermission(Activity activity, OnOpenPermissionResultCallback permissionResultCallback) {
        if (XPermissionUtil.checkPermissionForever(activity, Manifest.permission.CAMERA) || XPermissionUtil.checkPermissionForever(activity, Manifest.permission.WRITE_EXTERNAL_STORAGE) || XPermissionUtil.checkPermissionForever(activity, Manifest.permission.READ_EXTERNAL_STORAGE)) {
            Dialog dialog = XDialogUtils.showPermissionsErrorDialog(activity, "相机与文件存储", (code, id) -> {
                if (TextUtils.isEmpty(code)) {
                    XXPermissions.startPermissionActivity(activity, new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                }
            });
            dialog.show();
        } else if (ActivityCompat.checkSelfPermission(activity, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED || ActivityCompat.checkSelfPermission(activity, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED || ActivityCompat.checkSelfPermission(activity, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            permissionResultCallback.onRefuse();
        } else {
            permissionResultCallback.onSuccess();
        }
    }

    /**
     * 打开视频
     *
     * @param activity
     * @param permissionResultCallback
     */
    public static void openVideoAndToPermission(Activity activity, OnOpenPermissionResultCallback permissionResultCallback) {
        if (XPermissionUtil.checkPermissionForever(activity, Manifest.permission.CAMERA) ||
                XPermissionUtil.checkPermissionForever(activity, Manifest.permission.WRITE_EXTERNAL_STORAGE) ||
                XPermissionUtil.checkPermissionForever(activity, Manifest.permission.RECORD_AUDIO) ||
                XPermissionUtil.checkPermissionForever(activity, Manifest.permission.READ_EXTERNAL_STORAGE)) {
            Dialog dialog = XDialogUtils.showPermissionsErrorDialog(activity, "相机、文件、语音存储", (code, id) -> {
                if (TextUtils.isEmpty(code)) {
                    XXPermissions.startPermissionActivity(activity, new String[]{Manifest.permission.CAMERA, Manifest.permission.RECORD_AUDIO, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                }
            });
            dialog.show();
        } else if (ActivityCompat.checkSelfPermission(activity, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED ||
                ActivityCompat.checkSelfPermission(activity, Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED ||
                ActivityCompat.checkSelfPermission(activity, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED ||
                ActivityCompat.checkSelfPermission(activity, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            permissionResultCallback.onRefuse();
        } else {
            permissionResultCallback.onSuccess();
        }
    }

    /**
     * 创建文件
     */
    public static File createFileMuLu(Activity activity, DialogUtils.OnResult onResult) {
        if (ContextCompat.checkSelfPermission(activity, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {
            File cameraPhoto = new File(PhotoUtil.createPath(activity));
            try {
                if (cameraPhoto.exists()) {
                    cameraPhoto.delete();
                }
                cameraPhoto.createNewFile();
                return cameraPhoto;
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            // 权限未授权，需要申请权限
            onResult.onSuccess("", "");
        }
        return null;
    }

    /**
     * 打开系统相机
     *
     * @param activity
     * @return
     */
    public static Uri openCamera(Activity activity, File cameraPhotoFile, int requestCode) {
        Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        Uri photoUri = XFileUtil.getUriFromFile(activity, cameraPhotoFile);
        intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
        activity.startActivityForResult(intent, requestCode);
        return photoUri;
    }

    /**
     * 缩放Bitmap图片
     */
    private static Bitmap zoomBitmap(Bitmap bitmap, int width, int height) {
        int w = bitmap.getWidth();
        int h = bitmap.getHeight();
        Matrix matrix = new Matrix();
        float scaleWidth = ((float) width / w);
        float scaleHeight = ((float) height / h);
        matrix.postScale(scaleWidth, scaleHeight);//利用矩阵进行缩放不会造成内存溢出
        Bitmap newbmp = Bitmap.createBitmap(bitmap, 0, 0, w, h, matrix, true);
        return newbmp;
    }

    /**
     * 授权结果回调
     */
    public interface OnOpenPermissionResultCallback {
        void onRefuse();

        void onSuccess();
    }
}
