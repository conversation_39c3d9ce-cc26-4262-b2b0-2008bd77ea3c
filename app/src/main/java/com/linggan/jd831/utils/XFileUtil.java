package com.linggan.jd831.utils;

import android.app.Activity;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.media.ExifInterface;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.DocumentsContract;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Base64;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.core.content.FileProvider;

import com.lgfzd.base.utils.XAppUtil;
import com.lgfzd.base.utils.XDateUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.R;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.channels.FileChannel;


/**
 * 文件工具类
 */
public class XFileUtil {

    /**
     * SD卡路径
     */
    public static final String SD_BASE = Environment.getExternalStorageDirectory().getAbsolutePath() + "/pdf/";
    /**
     * 自定义SD文件选择路径回调标识code
     */
    public static final int FILE_SELECTOR_CODE = 0x5024;

    /**
     * 获取路径（从Uri获取）
     *
     * @param uri Uri
     */
    public static String getPathFromUri(Context context, Uri uri) {
        if ("file".equalsIgnoreCase(uri.getScheme())) {
            return uri.getPath();
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            return getPath(context, uri);
        } else {
            return getPathFromUri(context, uri, null, null);
        }
    }

    /**
     * 获取路径
     *
     * @param uri Uri
     */
    private static String getPath(Context context, Uri uri) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT && DocumentsContract.isDocumentUri(context, uri)) {
            if ("com.android.externalstorage.documents".equals(uri.getAuthority())) {
                String id = DocumentsContract.getDocumentId(uri);
                String[] split = id.split(":");
                if ("primary".equalsIgnoreCase(split[0])) {
                    return Environment.getExternalStorageDirectory() + "/" + split[1];
                }
            } else if ("com.android.providers.downloads.documents".equals(uri.getAuthority())) {
                String id = DocumentsContract.getDocumentId(uri);
                Uri URI = ContentUris.withAppendedId(Uri.parse("content://downloads/public_downloads"), Long.parseLong(id));
                return getPathFromUri(context, URI, null, null);
            } else if ("com.android.providers.media.documents".equals(uri.getAuthority())) {
                String id = DocumentsContract.getDocumentId(uri);
                String[] split = id.split(":");
                Uri URI = null;
                if ("image".equals(split[0])) {
                    URI = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
                } else if ("video".equals(split[0])) {
                    URI = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
                } else if ("audio".equals(split[0])) {
                    URI = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
                }
                return getPathFromUri(context, URI, "_id=?", new String[]{split[1]});
            }
        } else if ("content".equalsIgnoreCase(uri.getScheme())) {
            return getPathFromUri(context, uri, null, null);
        } else if ("file".equalsIgnoreCase(uri.getScheme())) {
            uri.getPath();
        }
        return null;
    }

    /**
     * 获取路径
     *
     * @param uri Uri
     */
    private static String getPathFromUri(Context context, Uri uri, String selection, String[] args) {
        String path = null;
        String[] select = {"_data"};
        Cursor cursor = context.getContentResolver().query(uri, select, selection, args, null);
        if (cursor != null && cursor.moveToFirst()) {
            int index = cursor.getColumnIndexOrThrow(select[0]);
            path = cursor.getString(index);
            cursor.close();
        }
        return path;
    }

    /**
     * 估计安卓版本的不同来得到对应的Uri
     *
     * @param context 上下文对象
     * @param file    文件
     * @return Uri
     */
    public static Uri getUriFromFile(Context context, File file) {
        Uri uri;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            uri = FileProvider.getUriForFile(context, StrUtils.getFileProviderName(context), file);
        } else {
            uri = Uri.fromFile(file);
        }
        return uri;
    }

    /**
     * 打开文件
     *
     * @param activity 上下文对象
     * @param path     文件路径
     */
    public static void openFile(Activity activity, String path) {
        String type = path.substring(path.lastIndexOf(".") + 1, path.length());
        File fileLocation = new File(path);
        Intent intent = new Intent(Intent.ACTION_VIEW);
        Uri uri = getUriFromFile(activity, fileLocation);
        switch (type) {
            case "docx":
            case "doc":
                intent.addCategory("android.intent.category.DEFAULT");
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.setDataAndType(uri, "application/msword");
                break;
            case "txt":
                intent.addCategory("android.intent.category.DEFAULT");
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.setDataAndType(uri, "text/plain");
                break;
            case "pdf":
                intent.addCategory("android.intent.category.DEFAULT");
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.setDataAndType(uri, "application/pdf");
                break;
            case "html":
                intent.addCategory("android.intent.category.DEFAULT");
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.setDataAndType(uri, "text/html");
                break;
            case "jpeg":
            case "jpg":
            case "gif":
            case "png":
            case "bmp":
                intent.addCategory("android.intent.category.DEFAULT");
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.setDataAndType(uri, "image/*");
                break;
            case "mp3":
            case "wmv":
            case "wav":
            case "mid":
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                intent.putExtra("oneshot", 0);
                intent.putExtra("configchange", 0);
                intent.setDataAndType(uri, "audio/*");
                break;
            case "mpeg":
            case "mp4":
            case "avi":
            case "3gp":
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                intent.putExtra("oneshot", 0);
                intent.putExtra("configchange", 0);
                intent.setDataAndType(uri, "video/*");
                break;
            case "xls":
                intent.addCategory("android.intent.category.DEFAULT");
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.setDataAndType(uri, "application/vnd.ms-excel");
                break;
            case "ppt":
                intent.addCategory("android.intent.category.DEFAULT");
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.setDataAndType(uri, "application/vnd.ms-powerpoint");
                break;
        }
        try {
            activity.startActivity(intent);
        } catch (Exception e) {
            Toast.makeText(activity, "无法打开此文件", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 判断文件夹是否存在
     *
     * @param path
     * @return
     */
    public static boolean isExist(String path) {
        File file = new File(path);
        if (file.exists()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 删除单个文件
     *
     * @param filePath 被删除文件的文件名
     * @return 文件删除成功返回true，否则返回false
     */
    public static boolean deleteFile(String filePath) {
        File file = new File(filePath);
        if (file.exists() && file.isFile()) {
            return file.delete();
        }
        return false;
    }

    /**
     * 字节数据保存为文
     *
     * @param data
     */
    public static void saveBytesToFile(File file, byte[] data) {
        BufferedOutputStream outStream = null;
        try {
            outStream = new BufferedOutputStream(new FileOutputStream(file));
            outStream.write(data);
            outStream.flush();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != outStream) {
                try {
                    outStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 获取上传的路径
     *
     * @return 路径
     */
    public static String createPath(Context context) {
        //先判断外设SD卡是不是可用
        if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            //判断是否存在同文件
            File file = new File(context.getExternalCacheDir().getPath());
            if (file.exists()) {
                file.delete();
            }
            return context.getExternalCacheDir().getPath();
        } else {
            if (TextUtils.isEmpty(context.getFilesDir().getPath())) {
                File file = new File(context.getFilesDir().getPath());
                if (file.exists()) {
                    file.delete();
                }
                return context.getFilesDir().getPath();
            } else {
                File file = new File(Environment.getExternalStorageDirectory().getPath());
                if (file.exists()) {
                    file.delete();
                }
                return Environment.getExternalStorageDirectory().getPath();
            }
        }
    }

    /**
     * 打开本地文件器
     */
    public static void choiceSDCardFile(Activity context) {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("*/*");
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        context.startActivityForResult(intent, FILE_SELECTOR_CODE);
    }

    /**
     * 保存图片/视频--通知到相册
     *
     * @param context
     */
    public static boolean insertMediaPic(Context context, File file, boolean isImg) {
        if (file == null) return false;
        //判断android Q  (10 ) 版本
        if (isAdndroidQ()) {
            if (file == null || !file.exists()) {
                return false;
            } else {
                try {
                    if (isImg) {
                        MediaStore.Images.Media.insertImage(context.getContentResolver(), file.getAbsolutePath(), file.getName(), null);
                    } else {
                        ContentValues values = new ContentValues();
                        values.put(MediaStore.Video.Media.DATA, file.getAbsolutePath());
                        values.put(MediaStore.Video.Media.DISPLAY_NAME, file.getName());
                        values.put(MediaStore.Video.Media.MIME_TYPE, "video/*");
                        values.put(MediaStore.Video.Media.DATE_ADDED, System.currentTimeMillis() / 1000);
                        values.put(MediaStore.Video.Media.DATE_MODIFIED, System.currentTimeMillis() / 1000);
                        ContentResolver resolver = context.getContentResolver();
                        resolver.insert(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, values);
                    }
                    return true;
                } catch (Exception e) {
                    e.printStackTrace();
                    return false;
                }
            }
        } else {//老方法
            if (isImg) {
                ContentValues values = new ContentValues();
                values.put(MediaStore.Images.Media.DATA, file.getAbsolutePath());
                values.put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg");
                values.put(MediaStore.Images.ImageColumns.DATE_TAKEN, System.currentTimeMillis() + "");
                context.getContentResolver().insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
                context.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.parse("file://" + file.getAbsolutePath())));
            } else {
                ContentResolver localContentResolver = context.getContentResolver();
                ContentValues localContentValues = getVideoContentValues(file, System.currentTimeMillis());
                Uri localUri = localContentResolver.insert(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, localContentValues);
                context.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, localUri));
            }
            return true;
        }
    }

    private static boolean isAdndroidQ() {
        return Build.VERSION.SDK_INT >= 29;
    }

    public static ContentValues getVideoContentValues(File paramFile, long paramLong) {
        ContentValues localContentValues = new ContentValues();
        localContentValues.put(MediaStore.Video.Media.TITLE, paramFile.getName());
        localContentValues.put(MediaStore.Video.Media.DISPLAY_NAME, paramFile.getName());
        localContentValues.put(MediaStore.Video.Media.MIME_TYPE, "video/mp4");
        localContentValues.put(MediaStore.Video.Media.DATE_TAKEN, Long.valueOf(paramLong));
        localContentValues.put(MediaStore.Video.Media.DATE_MODIFIED, Long.valueOf(paramLong));
        localContentValues.put(MediaStore.Video.Media.DATE_ADDED, Long.valueOf(paramLong));
        localContentValues.put(MediaStore.Video.Media.DATA, paramFile.getAbsolutePath());
        localContentValues.put(MediaStore.Video.Media.SIZE, Long.valueOf(paramFile.length()));
        return localContentValues;
    }

    /**
     * 将图片转换成Base64编码的字符串
     */
    public static String imageToBase64(String path) {
        if (TextUtils.isEmpty(path)) {
            return null;
        }
        InputStream is = null;
        byte[] data = null;
        String result = null;
        try {
            is = new FileInputStream(path);
            //创建一个字符流大小的数组。
            data = new byte[is.available()];
            //写入数组
            is.read(data);
            //用默认的编码格式进行编码
            result = Base64.encodeToString(data, Base64.NO_WRAP);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != is) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }

    /**
     * 获取文件大小
     *
     * @param path
     * @return
     */
    public static long getFileSize(String path) {
        File file = new File(path);
        long size = 0;
        FileInputStream fis = null;
        FileChannel channel = null;
        try {
            fis = new FileInputStream(file);
            channel = fis.getChannel();
            size = channel.size();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (channel != null) {
                    channel.close();
                }
                if (fis != null) {
                    fis.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return size;
    }

    /**
     * 离线水印添加水印
     *
     * @param context
     * @param originalBitmap
     * @param filePath
     * @return
     */
    public static boolean bitmapToFilePath(Activity context, Bitmap originalBitmap, Uri uri, File filePath, Boolean offline) {
        if (originalBitmap == null) {
            XToastUtil.showToast(context, "拍摄失败，请退出当前页面，重新进入拍照");
            return false;
        }
        try {
            XFileUtil.insertMediaPic(context, filePath, true);
            Bitmap waterBitmapNew = rotateImageIfRequired(context, originalBitmap, uri);
            View yourCustomView = LayoutInflater.from(context).inflate(R.layout.laout_water, null);
            TextView mTvSj = yourCustomView.findViewById(R.id.tv_sj);
            TextView mTvRq = yourCustomView.findViewById(R.id.tv_nyr);
            TextView mTvIe = yourCustomView.findViewById(R.id.tv_ie);
            TextView mTvJd = yourCustomView.findViewById(R.id.tv_jd);
            TextView mTvWd = yourCustomView.findViewById(R.id.tv_wd);
            TextView mTvDz = yourCustomView.findViewById(R.id.tv_dz);
            mTvSj.setText(XDateUtil.getCurrentDate(XDateUtil.dateFormatHM));
            mTvRq.setText(XDateUtil.getCurrentDate(XDateUtil.dateFormatYMDText));
            String num = "";
            try {
                num = XAppUtil.getIMEI(context);
            } catch (Exception e) {
            }
            mTvIe.setText("设备号：" + num);
            mTvJd.setText("经度：" + XShareCacheUtils.getInstance().getString("lon"));
            mTvWd.setText("维度：" + XShareCacheUtils.getInstance().getString("lat"));
            if (offline) {
                mTvDz.setText("地址：离线排查上传");
            } else {
                mTvDz.setText("地址：" + XShareCacheUtils.getInstance().getString("city"));

            }
            Bitmap customViewBitmap = renderViewToBitmapCamera(yourCustomView, waterBitmapNew.getWidth()); // 假设这个方法已经定义好了，用于将自定义View渲染成Bitmap
            Bitmap waterBitmap = PhotoUtil.addWatermark(context, waterBitmapNew, customViewBitmap);
            if (waterBitmap == null) {
                MdFactoryUtils.mdSave("照片回调加水印-工作人员-失败", "", StrUtils.getSbxxCj());
            }
            FileOutputStream fos = new FileOutputStream(filePath);
            waterBitmap.compress(Bitmap.CompressFormat.JPEG, 80, fos);
            fos.flush();
            fos.close();
            if (waterBitmap != null) {
                waterBitmap.recycle();
            }
            if (waterBitmapNew != null) {
                waterBitmapNew.recycle();
            }
            if (originalBitmap != null) {
                originalBitmap.recycle();
            }
            return true;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 添加水印
     *
     * @param context
     * @param originalBitmap
     * @param filePath
     * @return
     */
    public static boolean bitmapToFilePath(Activity context, Bitmap originalBitmap, Uri uri, File filePath) {
        if (originalBitmap == null) {
            XToastUtil.showToast(context, "拍摄失败，请退出当前页面，重新进入拍照");
            return false;
        }
        try {
            XFileUtil.insertMediaPic(context, filePath, true);
            Bitmap waterBitmapNew = rotateImageIfRequired(context, originalBitmap, uri);
            View yourCustomView = LayoutInflater.from(context).inflate(R.layout.laout_water, null);
            TextView mTvSj = yourCustomView.findViewById(R.id.tv_sj);
            TextView mTvRq = yourCustomView.findViewById(R.id.tv_nyr);
            TextView mTvIe = yourCustomView.findViewById(R.id.tv_ie);
            TextView mTvJd = yourCustomView.findViewById(R.id.tv_jd);
            TextView mTvWd = yourCustomView.findViewById(R.id.tv_wd);
            TextView mTvDz = yourCustomView.findViewById(R.id.tv_dz);
            mTvSj.setText(XDateUtil.getCurrentDate(XDateUtil.dateFormatHM));
            mTvRq.setText(XDateUtil.getCurrentDate(XDateUtil.dateFormatYMDText));
            String num = "";
            try {
                num = XAppUtil.getIMEI(context);
            } catch (Exception e) {
            }
            mTvIe.setText("设备号：" + num);
            mTvJd.setText("经度：" + XShareCacheUtils.getInstance().getString("lon"));
            mTvWd.setText("维度：" + XShareCacheUtils.getInstance().getString("lat"));
            mTvDz.setText("地址：" + XShareCacheUtils.getInstance().getString("city"));
            Bitmap customViewBitmap = renderViewToBitmapCamera(yourCustomView, waterBitmapNew.getWidth()); // 假设这个方法已经定义好了，用于将自定义View渲染成Bitmap
            Bitmap waterBitmap = PhotoUtil.addWatermark(context, waterBitmapNew, customViewBitmap);
            if (waterBitmap == null) {
                MdFactoryUtils.mdSave("照片回调加水印-工作人员-失败", "", StrUtils.getSbxxCj());
            }
            FileOutputStream fos = new FileOutputStream(filePath);
            waterBitmap.compress(Bitmap.CompressFormat.JPEG, 80, fos);
            fos.flush();
            fos.close();
            if (waterBitmap != null) {
                waterBitmap.recycle();
            }
            if (waterBitmapNew != null) {
                waterBitmapNew.recycle();
            }
            if (originalBitmap != null) {
                originalBitmap.recycle();
            }
            return true;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 添加水印--相册图片加水印
     *
     * @param context
     * @param filePath
     * @return
     */
    public static boolean bitmapToFilePathAlbum(Activity context, Bitmap waterBitmapNew, File filePath) {
        try {
            View yourCustomView = LayoutInflater.from(context).inflate(R.layout.laout_water, null);
            TextView mTvSj = yourCustomView.findViewById(R.id.tv_sj);
            TextView mTvRq = yourCustomView.findViewById(R.id.tv_nyr);
            TextView mTvIe = yourCustomView.findViewById(R.id.tv_ie);
            TextView mTvJd = yourCustomView.findViewById(R.id.tv_jd);
            TextView mTvWd = yourCustomView.findViewById(R.id.tv_wd);
            TextView mTvDz = yourCustomView.findViewById(R.id.tv_dz);
            mTvSj.setText(XDateUtil.getCurrentDate(XDateUtil.dateFormatHM));
            mTvRq.setText(XDateUtil.getCurrentDate(XDateUtil.dateFormatYMDText));
            String num = "";
            try {
                num = XAppUtil.getIMEI(context);
            } catch (Exception e) {
            }
            mTvIe.setText("设备号：" + num);
            mTvJd.setText("经度：" + XShareCacheUtils.getInstance().getString("lon"));
            mTvWd.setText("维度：" + XShareCacheUtils.getInstance().getString("lat"));
            mTvDz.setText("地址：" + XShareCacheUtils.getInstance().getString("city"));
            Bitmap customViewBitmap = renderViewToBitmapCamera(yourCustomView, waterBitmapNew.getWidth());
            Bitmap waterBitmap = PhotoUtil.addWatermark(context, waterBitmapNew, customViewBitmap);
            if (waterBitmap == null) {
                MdFactoryUtils.mdSave("照片回调加水印-工作人员-失败", "", StrUtils.getSbxxCj());
            }
            FileOutputStream fos = new FileOutputStream(filePath);
            waterBitmap.compress(Bitmap.CompressFormat.JPEG, 85, fos);
            fos.flush();
            fos.close();
            if (waterBitmap != null) {
                waterBitmap.recycle();
            }
            if (waterBitmapNew != null) {
                waterBitmapNew.recycle();
            }
            return true;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 无水印水印
     *
     * @param context
     * @param originalBitmap
     * @param filePath
     * @return
     */
    public static boolean bitmapToFilePathNo(Activity context, Bitmap originalBitmap, Uri uri, File filePath) {
        if (originalBitmap == null) {
            XToastUtil.showToast(context, "拍摄失败，请退出当前页面，重新进入拍照");
            return false;
        }
        try {
            Bitmap originalBitmapNew = rotateImageIfRequired(context, originalBitmap, uri);
            int w = originalBitmapNew.getWidth();
            int h = originalBitmapNew.getHeight();
            //预想图宽高
            float newW = XAppUtil.screenWidth(context);
            float newH = h * (newW / w);
            //缩放比例
            float scaleW = newW / w;
            float scaleH = newH / h;
            Matrix matrix = new Matrix();
            matrix.postScale(scaleW, scaleH);
            Bitmap newBitmap = Bitmap.createBitmap(originalBitmapNew, 0, 0, w, h, matrix, true);
            FileOutputStream fos = new FileOutputStream(filePath);
            newBitmap.compress(Bitmap.CompressFormat.JPEG, 70, fos);
            fos.flush();
            fos.close();
            if (newBitmap != null) {
                newBitmap.recycle();
            }
            return true;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 读取照片是否被旋转
     *
     * @param img
     * @param selectedImage
     * @return
     * @throws IOException
     */
    private static Bitmap rotateImageIfRequired(Context context, Bitmap img, Uri selectedImage) throws IOException {
        InputStream input = context.getContentResolver().openInputStream(selectedImage);
        ExifInterface ei;
        if (Build.VERSION.SDK_INT > 23)
            ei = new ExifInterface(input);
        else
            ei = new ExifInterface(selectedImage.getPath());
        int orientation = ei.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
        switch (orientation) {
            case ExifInterface.ORIENTATION_ROTATE_90:
                return rotateImage(img, 90);
            case ExifInterface.ORIENTATION_ROTATE_180:
                return rotateImage(img, 180);
            case ExifInterface.ORIENTATION_ROTATE_270:
                return rotateImage(img, 270);
            default:
                return img;
        }
    }

    private static Bitmap rotateImage(Bitmap img, int degree) {
        Matrix matrix = new Matrix();
        matrix.postRotate(degree);
        Bitmap rotatedImg = Bitmap.createBitmap(img, 0, 0, img.getWidth(), img.getHeight(), matrix, true);
        img.recycle();
        return rotatedImg;
    }

//    /**
//     * 将View转为Bitmap
//     * @param view
//     * @return
//     */
//    private static Bitmap renderViewToBitmap(View view, int width) {
//        view.measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.EXACTLY), View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
//        view.layout(0, 0, view.getMeasuredWidth(), view.getMeasuredHeight());
//        Bitmap bitmap = Bitmap.createBitmap(width, view.getMeasuredHeight(), Bitmap.Config.ARGB_8888);
//        Canvas canvas = new Canvas(bitmap);
//        view.draw(canvas);
//        return bitmap;
//    }

    /**
     * 将View转为Bitmap  这里分辨率大可以直接默认全部展示
     *
     * @param view
     * @return
     */
    private static Bitmap renderViewToBitmapCamera(View view, int width) {
        view.measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED), View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
        view.layout(0, 0, view.getMeasuredWidth(), view.getMeasuredHeight());
        view.buildDrawingCache();
        Bitmap bitmap = view.getDrawingCache();
        return bitmap;
    }

    public static String bitmapToBase64(Bitmap bitmap) {
        String result = "";
        ByteArrayOutputStream baos = null;
        try {
            if (bitmap != null) {
                baos = new ByteArrayOutputStream();
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, baos);
                baos.flush();
                baos.close();
                byte[] bitmapBytes = baos.toByteArray();
                result = Base64.encodeToString(bitmapBytes, Base64.DEFAULT);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (baos != null) {
                    baos.flush();
                    baos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }
}
