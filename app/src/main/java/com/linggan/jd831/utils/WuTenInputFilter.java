package com.linggan.jd831.utils;

import android.text.InputFilter;
import android.text.Spanned;

import com.lgfzd.base.XBaseApp;
import com.lgfzd.base.utils.XToastUtil;

/**
 * 限制最大输入50次
 */
public class WuTenInputFilter implements InputFilter {

    @Override
    public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
        try {
            int input = Integer.parseInt(dest.toString() + source.toString());
            if (input >= 1 && input <= 50) {
                return null;
            } else {
                XToastUtil.showToast(XBaseApp.instance(), "最大输入值为50次");
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return "";
    }
}
