package com.linggan.jd831.utils;

import android.text.Editable;
import android.text.TextWatcher;
import android.widget.EditText;
import android.widget.Toast;

public class TextLengthWatcher implements TextWatcher {
    private EditText editText;
    private int maxLength;
    private String errorMessage;

    public TextLengthWatcher(EditText editText, int maxLength, String errorMessage) {
        this.editText = editText;
        this.maxLength = maxLength;
        this.errorMessage = errorMessage;
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {
    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
        if (s.length() >= maxLength - 5) {
            int remainingChars = maxLength - s.length();
            if (remainingChars <= 0) {
                Toast.makeText(editText.getContext(), errorMessage, Toast.LENGTH_SHORT).show();
            }
        }
    }

    @Override
    public void afterTextChanged(Editable s) {
    }
} 