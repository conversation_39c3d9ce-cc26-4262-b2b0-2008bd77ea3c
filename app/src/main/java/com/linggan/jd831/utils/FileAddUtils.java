package com.linggan.jd831.utils;

import android.content.Context;
import android.widget.GridView;

import com.linggan.jd831.adapter.FileAllAddAdapter;
import com.linggan.jd831.bean.OssFileEntity;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 附件上传工具类
 */
public class FileAddUtils {

    private List<OssFileEntity> list = new ArrayList<>();//图片路径集合
    private FileAllAddAdapter adapter;//适配器
    private OnImageAddListener onImageAddListener;//添加图片监听器

    public FileAddUtils(Context context, GridView gridView) {
        adapter = new FileAllAddAdapter(list);
        adapter.setDel(true);
        gridView.setAdapter(adapter);
        gridView.setOnItemClickListener((adapterView, view, i, l) -> {
            if (i < list.size()) {
            } else {
                if (onImageAddListener != null) onImageAddListener.onAdd();
            }
        });
        adapter.setOnImageDeleteListener(i -> {
            if (i < list.size()) {
//                delete(list.get(i));//删除本地图片
                list.remove(i);//把图片路径从集合中移除
                adapter.notifyDataSetChanged();//刷新列表
            }
        });
    }

    /**
     * 添加图片
     *
     * @param path 图片路径
     */
    public void addImage(OssFileEntity path) {
        if (list.size() < 8) list.add(path);
    }

    /**
     * 添加图片集
     *
     * @param paths 图片路径集
     */
    public void addImages(List<OssFileEntity> paths) {
        if (list.size() < 8) list.addAll(paths);
    }

    /**
     * 刷新列表
     */
    public void notifyData() {
        adapter.notifyDataSetChanged();
    }

    /**
     * 设置路径集合(重新设置，不是添加)
     *
     * @param paths 路径集
     */
    public void setPaths(List<OssFileEntity> paths) {
        this.list.clear();
        this.list.addAll(paths);
        adapter.setDel(false);
        adapter.notifyDataSetChanged();
    }

    /**
     * 设置最大添加数量
     *
     * @param max 最大可添加数量
     */
    public void setMax(int max) {
        adapter.setMax(max);
    }

    /**
     * 获取图片路径集合
     *
     * @return 集合
     */
    public List<OssFileEntity> getPaths() {
        return this.list;
    }

    /**
     * 获取图片数量
     *
     * @return 数量
     */
    public int getSize() {
        return this.list.size();
    }

    /**
     * 清除文件
     *
     * @param path 文件路径
     */
    private void delete(String path) {
        if (path == null) return;
        File file = new File(path);
        if (file.exists()) file.delete();
    }

    /**
     * 清除本地文件
     */
    public void clean() {
        for (OssFileEntity path : list) {
            if (path == null) return;
//            File file = new File(path);
//            if (file.exists()) file.delete();
        }
    }

    /**
     * 设置图片添加监听
     *
     * @param onImageAddListener 监听器
     */
    public void setOnImageAddListener(OnImageAddListener onImageAddListener) {
        this.onImageAddListener = onImageAddListener;
    }

    /**
     * 图片添加监听器
     */
    public interface OnImageAddListener {
        void onAdd();
    }
}
