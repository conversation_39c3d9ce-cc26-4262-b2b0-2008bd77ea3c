package com.linggan.jd831;

/**
 * 类  名： 接口地址工具类（一般不做修改固定地址）
 * 描   述：这里是记录接口具体地址
 * 作  者：lgkj
 * 时  间：2023-04-01
 * 版  权：lgkj
 */
public interface ApiUrlsUtils {

    /**
     * iM地址与端口
     */
    String IM1 = "192.168.1.128";
    int IM_PORT1 = 23456;
    //正式
    String IM = "118.114.172.196";
    int IM_PORT = 30456;
    //测试
//    String IM = "121.30.199.78";
//    int IM_PORT = 23456;
    /**
     * 这里来处理api带的路径--预留的
     */
    String API_VERSION = "";
    /**
     * 埋点数据接口--个人辅助测试接口不属于项目接口
     */
    String MD_URL = "http://md.cdqtkeji.top/index/index/index";
    /**
     * 获取登录时候的公钥
     */
    String LOGIN_KEY_URL = API_VERSION + "/certificat/getLoginKey";
    /**
     * 数据字典查询
     */
    String ZIDIAN_QUERY_DATA = API_VERSION + "/dictionary/web/jdzd/listZdInfo";
    /**
     * 文件上传
     */
    String FILE_UPLOAD = API_VERSION + "/certificat/upload";

    String HYBD_UPLOAD = API_VERSION + "/personinfo/web/ywwczl/fyjl/hybd";
    /**
     * 登录
     */
    String LOGIN_URL = API_VERSION + "/certificat/login";
    /**
     * 修改密码
     */
    String CHANGE_PASS_WORD = API_VERSION + "/certificat/changePasswordApp";
    /**
     * 登出
     */
    String LOGIN_OUT = API_VERSION + "/certificat/loginout";
    /**
     * 验证码登录
     */
    String LOGIN_MSG = API_VERSION + "/certificat/msgLogin";
    /**
     * 注册密码发送验证码
     */
    String SEND_REG_MSG = API_VERSION + "/msg/sendRegisterCode";
    /**
     * 忘记密码发送验证码
     */
    String SEND_FIND_MSG = API_VERSION + "/msg/sendForgetCode";
    /**
     * 登录发送验证码
     */
    String SEND_LOGIN_MSG = API_VERSION + "/msg/sendLoginCode";
    /**
     * 首页-人员列表查询
     */
    String PEOPLE_QUERY_LIST = API_VERSION + "/personinfo/app/xdryJbxx/queryJbxx";
    /**
     * 首页-人员列表查询--详情
     */
    String PEOPLE_INFO = API_VERSION + "/personinfo/app/xdryJbxx/querySimple";
    /**
     * 首页-人员列表查询--详情
     */
    String PEOPLE_INFO_TOTAL_NUM = API_VERSION + "/personinfo/app/xdryJbxx/totalPersonNum";
    /**
     * 首页-人员列表-人员状态
     */
    String PEOPLE_STATUS_LIST = API_VERSION + "/personinfo/app/ryejzt/dqztlb";
    /**
     * 首页-人员列表-人员结束
     */
    String PEOPLE_STATUS_END = API_VERSION + "/personinfo/app/ryejzt/jszt";
    /**
     * 首页-人员列表-人员状态-详情
     */
    String PEOPLE_STATUS_INFO = API_VERSION + "/personinfo/app/ryejzt/lsztxq";
    /**
     * 首页-人员列表-人员状态-新增
     */
    String PEOPLE_STATUS_ADD = API_VERSION + "/personinfo/app/ryejzt/ryztxz";
    /**
     * 首页(工作人员)-人员新增-人员所有顶级状态和顶级状态所包含的一级状态
     */
    String PEOPLE_ADD_ALL_TYPE = API_VERSION + "/personinfo/xdryJbxx/findAllType";
    /**
     * 家访列表
     */
    String HOME_VISIT_LIST = API_VERSION + "/homevisiting/app/jf/page";
    /**
     * 家访--添加家访记录
     */
    String HOME_VISIT_SAVE = API_VERSION + "/homevisiting/app/jf/save";
    /**
     * 家访--编辑家访记录
     */
    String HOME_VISIT_UPDATE = API_VERSION + "/homevisiting/app/jf/updateByBh";
    //核查列表
    String HECHA_LIST = API_VERSION + "/homevisiting/app/hc/page";
    //核查新增
    String HECHA_SAVE = API_VERSION + "/homevisiting/app/hc/save";
    //核查编辑
    String HECHA_UPDATE = API_VERSION + "/homevisiting/app/hc/updateByBh";
    //核查详情
    String HECHA_INFO = API_VERSION + "/homevisiting/app/hc/getByBh/";
    /**
     * 走访--添加走访记录
     */
    String ZOU_VISIT_SAVE = API_VERSION + "/homevisiting/app/zfjy/save";
    /**
     * 走访--编辑走访记录
     */
    String ZOU_VISIT_UPDATE = API_VERSION + "/homevisiting/app/zfjy/updateByBh";
    /**
     * 走访--走访记录列表
     */
    String ZOU_VISIT_LIST = API_VERSION + "/homevisiting/app/zfjy/page";
    /**
     * 走访--走访记录详情
     */
    String ZOU_VISIT_INFO = API_VERSION + "/homevisiting/app/zfjy/getByBh/";
    /**
     * 注册-获取行政区域
     */
    String getXzqhTree = API_VERSION + "/organize/xzqh/getXzqhTree";
    /**
     * 行政区域新接口
     */
    String getXzqhTreeV2 = API_VERSION + "/organize/xzqh/getXzqhTreeV2";
    /**
     * 注册-根据行政区域-获取所在单位
     */
    String getDwByxzqh = API_VERSION + "/organize/dwxx/getDwByxzqh";
    /**
     * 宣教-根据行政区域-获取所在单位
     */
    String getDwByxzqh_Xj = API_VERSION + "/organize/dwxx/getDwByxzqhBj";
    /**
     * 注册会员
     */
    String REGISTER_USER = API_VERSION + "/certificat/user/save";
    /**
     * 工作人员列表
     */
    String WORK_USER_LIST = API_VERSION + "/certificat/user/listNoPage";
    /**
     * 工作人员列表---新
     */
    String WORK_USER_LIST_NEM = API_VERSION + "/certificat/user/listNoPageNew";
    /**
     * 家访详情
     */
    String HOME_VISIT_INFO = API_VERSION + "/homevisiting/app/jf/getByBh/";
    /**
     * 家访详情-周期
     */
    String HOME_VISIT_ZQ = API_VERSION + "/homevisiting/app/jf/getCycle";
    /**
     * 谈话列表
     */
    String TALK_LIST = API_VERSION + "/talk/app/th/page";
    // 工作计划-列表
    String GZJH_LIST = API_VERSION + "/personinfo/web/gzjh/list";
    // 工作计划-详情
    String GZJH_INFO = API_VERSION + "/personinfo/web/gzjh/get";
    // 工作计划-删除
    String GZJH_DEL = API_VERSION + "/personinfo/web/gzjh/del";
    // 工作计划-新增
    String GZJH_SAVE = API_VERSION + "/personinfo/web/gzjh/save";
    // 工作计划-更新
    String GZJH_UPDATE = API_VERSION + "/personinfo/web/gzjh/update";
    // 季度评估-列表
    String JIDU_PINGGU_LIST = API_VERSION + "/assess/web/jdPg/list";
    // 季度评估-新增
    String JIDU_PINGGU_ADD = API_VERSION + "/assess/web/jdPg/save";
    // 季度评估-详情
    String JIDU_PINGGU_INFO = API_VERSION + "/assess/web/jdPg/get";
    // 季度评估-更新
    String JIDU_PINGGU_UPDATE = API_VERSION + "/assess/web/jdPg/update";
    // 季度评估-删除
    String JIDU_PINGGU_DEL = API_VERSION + "/assess/web/jdPg/del";
    /**
     * 谈话详情
     */
    String TALK_INFO = API_VERSION + "/talk/app/th/getByBh/";
    /**
     * 谈话保存
     */
    String TALK_SAVE = API_VERSION + "/talk/app/th/save";
    /**
     * 谈话更新
     */
    String TALK_UPDATE = API_VERSION + "/talk/app/th/updateByBh";
    /**
     * 谈话模板
     */
    String TALK_MUBAN_LIST = API_VERSION + "/talk/app/th/mb/list";

    // 帮扶模板-列表
    String BANGFU_MUBAN_LIST = API_VERSION + "/care/app/yryc/listMb";
    /**
     * 谈话模板新增
     */
    String TALK_MUBAN_ADD = API_VERSION + "/talk/app/th/mb/save";

    // 帮扶模板新增
    String BANGFU_MUBAN_ADD = API_VERSION + "/care/app/yryc/saveMb";
    /**
     * 谈话模板删除
     */
    String TALK_MUBAN_DEL = API_VERSION + "/talk/app/th/mb/deleteByMbBh";
    /**
     * 谈话模板删除
     */
    String BANGFU_MUBAN_DEL = API_VERSION + "/care/app/yryc/delMb";
    /**
     * 谈话模板具体详情
     */
    String TALK_MUBAN_INFO = API_VERSION + "/talk/app/th/mb/listByMbBh/";

    /**
     * 谈话模板具体详情
     */
    String BANGFU_MUBAN_INFO = API_VERSION + "/care/app/yryc/getMb";
    /**
     * 谈话周期
     */
    String TALK_ZHOU_QI = API_VERSION + "/talk/app/th/getCycle";
    /**
     * 检测列表
     */
    String URINE_LIST = API_VERSION + "/check/app/jc/page";
    /**
     * 检测详情
     */
    String URINE_INFO = API_VERSION + "/check/app/jc/getByBh/";
    /**
     * 检测周期
     */
    String URINE_ZHOU_QI = API_VERSION + "/check/app/jc/getCycle";
    /**
     * 检测--根据检测类型查询检测方式
     */
    String URINE_JIANCHE_TYPE = API_VERSION + "/check/app/jc/getCheckWayByLx";
    /**
     * 检测保存
     */
    String URINE_SAVE = API_VERSION + "/check/app/jc/save";
    /**
     * 检测更新
     */
    String URINE_UPDATE = API_VERSION + "/check/app/jc/updateByBh";
    /**
     * 请假列表
     */
    String LEAVE_LIST = API_VERSION + "/leave/app/qj/page";
    /**
     * 请假详情
     */
    String LEAVE_INFO = API_VERSION + "/leave/app/qj/getByBh/";
    /**
     * 请假保存
     */
    String LEAVE_SAVE = API_VERSION + "/leave/app/qj/save";
    /**
     * 请假更新
     */
    String LEAVE_UPDATE = API_VERSION + "/leave/app/qj/updateByBh";
    /**
     * 销假
     */
    String LEAVE_XIAO = API_VERSION + "/leave/app/qj/annualLeave";
    /**
     * 销假-根据任务编号-查询详情信息
     */
    String LEAVE_XIAO_INFO = API_VERSION + "/leave/app/qj/getByZqBh/";
    /**
     * 销假-根据销假编号查询请假信息
     */
    String LEAVE_XJBH_XIAO_INFO = API_VERSION + "/leave/app/qj/getByXjBh/";
    /**
     * 季度评估列表
     */
    String JIDU_LIST = API_VERSION + "/assess/app/drugAssess/listAssesses";
    /**
     * 季度评估详情
     */
    String JIDU_INFO = API_VERSION + "/assess/app/drugAssess/getAssesse";
    /**
     * 季度评估保存
     */
    String JIDU_SAVE = API_VERSION + "/assess/app/drugAssess/saveAssess";
    String JIDU_SAVE_V2 = API_VERSION + "/assess/app/drugAssess/saveAssessV2";
    /**
     * 季度评估更新
     */
    String JIDU_UPDATE = API_VERSION + "/assess/app/drugAssess/updateAssess";
    String JIDU_UPDATE_V2 = API_VERSION + "/assess/app/drugAssess/updateAssessV2";
    /**
     * 季度评估-通过初步风险等级选项获取对应风险等级信息
     */
    String JIDU_FIRST_RISK = API_VERSION + "/assess/app/lhpg/getFirstRiskInfo";
    /**
     * 季度评估-低风险额外验证列表
     */
    String JIDU_LOW = API_VERSION + "/assess/app/drugAssess/listLowRiskCheck";
    /**
     * 季度评估-量化评估题目和内容
     */
    String JIDU_LHPG = API_VERSION + "/assess/app/lhpg/getLhpgTm";
    /**
     * 年度考察列表
     */
    String YEAR_KAO_LIST = API_VERSION + "/yearcheck/app/drugYearCheck/lists";
    /**
     * 年度考察详情
     */
    String YEAR_KAO_INFO = API_VERSION + "/yearcheck/app/drugYearCheck/getYearCheck";
    /**
     * 年度考察保存
     */
    String YEAR_KAO_SAVE = API_VERSION + "/yearcheck/app/drugYearCheck/save";
    /**
     * 年度考察更新
     */
    String YEAR_KAO_UPDATE = API_VERSION + "/yearcheck/app/drugYearCheck/update";
    /**
     * 人员类型
     */
    String PERSON_INFO_TYPE = API_VERSION + "/personinfo/app/xdryJbxx/queryRylx";
    /**
     * 人员新增
     */
    String PERSON_INFO_ADD = API_VERSION + "/personinfo/app/xdryJbxx/saveJbxx";
    /**
     * 人员详情更新
     */
    String PERSON_INFO_UPDATE = API_VERSION + "/personinfo/app/xdryJbxx/updateJbxx";
    /**
     * 评估/考察--周期
     */
    String PING_YEAR_ZHOU_QI = API_VERSION + "/rule/cycle/listCycles";

    /**
     * 根据行政区域代码回显地区
     */
    String FROM_CODE_GET_AREA_NAME = API_VERSION + "/organize/xzqh/getWholeAreaName";
    /**
     * 查看可转换的类型
     */
    String PEOPLE_TYPE_LIST = API_VERSION + "/personinfo/app/xdryJbxx/lxzh";
    /**
     * 人员类型转换
     */
    String PEOPLE_TYPE_UPDATE = API_VERSION + "/personinfo/app/xdryJbxx/rylxzh";
    /**
     * 人员类型查看
     */
    String PEOPLE_TYPE_INFO = API_VERSION + "/personinfo/app/xdryJbxx/dqlx";
    /**
     * 违反协议保存
     */
    String WEI_FAN_XIE_YI_SAVE = API_VERSION + "/violation/app/wfxy/saveWfxy";
    /**
     * 违反协议详情
     */
    String WEI_FAN_XIE_YI_INFO = API_VERSION + "/violation/app/wfxy/getWfxyDetail";

    /**
     * 户籍地迁移审批详情
     */
    String RESIDENCE_CHANGE_DETAIL = API_VERSION + "/personinfo/web/ryejzt/lsztxq";
    /**
     * 违反协议-编辑
     */
    String WEI_FAN_XIE_YI_UPDATE = API_VERSION + "/violation/app/wfxy/updateWfxy";
    /**
     * 违反协议-列表
     */
    String WEI_FAN_XIE_YI_LIST = API_VERSION + "/violation/app/wfxy/listWfxys";
    /**
     * 严重违反协议-列表
     */
    String WEI_FAN_YAN_XIE_YI_LIST = API_VERSION + "/violation/app/wfxy/listYzwfxys";
    /**
     * 违反协议撤销
     */
    String WEI_FAN_XIE_YI_CHE_XIAO = API_VERSION + "/violation/app/wfxy/updateUndo";
    /**
     * 违反协议事实
     */
    String WEI_FAN_XIE_YI_SHI_SHI_LIST = API_VERSION + "/violation/app/wfxy/listYzwfxyss";
    /**
     * 任务中心列表
     */
    String TASK_CEN_LIST = API_VERSION + "/rule/app/rwzq/findList";
    /**
     * 任务中心列表
     */
    String TASK_CEN_LIST_FROM_QY = API_VERSION + "/rule/app/rwzq/findRwList";
    /**
     * 任务中心数量
     */
    String TASK_CEN_NUM = API_VERSION + "/rule/app/rwzq/findTotalNum";
    /**
     * 任务中心数量-审批数量
     */
    String TASK_CEN_SP_NUM = API_VERSION + "/work/processInstance/count";
    /**
     * 审批不通过列表
     */
    String TASK_SP_NO_PASS = API_VERSION + "/work/processInstance/rejected";
    /**
     * 审批中通过列表
     */
    String TASK_SP_ING = API_VERSION + "/work/processInstance/getStartByMe";
    /**
     * 审批--待审批列表
     */
    String TASK_SP_IS_PASS = API_VERSION + "/work/processInstance/getMyTask";
    /**
     * 审批--查看审批进度
     */
    String TASK_SP_PROGRESS = API_VERSION + "/work/processInstance/getProcessProgress";
    /**
     * 审批--提交
     */
    String TASK_SP_FINISH = API_VERSION + "/work/processInstance/completeTask";
    /**
     * 审批-历史记录
     */
    String TASK_SP_LISHI = API_VERSION + "/work/processInstance/myHistoryTask";
    /**
     * 签到-新增
     */
    String SIGN_ADD = API_VERSION + "/sign/app/qd/saveQd";
    /**
     * 签到-列表
     */
    String SIGN_LIST = API_VERSION + "/sign/app/qd/list";
    /**
     * 签到-详情
     */
    String SIGN_INFO = API_VERSION + "/sign/app/qd/qdDetail";
    /**
     * 签到-所有周期
     */
    String SIGN_ZHOU_QI = API_VERSION + "/rule/app/rwzq/getQdRwzqInNow";
    /**
     * 签到-下次签到周期
     */
    String SIGN_NEXT_ZHOU_QI = API_VERSION + "/rule/app/rwzq/getNextQdzq";
    /**
     * 人员动向确认
     */
    String PER_DONG_XIANG_SURE = API_VERSION + "/personinfo/app/dtqr/sureTrendNew";
    /**
     * 人员动向--判断当前人员是否在安保区域
     */
    String SURE_PEO_IS_ANBAOQU = API_VERSION + "/organize/xzqh/isParentOrEq";
    /**
     * 违规报告列表
     */
    String REPORT_LIST = API_VERSION + "/violationreport/app/wgbg/appPageList.do";
    /**
     * 违规报告--新增
     */
    String REPORT_SAVE = API_VERSION + "/violationreport/app/wgbg/reportAdd.do";
    /**
     * 违规报告--详情
     */
    String REPORT_INFO = API_VERSION + "/violationreport/app/wgbg/reportGet.do";
    /**
     * 违规报告--删除
     */
    String REPORT_DEL = API_VERSION + "/violationreport/app/wgbg/reportDel.do";
    /**
     * 人员类型转换/延期信息(rwRwlx的code为13 14的时候调用)
     */
    String PEOPLE_ZHUAN_YUJING = API_VERSION + "/personinfo/app/lxzhyq/warningItem";
    /**
     * 人员类型转换预警--延期结果查询
     */
    String PEOPLE_YUJING_RESULT = API_VERSION + "/personinfo/app/lxzhyq/findDelayCycle";
    /**
     * 人员类型转换预警--延期保存接口
     */
    String PEOPLE_YUJING_SAVE = API_VERSION + "/personinfo/app/lxzhyq/addDelayTask";
    /**
     * 人员类型转换预警--任务完成后，变更任务状态
     */
    String PEOPLE_YUJING_UPDATE = API_VERSION + "/rule/app/rwzq/dealTask";
    /**
     * 根据代码获取公安局派出所名字
     */
    String GET_GONG_ANJU_NAME = API_VERSION + "/organize/dwxx/getDwxx";
    /**
     * 一人一策记录列表
     */
    String ONE_PEO_CE_LIST = API_VERSION + "/care/app/yryc/listYrycJl";

    /**
     * 一人一策记录新增
     */
    String ONE_PEO_CE_ADD = API_VERSION + "/care/app/yryc/saveYryc";
    /**
     * 一人一策记录详情
     */
    String ONE_PEO_CE_INFO = API_VERSION + "/care/app/yryc/getYrycXq";
    /**
     * 一人一策知晓
     */
    String ONE_PEO_CE_ZHI_XIAO = API_VERSION + "/care/app/yryc/saveYzx";

    /**
     * 一人一策知晓--信息有误反馈
     */
    String ONE_PEO_CE_ERROR_MSG = API_VERSION + "/care/app/yryc/saveXxyw";

    /**
     * 帮扶成效（关爱类型）
     */
    String ONE_PEO_CE_CARE_TYPE = API_VERSION + "/care/app/yryc/getBfcx";
    /**
     * 平安关爱推荐列表
     */
    String CARE_TUI_LIST = API_VERSION + "/care/app/pagaTj/listAppPagaTj";
    /**
     * 平安关爱推荐列表-详情
     */
    String CARE_TUI_INFO = API_VERSION + "/care/app/pagaTj/getPagaXq";
    /**
     * 无法关爱-提交
     */
    String CARE_TUI_NO_CARE_SAVE = API_VERSION + "/care/app/pagaTj/saveWfga";
    /**
     * 惩戒-列表
     */
    String PUNISH_LIST = API_VERSION + "/punish/app/punish/list";
    /**
     * 惩戒-新增
     */
    String PUNISH_SAVE = API_VERSION + "/punish/app/punish/save";
    /**
     * 惩戒-更新
     */
    String PUNISH_EDIT = API_VERSION + "/punish/app/punish/update";
    /**
     * 惩戒-详情
     */
    String PUNISH_INFO = API_VERSION + "/punish/app/punish/getOne";
    /**
     * 惩戒-任务中心-管控记录
     */
    String PUNISH_TASK_INFO = API_VERSION + "/punish/app/punish/listByZqBh";
    /**
     * 惩戒-任务中心-管控记录--保存
     */
    String PUNISH_TASK_INFO_EDIT = API_VERSION + "/personinfo/app/xdryJbxx/updateFxgk";
    /**
     * 任务中心-放弃审批
     */
    String TASK_NEVE_GIVE_UP = API_VERSION + "/work/processInstance/processInstanceVariable";
    /**
     * 任务中心-请假动向确认
     */
    String LEAVE_SURE_DONG_XIANG = API_VERSION + "/leave/app/qj/dxqr/save";
    /**
     * 工作人员APP查询首页功能
     */
    String WORK_INDEX_MENU = API_VERSION + "/certificat/app/function/getMyFunction";
    /**
     * 工作人员APP查询首页功能(new)
     */
    String WORK_INDEX_MENU_NEW = API_VERSION + "/certificat/app/function/getMyFunctionV2";
    //工作人员APP查询吸毒人员主页按钮
    String WORK_PEO_INFO_MENU = API_VERSION + "/certificat/app/function/getXdryZyGn";
    //社工获取考核数据
    String KAO_HE_LV = API_VERSION + "/personinfo/dashbord/lghqkhsjV2";
    /**
     * APP版本更新
     */
    String APP_UPDATE_VERSION = API_VERSION + "/certificat/app/function/getAppBb";
    /**
     * 平安关爱推荐人数，负责人数
     */
    String CARE_INDEX_NUM = API_VERSION + "/care/app/pagaTj/findTjrs";
    /**
     * app端保存或更新推送token
     */
    String PUSH_TOKEN = API_VERSION + "/msg/app/user/saveOrUpdateToken";
    /**
     * 场所-列表
     */
    String PLACE_AREA_LIST = API_VERSION + "/sdpc/sdcs/list";
    /**
     * 新增场所
     */
    String PLACE_ADD = API_VERSION + "/sdpc/sdcs/xzcs";
    /**
     * 新增场所-详情
     */
    String PLACE_INFO = API_VERSION + "/sdpc/sdcs/csxq";
    /**
     * 查询排查记录
     */
    String PLACE_PC_CODE_LIST = API_VERSION + "/sdpc/pcjl/cxcspcjl";
    /**
     * 查询排查记录
     */
    String PLACE_PC_CODE_INFO = API_VERSION + "/sdpc/pcjl/cxpcxq";
    /**
     * 排查场所类型
     */
    String PLACE_AREA_TYPE = API_VERSION + "/rule/sdpc/cszq/cxcslx";
    /**
     * 新增排查记录
     */
    String PLACE_PC_CODE_ADD = API_VERSION + "/sdpc/pcjl/xzpcjl";
    /**
     * 查询排查项目
     */
    String PLACE_PC_XM_LIST = API_VERSION + "/sdpc/pcxm/list";
    /**
     * 场所排查--任务中心
     */
    String PLACE_PC_TASK_LIST = API_VERSION + "/sdpc/rwxf/dpccs";
    /**
     * 场所排查--关联任务
     */
    String PLACE_GL_TASK = API_VERSION + "/sdpc/rwxf/cxdwrw";
    /**
     * 场所排查记录-民警
     */
    String PLACE_PC_MIN_JING = API_VERSION + "/certificat/user/hqmj";
    /**
     * 场所排查-地区查询待排查场所统计
     */
    String PLACE_PC_TASK_AREA = API_VERSION + "/sdpc/rwxf/countByXzqh";
    /**
     * 人员-采集设别信息
     */
    String WORK_DEVER_INFO_ADD = API_VERSION + "/log/gzry/xzsbxx";

    /**
     * 自动计算量表（评估时回显）
     */
    String PING_GU_FXF = API_VERSION + "/assess/app/lhpg/findZdjs";
    /**
     * 人员状态审批接口
     */
    String PEO_TYPE_SH = API_VERSION + "/personinfo/app/ryejzt/getByBh";
    /**
     * 管控措施列表
     */
    String PEO_WGCS_LIST = API_VERSION + "/personinfo/app/xdryGkcs/findControlMeasure.do";
    /**
     * 管控措施列表
     */
    String PEO_WGCS_ADD = API_VERSION + "/personinfo/app/xdryGkcs/addControlMeasure.do";
    /**
     * 管控措施列表
     */
    String PEO_WGCS_INFO = API_VERSION + "/personinfo/app/xdryGkcs/getControlMeasure.do";
    /**
     * 管控措施列表
     */
    String PEO_WGCS_DEL = API_VERSION + "/personinfo/app/xdryGkcs/delControlMeasure.do";
    /**
     * 人员认领--列表
     */
    String PEO_REN_LING = API_VERSION + "/personinfo/app/xdryJbxx/listNoXzApp";
    /**
     * 人员认领-确认
     */
    String PEO_REN_LING_SAVE = API_VERSION + "/personinfo/app/xdryJbxx/updateXzApp";
    /**
     * 禁毒办-任务中心(最新接口)
     */
    String TASK_CEN_DATA_NEW = API_VERSION + "/rule/app/rwzq/findUndealByAreaAndLx";
    /**
     * 易制毒企业-信息员注册企业信息
     */
    String YZDQY_MSG_USER_REG = API_VERSION + "/precursor/app/yzdqy/saveYzdQy";
    /**
     * 易制毒企业-判断是否注册企业，是否初始化
     */
    String YZDQY_MSG_IS_QY = API_VERSION + "/precursor/app/yzdqy/getSfZcCsh";
    /**
     * 易制毒企业-判断是否仓管员信息是否完善
     */
    String YZDQY_CG_MSG = API_VERSION + "/precursor/app/yzdqy/getSfZcCk";
    /**
     * 易制毒企业-通过统一社会信用代码获取仓库信息
     */
    String YZDQY_CANG_KU_LIST = API_VERSION + "/precursor/app/yzdqy/listYzdCkXx";
    /**
     * 易制毒企业-仓管员注册仓库
     */
    String YZDQY_CANG_KU_USER_REG = API_VERSION + "/precursor/app/yzdqy/saveYzdCkZc";

    /**
     * 易制毒企业-初始化时获取仓库信息和化学品信息
     */
    String YZDQY_CANG_CSH_CK_LIST = API_VERSION + "/precursor/app/yzdqy/getCkHxpXx";

    /**
     * 易制毒企业-初始化时获取仓库信息和化学品信息-保存
     */
    String YZDQY_CANG_CSH_CK_ADD = API_VERSION + "/precursor/app/yzdqy/saveCshCkHxp";
    /**
     * 宣教活动列表
     */
    String XJHD_LIST = API_VERSION + "/propaganda/app/xjhd/page";
    /**
     * 宣教活动列表
     */
    String XJHD_LIST_NO = API_VERSION + "/propaganda/app/xjhd/list";
    /**
     * 宣教活动--新增
     */
    String XJHD_ADD = API_VERSION + "/propaganda/app/xjhd/save";
    /**
     * 宣教活动--详情
     */
    String XJHD_INFO = API_VERSION + "/propaganda/app/xjhd/getByBh";
    /**
     * 宣教活动--上传
     */
    String XJHD_UPLOAD = API_VERSION + "/propaganda/app/xjhd/xchd/save";
    /**
     * 宣教活动--场馆上传
     */
    String XJHD_CG_UPLOAD = API_VERSION + "/propaganda/app/xjhd/cgcg/sc/save";
    /**
     * 宣教活动--线上宣传
     */
    String XJHD_XSXC_UPLOAD = API_VERSION + "/propaganda/app/xjhd/xsxc/save";
    /**
     * 宣教活动--未选择活动方式上传接口
     */
    String XJHD_UNSELECTED_UPLOAD = API_VERSION + "/propaganda/app/xjhd/xsxc/saveKbxx";
    /**
     * 宣教活动--上传之后-���情
     */
    String XJHD_UPLOAD_INFO = API_VERSION + "/propaganda/app/xjhd/xchd/getScXq";
    /**
     * 宣教活动--任务中心我的
     */
    String XJHD_TASK_ME = API_VERSION + "/propaganda/app/rwgl/wdRwThirty";
    /**
     * 宣教活动--辖区任务中心
     */
    String XJHD_TASK_XQ = API_VERSION + "/propaganda/app/rwgl/wdXqRwThirty";
    /**
     * 宣教活动--任务中心辖区点击辖区展示的列表
     */
    String XJHD_TASK_XQ_LIST = API_VERSION + "/propaganda/app/rwgl/wdXqRwLb";

    /**
     * 宣教活动--任务中心-完成任务
     */
    String XJHD_TASK_FINISH = API_VERSION + "/propaganda/app/rwzx/finishRw";

    /**
     * 站点切换
     */
    String ZHAN_CHANGE = API_VERSION + "/certificat/getAddr";
    /**
     * 易制毒企业--首页顶部数据
     */
    String YZDQY_INDEX = API_VERSION + "/precursor/app/pdzx/findQysy";
    /**
     * 易制毒企业--首页顶部数据-数量
     */
    String YZDQY_INDEX_NUM = API_VERSION + "/precursor/app/index/noAckCount";
    /**
     * 易制毒企业--历史记录列表
     */
    String YZDQY_LS_LIST = API_VERSION + "/precursor/app/yzd/history/list";
    /**
     * 易制毒企业--历史记录详情
     */
    String YZDQY_LS_LIST_INFO = API_VERSION + "/precursor/app/yzd/history/getOne";
    /**
     * 易制毒企业--仓管员-出库/入库/报损登记
     */
    String YZDQY_CGY_CRBDJ = API_VERSION + "/precursor/app/yzd/save";
    /**
     * 易制毒企业--仓管员-出（入）库、报损新增时，查询仓库列表
     */
    String YZDQY_CGY_CK_LIST = API_VERSION + "/precursor/app/yzd/listCk";
    /**
     * 易制毒企业--仓管员-出（入）库、报损新增时，查询化学品列表
     */
    String YZDQY_CGY_HXP_LIST = API_VERSION + "/precursor/app/yzd/listHxp";
    /**
     * 易制毒企业--仓管员--出（入）库、报损新增时，查询化学品库存结余
     */
    String YZDQY_CGY_KCJY_SS = API_VERSION + "/precursor/app/yzd/getKcInfo";

    /**
     * 易制毒企业--仓管员--出（入）库、报损新增时，查询化学品规格信息及库存结余
     */
    String YZDQY_CGY_CKKXP_MSG = API_VERSION + "/precursor/app/yzd/getKcGgInfo";
    /**
     * 易制毒企业-- 出（入）库、报损确认
     */
    String YZDQY_CRK_SURE = API_VERSION + "/precursor/app/yzd/ack";
    /**
     * 易制毒企业--获取仓库库存信息
     */
    String YZDQY_CANG_KU_INFO = API_VERSION + "/precursor/app/yzdqy/listCkKcXx";
    /**
     * 易制毒企业--注册企业时获取仓库信息
     */
    String YZDQY_CANG_KU_ONTHER_LIST = API_VERSION + "/precursor/app/yzdqy/listCkxx";
    /**
     * 易制毒企业--注销企业
     */
    String YZDQY_QIYE_ZHU_XIAO = API_VERSION + "/precursor/app/yzdqy/yzdQyZx";


    /**
     * 站点切换--得到地区id
     */
    String ZHAN_CHANGE_AREAID = API_VERSION + "/organize/xzqh/getXzqhdmByName";
    /**
     * 异地服务--待确认名单列表
     */
    String YI_DI_SER_SURE_LIST = API_VERSION + "/personinfo/app/ryejzt/listYdfw";
    /**
     * 异地服务--待确认名单详情
     */
    String YI_DI_SER_INFO = API_VERSION + "/personinfo/app/ryejzt/getYdfwDetail";
    /**
     * 异地服务--待确认名单详情
     */
    String YI_DI_SER_SURE = API_VERSION + "/personinfo/app/ryejzt/ackYdfw";
    /**
     * 异地服务--人员服务名单
     */
    String YI_DI_SER_MD = API_VERSION + "/personinfo/app/ryejzt/listYdfwRy";
    /**
     * 禁毒宣教--修改查阅
     */
    String JDXU_CY = API_VERSION + "/propaganda/app/rwzx/cyInfo";
    /**
     * 禁毒宣教--首页数据
     */
    String JDXU_HOME_LIST = API_VERSION + "/wsdatas/app/article/articleListApp.do";
    /**
     * 禁毒宣教--首页视频数据
     */
    String JDXU_HOME_VIDEO_LIST = API_VERSION + "/wsdatas/app/article/wxDyListApp.do";
    /**
     * 禁毒宣教--首页视频数据
     */
    String JDXU_HOME_NR_LIST = API_VERSION + "/propaganda/app/xcnr/page";
    /**
     * 禁毒宣教--首页视频数据
     */
    String JDXU_HOME_NR_INFO = API_VERSION + "/propaganda/app/xcnr/getByBh";
    /**
     * 核查请求列表
     */
    String HE_CHA_LIST = API_VERSION + "/personinfo/app/hcqq/findHcqqList.do";
    /**
     * 核查请求列表-详情
     */
    String HE_CHA_INFO = API_VERSION + "/personinfo/app/hcqq/findHcqqById.do";
    /**
     * 核查请求-新增
     */
    String HE_CHA_ADD = API_VERSION + "/personinfo/app/hcqq/addHcqq.do";

    /**
     * 核查请求-任务-确认
     */
    String HE_CHA_TASK_SURE = API_VERSION + "/personinfo/app/hcqq/ryhcRw.do";
    /**
     * 核查请求-任务-关押情况
     */
    String HE_CHA_TASK_GYQK = API_VERSION + "/dictionary/web/jdzd/listGyqk";

    /**
     * 风险等级修改
     */
    String RISK_LVL_EDIT = API_VERSION + "/personinfo/app/fxdj/xg/save";
    /**
     * 风险等级修改-查询人员是否存在审批中的风险等级修改记录
     */
    String RISK_LVL_EDIT_SPZ = API_VERSION + "/personinfo/app/fxdj/xg/wsp";

    /**
     * 社工/禁毒办--污水点位列表
     */
    String SEWAGE_WS_DW_LIST = API_VERSION + "/sewage/cyd/lb";
    /**
     * 社工/禁毒办--采样点新增
     */
    String SEWAGE_WS_DW_ADD = API_VERSION + "/sewage/cyd/xz";
    /**
     * 社工/禁毒办--采样点编辑
     */
    String SEWAGE_WS_DW_EDIT = API_VERSION + "/sewage/cyd/xg";
    /**
     * 社工/禁毒办--采样点新增--获取生成编号
     */
    String SEWAGE_WS_DW_BH = API_VERSION + "/sewage/cyd/xz/bh";
    /**
     * 社工/禁毒办--污水样本详情
     */
    String SEWAGE_WS_YB_INFO = API_VERSION + "/sewage/app/yb/getYb";
    /**
     * 社工/禁毒办--污水样本列表
     */
    String SEWAGE_WS_YB_LIST = API_VERSION + "/sewage/app/yb/listYb";
    /**
     * 社工/禁毒办--污水样本寄送新增更新（可批量）
     */
    String SEWAGE_WS_YB_SEND = API_VERSION + "/sewage/app/yb/saveOrUpdateJy";
    /**
     * 社工/禁毒办--污水样本检测信息新增更新
     */
    String SEWAGE_WS_YB_JCW_SAVE = API_VERSION + "/sewage/app/yb/saveOrUpdateJc";
    /**
     * 社工/禁毒办--勾选多个样本时，判断是否属于同一任务组
     */
    String SEWAGE_WS_YB_JCW_SJPD = API_VERSION + "/sewage/app/yb/canBatch";
    /**
     * 目标物下拉列表
     */
    String SEWAGE_WS_MBW_LIST = API_VERSION + "/sewage/mbw/lb/xl";

    /**
     * 污水--获取各辖区任务数量
     */
    String SEWAGE_WS_TASK_NUM_LIST = API_VERSION + "/sewage/rwxf/getDqsl";
    /**
     * 污水--获取各辖区任务数量
     */
    String SEWAGE_WS_TASK_SL_LIST = API_VERSION + "/sewage/rwxf/getXqRwsl";
    /**
     * 污水--获取各辖区任务列表
     */
    String SEWAGE_WS_TASK_LIST = API_VERSION + "/sewage/rwxf/listXqRw";
    /**
     * 污水-辖区--任务分配社工
     */
    String SEWAGE_WS_FP_WORKER = API_VERSION + "/sewage/rwxf/wsrwFp";
    /**
     * 污水-辖区--社工列表
     */
    String SEWAGE_WS_WORKER_LIST = API_VERSION + "/certificat/listWsSg";
    /**
     * 污水-=任务中心-个人任务列表
     */
    String SEWAGE_WS_GR_TASK_LIST = API_VERSION + "/sewage/rwxf/listGrRw";
    /**
     * 污水-个人-任务中心-任务详情信息
     */
    String SEWAGE_WS_GR_TASK_INFO = API_VERSION + "/sewage/rwxf/getRwxq";
    /**
     * 污水-个人-任务中心-污水样本采集上传
     */
    String SEWAGE_WS_TASK_JC_SAVE = API_VERSION + "/sewage/app/yb/save";
    /**
     * 污水-个人-任务中心-污水样本采集上传--样本编号生成
     */
    String SEWAGE_WS_TASK_JC_YBBH = API_VERSION + "/sewage/app/yb/getNumberCode";
    /**
     * 污水-机构任务数量
     */
    String SEWAGE_WS_TASK_JG_NUM = API_VERSION + "/sewage/rwxf/getJgRwsl";
    /**
     * 污水-机构任务列表
     */
    String SEWAGE_WS_TASK_JG_LIST = API_VERSION + "/sewage/rwxf/listJgRw";
    /**
     * 污水-上传任务组报告
     */
    String SEWAGE_WS_TASK_BGSC = API_VERSION + "/sewage/rwz/bg/sc";
    /**
     * 污水-上传任务组-任务列表
     */
    String SEWAGE_WS_TASK_BGRW = API_VERSION + "/sewage/rwxf/listJgRwz";
    /**
     * 污水-机构首页
     */
    String SEWAGE_WS_JG_INDEX = API_VERSION + "/sewage/rwz/bg/jg/tj";
    /**
     * 污水-机构首页-数量
     */
    String SEWAGE_WS_JG_INDEX_NUM = API_VERSION + "/sewage/rwxf/getJgRwsl";
    /**
     * 人脸注册
     */
    String FACE_REG = API_VERSION + "/face/library/qdzc";
    /**
     * 任务中心--任务协同-平爱关爱
     */
    String TASK_XTRW_LX = API_VERSION + "/propaganda/app/rwgl/rwzxLxLb";
    /**
     * 任务中心--任务协同-平爱关爱-任务列表
     */
    String TASK_PAGA_LIST = API_VERSION + "/care/app/pagaTj/listXtblRwThirty";
    /**
     * 任务中心--任务协同-平爱关爱-任务列表
     */
    String TASK_PAGA_INFO = API_VERSION + "/care/app/pagaTj/getXtzbXq";
    /**
     * 任务中心--任务协同-平爱关爱-处理平安关爱协同转办
     */
    String TASK_PAGA_SUBMIT = API_VERSION + "/care/app/pagaTj/clXtbl";
    /**
     * 查获信息列表
     */
    String CHXX_LIST = API_VERSION + "/personinfo/app/chcz/list";
    /**
     * 任务列表--人员报道列表
     */
    String TASK_RYBD_LIST = API_VERSION + "/personinfo/dbd/list";
    /**
     * 任务列表--辖区--人员信息列表
     */
    String TASK_RYXX_LIST = API_VERSION + "/personinfo/dbd/xq/tj";
    /**
     * 各种文书查看
     */
    String WEN_SHU_LIST = API_VERSION + "/personinfo/dbdxdryws";
    /**
     * 根据行政区划查询尿检考核率
     */
    String KH_URINE_LV_LIST = API_VERSION + "/rule/web/rwzq/listKhlByXzqh/nj";
    /**
     * 分页查询尿液检测考核率详情
     */
    String KH_URINE_LV_INFO_LIST = API_VERSION + "/rule/web/rwzq/pageNjRwKhlXq";
    /**
     * 根据行政区划查询毛检考核率
     */
    String KH_MAOJIAN_LV_LIST = API_VERSION + "/rule/web/rwzq/listKhlByXzqh/mj";
    /**
     * 分页查询尿液检测毛核率详情
     */
    String KH_MAOJIAN_LV_INFO_LIST = API_VERSION + "/rule/web/rwzq/pageMjRwKhlXq";
    /**
     * 分页查询签到率详情
     */
    String KH_SIGN_LV_INFO_LIST = API_VERSION + "/rule/web/rwzq/pageQdRwKhlXq";
    /**
     * 分页查询评估率详情
     */
    String KH_PINGGU_LV_INFO_LIST = API_VERSION + "/rule/web/rwzq/pagePgRwKhlXq";
    /**
     * app使用率下钻列表
     */
    String KH_APP_LV_LIST = API_VERSION + "/personinfo/xdry/kh/listAppRateDown";
    /**
     * app使用率下钻列表详情
     */
    String KH_APP_LV_INFO_LIST = API_VERSION + "/personinfo/xdry/kh/listAppRateDownDetails";
    /**
     * app使用率列表（指定行政区划）
     */
    String KH_APP_LV_XZQH_LIST = API_VERSION + "/personinfo/xdry/kh/getAppRateDown";
    /**
     * 家访率列表（指定行政区划）
     */
    String KH_LV_JF_LIST = API_VERSION + "/rule/app/kh/jf";
    /**
     * 家访率列表详情
     */
    String KH_LV_JF_INOF_LIST = API_VERSION + "/rule/app/kh/jf/xq";
    /**
     * 家访率列表汇总率
     */
    String KH_LV_JF_XL_LIST = API_VERSION + "/rule/app/kh/jf/dq";
    /**
     * 谈话率列表（指定行政区划）
     */
    String KH_LV_TH_LIST = API_VERSION + "/rule/app/kh/th";
    /**
     * 谈话率列表详情
     */
    String KH_LV_TH_INFO_LIST = API_VERSION + "/rule/app/kh/th/xq";
    /**
     * 谈话率列表汇总率
     */
    String KH_LV_TH_XL_LIST = API_VERSION + "/rule/app/kh/th/dq";
    /**
     * 根据行政区划查询本级汇总尿检考核率
     */
    String KH_LV_NJ_XL_LIST = API_VERSION + "/rule/web/rwzq/getKhlByXzqh/nj";
    /**
     * 根据行政区划查询本级汇总毛检考核率
     */
    String KH_LV_MJ_XL_LIST = API_VERSION + "/rule/web/rwzq/getKhlByXzqh/mj";
    /**
     * 下级辖区考核率-执行
     */
    String KH_LV_ZXL_LIST = API_VERSION + "/personinfo/app/kh/zx";
    /**
     * 下级辖区考核率-执行--详情
     */
    String KH_LV_ZXL_INFO_LIST = API_VERSION + "/personinfo/app/kh/zx/xq";
    /**
     * 当前辖区考核率-执行
     */
    String KH_LV_ZXL_XL_LIST = API_VERSION + "/personinfo/app/kh/zx/dq";
    /**
     * 下级辖区考核率-周期排查
     */
    String KH_LV_PC_LIST = API_VERSION + "/sdpc/rwxf/getKhlByXzqh/zqrw";
    /**
     * 下级辖区考核率-临时周期排查
     */
    String KH_LV_PCLS_LIST = API_VERSION + "/sdpc/rwxf/getKhlByXzqh/lsrw";
    /**
     * 下级辖区考核率-签到排查
     */
    String KH_LV_SIGN_LIST = API_VERSION + "/rule/web/rwzq/listKhlByXzqh/qd";
    /**
     * 下级辖区考核率-评估期排查
     */
    String KH_LV_PINGGU_LIST = API_VERSION + "/rule/web/rwzq/listKhlByXzqh/pg";
    /**
     * 下级辖区考核率-排查场所列表
     */
    String KH_LV_PC_INFO_LIST = API_VERSION + "/sdpc/rwxf/wcqkLbApp";
    /**
     * 下级辖区考核率-排查场所列表
     */
    String KH_LV_PC_XL_LIST = API_VERSION + "/sdpc/rwxf/getKhlByXzqh/zqrwOne";
    /**
     * 下级辖区考核率-询行政区划周期任务临时任务单个
     */
    String KH_LV_PCLS_XL_LIST = API_VERSION + "/sdpc/rwxf/getKhlByXzqh/lsrwOne";
    /**
     * 下级辖区考核率-询行政区划周期任务临时任务单个--签到
     */
    String KH_LV_SIGN_XL_LIST = API_VERSION + "/rule/web/rwzq/getKhlByXzqh/qd";
    /**
     * 下级辖区考核率-询行政区划周期任务临时任务单个--评估
     */
    String KH_LV_PINGGU_XL_LIST = API_VERSION + "/rule/web/rwzq/getKhlByXzqh/pg";
    /**
     * 按区域展示信息完整度列表
     */
    String KH_LV_XXWZD_LIST = API_VERSION + "/personinfo/app/xxwzd/xxwzdList";
    /**
     * 查询当前区域信息完整度
     */
    String KH_LV_XXWZD_XL_LIST = API_VERSION + "/personinfo/app/xxwzd/dqjXzqh";
    /**
     * 信息完整度人员名单
     */
    String KH_LV_XXWZD_INFO_LIST = API_VERSION + "/personinfo/app/xxwzd/xxwzdMd";
    /**
     * app生命管控周期
     */
    String GKZQ_LIST = API_VERSION + "/personinfo/app/xdryJbxx/smgkzq";
    /**
     * 目标任务列表
     */
    String MBRW_LIST = API_VERSION + "/organize/app/mbwcrw/listMbrw";
    /**
     * 目标任务详情
     */
    String MBRW_INFO = API_VERSION + "/organize/app/mbwcrw/getRwxq";
    /**
     * 目标任务--转发下级
     */
    String MBRW_ZF = API_VERSION + "/organize/app/mbwcrw/zfrw";
    /**
     * 目标任务--完成任务
     */
    String MBRW_WCRW = API_VERSION + "/organize/app/mbwcrw/wcrw";
    /**
     * app审核注销
     */
    String APP_SHZX = API_VERSION + "/certificat/user/freeze";
    /**
     * 服务人员通信录
     */
    String DRUG_TXL_LIST = API_VERSION + "/chat/app/contacts/listDrug";
    /**
     * 单位通信录
     */
    String WORK_TXL_LIST = API_VERSION + "/chat/app/contacts/listWork";
    /**
     * 单位通信录--单位查询
     */
    String UNIT_TXL_LIST = API_VERSION + "/organize/dwxx/listByXzqh2";
    //联系人消息
    String CHAT_MSG_LIST = API_VERSION + "/chat/app/recentContacts";
    //发送消息
    String CHAT_SEND = API_VERSION + "/chat/app/sendMsg";
    //聊天记录
    String CHAT_HISTORY = API_VERSION + "/chat/app/listHostory";
    //未读 -> 已读(查看聊天记录时)
    String CHAT_READ_NUM = API_VERSION + "/chat/app/unReadToReadForLookChatRecord";
    //app图标未读消息个数
    String CHAT_WIN_READ_NUM = API_VERSION + "/chat/app/iconUnReadCountForApp";
    //安卓：退出登录或卸载时，删除推送TOKEN
    String DEL_PUSH_TOKEN = API_VERSION + "/msg/app/user/delAndroidTokenByUserIdAndUserType";
    //获取发起视频通话
    String CHAT_FJH = API_VERSION + "/chat/webrtc/video";
    //获取发起音频通话
    String CHAT_YP_FJH = API_VERSION + "/chat/webrtc/voice";
    //接受通话
    String CHAT_ACCEPT = API_VERSION + "/chat/webrtc/accept";
    //挂断通话
    String CHAT_GD = API_VERSION + "/chat/webrtc/hangup";
    //取消通话
    String CHAT_QX = API_VERSION + "/chat/webrtc/cancel";
    //拒绝通话
    String CHAT_JJ = API_VERSION + "/chat/webrtc/reject";
    //同步answer
    String CHAT_ANSWER = API_VERSION + "/chat/webrtc/transmit/answer";
    //同步offer
    String CHAT_OFFER = API_VERSION + "/chat/webrtc/transmit/offer";
    //同步IceCandidate
    String CHAT_ICE = API_VERSION + "/chat/webrtc/transmit/ice";
    //分类查询预警数（app端）
    String YUJING_INDEX_NUM = API_VERSION + "/personinfo/app/yj/yjNum";
    //预警列表
    String YU_JING_LIST = API_VERSION + "/personinfo/app/yj/yjList";
    //预警详情
    String YU_JING_INFO = API_VERSION + "/personinfo/app/yj/yjxqnew";
    //预警处置
    String YU_JING_CZ = API_VERSION + "/personinfo/app/yj/dealFxczxx";
    //预警处置
    String YU_JING_CZ_EDIT = API_VERSION + "/personinfo/web/yj/updatateFxczxx";
    //预警--历史登录记录
    String YU_JING_LOGIN_CODE = API_VERSION + "/personinfo/app/yj/appLsdl";
    //预警--未假外出记录
    String YU_JING_LEAVE_CODE = API_VERSION + "/personinfo/app/yj/wjwcxx";
    //预警-处置详情
    String YU_JING_CZ_INFO = API_VERSION + "/personinfo/app/yj/ywcrwxq";
    //预警-风险变更详情
    String YU_JING_FXBG_INFO = API_VERSION + "/personinfo/app/yj/fxdjxgXx";
    //预警-查询任务中心辖区列表
    String YU_JING_XQ_LIST = API_VERSION + "/personinfo/app/yj/xqtaskList";
    //预警-查询任务中心我的列表
    String YU_JING_WD_LIST = API_VERSION + "/personinfo/app/yj/mytask";
    //预警-查询任务中心辖区列表
    String YU_JING_XQ_AREA_DATA = API_VERSION + "/personinfo/app/yj/xqtask";
    //预警-根据行政区划查询单位
    String YU_JING_UNIT = API_VERSION + "/personinfo/app/yj/sjdw";
    //预警-单位代码查询工作人员
    String YU_JING_WORK_LIST = API_VERSION + "/personinfo/app/yj/zrrxx";
    //预警-单位代码查询工作人员--新接口
    String YU_JING_WORK_LIST_NEW = API_VERSION + "/personinfo/app/yj/zrrxxNew";
    //预警-完成任务
    String YU_JING_TASK_FIN = API_VERSION + "/personinfo/app/yj/wcrw";
    //查看待报道/超期未报到任务详情
    String TASK_DBD_CQWBD_INFO = API_VERSION + "/rule/app/rwzq/dbdXq";
    //出所衔接详情
    String TASK_CSXJ_INFO = API_VERSION + "/personinfo/app/csmd/csmdInfo";
    //出所衔接完成任务
    String TASK_CSXJ_FIN_INFO = API_VERSION + "/personinfo/app/csmd/csmdRwWc";
    //我的--任务中心-标签数据数量
    String TASK_WD_TAG_NUM = API_VERSION + "/certificat/app/function/rwzxSl";
    //我的--任务中心-标签数据数量
    String TASK_RYHC_FIN_TASK = API_VERSION + "/personinfo/web/hcqq/ryhcRw.do";
    //目标任务辖区-根据城市展示列表
    String TASK_MBRW_CITY = API_VERSION + "/organize/mbgl/findMbrwByArea";
    //任务推送--通过人员编号、人员类型获取是否有未读的待办记录
    String TASK_PUSH_DEC = API_VERSION + "/personinfo/app/smartHelper/getTodoNotice";
    //任务推送--工作人员系统通知
    String TASK_PUSH_MSG_LIST = API_VERSION + "/personinfo/app/smartHelper/sysNotice";
    //gpt--资料里列表
    String GPT_ZLK_LIST = API_VERSION + "/chat/app/mindHelper/listHotV2";
    //查询资料库详情
    String GPT_ZLK_INFO = API_VERSION + "/chat/app/wisdomChat/getZlkXq";
    //gpt智能助手--获取会话bh
    String GPT_HHBH = API_VERSION + "/chat/app/mindHelper/getTalkBh";
    //gpt智能助手--保存会话记录
    String GPT_SAVE_JL = API_VERSION + "/chat/app/mindHelper/saveAIRecord";
    //gpt智能助手--结束会话
    String GPT_CLOSE_DH = API_VERSION + "/chat/app/mindHelper/killTalk";
    //GPT-AI问答接口
    String GPT = "https://gpt2.lgfzd.com/stream/data";
    //    String GPT = "http://*************:8956/stream/data";
    //GPT-智能助手-获取聊天记录
    String GPT_CHAT_LTJL_LIST = API_VERSION + "/chat/app/mindHelper/chatRecords";
    //默认智能助手的头像
    String GPT_ZS_HEAD_IMG = "https://static.lgfzd.com/drugControl/drug_control/1698222146823.png";
    //客服--消息列表
    String KF_MSG_LIST = API_VERSION + "/chat/app/wisdomChat/recentContacts";
    //客服--消息历史记录
    String KF_MSG_LSJL = API_VERSION + "/chat/app/wisdomChat/listHostory";
    //客服--客服人员列表
    String KF_RY_LSJL = API_VERSION + "/certificat/app/user/getYyry";
    //客服--发送消息（客服发送调用，工作人员调原接口）
    String KF_sendMsg = API_VERSION + "/chat/app/wisdomChat/sendMsg";
    //宣教点赞
    String XJ_DZ = API_VERSION + "/propaganda/web/xcnr/dz/save";
    //宣教阅读数
    String XJ_YD_SAVE = API_VERSION + "/propaganda/web/xcnr/cy/save";
    //人员认领时负责人列表
    String RYRL_FZR_LIST = API_VERSION + "/certificat/user/listForPage";
    //资料库查看数
    String GPT_ZLK_NUM = API_VERSION + "/chat/app/mindHelper/seeAdd";
    //任务中心车辆驾照任务详情
    String CAR_JZ_INFO = API_VERSION + "/rule/app/djrw/findRwxq";
    //处理驾照车辆任务
    String CAR_JZ_DEAL = API_VERSION + "/rule/app/djrw/dealDjrw";
    //通过社会统一信用代码查询企业信息
    String TYSHDM_CQY = API_VERSION + "/precursor/app/yzdqy/qyxx/tyshxydm";
    //校验验证码
    String Verify_Code = API_VERSION + "/certificat/changePassword/verifyCode";
    //查询该吸毒人员最新一次风险评估选项
    String lastRisk = API_VERSION + "/assess/app/drugAssess/lastRisk";
    //宣教-执行人
    String xjGzry = API_VERSION + "/certificat/user/list/gzry";
    //宣教-转发任务/直接完成任务
    String rwZfQd = API_VERSION + "/propaganda/web/rwzx/rwZfQd";
    //宣教-转发任务/直接完成任务2
    String rwZfQd2 = API_VERSION + "/propaganda/web/rwzx/rwZfQdPl";
    //新增修改赋码情况
    String USER_xgFmqk = API_VERSION + "/personinfo/web/fmqk/xgFmqk";
    //宣教活动--活动类别
    String XGHD_HDLB = API_VERSION + "/propaganda/app/xjhd/hdlb";
    //谈话--电子签名
    String TALK_DZQM = API_VERSION + "/talk/app/th/saveDzqm";
    //谈话--电子签名-提醒
    String TALK_DZQM_TX = API_VERSION + "/talk/app/th/dzqmTx";
    //家访--电子签名
    String HOME_DZQM = API_VERSION + "/homevisiting/app/jf/saveDzqm";
    //家访--电子签名-提醒
    String HOME_DZQM_TX = API_VERSION + "/homevisiting/app/jf/dzqmTx";
    //走访教育--电子签名
    String ZF_EDU_DZQM = API_VERSION + "/homevisiting/app/zfjy/saveDzqm";
    //走访教育--电子签名-提醒
    String ZF_EDU_DZQM_TX = API_VERSION + "/homevisiting/app/zfjy/dzqmTx";
    //获取所有二级状态（不包含药物维持治疗脱失，暂停）
    String findAllEjzt = API_VERSION + "/personinfo/app/ryejzt/findAllEjzt";
    //
    String findEjzt = API_VERSION + "/personinfo/app/ryejzt/ryejzt";
    //累计服药次数查询
    String findFycs = API_VERSION + "/personinfo/app/ywwczl/fyjl/findFycs";
    //送样详情
    String syInfo = API_VERSION + "/sewage/app/yb/syInfo";
    //送样编号-进行检测
    String syJy_bh = API_VERSION + "/sewage/app/yb/syJy";
    //送样检测编号上传
    String sy_bh = API_VERSION + "/sewage/app/yb/sy";
    //查询必上传目标物
    String mustMbw = API_VERSION + "/sewage/app/yb/mustMbw";
    //获取机构未上传报告样本
    String jg_wsyb_bh = API_VERSION + "/sewage/rwz/bg/jg/ybbh";
    //样本的调酸视频
    String updateYbTS = API_VERSION + "/sewage/app/yb/updateYbTS";
    //易制毒-新增报备
    String YZD_ADD_BB = API_VERSION + "/precursor/crkbb/xz";
    //易制毒-报备列表
    String YZD_BB_LIST = API_VERSION + "/precursor/crkbb/lb";
    //易制毒-报备确认列表
    String YZD_BB_SURE_LIST = API_VERSION + "/precursor/crkbb/qrlb";
    //易制毒-仓库列表
    String YZD_CK_LIST = API_VERSION + "/precursor/crkbb/qy/ck";
    //易制毒-报备照片
    String YZD_BB_ZP_LIST = API_VERSION + "/precursor/crkbb/zp";
    //易制毒-报备-确认
    String YZD_BB_QR = API_VERSION + "/precursor/crkbb/qr";
    //易制毒-报备--驳回
    String YZD_BB_BH = API_VERSION + "/precursor/crkbb/bh";
    //易制毒-首页-报备
    String YZD_INDEX_BB = API_VERSION + "/precursor/crkbb/qy/zxbb";
    //易制毒-首页-报备-提示
    String YZD_INDEX_BB_TS = API_VERSION + "/precursor/crkbb/dqsjbb";
    //易制毒-监控中心
    String YZD_SXT_LIST = API_VERSION + "/precursor/crkbb/qy/ck/sxt/lb";
    //易制毒-报备--摄像头
    String YZD_BB_SXT = API_VERSION + "/precursor/crkbb/bb/sxt/lb";
    //易制毒-报备--手动上报
    String YZD_BB_SDSB = API_VERSION + "/precursor/crkbb/sczp";
    //易制毒-报备--报备撤销
    String YZD_BB_CX = API_VERSION + "/precursor/crkbb/cx";
    //易制毒-报备--报备详情
    String YZD_BB_INFO = API_VERSION + "/precursor/crkbb/xq";
    //易制毒-企业人员-是否验证人脸
    String YZD_FACE_CHECK = API_VERSION + "/precursor/app/yzdqy/faceCheck";
    //易制毒-报备--人员图片上传
    String YZD_FACE_SAVE = API_VERSION + "/precursor/app/yzdqy/saveTp";
    //易制毒-评定中心列表
    String YZD_PJ_LIST = API_VERSION + "/precursor/app/pdzx/pdzxlb";
    //易制毒-评定中心列表--评分/等级
    String YZD_PJ_DF_DJ = API_VERSION + "/precursor/app/pdzx/findDjpf";
    //易制毒-评定中心--周期
    String YZD_PJ_ZQ = API_VERSION + "/precursor/app/pdzx/zqsj";
    //易制毒-监控详情
    String YZD_JK_INFO = API_VERSION + "/precursor/sxt/lxxx";
    //易制毒-获取录像播放地址
    String YZD_JK_SK_VIDEO = API_VERSION + "/precursor/sxt/hqLxbfdz";
    //易制毒-播放地址先停止再播放
    String YZD_JK_VIDEO_TZBF = API_VERSION + "/precursor/sxt/tzbf";
    //易制毒-评定中心-详情
    String YZD_PJ_INFO = API_VERSION + "/precursor/app/pdzx/pdxq";
    //易制毒-评定中心-评级首页
    String YZD_PJ_INDEX = API_VERSION + "/precursor/app/pdzx/findLastPdxx";
    //易制毒-评定中心--审批详情
    String YZD_PJ_SP_INFO = API_VERSION + "/precursor/app/pdzx/pdxq";
    //毒驾--首页-营运概况
    String CAR_YYGK = API_VERSION + "/personinfo/app/djzl/cl/yyclGk";
    //毒驾--首页-卡口检测概况
    String CAR_KKJCGK = API_VERSION + "/personinfo/app/djkk/kkjcQk";
    //毒驾--卡口列表
    String CAR_KK_LIST = API_VERSION + "/personinfo/app/djkk/kkfyCx";
    //毒驾--卡口新增
    String CAR_KK_ADD = API_VERSION + "/personinfo/app/djkk/kkxz";
    //毒驾--卡口删除
    String CAR_KK_DEL = API_VERSION + "/personinfo/app/djkk/kkxg";
    //毒驾--卡口-检测-列表
    String CAR_KK_jC_LIST = API_VERSION + "/personinfo/app/djjc/kkjcLb";
    //毒驾--卡口检测-根据区划查卡口
    String CAR_XZQH_KK = API_VERSION + "/personinfo/app/djkk/kkLbNoPage";
    //毒驾--车辆管理
    String CAR_LIST = API_VERSION + "/personinfo/app/djzl/cl/page";
    //毒驾--车辆管理-详情
    String CAR_INFO = API_VERSION + "/personinfo/app/djzl/cl/xq";
    //毒驾--车辆管理-详情-删除
    String CAR_INFO_DEL = API_VERSION + "/personinfo/app/djzl/cl/delete";
    //毒驾--车辆管理-分页查询
    String CAR_INFO_JCGX = API_VERSION + "/personinfo/app/djjc/cljcLb";
    //毒驾--车辆新增
    String CAR_ADD = API_VERSION + "/personinfo/app/djzl/cl/save";
    //毒驾--车辆新增
    String CAR_UPDATE = API_VERSION + "/personinfo/app/djzl/cl/update";
    //毒驾--驾驶员
    String CAR_JSY = API_VERSION + "/personinfo/app/djzl/jsy/page";
    //毒驾--检测-子项提交
    String CAR_JSY_URINE = API_VERSION + "/personinfo/app/djjc/jcxxXz";
    //毒驾--检测 -最终提交
    String CAR_JSY_URINE_ADD = API_VERSION + "/personinfo/app/djjc/jcScXyb";
    //毒驾--检测 -详情
    String CAR_JSY_URINE_INFO = API_VERSION + "/personinfo/app/djjc/kkjcXqXzHx";
    //公众活动详情
    String gzHdInfo = API_VERSION + "/propaganda/app/gzhd/gzHdSpInfo";
    //考核--通过时间、行政区划查询平安关爱协同完成率
    String getGaXtRate = API_VERSION + "/care/kh/app/getGaXtRate";
    //考核--查询平安关爱协同完成率--最后具体任务列表
    String listGaXtRw = API_VERSION + "/care/kh/app/listGaXtRw";
    //考核--查询平安关爱协同完成率统计区县列表
    String listGaXtForApp = API_VERSION + "/care/kh/app/listGaXtForApp";
    //考核--查询目标任务完成率统计
    String getMbRwRate = API_VERSION + "/organize/mb/kh/app/getMbRwRate";
    //考核--查询目标任务完成率统计--页面下面列表
    String listMbRwForApp = API_VERSION + "/organize/mb/kh/app/listMbRwForApp";
    //考核--目标任务完成率--最后具体任务列表
    String listMbRw = API_VERSION + "/organize/mb/kh/app/listMbRw";
    //考核--目标任务完成率/平安关爱协同--最后具体任务列表---联系人
    String listYhByDwdm = API_VERSION + "/organize/mb/kh/app/listYhByDwdm";
    //考核--禁毒宣教任务--顶部--下面列表
    String xjrwqkApp = API_VERSION + "/propaganda/app/xjkh/xjrwqkApp";
    //考核--禁毒宣教任务统计--顶部数据
    String rwwclApp = API_VERSION + "/propaganda/app/xjkh/rwwclApp";
    //协议书列表
    String XYS_LIST = API_VERSION + "/personinfo/app/xys/page";
    //协议书新增
    String XYS_ADD = API_VERSION + "/personinfo/app/xys/saveOrUpdate";
    //协议书详情
    String XYS_INFO = API_VERSION + "/personinfo/app/xys/getByBh";
    //区划宣传内容分类
    String XCNR_CLASS = API_VERSION + "/propaganda/web/xcnr/fllb";
    //获取阿里云语音识别所需令牌Token
    String ALY_YY_TOKEN = API_VERSION + "/personinfo/app/aliyun/sr/getToken";
    //新增或更新-宣传栏
    String XCL_BC_GX = API_VERSION + "/propaganda/app/xcl/saveOrUpdate";
    //宣传栏列表
    String XCL_LIST = API_VERSION + "/propaganda/app/xcl/page";
    //宣传栏详情
    String XCL_INFO = API_VERSION + "/propaganda/app/xcl/getByBh";
    //涉毒排查-更新场所负责人
    String PLACE_EDIT_USERS = API_VERSION + "/sdpc/sdcs/gxfzr";
    //信息中心首页
    String TZWJ_TC = API_VERSION + "/personinfo/web/xxzx/syList";

    String BATCH_READ = API_VERSION + "/personinfo/web/xxzx/batchRead";

    //问卷详情
    String WEN_JUAN_INFO = API_VERSION + "/personinfo/web/xxzx/wjdcInfo";
    //问卷列表
    String WEN_JUAN_LIST = API_VERSION + "/personinfo/web/xxzx/syListPage";
    //公告-宣教基础地址加载
    String NOTICE_XJ_BASE = "https://appnotices.lgfzd.com";
    //    String NOTICE_XJ_BASE1 = "http://192.168.1.55:5500/index.html";
    //问卷保存
    String WEN_JUAN_SAVE = API_VERSION + "/personinfo/web/xxzx/wj/wc";
    //考核-周期性（临时性）宣教任务率值 顶部筛选数据
    String KH_XJ_TOP_DATA = API_VERSION + "/propaganda/app/rwzx/zqlsXjRwRateApp";
    //考核-周期性（临时性）宣教任务率值 统计页面数据
    String KH_XJ_LIST_DATA = API_VERSION + "/propaganda/app/rwzx/listZqlsXjRwRateApp";
    //考核-周期性（临时性）宣教任务率值 具体已完成/未完成情况列表
    String KH_XJ_LIST_WCQK = API_VERSION + "/propaganda/app/rwzx/zqlsXjRwListApp";
    //审批排查中止详情
    String PC_ZZ_INFO = API_VERSION + "/sdpc/sdcs/zzspxq";
    //审批排查冻结场所
    String PC_ZZ_SUBMIT = API_VERSION + "/sdpc/sdcs/djcs";

    // 当前辖区季度思想汇报率
    String KH_LV_JDSXHB_XL_DATA = API_VERSION + "/rule/app/kh/jdsxhbxq";
    // 当前辖区季度思想汇报率
    String KH_LV_JDSXHB_XL_LIST = API_VERSION + "/rule/app/kh/jdsxhblb";
    // app端-季度小结列表
    String KH_LV_JDXJ_LIST = API_VERSION + "/rule/app/kh/jdxjlb";
    // app端-当前辖区季度小结率
    String KH_LV_JDXJ_RATE = API_VERSION + "/rule/app/kh/jdxjxq";
    // app端-当前辖区季度思想汇报列表（最终列表）
    String KH_LV_JD_SXHB_LIST = API_VERSION + "/rule/app/kh/jdsxList";
    // app端-当前辖区季度小结列表(最终列表)
    String KH_LV_JD_XJ_LIST = API_VERSION + "/rule/app/kh/jdxjList";

    // app-季度汇报-新增
    String KH_LV_JDSXHB_ADD = API_VERSION + "/personinfo/app/jdhb/save";

    // app-季度小结-新增
    String KH_LV_JDXJ_ADD = API_VERSION + "/personinfo/app/jdxj/save";

    // app-季度汇报-详情
    String KH_LV_JDSXHB_INFO = API_VERSION + "/personinfo/app/jdhb/get";

    // app-季度小结-详情
    String KH_LV_JDXJ_INFO = API_VERSION + "/personinfo/app/jdxj/get";

    // 人员详情-季度汇报列表
    String KH_LV_JDSXHB_LIST_RY = API_VERSION + "/personinfo/app/jdhb/list";

    // 人员详情-季度小结列表
    String KH_LV_JDXJ_LIST_RY = API_VERSION + "/personinfo/app/jdxj/list";
    // 首页-管控动态组件
    String GK_LIST = API_VERSION + "/personinfo/xsy/shmxdry/gk/wcl";

    // 管控-核查率-列表
    String HC_RATE = API_VERSION + "/personinfo/web/ryhc/list";
    // 社会面吸毒人员评估率
    String PG_RATE = API_VERSION + "/personinfo/xsy/shmxdry/gk/pglb";
    // 社会面吸毒人员检测率
    String JC_RATE = API_VERSION + "/personinfo/web/ryjc/list";
    // 信息完整率
    String XXWZ_RATE = API_VERSION + "/personinfo/xsy/gk/xxwzlb";
    // 救助申请办结率
    String JZSQBLLB_RATE = API_VERSION + "/care/xsy/gk/jzsqbllb";
    // 社戒社康检测率
    String SJSKJCL_RATE = API_VERSION + "/personinfo/xsy/sjsk/sjskJcl/page";
    // 社戒社康管控率
    String SJSKGKL_RATE = API_VERSION + "/personinfo/xsy/sjsk/sjskGkl/page";

    String GAODE_KEY = API_VERSION + "/certificat/getApiKey";
    // 污水采样设备管理-列表
    String CYSB_LIST = API_VERSION + "/sewage/cysb/lb";
    // 污水采样设备管理-详情
    String CYSB_DETAIL = API_VERSION + "/sewage/cysb/xq";
    // 设备登录
    String CYSB_DEVICE_LOGIN = API_VERSION + "/openLogin";
    // 远程开关门
    String CYSB_DOOR_GATE = API_VERSION + "/sewage/device/gate";
    // 下发任务
    String CYSB_TASK_PLAN = API_VERSION + "/sewage/task/plan";
    // 处理警告
    String CYSB_GJXX = API_VERSION + "/sewage/cysb/gjxx/cl";
    // 获取监控
    String CYSB_VIDEO = API_VERSION + "/sewage/device/getVideoURL";

    String WAREHOUSE_VIDEO = API_VERSION + "/precursor/sxt/startBf";

    String YZD_CK_KC_LIST = API_VERSION + "/precursor/yzd/ck/kc/lb";
    //易制毒-仓库管理列表
    String YZD_CK_NEW_LIST = API_VERSION + "/precursor/web/yzdqy/listCkgl";
    // 仓库库存列表 /precursor/yzd/ck/kc/lb
    String YZD_CK_KC_LIST_NEW = API_VERSION + "/precursor/yzd/ck/kc/lb";
    // 库存报损 /precursor/yzd/ck/kc/bs
    String YZD_CK_KC_BS = API_VERSION + "/precursor/yzd/ck/kc/bs";
    // 查看仓库详情 /precursor/web/yzdqy/getCkglXq
    String YZD_CK_KC_XQ = API_VERSION + "/precursor/web/yzdqy/getCkglXq";
    // 仓库新增编辑 /precursor/web/yzdqy/saveCk
    String YZD_CK_KC_SAVE = API_VERSION + "/precursor/web/yzdqy/saveCk";

    // 通过登录信息或者实验室编号 precursor/app/sys/getSysBh
    String GET_SYS_BH = API_VERSION + "/precursor/app/sys/getSysBh";


    // 获取摄像头类型 dictionary/web/jdzd/listZdInfoByParams
    String GET_SXT_TYPE = API_VERSION + "/dictionary/web/jdzd/listZdInfoByParams";

    // app人员管理-查询该实验室设置的区域 /app/sys/listSysSetQy
    String GET_SYS_SET_QY = API_VERSION + "/precursor/app/sys/listSysSetQy";

    // app人员管理-新增 /app/sys/saveSysRy
    String SAVE_SYS_RY = API_VERSION + "/precursor/app/sys/saveSysRy";
    // app人员管理-编辑 /app/sys/updateSysRy
    String UPDATE_SYS_RY = API_VERSION + "/precursor/app/sys/updateSysRy";

    //公用--人员管理-列表及详情 /app/sys/listSysRy
    String GET_SYS_RY = API_VERSION + "/precursor/app/sys/listSysRy";

    // 预警处置列表 /precursor/app/sys/listSysYj
    String GET_SYS_YJ = API_VERSION + "/precursor/app/sys/listSysYj";

    // 区域管理列表 /precursor/app/sys/listSysQy
    String GET_SYS_QY = API_VERSION + "/precursor/app/sys/listSysQy";

    // app首页-查询该实验室设置的区域安装摄像头占比 /app/sys/qySxt
    String GET_SYS_QYSXT = API_VERSION + "/precursor/app/sys/qySxt";

    // 公用--区域管理-新增 /app/sys/saveSysQy
    String SAVE_SYS_QY = API_VERSION + "/precursor/app/sys/saveSysQy";
    // 区域管理 更新
    String UPDATE_SYS_QY = API_VERSION + "/precursor/app/sys/updateSysQy";
    // app区域管理-删除 /app/sys/delSysQy
    String DEL_SYS_QY = API_VERSION + "/precursor/app/sys/delSysQy";
    // 公用--预警管理-处置预警 /app/sys/czSysYj
    String CZ_SYS_YJ = API_VERSION + "/precursor/app/sys/czSysYj";
    // 巡查列表 /precursor/yzd/xc/lb
    String YZD_XC_LIST = API_VERSION + "/precursor/yzd/xc/lb";
    // 巡查详情 /precursor/yzd/xc/xq
    String YZD_XC_XQ = API_VERSION + "/precursor/yzd/xc/xq";
    // 人脸验证
    String FACE_SB_ALY = "https://zfa.market.alicloudapi.com/efficient/idfaceIdentity";

    // 运输首页 /precursor/ysgl/fj/lb
    String YSG_YSSY = API_VERSION + "/precursor/ysgl/fj/lb";

    // 实验室摄像头 /precursor/app/sys/qy/sxt/lb
    String SYS_SXT = API_VERSION + "/precursor/app/sys/qy/sxt/lb";

    // 巡查新增 /precursor/yzd/xc/xz
    String YZD_XC_XZ = API_VERSION + "/precursor/yzd/xc/xz";

    // 图片校验
    String CHECK_IMG = API_VERSION + "/precursor/zj/sb";

    // 保存 运输许可证 /precursor/ysgl/baxx/bc
    String SAVE_TRANSPORT_PERMIT = API_VERSION + "/precursor/ysgl/baxx/bc";

    // 保存运输流 /precursor/ysgl/bc
    String SAVE_TRANSPORT = API_VERSION + "/precursor/ysgl/bc";


    String AI_WELCOME_MESSAGE = "/talk/web/th/ai";
    //
    String AI_MESSAGE_NEW = "/talk/web/th/aiTalk";

    // 查询案由（查询顶级和指定code下一级） /personinfo/web/wffz/getAy
    String GET_AY = API_VERSION + "/personinfo/app/wffz/getAy";

    // 新增编辑 /personinfo/app/wffz/add
    String ADD_AY = API_VERSION + "/personinfo/app/wffz/add";
    // 列表 /personinfo/app/wffz/list
    String LIST_AY = API_VERSION + "/personinfo/app/wffz/list";

    // 详情 /personinfo/app/wffz/get
    String INFO_AY = API_VERSION + "/personinfo/app/wffz/get";

    //人脸识别-登录验证
    String FACE_SB_NEW = API_VERSION + "/personinfo/web/ywwczl/fyjl/rlsbNew";

    String FACE_SB = API_VERSION + "/personinfo/app/ywwczl/fyjl/rlsb";

    // 药物治疗-出入库
    String FYJL_RK = API_VERSION + "/personinfo/web/ywwczl/fyjl/rk";
    // 药物治疗-入库记录
    String FYJL_RK_LIST = API_VERSION + "/personinfo/web/ywwczl/fyjl/rk/list";
    // 服药记录
    String FYJL_GR_XQ = API_VERSION + "/personinfo/web/ywwczl/fyjl/grXq/page";
    // 服药记录
    String FYJL_GR_LIST = API_VERSION + "/personinfo/web/ywwczl/fyjl/fyjl/page";
    // 机构信息与库存初始化信息校验
    String FYJL_JY = API_VERSION + "/personinfo/web/ywwczl/fyjl/jy";
    // 累计入库 当前剩余
    String FYJL_RK_KC = API_VERSION + "/personinfo/web/ywwczl/fyjl/rk/kc";
    // 报损记录
    String FYJL_BS_LIST = "/personinfo/web/ywwczl/fyjl/bs/list";
    // 报损
    String FYJL_BS_ADD = "/personinfo/web/ywwczl/fyjl/bs";
    // 医疗机构完善信息
    String WSXX = "/organize/dwxx/wsxx";

    // 药物维持治疗人员
    String YWWCZLRY = "/personinfo/jbxx/ywwczlry";


    // 保存机构工作人员
    String SAVE_LXR = "/organize/saveLxr";

    // 查询机构工作人员
    String CXJGRY = "/organize/cxjgry";

    String ORGANIZE_JGXQ = "/organize/jgxq";
}


