package com.linggan.jd831.constants;

import java.util.ArrayList;
import java.util.List;

public enum JZSQBLJStatus {
    PENDING(0, "待关爱"),
    COMPLETED(1, "已完成关爱"),
    UNABLE_TO_CARE(2, "已驳回"),
    SUBMITTED_FOR_APPROVAL(3, "已提交资料供审批"),
    CARE_PLAN_ESTABLISHED(4, "已制定帮扶计划"),
    UNABLE_TO_PROCESS(5, "无法办理");

    private final int code;
    private final String description;

    JZSQBLJStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static JZSQBLJStatus fromCode(int code) {
        for (JZSQBLJStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的状态码: " + code);
    }

    // 返回所有状态码的列表
    public static List<Integer> getAllCodes() {
        List<Integer> codes = new ArrayList<>();
        for (JZSQBLJStatus status : values()) {
            codes.add(status.getCode());
        }
        return codes;
    }

    // 返回所有状态描述的列表
    public static List<String> getAllDescriptions() {
        List<String> descriptions = new ArrayList<>();
        for (JZSQBLJStatus status : values()) {
            descriptions.add(status.getDescription());
        }
        return descriptions;
    }
}
