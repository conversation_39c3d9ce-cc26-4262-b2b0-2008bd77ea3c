package com.linggan.jd831.service;

import android.util.Log;

import com.hihonor.push.sdk.HonorMessageService;
import com.hihonor.push.sdk.HonorPushDataMsg;
import com.lgfzd.base.XBaseApp;
import com.linggan.jd831.utils.PushUtil;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 荣耀手机推送
 */
public class HonorAppMsgService extends HonorMessageService {
    //Token发生变化时，会以onNewToken方法返回
    @Override
    public void onNewToken(String pushToken) {
        // TODO: 处理收到的新PushToken。
        Log.i("推送", "荣耀onNewToken: " + pushToken);
        PushUtil.bindToken(XBaseApp.instance(), pushToken, 6);
    }

    @Override
    public void onMessageReceived(HonorPushDataMsg msg) {
        // TODO: 处理收到的透传消息。
        try {
            JSONObject jsonObject = new JSONObject(msg.getData());
            JSONObject jsonObjectS = jsonObject.getJSONObject("myMessage");
            String action = jsonObject.getString("action");
            PushUtil.clickNotification(XBaseApp.instance(), "", action, jsonObjectS.getString("sender"), jsonObjectS.getString("senderName"), jsonObjectS.getString("senderImg"));
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
    }
}