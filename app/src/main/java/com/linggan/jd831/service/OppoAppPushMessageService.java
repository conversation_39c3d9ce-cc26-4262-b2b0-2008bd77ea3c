package com.linggan.jd831.service;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.heytap.msp.push.mode.DataMessage;
import com.heytap.msp.push.service.DataMessageCallbackService;
import com.lgfzd.base.XBaseApp;
import com.linggan.jd831.utils.PushUtil;

import org.json.JSONException;
import org.json.JSONObject;

public class OppoAppPushMessageService extends DataMessageCallbackService {

    /**
     * 透传消息处理，应用可以打开页面或者执行命令,如果应用不需要处理透传消息，则不需要重写此方法
     *
     * @param context
     * @param message
     */
    @Override
    public void processMessage(Context context, DataMessage message) {
        super.processMessage(context, message);
        Log.i("推送", "oppo-app: " + new Gson().toJson(message));
        try {
            JSONObject jsonObject = new JSONObject(message.getContent());
            JSONObject jsonObjectS = jsonObject.getJSONObject("myMessage");
            String action = jsonObject.getString("action");
            PushUtil.clickNotification(XBaseApp.instance(), "", action, jsonObjectS.getString("sender"), jsonObjectS.getString("senderName"), jsonObjectS.getString("senderImg"));
        } catch (JSONException e) {
        }
    }
}
