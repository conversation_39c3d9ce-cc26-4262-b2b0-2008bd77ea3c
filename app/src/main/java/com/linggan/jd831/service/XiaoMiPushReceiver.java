package com.linggan.jd831.service;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;
import com.lgfzd.base.XBaseApp;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.linggan.jd831.utils.PushUtil;
import com.xiaomi.mipush.sdk.ErrorCode;
import com.xiaomi.mipush.sdk.MiPushClient;
import com.xiaomi.mipush.sdk.MiPushCommandMessage;
import com.xiaomi.mipush.sdk.MiPushMessage;
import com.xiaomi.mipush.sdk.PushMessageReceiver;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;

/**
 * 小米消息推送接收器
 */
public class XiaoMiPushReceiver extends PushMessageReceiver {


    @Override
    public void onReceivePassThroughMessage(Context context, MiPushMessage miPushMessage) {
        super.onReceivePassThroughMessage(context, miPushMessage);
        //接收服务器向客户端发送的透传消息
    }

    @Override
    public void onNotificationMessageClicked(Context context, MiPushMessage miPushMessage) {
        Log.i("推送", "xiaomi11: " + new Gson().toJson(miPushMessage));
        try {
            Map<String, String> map = miPushMessage.getExtra();
            if (!TextUtils.isEmpty(map.get("action"))) {
                String myMessage = map.get("myMessage");
                JSONObject jsonObject = new JSONObject(myMessage);
                PushUtil.clickNotification(XBaseApp.instance(), "", map.get("action"), jsonObject.getString("sender"), jsonObject.getString("senderName"), jsonObject.getString("senderImg"));
            }
        } catch (JSONException e) {
        }
    }

    @Override
    public void onNotificationMessageArrived(Context context, MiPushMessage miPushMessage) {
        super.onNotificationMessageArrived(context, miPushMessage);
        Log.i("推送", "xiaomi: " + new Gson().toJson(miPushMessage));
    }

    @Override
    public void onCommandResult(Context context, MiPushCommandMessage message) {
//        String command = message.getCommand();
//        List<String> arguments = message.getCommandArguments();
//        if (MiPushClient.COMMAND_REGISTER.equals(command)) {
//            if (message.getResultCode() == ErrorCode.SUCCESS) {
//                String mRegID = arguments.get(0);
//                MiPushClient.setAlias(context, mRegID, null);
//                String aliasName = Build.MANUFACTURER;
//                switch (aliasName) {
//                    case "xiaomi"://小米
//                    case "XIAOMI"://小米
//                        PushUtil.bindToken(context, mRegID, 0);
//                        break;
//                    case "redmi"://红米
//                    case "REDMI"://红米
//                        PushUtil.bindToken(context, mRegID, 7);
//                        break;
//                }
//            }
//        }
    }

    @Override
    public void onReceiveRegisterResult(Context context, MiPushCommandMessage message) {
        if (MiPushClient.COMMAND_REGISTER.equals(message.getCommand())) {
            if (message.getResultCode() == ErrorCode.SUCCESS) {
                //设置别名
                if (!TextUtils.isEmpty(XShareCacheUtils.getInstance().getString(XConstantUtils.USER_ID))) {
                    MiPushClient.setAlias(context, XShareCacheUtils.getInstance().getString(XConstantUtils.USER_ID), null);
                }
            }
        }
    }
}
