package com.linggan.jd831.ui.common;


import android.content.res.Configuration;
import android.os.Handler;
import android.text.TextUtils;
import android.view.ViewGroup;

import com.lgfzd.base.base.XBase3Activity;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.databinding.ActivityVideoIjkBinding;
import com.linggan.jd831.utils.VideoWatermarkManager;

import xyz.doikki.videocontroller.StandardVideoController;
import xyz.doikki.videocontroller.component.CompleteView;
import xyz.doikki.videocontroller.component.GestureView;
import xyz.doikki.videocontroller.component.LiveControlView;
import xyz.doikki.videocontroller.component.VodControlView;
import xyz.doikki.videoplayer.player.BaseVideoView;


/**
 * 视频播放
 */

public class VideoIjkActivity extends XBase3Activity<ActivityVideoIjkBinding> {

    private String time, isHf, path;//视频路径
    // 视频播放-控件
    protected StandardVideoController mController;
    private Handler mainHandler = new Handler();
    private long lastRefreshTime = 0;
    private static final long REFRESH_INTERVAL = 500; // 500ms的刷新间隔

    @Override
    protected ActivityVideoIjkBinding getViewBinding() {
        return ActivityVideoIjkBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        // 先初始化播放器控制器
        mController = new StandardVideoController(this);
        mController.addControlComponent(new CompleteView(this));
        mController.addControlComponent(new GestureView(this));

        if (TextUtils.isEmpty(isHf)) {
            mController.addControlComponent(new VodControlView(this));
        } else {
            mController.addControlComponent(new LiveControlView(this));
        }
        binding.videoView.setVideoController(mController);

        // 在根布局上添加水印
        binding.getRoot().post(() -> {
            ViewGroup rootView = binding.getRoot();
            if (rootView != null) {
                VideoWatermarkManager.getInstance().addWatermark(
                        this,
                        rootView,
                        binding.videoView
                );
            }
        });
    }

    @Override
    protected void initListener() {

    }

    @Override
    protected void getData() {
        path = getIntent().getStringExtra("path");
        time = getIntent().getStringExtra("time");
        isHf = getIntent().getStringExtra("isHf");
        if (TextUtils.isEmpty(path)) {
            XToastUtil.showToast(this, "当前视频链接无法播放");
            finish();
        } else {
            initVideoPlayer();
        }
        if (!TextUtils.isEmpty(time)) {
            setTitle(time);
        }
    }

    /**
     * 初始化播放器
     */
    private void initVideoPlayer() {
        binding.videoView.setUrl(path);

        // 监听播放器状态
        binding.videoView.addOnStateChangeListener(new BaseVideoView.OnStateChangeListener() {
            @Override
            public void onPlayerStateChanged(int playerState) {
                // 在播放器状态改变时刷新水印
                if (playerState == BaseVideoView.PLAYER_NORMAL ||
                        playerState == BaseVideoView.PLAYER_FULL_SCREEN) {
                    refreshWatermark();
                }
            }

            @Override
            public void onPlayStateChanged(int playState) {
                if (playState == BaseVideoView.STATE_PLAYING) {
                    refreshWatermark();
                }
            }
        });

        binding.videoView.start();
    }

    private void refreshWatermark() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastRefreshTime < REFRESH_INTERVAL) {
            return;
        }
        lastRefreshTime = currentTime;

        mainHandler.post(() -> {
            VideoWatermarkManager.getInstance().removeWatermark(this);

            boolean isLandscape = getResources().getConfiguration().orientation
                    == Configuration.ORIENTATION_LANDSCAPE;

            ViewGroup container;
            if (isLandscape) {
                container = (ViewGroup) mController.getParent();
            } else {
                container = binding.getRoot();
            }

            if (container != null) {
                VideoWatermarkManager.getInstance().addWatermark(
                        this,
                        container,
                        binding.videoView
                );
            }
        });
    }

    @Override
    protected void onPause() {
        super.onPause();
        binding.videoView.pause();
    }

    @Override
    protected void onResume() {
        super.onResume();
        binding.videoView.resume();
        refreshWatermark();
    }

    @Override
    protected void onDestroy() {
        VideoWatermarkManager.getInstance().removeWatermark(this);
        if (mainHandler != null) {
            mainHandler.removeCallbacksAndMessages(null);
        }
        super.onDestroy();
        binding.videoView.release();
    }

    @Override
    public void onBackPressed() {
        if (!binding.videoView.onBackPressed()) {
            super.onBackPressed();
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        // 在屏幕旋转时刷新水印
        mainHandler.postDelayed(this::refreshWatermark, 300);
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus) {
            refreshWatermark();  // 直接调用refreshWatermark方法
        }
    }
}
