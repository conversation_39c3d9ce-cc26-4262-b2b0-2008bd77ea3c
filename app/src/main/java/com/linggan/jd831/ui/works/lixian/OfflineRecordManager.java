
package com.linggan.jd831.ui.works.lixian;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Environment;

import androidx.core.content.ContextCompat;

import java.io.*;
import java.lang.reflect.Type;
import java.util.*;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

public class OfflineRecordManager {
    private static final String RECORDS_FILE = "offline_records.json";
    private static final String MEDIA_FOLDER = "Camera";
    private Context context;
    private Gson gson;

    public OfflineRecordManager(Context context) {
        this.context = context;
        this.gson = new Gson();
    }

    public void saveRecord(OfflineRecord record) {
        List<OfflineRecord> records = getRecords();
        records.add(record);
        this.saveRecordsToFile(records);
    }

    public List<OfflineRecord> getRecords() {
        File file = new File(context.getFilesDir(), RECORDS_FILE);
        if (!file.exists()) {
            return new ArrayList<>();
        }

        try (FileReader reader = new FileReader(file)) {
            Type type = new TypeToken<List<OfflineRecord>>() {
            }.getType();
            return gson.fromJson(reader, type);
        } catch (IOException e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    // 删除指定记录
    public void deleteRecord(OfflineRecord record) {
        List<OfflineRecord> records = getRecords();
        for (OfflineRecord r : records) {
            if (r.getId().equals(record.getId())) {
                records.remove(r);
                break;
            }
        }
        saveRecordsToFile(records);
    }

    public void updateRecord(OfflineRecord record) {
        List<OfflineRecord> records = getRecords();
        for (OfflineRecord r : records) {
            if (r.getId().equals(record.getId())) {
                records.remove(r);
                records.add(record);
                break;
            }
        }
        saveRecordsToFile(records);
    }

    private void saveRecordsToFile(List<OfflineRecord> records) {
        File file = new File(context.getFilesDir(), RECORDS_FILE);
        try (FileWriter writer = new FileWriter(file)) {
            gson.toJson(records, writer);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public File getMediaFile(String fileName) {
        if (hasExternalStoragePermission()) {
            File mediaDir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM), MEDIA_FOLDER);
            if (!mediaDir.exists()) {
                mediaDir.mkdirs();
            }
            return new File(mediaDir, fileName);
        } else {
            File mediaDir = new File(context.getFilesDir(), MEDIA_FOLDER);
            if (!mediaDir.exists()) {
                mediaDir.mkdirs();
            }
            return new File(mediaDir, fileName);
        }
    }

    public boolean hasExternalStoragePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10 及以上不需要存储权限也可以访问应用专属目录
            return true;
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6.0 到 Android 9.0 需要检查权限
            return ContextCompat.checkSelfPermission(context, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    == PackageManager.PERMISSION_GRANTED;
        } else {
            // Android 5.1 及以下默认授予权限
            return true;
        }
    }

    public void clearRecords() {
        File file = new File(context.getFilesDir(), RECORDS_FILE);
        file.delete();

        // 清理外部存储的媒体文件
        if (hasExternalStoragePermission()) {
            File externalMediaDir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM), MEDIA_FOLDER);
            deleteRecursive(externalMediaDir);
        }

        // 清理内部存储的媒体文件
        File internalMediaDir = new File(context.getFilesDir(), MEDIA_FOLDER);
        deleteRecursive(internalMediaDir);
    }

    private void deleteRecursive(File fileOrDirectory) {
        if (fileOrDirectory.isDirectory()) {
            for (File child : fileOrDirectory.listFiles()) {
                deleteRecursive(child);
            }
        }
        fileOrDirectory.delete();
    }
}
