package com.linggan.jd831.ui.works.transport.add;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.view.TimePickerView;
import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XAppUtil;
import com.lgfzd.base.utils.XDateUtil;
import com.lgfzd.base.utils.XImageFileCompressEngine;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.InputEntity;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.databinding.ActivityTransportPermitEditBinding;
import com.linggan.jd831.ui.works.transport.TransportRouteActivity;
import com.linggan.jd831.ui.works.transport.constants.TransportConstants;
import com.linggan.jd831.ui.works.transport.event.RouteSelectedEvent;
import com.linggan.jd831.ui.works.transport.model.RouteCity;
import com.linggan.jd831.ui.works.transport.model.TransportPermitData;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.GlideEngine;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.BaseStringSelectDialog;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;


// 新增运输许可证
public class TransportPermitEditActivity extends XBaseActivity<ActivityTransportPermitEditBinding> {

    private static final int REQUEST_IMAGE_CAPTURE = 1;
    private static final String DATE_FORMAT = "yyyy-MM-dd";
    private SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT, Locale.getDefault());

    private long timeDate1; // 开始时间戳
    private String xjBh; // 巡检编号
    private String kssj; // 任务开始时间
    private String jssj; // 任务结束时间

    private ImageAddUtil imageAddImg;

    private View rootView;
    private boolean isEdit;
    private TransportPermitData permit;
    private ArrayList<RouteCity> routeCities = new ArrayList<>();
    private List<String> deletedBhs = new ArrayList<>();

    public static void startForAdd(Activity activity, int requestCode) {
        Intent intent = new Intent(activity, TransportPermitEditActivity.class);
        intent.putExtra(TransportConstants.EXTRA_IS_EDIT, false);
        activity.startActivityForResult(intent, requestCode);
    }

    public static void startForEdit(Activity activity, int requestCode, TransportPermitData permit) {
        Intent intent = new Intent(activity, TransportPermitEditActivity.class);
        intent.putExtra(TransportConstants.EXTRA_PERMIT, permit);
        activity.startActivityForResult(intent, requestCode);
    }

    @Override
    protected ActivityTransportPermitEditBinding getViewBinding() {
        return ActivityTransportPermitEditBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EventBus.getDefault().register(this);

        permit = (TransportPermitData) getIntent().getSerializableExtra(TransportConstants.EXTRA_PERMIT);
        isEdit = permit != null;

        setTitle(isEdit ? "运输备案信息" : "运输备案信息");
        initViews();

        if (isEdit) {
            loadPermitData(permit);
        }

        // 直接从布局文件中获取 ScrollView
        rootView = binding.getRoot();

        // 移除原有的键盘监听代码，替换为以下代码
        binding.scrollView.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_DOWN) {
                View currentFocus = getCurrentFocus();
                if (currentFocus != null) {
                    InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                    imm.hideSoftInputFromWindow(currentFocus.getWindowToken(), 0);
                }
            }
            return false;
        });

        // 为所有EditText添加焦点变化监听
        addFocusChangeListener(binding.etPermitNumber);
        addFocusChangeListener(binding.etShipper);
        addFocusChangeListener(binding.etReceiver);
        addFocusChangeListener(binding.etApplicant);
        addFocusChangeListener(binding.etTransportCompany);
        addFocusChangeListener(binding.etTransportPhone);
        addFocusChangeListener(binding.etGoodsName);
        addFocusChangeListener(binding.etTransportQuantity);
        addFocusChangeListener(binding.etPoliceStation);

        // 设置Activity的windowSoftInputMode
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        EventBus.getDefault().removeStickyEvent(RouteSelectedEvent.class);
    }

    // EventBus订阅方法
    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onRouteSelected(RouteSelectedEvent event) {
        if (binding.tvTransportRoute != null) {
            binding.tvTransportRoute.setText(event.getRouteString());
            routeCities = new ArrayList<>(event.getRouteCities());
            if (event.getDeletedBhs() != null) {
                deletedBhs.addAll(event.getDeletedBhs());
            }
        }
    }

    private void initViews() {
//        binding.etPermitNumber.setOnEditorActionListener(this);
        // 取消页面进入的时候 binding.etSearch的焦点
        binding.etPermitNumber.setFocusable(false);
        binding.etPermitNumber.setFocusableInTouchMode(false);

        binding.etPermitNumber.setOnClickListener((v) -> {
            binding.etPermitNumber.setFocusable(true);
            binding.etPermitNumber.setFocusableInTouchMode(true);
            binding.etPermitNumber.requestFocus();
            // 显示键盘
            XAppUtil.showSoftInput(this);
        });
        imageAddImg = new ImageAddUtil(this, binding.gridImg);
        imageAddImg.setMax(1);
        imageAddImg.setOnImageAddListener(() -> {
//            DialogUtils.showPhotoDialog(this, (code, id) -> {
//                from = "1";
//                if (code.equals("1")) {
//                    choiceImg = 10;
//                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
//                } else {
//                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine())
//                            .setCompressEngine(new XImageFileCompressEngine()).setMaxSelectNum(imgNum - imageAddImg.getPaths().size()).forResult(PictureConfig.CHOOSE_REQUEST);
//                }
//            });
        });

        // 修改图片点击事件
        binding.flPermitPhoto.setOnClickListener(v -> {
            if (binding.ivPermitPhoto.getTag() != null) {
                showBigImage(imageAddImg.getPaths().get(0).getSavePath());
            } else {
                // 检查相机和存储权限
                requestPermission(new String[]{
                        Manifest.permission.CAMERA,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE,
                        Manifest.permission.READ_EXTERNAL_STORAGE
                }, new PermissionCallback() {
                    @Override
                    public void onPermissionGranted() {
                        // 权限已授予,打开相机
                        PictureSelector.create(TransportPermitEditActivity.this)
                                .openCamera(SelectMimeType.ofImage())
                                .setCompressEngine(new XImageFileCompressEngine())
                                .forResultActivity(PictureConfig.REQUEST_CAMERA);
                    }

                    @Override
                    public void onPermissionDenied() {
                        // 权限被拒绝,提示用户
                        XToastUtil.showToast(TransportPermitEditActivity.this,
                                "需要相机和存储权限才能拍照上传");
                    }
                });
            }
        });

        // 添加删除按钮点击事件
        binding.ivDeletePhoto.setOnClickListener(v -> {
            binding.ivPermitPhoto.setImageResource(R.drawable.ic_transport_upload);
            binding.ivPermitPhoto.setTag(null);
            binding.ivDeletePhoto.setVisibility(View.GONE);
            imageAddImg.clean();
            binding.tvUploadHint.setVisibility(View.VISIBLE);
        });

        // 有效次数
//        binding.tvValidTimes.setOnClickListener(v -> {
//            Toast.makeText(this, "请拍照识别有效次数", Toast.LENGTH_SHORT).show();
//        });
        binding.tvValidTimes.setOnClickListener(v -> {
            ArrayList<String> optionList = new ArrayList<>();
            for (int i = 1; i <= 10; i++) {
                optionList.add(String.valueOf(i));
            }
            optionList.add("多次有效");

            BaseStringSelectDialog dialog = new BaseStringSelectDialog(this, "确定", optionList);

            dialog.setOnClickDataListener(() -> {
                String selected = dialog.getData();
                binding.tvValidTimes.setText(selected);
            });

            // 先显示对话框
            dialog.show();
        });


        // 选择开始日期
        binding.tvKsTime.setOnClickListener(v -> {
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                timeDate1 = XDateUtil.getDateByFormat(timeDate, XConstantUtils.YMD).getTime();

                // 如果有任务时间限制,进行验证
                if (!TextUtils.isEmpty(xjBh) && !TextUtils.isEmpty(kssj) && jumpKssTime(kssj, timeDate1)) {
                    XToastUtil.showToast(this, "开始日期须大于(或等于)任务开始日期");
                    return;
                }

                binding.tvKsTime.setText(timeDate);
            })
                    .setType(new boolean[]{true, true, true, false, false, false})
                    .setCancelColor(R.color.black)
                    .setSubmitColor(R.color.black)
                    .build();

            pvTime.show();
        });

        // 选择结束日期
        binding.tvJsTime.setOnClickListener(v -> {
            if (TextUtils.isEmpty(binding.tvKsTime.getText().toString())) {
                XToastUtil.showToast(this, "请先选择开始日期");
                return;
            }

            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                long timeDate2 = XDateUtil.getDateByFormat(timeDate, XConstantUtils.YMD).getTime();

                // 验证结束日期不能早于开始日期
                if (timeDate2 < timeDate1) {
                    XToastUtil.showToast(this, "结束日期须大于(或等于)开始日期");
                    return;
                }

                // 如果有任务时间限制,进行验证
                if (!TextUtils.isEmpty(xjBh) && !TextUtils.isEmpty(jssj) && jumpJssTime(jssj, timeDate2)) {
                    XToastUtil.showToast(this, "结束日期须小于(或等于)任务结束日期");
                    return;
                }

                binding.tvJsTime.setText(timeDate);
            })
                    .setType(new boolean[]{true, true, true, false, false, false})
                    .setCancelColor(R.color.black)
                    .setSubmitColor(R.color.black)
                    .build();

            pvTime.show();
        });

        // 保存按钮
        binding.btnSave.setOnClickListener(v -> {
            if (validateInput()) {
                savePermitInfo();
            }
        });

        // 运输路线
        binding.tvTransportRoute.setOnClickListener(v -> {
            // 先发送当前路线数据，使用 sticky 模式
            if (routeCities != null && !routeCities.isEmpty()) {
                EventBus.getDefault().postSticky(new RouteSelectedEvent(binding.tvTransportRoute.getText().toString(), routeCities, deletedBhs));
            }

            // 然后跳转
            Intent intent = new Intent(this, TransportRouteActivity.class);
            startActivityForResult(intent, REQUEST_ROUTE);
        });
    }

    private boolean jumpKssTime(String taskStartTime, long selectedTime) {
        try {
            long taskStart = XDateUtil.getDateByFormat(taskStartTime, XConstantUtils.YMD).getTime();
            return selectedTime < taskStart;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private boolean jumpJssTime(String taskEndTime, long selectedTime) {
        try {
            long taskEnd = XDateUtil.getDateByFormat(taskEndTime, XConstantUtils.YMD).getTime();
            return selectedTime > taskEnd;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private boolean validateInput() {
        if (TextUtils.isEmpty(binding.etPermitNumber.getText())) {
            XToastUtil.showToast(this, "请输入证书号");
            return false;
        }
        if (TextUtils.isEmpty(binding.tvValidTimes.getText())) {
            XToastUtil.showToast(this, "请输入有效次数");
            return false;
        }
        if (TextUtils.isEmpty(binding.tvKsTime.getText())) {
            XToastUtil.showToast(this, "请选择开始日期");
            return false;
        }
        if (TextUtils.isEmpty(binding.tvJsTime.getText())) {
            XToastUtil.showToast(this, "请选择结束日期");
            return false;
        }
        if (TextUtils.isEmpty(binding.etShipper.getText())) {
            XToastUtil.showToast(this, "请输入发货单位");
            return false;
        }
        if (TextUtils.isEmpty(binding.etReceiver.getText())) {
            XToastUtil.showToast(this, "请输入收货单位");
            return false;
        }
        if (TextUtils.isEmpty(binding.etPoliceStation.getText())) {
            XToastUtil.showToast(this, "请输入发证机关");
            return false;
        }
        if (TextUtils.isEmpty(binding.etGoodsName.getText())) {
            XToastUtil.showToast(this, "请输入运输物品");
            return false;
        }
        if (TextUtils.isEmpty(binding.etTransportQuantity.getText())) {
            XToastUtil.showToast(this, "请输入运输数量");
            return false;
        }
        if (binding.ivPermitPhoto.getTag() == null) {
            XToastUtil.showToast(this, "请上传许可证照片");
            return false;
        }
        if (routeCities == null || routeCities.isEmpty()) {
            XToastUtil.showToast(this, "请选运输路线");
            return false;
        }

        return true;
    }


    private void loadPermitData(TransportPermitData permit) {
        // 加载许可证照片
        if (permit.getXkz() != null && !TextUtils.isEmpty(permit.getXkz().getLj())) {
            // 使用 Glide 加载图片
            Glide.with(this)
                    .load(permit.getXkz().getLj())
                    .into(binding.ivPermitPhoto);
            binding.ivPermitPhoto.setTag(permit.getXkz().getLj());
            binding.ivDeletePhoto.setVisibility(View.VISIBLE);
            binding.tvUploadHint.setVisibility(View.GONE);

            // 将图片添加到 imageAddUtil
            OssFileEntity ossFile = new OssFileEntity();
            ossFile.setSavePath(permit.getXkz().getLj());
            ossFile.setOriginName(permit.getXkz().getMc());
            ossFile.setFileSize(permit.getXkz().getDx());
            imageAddImg.addImage(ossFile);
        }

        // 设置证书号
        binding.etPermitNumber.setText(permit.getZsbh());

        // 置有效次数
        String validTimes = permit.getYxcs() != null ?
                (permit.getYxcs() >= 10000 ? "多次有效" : String.valueOf(permit.getYxcs())) : "";
        binding.tvValidTimes.setText(validTimes);

        // 设置日期
        binding.tvKsTime.setText(permit.getYxksrq());
        binding.tvJsTime.setText(permit.getYxjsrq());

        // 设置各种单位信息
        binding.etShipper.setText(permit.getFhdw());
        binding.etReceiver.setText(permit.getShdw());
        binding.etApplicant.setText(permit.getSqdw());
        binding.etTransportCompany.setText(permit.getYsdw());
        binding.etTransportPhone.setText(permit.getYslxdh());

        // 设置运输物品和数量
        binding.etGoodsName.setText(permit.getYswp());
        binding.etTransportQuantity.setText(permit.getYssl());

        // 设置发证机关
        binding.etPoliceStation.setText(permit.getFzjg());

        // 恢复运输路线
        if (permit.getBalx() != null && !permit.getBalx().isEmpty()) {
            // 保存路线城市列表
            routeCities = new ArrayList<>(permit.getBalx());

            // 构建路线显示文本
            StringBuilder routeText = new StringBuilder();
            for (int i = 0; i < permit.getBalx().size(); i++) {
                RouteCity city = permit.getBalx().get(i);
                routeText.append(city.getCsmc());
                if (i < permit.getBalx().size() - 1) {
                    routeText.append(" → ");
                }
            }
            binding.tvTransportRoute.setText(routeText.toString());
        }

        // 如果有开始时间,保存时间戳用于验证
        if (!TextUtils.isEmpty(permit.getYxksrq())) {
            try {
                timeDate1 = XDateUtil.getDateByFormat(permit.getYxksrq(), XConstantUtils.YMD).getTime();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void savePermitInfo() {
        // 表单验证
        if (!validateInput()) {
            return;
        }

        // 构建请求参数
//        JSONObject requestBody = new JSONObject();
        try {
            // 如果是编辑模式,需要传入编号


            // 构建许可证对象
//            JSONObject xkz = new JSONObject();
            Map<String, Object> objectMap = new HashMap<>();
            if (isEdit) {
                objectMap.put("bh", permit.getBh());
            }

            objectMap.put("yxksrq", binding.tvKsTime.getText().toString().trim()); // 有效开始日期
            objectMap.put("yxjsrq", binding.tvJsTime.getText().toString().trim()); // 有效结束日期

//            xkz.put("yxcs", Integer.parseInt(binding.tvValidTimes.getText().toString().trim())); // 有效次数
//
            int yxcs = -1;
            if (binding.tvValidTimes.getText().toString().equals("多次有效")) {
                yxcs = 10000;
            } else {
                yxcs = Integer.parseInt(binding.tvValidTimes.getText().toString());
            }

            try {
                yxcs = Integer.parseInt(binding.tvValidTimes.getText().toString().trim());
            } catch (Exception e) {
                e.printStackTrace();
            }
            objectMap.put("yxcs", yxcs); // 有效次数
            objectMap.put("zsbh", binding.etPermitNumber.getText().toString().trim()); // 证书编号
            objectMap.put("fhdw", binding.etShipper.getText().toString().trim()); // 发货单位
            objectMap.put("shdw", binding.etReceiver.getText().toString().trim()); // 收货单位
            objectMap.put("sqdw", binding.etApplicant.getText().toString().trim()); // 申请单位
            objectMap.put("ysdw", binding.etTransportCompany.getText().toString().trim()); // 运输单位
            objectMap.put("yslxdh", binding.etTransportPhone.getText().toString().trim()); // 运输联系电话
            objectMap.put("yswp", binding.etGoodsName.getText().toString().trim()); // 运输物品
            objectMap.put("yssl", binding.etTransportQuantity.getText().toString().trim()); // 运输数量
            objectMap.put("fzjg", binding.etPoliceStation.getText().toString().trim()); // 发证机关
            // 许可证图片
            if (imageAddImg != null && imageAddImg.getPaths() != null && imageAddImg.getPaths().size() > 0) {
                Map<String, Object> xkzObject = new HashMap<>();

                xkzObject.put("lj", imageAddImg.getPaths().get(0).getSavePath());
                xkzObject.put("mc", imageAddImg.getPaths().get(0).getOriginName());
                xkzObject.put("hz", StrUtils.getFileType(imageAddImg.getPaths().get(0).getSavePath()));
                xkzObject.put("dx", imageAddImg.getPaths().get(0).getFileSize());
                objectMap.put("xkz", xkzObject);
            }
            if (routeCities != null && !routeCities.isEmpty()) {
                // 创建路线数组
                List<Map<String, Object>> routeList = new ArrayList<>();

                for (RouteCity city : routeCities) {
                    Map<String, Object> cityMap = new HashMap<>();
                    if (isEdit) {
                        cityMap.put("bh", city.getBh());
                    }
                    cityMap.put("xh", city.getXh());
                    cityMap.put("cslx", city.getCslx());
                    cityMap.put("csmc", city.getCsmc());
                    cityMap.put("csqhdm", city.getCsqhdm());
                    routeList.add(cityMap);
                }

                // 将路线列表添加到 objectMap
                objectMap.put("balx", routeList);
            }

            // 添加删除的bh列表
            if (!deletedBhs.isEmpty()) {
                objectMap.put("scbhs", deletedBhs);
            }

            // 调用保存接口
            String url = ApiHostUtils.getHostUrl() + ApiUrlsUtils.SAVE_TRANSPORT_PERMIT;
            RequestParams params = new RequestParams(url);
            String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));

            XHttpUtils.postJson(this, params, jiaMiString,
                    DialogUtils.showLoadDialog(this, "保存中"), true,
                    new XHttpResponseCallBack() {
                        @Override
                        public void onSuccess(String result) {
                            try {
                                JSONObject response = new JSONObject(result);
                                if (response.getInt("status") == 0) {
                                    if (isEdit) {
                                        XToastUtil.showToast(TransportPermitEditActivity.this, "修改成功");
                                    } else {
                                        XToastUtil.showToast(TransportPermitEditActivity.this, "保存成功");
                                    }
                                    setResult(RESULT_OK);
                                    EventBus.getDefault().post(new InputEntity());
                                    finish();
                                } else {
                                    String errorMsg = response.optString("errorInfo", "保存失败");
                                    XToastUtil.showToast(TransportPermitEditActivity.this, errorMsg);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                                XToastUtil.showToast(TransportPermitEditActivity.this, "保存失败");
                            }
                        }

                        @Override
                        public void onFailed(int errorCode, String msg) {
                            XToastUtil.showToast(TransportPermitEditActivity.this,
                                    TextUtils.isEmpty(msg) ? "保存失败" : msg);
                        }

                        @Override
                        public void onFinished() {
                        }
                    });

        } catch (Exception e) {
            e.printStackTrace();
            XToastUtil.showToast(this, "数据处理失败");
        }
    }

    @Override
    protected void initView() {
    }

    @Override
    protected void initListener() {
    }

    @Override
    protected void getData() {
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && data != null) {
            switch (requestCode) {
                case PictureConfig.REQUEST_CAMERA:
                case PictureConfig.CHOOSE_REQUEST:
                    ArrayList<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                    if (null != selectList) {
                        for (LocalMedia media : selectList) {
                            if (media.isCompressed()) {
                                //压缩图片上传
                                uploadFile(media.getCompressPath());
                            } else if (!TextUtils.isEmpty(media.getRealPath())) {
                                //原图上传
                                uploadFile(media.getRealPath());
                            }
                        }
                    }
                    break;
                case REQUEST_ROUTE:
                    // 原有的路线处理代码...
                    break;
            }
        }
    }

    private static final int REQUEST_ROUTE = 2;

    /**
     * 上传文件
     *
     * @param filePath 文件路径
     */
    private void ocrFile(String filePath) {
        RequestParams params = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.CHECK_IMG);
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                XToastUtil.showToast(this, "文件不存在");
                return;
            }

            // 添加文件参数,设置文件的MIME类型
            params.addBodyParameter("file", file, "image/*");
            params.addBodyParameter("zjlx", "1");

            // 使用新的multipart上传方法
            XHttpUtils.uploadFileMultipart(this, params,
                    DialogUtils.showLoadDialog(this, "自动识别中"),
                    new XHttpResponseCallBack() {
                        @Override
                        public void onSuccess(String result) {
                            if (!TextUtils.isEmpty(result)) {
                                try {
                                    JSONObject jsonObject = new JSONObject(result);
                                    if (jsonObject.getInt("status") == 0) {
                                        String fileUrl = jsonObject.optString("data");

                                        // 处理OCR识别结果
                                        JSONObject ocrData = jsonObject.optJSONObject("data");
                                        if (ocrData != null) {
                                            handleOcrResult(ocrData);
                                        }

                                        // 更新片显示
                                        binding.ivPermitPhoto.setImageURI(Uri.fromFile(file));
                                        binding.ivPermitPhoto.setTag(fileUrl);
                                        binding.ivDeletePhoto.setVisibility(View.VISIBLE);
                                        binding.tvUploadHint.setVisibility(View.GONE);
                                        XToastUtil.showToast(TransportPermitEditActivity.this, "请仔细核对信息，编辑内容和运输许可证一致");
//                                        XToastUtil.showToast(TransportPermitEditActivity.this, "识别成功");
                                    } else {
                                        try {
                                            imageAddImg.clean();
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                        String msg = jsonObject.optString("errorInfo");
                                        XToastUtil.showToast(TransportPermitEditActivity.this,
                                                TextUtils.isEmpty(msg) ? "识别失败，请重新上传或手动编辑信息。" : msg);
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    XToastUtil.showToast(TransportPermitEditActivity.this, "识别失败，请重新上传或手动编辑信息。");
                                }
                            }
                        }

                        @Override
                        public void onFailed(int errorCode, String msg) {
                            XToastUtil.showToast(TransportPermitEditActivity.this, "服务器错误，请重新上传或手动编辑信息。");
                        }

                        @Override
                        public void onFinished() {
                            // 传完成
                        }
                    });
        } catch (Exception e) {
            e.printStackTrace();
            XToastUtil.showToast(this, "上传失败");
        }
    }

    // 修改显示大图方法
    private void showBigImage(String imageUrl) {
        // 先准备数据
        ArrayList<LocalMedia> mediaList = new ArrayList<>();
        if (imageUrl != null) {
            mediaList.add(LocalMedia.generateLocalMedia(this, imageUrl));
        }

        // 然后调用预览
        PictureSelector.create(this)
                .openPreview()
                .setImageEngine(GlideEngine.createGlideEngine())
                .startActivityPreview(0, false, mediaList);
    }


    //上传
    private void uploadFile(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        try {
                            File file = new File(path);
                            imageAddImg.clean();
                            binding.ivPermitPhoto.setImageURI(Uri.fromFile(file));
                            binding.ivPermitPhoto.setTag(xResultData.getData().get(0).getSavePath());
                            binding.ivDeletePhoto.setVisibility(View.GONE);
                            binding.tvUploadHint.setVisibility(View.VISIBLE);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        imageAddImg.addImage(xResultData.getData().get(0));
                        imageAddImg.notifyData();
                        ocrFile(path);
                    }
                } else {
                    XToastUtil.showToast(TransportPermitEditActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 处理OCR识别结果并填充表单
     *
     * @param jsonData OCR识别返回的JSON数据
     */
    private void handleOcrResult(JSONObject jsonData) {
        try {
            // 证书号
            String permitNumber = jsonData.optString("证书号");
            if (!TextUtils.isEmpty(permitNumber)) {
                binding.etPermitNumber.setText(permitNumber);
            }

            // 有效次数
            String validTimes = jsonData.optString("有效次数");
            if (!TextUtils.isEmpty(validTimes)) {
                // 判断有效次数是否在1-10之间或者是"多次有效"
                if (validTimes.equals("多次有效") ||
                        (validTimes.matches("\\d+") &&
                                Integer.parseInt(validTimes) >= 1 &&
                                Integer.parseInt(validTimes) <= 10)) {
                    binding.tvValidTimes.setText(validTimes);
                }
            }

            // 发货单位
            String shipper = jsonData.optString("发货单位");
            if (!TextUtils.isEmpty(shipper)) {
                binding.etShipper.setText(shipper);
            }

            // 收货单位
            String receiver = jsonData.optString("收货单位");
            if (!TextUtils.isEmpty(receiver)) {
                binding.etReceiver.setText(receiver);
            }

            // 申请单位
            String applicant = jsonData.optString("申请单位");
            if (!TextUtils.isEmpty(applicant)) {
                binding.etApplicant.setText(applicant);
            }

            // 运输单位
            String transportCompany = jsonData.optString("运输单位");
            if (!TextUtils.isEmpty(transportCompany)) {
                binding.etTransportCompany.setText(transportCompany);
            }

            // 运输单位联系电话
            String transportPhone = jsonData.optString("运输单位联系电话");
            if (!TextUtils.isEmpty(transportPhone)) {
                binding.etTransportPhone.setText(transportPhone);
            }

            // 运输物品品名
            String goodsName = jsonData.optString("运输物品品名");
            if (!TextUtils.isEmpty(goodsName)) {
                binding.etGoodsName.setText(goodsName);
            }

            // 运输数量
            String transportQuantity = jsonData.optString("运输数量");
            if (!TextUtils.isEmpty(transportQuantity)) {
                binding.etTransportQuantity.setText(transportQuantity);
            }

            // 发证机关
            String policeStation = jsonData.optString("发证机关");
            if (!TextUtils.isEmpty(policeStation)) {
                binding.etPoliceStation.setText(policeStation);
            }

//            // 运输路线
//            String transportRoute = jsonData.optString("运输路线");
//            if (!TextUtils.isEmpty(transportRoute)) {
//                //
//                binding.tvTransportRoute.setText(transportRoute);
//            }

            // 处理有效日期
            String validDate = jsonData.optString("有效日期");
            if (!TextUtils.isEmpty(validDate)) {
                // 假设格式为"自yyyy-MM-dd至yyyy-MM-dd"
                try {
                    String[] dates = validDate.replace("自", "").replace("至", ",").split(",");
                    if (dates.length == 2) {
                        // 处理开始日期
                        String startDate = dates[0].trim();
                        if (startDate.length() >= 10) { // 确保日期字符串足够长
                            startDate = formatDateString(startDate);
                            binding.tvKsTime.setText(startDate);
                            timeDate1 = XDateUtil.getDateByFormat(startDate, XConstantUtils.YMD).getTime();
                        }

                        // 处理结束日期
                        String endDate = dates[1].trim();
                        if (endDate.length() >= 10) {
                            endDate = formatDateString(endDate);
                            binding.tvJsTime.setText(endDate);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            XToastUtil.showToast(this, "据解析失败");
        }
    }

    /**
     * 格式化日期字符串
     *
     * @param dateStr 原始日期字符串
     * @return 格式后的日期字符串 yyyy-MM-dd
     */
    private String formatDateString(String dateStr) {
        // 移除所有非数字字符
        String numbers = dateStr.replaceAll("[^0-9]", "");
        if (numbers.length() >= 8) {
            // 假设格式为yyyyMMdd
            return numbers.substring(0, 4) + "-"
                    + numbers.substring(4, 6) + "-"
                    + numbers.substring(6, 8);
        }
        return dateStr;
    }

    // 修改addFocusChangeListener方法，增加滚动偏移量
    private void addFocusChangeListener(EditText editText) {
        editText.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                new Handler().postDelayed(() -> {
                    try {
                        // 获取 ScrollView 在屏幕中的位置
                        int[] scrollViewLocation = new int[2];
                        binding.scrollView.getLocationInWindow(scrollViewLocation);

                        // 获取 EditText 在 ScrollView 中的位置
                        int[] editTextLocation = new int[2];
                        editText.getLocationInWindow(editTextLocation);

                        // 获取键盘高度
                        Rect r = new Rect();
                        binding.getRoot().getWindowVisibleDisplayFrame(r);
                        int screenHeight = binding.getRoot().getRootView().getHeight();
                        int keyboardHeight = screenHeight - r.bottom;

                        // 获取按钮总高度（包括边距）
                        int buttonTotalHeight = binding.btnSave.getHeight();
                        ViewGroup.MarginLayoutParams buttonParams = (ViewGroup.MarginLayoutParams) binding.btnSave.getLayoutParams();
                        if (buttonParams != null) {
                            buttonTotalHeight += buttonParams.topMargin + buttonParams.bottomMargin;
                        }

                        // 计算 EditText 在 ScrollView 的相对位置
                        int editTextRelativeBottom = editTextLocation[1] - scrollViewLocation[1] + editText.getHeight();

                        // 计算 ScrollView 的可见高度
                        int scrollViewVisibleHeight = screenHeight - keyboardHeight - buttonTotalHeight - scrollViewLocation[1];

                        // 计算需要滚动的距离
                        if (editTextRelativeBottom > scrollViewVisibleHeight) {
                            int scrollNeeded = editTextRelativeBottom - scrollViewVisibleHeight;

                            // 添加额外偏移量，确保输入框在可见区域的中间位置
                            int extraOffset = (int) TypedValue.applyDimension(
                                    TypedValue.COMPLEX_UNIT_DIP,
                                    80,  // 调整这个值以获得更好的视觉效果
                                    getResources().getDisplayMetrics()
                            );

                            // 获取当前滚动位置
                            int currentScroll = binding.scrollView.getScrollY();

                            // 计算目标滚动位置
                            final int targetScroll = Math.min(
                                    currentScroll + scrollNeeded + extraOffset,
                                    binding.scrollView.getChildAt(0).getHeight() - binding.scrollView.getHeight()
                            );

                            // 使用平滑滚动
                            binding.scrollView.smoothScrollTo(0, targetScroll);

                            // 确保一定会滚动到位
                            binding.scrollView.postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    binding.scrollView.scrollTo(0, targetScroll);
                                }
                            }, 200);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }, 300);
            }
        });
    }

    // 添加权限回调接口
    private interface PermissionCallback {
        void onPermissionGranted();

        void onPermissionDenied();
    }

    // 添加权限请求方法
    private void requestPermission(String[] permissions, PermissionCallback callback) {
        if (checkSelfPermission(permissions[0]) != PackageManager.PERMISSION_GRANTED ||
                checkSelfPermission(permissions[1]) != PackageManager.PERMISSION_GRANTED ||
                checkSelfPermission(permissions[2]) != PackageManager.PERMISSION_GRANTED) {

            requestPermissions(permissions, REQUEST_PERMISSION);
            mPermissionCallback = callback;
        } else {
            callback.onPermissionGranted();
        }
    }

    // 添加权限回调变量
    private PermissionCallback mPermissionCallback;
    private static final int REQUEST_PERMISSION = 100;

    // 添加权限请求结果处理
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_PERMISSION) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }

            if (mPermissionCallback != null) {
                if (allGranted) {
                    mPermissionCallback.onPermissionGranted();
                } else {
                    mPermissionCallback.onPermissionDenied();
                }
            }
        }
    }

} 