package com.linggan.jd831.ui.works.urine;

import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.recyclerview.widget.GridLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.FileAllTypeAdapter;
import com.linggan.jd831.adapter.ImaVideoShowAdapter;
import com.linggan.jd831.adapter.PeoStatusInfoListAdapter;
import com.linggan.jd831.bean.PeopleInfoEntity;
import com.linggan.jd831.bean.TaskSpListEntity;
import com.linggan.jd831.bean.UrineInfoEntity;
import com.linggan.jd831.bean.UrineListEntity;
import com.linggan.jd831.databinding.ActivityUrineInfoBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.TaskApprovalFactory;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.utils.XImageUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

/**
 * 类  名： 检测-详情
 * 描   述：
 * 作  者：zxb
 * 时  间：
 * 版  权：凌感科技
 */
public class UrineInfoActivity extends XBaseActivity<ActivityUrineInfoBinding> {

    private String bh, peoId, tag, key, taskId, aid, pro, spls;
    private UrineInfoEntity urineInfoEntity;
    private boolean isSpecialArea = false;

    @Override
    protected ActivityUrineInfoBinding getViewBinding() {
        return ActivityUrineInfoBinding.inflate(getLayoutInflater());
    }

    private boolean zjwc = false;

    private String zjwcList;

    @Override
    protected void initView() {
        bh = getIntent().getStringExtra("id");
        peoId = getIntent().getStringExtra("pid");
        //这里是标识从任务审批跳转过来--下面三个参数
        tag = getIntent().getStringExtra("tag");
        taskId = getIntent().getStringExtra("tid");
        key = getIntent().getStringExtra("key");
        pro = getIntent().getStringExtra("pro");
        aid = getIntent().getStringExtra("aid");
        spls = getIntent().getStringExtra("spls");//审批历史过来
        zjwc = getIntent().getBooleanExtra("zjwc", false);//审批历史过来
        zjwcList = getIntent().getStringExtra("zjwcList");
        binding.top.recyclerPeoStatus.setLayoutManager(new GridLayoutManager(this, 3));
        String xzqhdm = UserInfoUtils.getUserInfo().getYhXzqhdm();
        isSpecialArea = xzqhdm.startsWith("510603") && zjwc;
        initSpecialArea();
        EventBus.getDefault().register(this);
    }

    // 旌阳区域定制显示
    private void initSpecialArea() {
        int visibility = isSpecialArea ? View.GONE : View.VISIBLE;
        Log.d("initSpecialArea", "isSpecialArea: " + isSpecialArea);
        // 隐藏检测地址
        binding.llTestAddress.setVisibility(visibility);
        // 配合度评价 tvPhdPjLabel
        binding.tvPhdPjLabel.setVisibility(visibility);
        // 不配合具体表现
        binding.phdPjLayout.setVisibility(visibility);
        // 检测报告
        binding.llTestReport.setVisibility(visibility);
        // 隐藏备注
        binding.llRemark.setVisibility(visibility);
        // 隐藏视频上传
        binding.llVideoUpload.setVisibility(visibility);
        // 隐藏附件上传
        binding.llAttachmentUpload.setVisibility(visibility);
        // 隐藏尿检通知单
//        binding.llNotificationReport.setVisibility(visibility);
    }

    @Override
    protected void initListener() {
        //非审批过来
        if (TextUtils.isEmpty(tag)) {
            getUserData();
            binding.base.tvRight.setText(getString(R.string.edit));
            binding.base.tvRight.setTextColor(getResources().getColor(R.color.color_main));
            binding.base.ivRight.setImageResource(R.mipmap.ic_edit);
            binding.base.btRight.setOnClickListener(v -> {
                //编辑
                Bundle bundle = new Bundle();
                bundle.putString("id", peoId);
                bundle.putString("name", binding.top.tvName.getText().toString());
                bundle.putSerializable("msg", urineInfoEntity);
                bundle.putSerializable("zjwc", zjwc);
                XIntentUtil.redirectToNextActivity(this, UrineAddActivity.class, bundle);
            });
        } else {
            //审批数据
            binding.linPopup.linInfo.setVisibility(View.VISIBLE);
            TaskApprovalFactory.getSpData(this, binding.linPopup.ivOnDown, binding.linPopup.recycleSp, binding.linPopup.etSpRemark, binding.linPopup.btNo, binding.linPopup.btTg, binding.linPopup.linDspShow, binding.linPopup.linSpth, binding.linPopup.btFangQi, binding.linPopup.btTgEdit, key, pro, aid, taskId, tag, spls, code -> {
                switch (code) {
                    case "pass":
                        //审批提交完成
                        EventBus.getDefault().post(new TaskSpListEntity());
                        finish();
                        break;
                    case "fin":
                        //放弃审批
                        finish();
                        break;
                    case "edit":
                        //编辑
                        Bundle bundle = new Bundle();
                        bundle.putString("id", peoId);
                        bundle.putString("tag", tag);
                        bundle.putString("pro", pro);
                        bundle.putString("name", binding.top.tvName.getText().toString());
                        bundle.putSerializable("msg", urineInfoEntity);
                        XIntentUtil.redirectToNextActivity(this, UrineAddActivity.class, bundle);
                        break;
                }
            });
        }
    }

    @Override
    protected void getData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.URINE_INFO + bh);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<UrineInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<UrineInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        urineInfoEntity = xResultData.getData();
                        if (!TextUtils.isEmpty(xResultData.getData().getZq())) {
                            binding.tvZhouqi.setText(xResultData.getData().getZq());
                        } else {
                            if (TextUtils.isEmpty(xResultData.getData().getZqKssj())) {
                                binding.tvZhouqi.setText("非周期检测");
                            } else {
                                binding.tvZhouqi.setText(xResultData.getData().getZqKssj() + "至" + xResultData.getData().getZqJssj());
                            }
                        }
                        binding.tvTime.setText(xResultData.getData().getSj());
                        if (xResultData.getData().getLx() != null) {
                            binding.tvLx.setText(xResultData.getData().getLx().getName());
                        }
                        if (xResultData.getData().getFs() != null) {
                            binding.tvType.setText(xResultData.getData().getFs().getName());
                        }
                        try {
                            if (xResultData.getData().getFs() != null) {
                                if (xResultData.getData().getFs().getCode().equals("0")) {
                                    int visibility = isSpecialArea ? View.GONE : View.VISIBLE;
                                    binding.llNotificationReport.setVisibility(visibility);
                                } else {
                                    binding.llNotificationReport.setVisibility(View.GONE);
                                }
                            }
                            binding.gridImgNotification.setAdapter(new ImaVideoShowAdapter(xResultData.getData().getTzd()));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }


                        if (xResultData.getData().getJg() != null) {
                            if (xResultData.getData().getJg().getCode().equals("0")) {
                                binding.tvResult.setTextColor(Color.parseColor("#262626"));
                            } else {
                                binding.tvResult.setTextColor(Color.parseColor("#ff0000"));
                            }
                            binding.tvResult.setText(xResultData.getData().getJg().getName());
                        }
                        // 判断是否为自主上传的检测记录
                        if (xResultData.getData().getScLx() != null
                                && xResultData.getData().getScLx().getCode() != null
                                && xResultData.getData().getScLx().getCode().equals("0")) {
                            // 如果是自主上传(code=0)，隐藏配合度评价相关元素
                            binding.tvPhdPj.setVisibility(View.GONE);
                            binding.tvPhdPjLabel.setVisibility(View.GONE); // 假设有对应的标签控件
                            binding.phdPjLayout.setVisibility(View.GONE); // 假设有包含配合度评价的布局
                        } else {
                            // 如果是代上传(code=1)或其他情况，显示配合度评价
                            binding.tvPhdPj.setVisibility(View.VISIBLE);
                            binding.tvPhdPjLabel.setVisibility(View.VISIBLE); // 假设有对应的标签控件
                            binding.phdPjLayout.setVisibility(View.VISIBLE); // 假设有包含配合度评价的布局
                        }
                        binding.tvPhdPj.setText(StrUtils.getPhdText(xResultData.getData().getPhdPj()));
                        binding.tvBphbx.setText(StrUtils.getDev(xResultData.getData().getBphBx(), "无"));
                        binding.tvJcry.setText(StrUtils.getDev(xResultData.getData().getJcryxm(), "无"));
                        binding.tvWorkName.setText(StrUtils.getDev(xResultData.getData().getGzryxm(), "无"));
                        binding.tvRemark.setText(StrUtils.getDev(xResultData.getData().getBz(), "无"));
                        // 处理地址显示逻辑
                        String sheng = xResultData.getData().getShengQhmc();
                        String shi = xResultData.getData().getShiQhmc();
                        String qu = xResultData.getData().getQuQhmc();
                        String xz = xResultData.getData().getXzQhmc();
                        String thdd = xResultData.getData().getDz();

                        // 检查第一组（省市区县）是否都为空
                        boolean isFirstGroupEmpty = TextUtils.isEmpty(sheng) &&
                                TextUtils.isEmpty(shi) &&
                                TextUtils.isEmpty(qu) &&
                                TextUtils.isEmpty(xz);

                        // 如果第一组都为空但谈话地点不为空，只显示谈话地点
                        if (isFirstGroupEmpty && !TextUtils.isEmpty(thdd)) {
                            binding.tvAddress.setText(thdd);
                        }
                        // 如果所有都为空，显示 "-"
                        else if (isFirstGroupEmpty && TextUtils.isEmpty(thdd)) {
                            binding.tvAddress.setText("-");
                        }
                        // 否则拼接所有非空值
                        else {
                            StringBuilder sb = new StringBuilder();
                            if (!TextUtils.isEmpty(sheng))
                                sb.append(sheng);
                            if (!TextUtils.isEmpty(shi))
                                sb.append(shi);
                            if (!TextUtils.isEmpty(qu))
                                sb.append(qu);
                            if (!TextUtils.isEmpty(xz))
                                sb.append(xz);
                            if (!TextUtils.isEmpty(thdd))
                                sb.append(thdd);
                            binding.tvAddress.setText(sb.toString());
                        }
                        // 处理地址之前的为null的情况
//                        binding.tvAddress.setText(xResultData.getData().getShengQhmc() + xResultData.getData().getShiQhmc() + xResultData.getData().getQuQhmc() + xResultData.getData().getXzQhmc() + xResultData.getData().getDz());
                        //视频
                        binding.gridVideo.setAdapter(new ImaVideoShowAdapter(xResultData.getData().getSp()));
                        // 报告
                        binding.gridImg.setAdapter(new ImaVideoShowAdapter(xResultData.getData().getBg()));
                        // 附件
                        binding.gridFj.setAdapter(new FileAllTypeAdapter(UrineInfoActivity.this, xResultData.getData().getQtFj()));
                        //审批中不能编辑
                        if (!TextUtils.isEmpty(xResultData.getData().getApproval()) && xResultData.getData().getApproval().equals("2")) {
                            binding.base.btRight.setVisibility(View.GONE);
                        }
                        //审批--获取获取详情
                        if (!TextUtils.isEmpty(tag)) {
                            peoId = xResultData.getData().getXyrbh();
                            getUserData();
                        }
                        initSpecialArea();
                    }
                } else {
                    XToastUtil.showToast(UrineInfoActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 人员行信息
     */
    private void getUserData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "xyrbh=" + peoId);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PEOPLE_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<PeopleInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<PeopleInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    XImageUtils.load(mContext, xResultData.getData().getXp(), binding.top.ivHead, R.mipmap.ic_def_head);
                    binding.top.tvName.setText(xResultData.getData().getXm());
                    binding.top.recyclerPeoStatus.setAdapter(new PeoStatusInfoListAdapter(UrineInfoActivity.this, xResultData.getData().getRyejzt()));
                    if (xResultData.getData().getRyyjzt() != null) {
                        if (xResultData.getData().getXbdm() != null) {
                            binding.top.tvSex.setText(xResultData.getData().getXbdm().getName() + " " + xResultData.getData().getRyyjzt().getName());
                        } else {
                            binding.top.tvSex.setText(xResultData.getData().getRyyjzt().getName());
                        }
                    } else {
                        if (xResultData.getData().getXbdm() != null) {
                            binding.top.tvSex.setText(xResultData.getData().getXbdm().getName());
                        }
                    }
                    if (xResultData.getData().getMzdm() != null) {
                        binding.peo.tvMingZu.setText(StrUtils.getDev(xResultData.getData().getMzdm().getName(), "无"));
                    }
                    binding.peo.tvIdCard.setText(StrUtils.hideIdCard(xResultData.getData().getZjhm()));
                    binding.peo.tvMobile.setText(xResultData.getData().getLxdh());
                    if (xResultData.getData().getZzmm() != null) {
                        binding.peo.tvZzmm.setText(StrUtils.getDev(xResultData.getData().getZzmm().getName(), "无"));
                    }
                    binding.peo.tvBrith.setText(xResultData.getData().getCsrq());
                    if (xResultData.getData().getXldm() != null) {
                        binding.peo.tvEdu.setText(StrUtils.getDev(xResultData.getData().getXldm().getName(), "无"));
                    }
                    if (!TextUtils.isEmpty(zjwcList) && zjwcList.equals("0")) {
                        return;
                    }
                    if (xResultData.getData().getFxdj() != null) {
                        try {
                            zjwc = !xResultData.getData().getFxdj().getCode().equals("3");
//                            Log.d("TAG", "onSuccess: " + xResultData.getData().getFxdj().getCode() + " " + zjwc);
                            String xzqhdm = UserInfoUtils.getUserInfo().getYhXzqhdm();
                            isSpecialArea = xzqhdm.startsWith("510603") && zjwc;
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        initSpecialArea();
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(UrineListEntity event) {
        if (event != null) {
            finish();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
