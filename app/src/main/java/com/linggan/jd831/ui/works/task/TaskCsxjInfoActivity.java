package com.linggan.jd831.ui.works.task;


import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.view.TimePickerView;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XDateUtil;
import com.lgfzd.base.utils.XGlideEngine;
import com.lgfzd.base.utils.XImageFileCompressEngine;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.CodeNameComListAdapter;
import com.linggan.jd831.bean.CodeNameEntity;
import com.linggan.jd831.bean.CsXjInfoEntity;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.PeoStatusEntity;
import com.linggan.jd831.bean.PeopleTypeInfoEntity;
import com.linggan.jd831.bean.TaskSpListEntity;
import com.linggan.jd831.databinding.ActivityCsxjInfoBinding;
import com.linggan.jd831.ui.user.edit.PeopleStatusAddActivity;
import com.linggan.jd831.ui.user.edit.PeopleTypeInfoActivity;
import com.linggan.jd831.ui.user.edit.xian.PeopleTypeInfoNewActivity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.PhotoUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.CodeNameDialog;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 类  名：处所衔接详情
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/11/22  9:37
 * 版  权：凌感科技
 */
public class TaskCsxjInfoActivity extends XBaseActivity<ActivityCsxjInfoBinding> implements View.OnClickListener {

    private String bh, xjqkCode, peoId, yjztbh, xm, cshGkfs, jcrq;
    private int from = 0;
    private ImageAddUtil imageAddImg;
    private Uri photoUri;
    private File cameraPhoto;

    @Override
    protected ActivityCsxjInfoBinding getViewBinding() {
        return ActivityCsxjInfoBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        bh = getIntent().getStringExtra("ywbh");
        peoId = getIntent().getStringExtra("id");
        yjztbh = getIntent().getStringExtra("yjztbh");
        xm = getIntent().getStringExtra("xm");
        binding.mRecycle.setLayoutManager(new LinearLayoutManager(this));
        imageAddImg = new ImageAddUtil(this, binding.gridSmwj);
        imageAddImg.setMax(4);
        imageAddImg.setOnImageAddListener(() -> {
            from = 2;
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                if (code.equals("1")) {
                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setMaxSelectNum(4 - imageAddImg.getSize()).setCompressEngine(new XImageFileCompressEngine()).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        });
        EventBus.getDefault().register(this);
    }

    @Override
    protected void getData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + bh);
        Log.d("jiaMiString", "bh=" + bh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.TASK_CSXJ_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<CsXjInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<CsXjInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    jcrq = xResultData.getData().getJcrq();
                    List<CodeNameEntity> entityList = new ArrayList<>();
                    entityList.add(new CodeNameEntity("姓名:\u3000\u3000\u3000\u3000", xResultData.getData().getXm()));
                    entityList.add(new CodeNameEntity("身份证号:\u3000\u3000", StrUtils.hideIdCard(xResultData.getData().getZjhm())));
                    entityList.add(new CodeNameEntity("性别:\u3000\u3000\u3000\u3000", StrUtils.getSexText(xResultData.getData().getXbdm())));
                    entityList.add(new CodeNameEntity("家庭住址:\u3000\u3000", xResultData.getData().getJtzz()));
                    entityList.add(new CodeNameEntity("戒毒所名称:\u3000", xResultData.getData().getJdsMc()));
                    entityList.add(new CodeNameEntity("解除日期:\u3000\u3000", xResultData.getData().getJcrq()));
                    binding.mRecycle.setAdapter(new CodeNameComListAdapter(TaskCsxjInfoActivity.this, entityList));
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                Log.d("Tag","failedMsg");
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void initListener() {
        binding.btSubmit.setOnClickListener(this);
        binding.tvXjQk.setOnClickListener(this);
        binding.tvQjycTime.setOnClickListener(this);
        binding.tvXjTime.setOnClickListener(this);
        binding.tvCshGkfs.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.bt_submit) {
            //提交
            if (TextUtils.isEmpty(xjqkCode)) {
                XToastUtil.showToast(this, "请选择衔接情况");
                return;
            }
            if (ButtonUtils.isFastClick()) {
                if (xjqkCode.equals("3") || xjqkCode.equals("2")) {
                    postData();
                } else if (xjqkCode.equals("4")) {
                    //出所转服刑
                    Bundle bundle = new Bundle();
                    bundle.putString("rwbh", bh);
                    bundle.putString("from", "csxj");
                    bundle.putString("id", peoId);//人员编号
                    bundle.putString("csZt", xjqkCode);
                    try {
                        Class<?> targetActivity = StrUtils.isHuNan() ?
                                PeopleTypeInfoActivity.class : PeopleTypeInfoNewActivity.class;
                        XIntentUtil.redirectToNextActivity(this, targetActivity, bundle);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else if (xjqkCode.equals("5")) {
                    //出所转看守羁押
                    Bundle bundle = new Bundle();
                    bundle.putString("code", "1");
                    bundle.putString("name", "看守所羁押");
                    bundle.putString("bh", peoId);
                    bundle.putString("from", "csxj");
                    bundle.putString("yjzt", yjztbh);
                    bundle.putString("rwbh", bh);
                    bundle.putString("xyrXm", xm);
                    bundle.putString("csZt", xjqkCode);
                    XIntentUtil.redirectToNextActivity(mContext, PeopleStatusAddActivity.class, bundle);
                }
            }
        } else if (v.getId() == R.id.tv_xj_qk) {
            //衔接情况
            CodeNameDialog codeNameDialog = new CodeNameDialog(this, StrUtils.getCsztList());
            codeNameDialog.setOnClickDataListener(() -> {
                binding.tvXjQk.setText(codeNameDialog.getCode().getName());
                xjqkCode = codeNameDialog.getCode().getCode();
                if (xjqkCode.equals("3")) {
                    //延期
                    binding.linYq.setVisibility(View.VISIBLE);
                    binding.tvWjTitle.setText("强戒延长证明文件");
                    binding.tvWjTitle.setVisibility(View.VISIBLE);
                    binding.gridSmwj.setVisibility(View.VISIBLE);
                    binding.tvGridInfo.setText("(强制隔离戒毒决定书/延长强制隔离戒毒决定书)");
                    binding.tvGridInfo.setVisibility(View.VISIBLE);
                    binding.linZc.setVisibility(View.GONE);
                } else if (xjqkCode.equals("2")) {//正常出所
                    binding.linYq.setVisibility(View.GONE);
                    binding.linZc.setVisibility(View.VISIBLE);
                    binding.tvWjTitle.setText("出所衔接照片");
                    StrUtils.getPhotoVideoText(binding.tvGridInfo, null);
                    binding.tvGridInfo.setVisibility(View.VISIBLE);
                    binding.tvWjTitle.setVisibility(View.VISIBLE);
                    binding.gridSmwj.setVisibility(View.VISIBLE);
                } else {
                    binding.linYq.setVisibility(View.GONE);
                    binding.linZc.setVisibility(View.GONE);
                    binding.tvWjTitle.setVisibility(View.GONE);
                    binding.gridSmwj.setVisibility(View.GONE);
                    binding.tvGridInfo.setVisibility(View.GONE);
                }
            });
            codeNameDialog.show();
        } else if (v.getId() == R.id.tv_qjyc_time) {
            //强戒延长至以下日期--解除日期之后
            int year = 1900, mon = 0, day = 1;
            if (!TextUtils.isEmpty(jcrq)) {
                String ymd = StrUtils.jieQuTime(jcrq);
                if (!TextUtils.isEmpty(ymd) && ymd.contains("-")) {
                    String[] ymdArr = ymd.split("-");
                    if (ymdArr != null && ymdArr.length > 0) {
                        year = Integer.parseInt(ymd.split("-")[0]);
                        mon = Integer.parseInt(ymd.split("-")[1]);
                        day = Integer.parseInt(ymd.split("-")[2]);
                    }
                }
            }
            Calendar startDate = Calendar.getInstance();
            startDate.set(year, (mon - 1), day);
            Calendar selectedDate = Calendar.getInstance();
            selectedDate.set(2100, 11, 31);
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.tvQjycTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setRangDate(startDate, selectedDate).setDate(startDate).setCancelColor(R.color.black).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_xj_time) {
            //衔接日期---选择范围：解除日期到当前日期之间
            long jcTime = XDateUtil.getDateByFormat(jcrq, XDateUtil.dateFormatYMD).getTime();
            long nowTime = XDateUtil.getDateByFormat(XDateUtil.getCurrentDate(XDateUtil.dateFormatYMD), XDateUtil.dateFormatYMD).getTime();
            if (jcTime > nowTime) {
                XToastUtil.showToast(this, "请出所后再进行操作");
                return;
            }
            int year = 1900, mon = 0, day = 1;
            if (!TextUtils.isEmpty(jcrq)) {
                String ymd = StrUtils.jieQuTime(jcrq);
                if (!TextUtils.isEmpty(ymd) && ymd.contains("-")) {
                    String[] ymdArr = ymd.split("-");
                    if (ymdArr != null && ymdArr.length > 0) {
                        year = Integer.parseInt(ymd.split("-")[0]);
                        mon = Integer.parseInt(ymd.split("-")[1]);
                        day = Integer.parseInt(ymd.split("-")[2]);
                    }
                }
            }
            Calendar startDate = Calendar.getInstance();
            startDate.set(year, (mon - 1), day);
            Calendar selectedDate = Calendar.getInstance();
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.tvXjTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setRangDate(startDate, selectedDate).setDate(selectedDate).setCancelColor(R.color.black).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_csh_gkfs) {
            //出所后管控方式
            CodeNameDialog baseStringDialog = new CodeNameDialog(this, StrUtils.getCshgkztList());
            baseStringDialog.setOnClickDataListener(() -> {
                binding.tvCshGkfs.setText(baseStringDialog.getCode().getName());
                cshGkfs = baseStringDialog.getCode().getCode();
            });
            baseStringDialog.show();
        }
    }

    /**
     * 数据提交
     */
    private void postData() {
        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("bh", bh);
            objectMap.put("sjLy", "3");
            objectMap.put("csZt", xjqkCode);
            JSONArray jsonArray = new JSONArray();
            if (imageAddImg != null && imageAddImg.getPaths() != null && imageAddImg.getPaths().size() > 0) {
                for (int i = 0; i < imageAddImg.getPaths().size(); i++) {
                    JSONObject jsonObjectSP = new JSONObject();
                    jsonObjectSP.put("lj", imageAddImg.getPaths().get(i).getSavePath());
                    jsonObjectSP.put("mc", imageAddImg.getPaths().get(i).getOriginName());
                    jsonObjectSP.put("hz", StrUtils.getFileType(imageAddImg.getPaths().get(i).getSavePath()));
                    jsonObjectSP.put("dx", imageAddImg.getPaths().get(i).getFileSize());
                    jsonObjectSP.put("lrrbh", XShareCacheUtils.getInstance().getString(XConstantUtils.USER_ID));
                    jsonArray.put(jsonObjectSP);
                }
            }
            if (jsonArray.length() <= 0) {
                XToastUtil.showToast(this, "请上传出所衔接照片");
                return;
            }
            if (xjqkCode.equals("2") && TextUtils.isEmpty(binding.tvXjTime.getText().toString())) {
                XToastUtil.showToast(this, "请选择衔接日期");
                return;
            }
            if (xjqkCode.equals("2") && TextUtils.isEmpty(binding.tvCshGkfs.getText().toString())) {
                XToastUtil.showToast(this, "请选择出所后管控方式");
                return;
            }
            if (xjqkCode.equals("2")) {
                objectMap.put("csxjZp", jsonArray);
                objectMap.put("xjRq", binding.tvXjTime.getText().toString());
                objectMap.put("csGkfs", cshGkfs);
            }
            if (xjqkCode.equals("3")) {
                objectMap.put("qjYcWj", jsonArray);
                objectMap.put("qjRq", binding.tvQjycTime.getText().toString());
            }
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.TASK_CSXJ_FIN_INFO);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        binding.btSubmit.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), true, true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(TaskCsxjInfoActivity.this, getString(R.string.sub_sucess));
                    EventBus.getDefault().post(new TaskSpListEntity());
                    finish();
                } else {
                    XToastUtil.showToast(TaskCsxjInfoActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSubmit.setEnabled(true);
            }

            @Override
            public void onFinished() {
                binding.btSubmit.setEnabled(true);
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.CHOOSE_REQUEST:
                    List<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                    if (null != selectList) {
                        for (LocalMedia media : selectList) {
                            if (!TextUtils.isEmpty(media.getCompressPath())) {
                                uploadFile(media.getCompressPath());
                            } else if (!TextUtils.isEmpty(media.getRealPath())) {
                                long fileSize = XFileUtil.getFileSize(media.getRealPath());
                                long fileNewSize = fileSize / 1000000;
                                if (fileNewSize >= 20) {
                                    DialogUtils.showVideoError(this, getString(R.string.tishi), getString(R.string.video_big_info), (code, id) -> {
                                        uploadFile(media.getRealPath());
                                    });
                                    break;
                                } else {
                                    uploadFile(media.getRealPath());
                                }
                            } else if (!TextUtils.isEmpty(media.getPath())) {
                                long fileSize = XFileUtil.getFileSize(media.getPath());
                                long fileNewSize = fileSize / 1000000;
                                if (fileNewSize >= 20) {
                                    DialogUtils.showVideoError(this, getString(R.string.tishi), getString(R.string.video_big_info), (code, id) -> {
                                        uploadFile(media.getPath());
                                    });
                                    break;
                                } else {
                                    uploadFile(media.getPath());
                                }
                            }
                        }
                    }
                    break;
                case 1:
                    //图片
//                    uploadFile(data.getStringExtra("path"));
                    try {
                        boolean isFinish = XFileUtil.bitmapToFilePath(this, BitmapFactory.decodeFile(cameraPhoto.getAbsolutePath()), photoUri, cameraPhoto);
                        if (isFinish) {
                            uploadFile(cameraPhoto.getAbsolutePath());
                        }
                    } catch (Exception e) {
                    }
                    break;
            }
        }
    }

    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (from == 2) {
//            Intent intent = new Intent(this, CameraActivity.class);//调用系统相机
//            startActivityForResult(intent, 1);

            createFileMuLu();
            if (cameraPhoto != null) {
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                photoUri = XFileUtil.getUriFromFile(this, cameraPhoto);
                intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                startActivityForResult(intent, 1);
            }
        }
    }

    /**
     * 创建文件
     */
    private void createFileMuLu() {
        if ((ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED)
                && (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED)) {
            cameraPhoto = new File(PhotoUtil.createPath(this));
            try {
                if (cameraPhoto.exists()) {
                    cameraPhoto.delete();
                }
                cameraPhoto.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            // 权限未授权，需要申请权限
            requestPermission(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        }
    }

    //上传
    private void uploadFile(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        imageAddImg.addImage(xResultData.getData().get(0));
                        imageAddImg.notifyData();
                    }
                } else {
                    XToastUtil.showToast(TaskCsxjInfoActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PeopleTypeInfoEntity event) {
        if (event != null) {
            finish();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(TaskSpListEntity event) {
        if (event != null) {
            finish();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PeoStatusEntity event) {
        if (event != null) {
            finish();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
