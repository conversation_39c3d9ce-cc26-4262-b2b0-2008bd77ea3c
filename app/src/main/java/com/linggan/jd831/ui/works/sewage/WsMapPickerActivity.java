package com.linggan.jd831.ui.works.sewage;

import android.Manifest;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.amap.api.maps.AMap;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.LocationSource;
import com.amap.api.maps.MapView;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.CameraPosition;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.MyLocationStyle;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.core.PoiItem;
import com.amap.api.services.help.Inputtips;
import com.amap.api.services.help.InputtipsQuery;
import com.amap.api.services.help.Tip;
import com.amap.api.services.poisearch.PoiResult;
import com.amap.api.services.poisearch.PoiSearch;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;
import com.lgfzd.base.utils.XAppUtil;
import com.lgfzd.base.utils.XPermissionUtil;
import com.lgfzd.base.utils.XToastUtil;
import com.lgfzd.base.view.XToolbar;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.MapChoiceEntity;
import com.linggan.jd831.utils.DialogUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

/**
 * 地址选择
 */
public class WsMapPickerActivity extends AppCompatActivity implements LocationSource, AMapLocationListener, Inputtips.InputtipsListener {
    private MapView mMapView;//地图显示控件
    private AMap mBaiduMap;//地图对象
    private OnLocationChangedListener listener;
    private AMapLocationClient client;
    private AMapLocationClientOption option;
    // 当前经纬度和地理信息
    private String mStreet;//详细地址
    private String mCity = "", searchCity = "", areaCode = "";
    private double mLatitude;//纬度
    private double mLongitude;//经度
    private boolean changeCamera = false;
    private EditText mEtAddress;
    private EditText mTvCydDz;
    private TextView mTvCydJd;
    private TextView mTvCydWd;
    private TextView mBtCancel;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        createStatusBarConfig();
        setContentView(R.layout.activity_map_picker_ws);
        //检测手机是否开启定位服务
        XPermissionUtil.initPermission(this, new String[]{Manifest.permission.ACCESS_FINE_LOCATION});
        XToolbar toolbar = findViewById(com.lgfzd.base.R.id.toolbar);
        if (toolbar != null) {
            setSupportActionBar(toolbar);
            Drawable upArrow = getResources().getDrawable(com.lgfzd.base.R.drawable.ic_black_back);
            getSupportActionBar().setHomeAsUpIndicator(upArrow);
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        initViews();
        View btRight = findViewById(R.id.bt_sure);
        mMapView = findViewById(R.id.bmapView);
        mMapView.onCreate(savedInstanceState);
        initMap();
        mBaiduMap.setOnMapTouchListener(touchListener);
        mBaiduMap.getUiSettings().setMyLocationButtonEnabled(true);//显示定位按钮
        mBaiduMap.setOnCameraChangeListener(new AMap.OnCameraChangeListener() {
            @Override
            public void onCameraChange(CameraPosition position) {
            }

            @Override
            public void onCameraChangeFinish(CameraPosition position) {
                if (changeCamera) {
                    doSearchQuery(mCity, position.target.latitude, position.target.longitude, "");
                }
            }
        });
        mEtAddress = findViewById(R.id.et_address);
        mEtAddress.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_SEND || (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                switch (event.getAction()) {
                    case KeyEvent.ACTION_UP:
                        //发送请求
                        searchCity = "";
                        InputtipsQuery inputquery = new InputtipsQuery(mEtAddress.getText().toString(), "");
                        Inputtips inputTips = new Inputtips(WsMapPickerActivity.this, inputquery);
                        inputTips.setInputtipsListener(WsMapPickerActivity.this);
                        inputTips.requestInputtipsAsyn();
                        XAppUtil.closeSoftInput(this);
                        return true;
                    default:
                        return true;
                }
            }
            return false;
        });
        btRight.setOnClickListener(v -> {
            //数据提交
            MapChoiceEntity mapChoiceEntity = new MapChoiceEntity();
            mapChoiceEntity.setLat(mLatitude);
            mapChoiceEntity.setLon(mLongitude);
            mapChoiceEntity.setAddress(mTvCydDz.getText().toString());
            mapChoiceEntity.setAreaCode(areaCode);
            EventBus.getDefault().post(mapChoiceEntity);
            finish();
        });
    }

    private void initMap() {
        mBaiduMap = mMapView.getMap();
        mBaiduMap.setLocationSource(this);
        mBaiduMap.setMyLocationEnabled(true);
        mBaiduMap.getUiSettings().setZoomControlsEnabled(false);//隐藏缩放按钮
        mBaiduMap.getUiSettings().setRotateGesturesEnabled(false);//设置旋转手势不可用
        mBaiduMap.moveCamera(CameraUpdateFactory.zoomTo(16));
        MyLocationStyle style = new MyLocationStyle();
        style.myLocationIcon(BitmapDescriptorFactory.fromResource(R.mipmap.navi_map_gps_locked));
        style.interval(5000);
        style.radiusFillColor(Color.argb(10, 0, 0, 180));
        style.myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER);
        mBaiduMap.setMyLocationStyle(style);
    }

    @Override
    protected void onResume() {
        super.onResume();
        mMapView.onResume();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mMapView.onDestroy();
        if (client != null) {
            client.onDestroy();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        mMapView.onPause();
        deactivate();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        mMapView.onSaveInstanceState(outState);
    }

    // 地图触摸事件监听器
    private AMap.OnMapTouchListener touchListener = event -> {
        if (event.getAction() == MotionEvent.ACTION_UP) {
            changeCamera = true;
        }
    };

    /**
     * 搜索周边建筑物
     *
     * @param city      城市
     * @param latitude  纬度
     * @param longitude 经度
     */
    private void doSearchQuery(String city, final double latitude, final double longitude, String from) {
        String mType = "生活服务|体育休闲服务|医疗保健服务|住宿服务|风景名胜|商务住宅|政府机构及社会团体" + "|科教文化服务|交通设施服务|金融保险服务|公司企业|道路附属设施|地名地址信息|公共设施|餐饮服务|购物服务|事件活动|室内设施|通行设施";
        PoiSearch.Query query = new PoiSearch.Query("", mType, city);
        query.setPageSize(20);
        query.setPageNum(1);
        PoiSearch search = null;
        try {
            search = new PoiSearch(this, query);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (search == null) {
            XToastUtil.showToast(WsMapPickerActivity.this, "请稍后再试");
            return;
        }
//        PoiSearch search = new PoiSearch(this, query);
        if (TextUtils.isEmpty(from)) {
            search.setBound(new PoiSearch.SearchBound(new LatLonPoint(latitude, longitude), 5000, true));
        }
        search.setOnPoiSearchListener(new PoiSearch.OnPoiSearchListener() {
            @Override
            public void onPoiSearched(PoiResult result, int i) {
//                Log.i("map", "onPoiSearched: " + new Gson().toJson(result));
                //获取到周边信息
                if (i == 1000 && result != null && result.getQuery() != null && result.getPois() != null && result.getPois().size() > 0) {
                    PoiItem item = result.getPois().get(0);
                    mLatitude = latitude;
                    mLongitude = longitude;
                    areaCode = item.getAdCode();
                    Log.i("ppp", "onPoiSearched: " + latitude + "---" + longitude + "---" + item.getAdCode());
                    mStreet = item.getProvinceName() + item.getCityName() + item.getAdName() + item.getSnippet();
                    //展示地址
                    mTvCydDz.setText(mStreet);
                    mTvCydJd.setText(longitude + "");
                    mTvCydWd.setText(latitude + "");
                    if (!TextUtils.isEmpty(searchCity)) {
                        mBaiduMap.clear(true);
                        LatLng la = new LatLng(item.getLatLonPoint().getLatitude(), item.getLatLonPoint().getLongitude());
                        mBaiduMap.animateCamera(CameraUpdateFactory.newLatLng(la));
                    }
                    if (!TextUtils.isEmpty(from)) {
                        if (result.getPois() != null && result.getPois().size() > 0) {
                        } else {
                            XToastUtil.showToast(WsMapPickerActivity.this, "暂无地址信息");
                        }
                    }
                }
            }

            @Override
            public void onPoiItemSearched(PoiItem poiItem, int i) {
            }
        });
        search.searchPOIAsyn();


    }

    /**
     * 激活定位
     *
     * @param listener 定位监听
     */
    @Override
    public void activate(OnLocationChangedListener listener) {
        this.listener = listener;
        if (client == null) {
            AMapLocationClient client;
            try {
                client = new AMapLocationClient(getApplicationContext());
            } catch (Exception e) {
                XToastUtil.showToast(this, "定位初始化失败");
                return;
            }
//            client = new AMapLocationClient(this);
            option = new AMapLocationClientOption();
            option.setOnceLocation(true);//定位一次
            option.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);//可选，默认高精度，设置定位模式，高精度，低功耗，仅设备
            client.setLocationListener(this);
            client.setLocationOption(option);
            client.startLocation();

        }
    }

    /**
     * 停止定位
     */
    @Override
    public void deactivate() {
        listener = null;
        if (client != null) {
            client.stopLocation();
            client.onDestroy();
        }
        client = null;
    }

    /**
     * 位置信息改变
     *
     * @param location 回调接口
     */
    @Override
    public void onLocationChanged(AMapLocation location) {
        if (listener != null && location != null) {
            if (location.getErrorCode() == 0) {
                listener.onLocationChanged(location);
                mBaiduMap.moveCamera(CameraUpdateFactory.newLatLng(new LatLng(location.getLatitude(), location.getLongitude())));
                doSearchQuery(location.getCity(), location.getLatitude(), location.getLongitude(), "");
            }
        }
    }

    @Override
    public void onGetInputtips(List<Tip> list, int i) {
        //输入
//        Log.i("map", "onGetInputtips: " + new Gson().toJson(list));
        DialogUtils.showPopupCity(this, list, mEtAddress, code -> {
            if (code.getPoint() != null) {
                searchCity = "";
                mBaiduMap.moveCamera(CameraUpdateFactory.newLatLngZoom(new LatLng(code.getPoint().getLatitude(), code.getPoint().getLongitude()), 16));
                String[] split = code.getDistrict().split("市");
                Log.i("dizhi", "onGetInputtips: " + code.getDistrict() + "--" + new Gson().toJson(split));
                try {
                    if (split != null && split.length > 0) {
                        doSearchQuery(split[1], code.getPoint().getLatitude(), code.getPoint().getLongitude(), "1");
                    }
                } catch (Exception e) {
                }
            }
            XAppUtil.closeSoftInput(this);
        });
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 初始化沉浸式状态栏
     */
    protected void createStatusBarConfig() {
        ImmersionBar.with(this)
                // 默认状态栏字体颜色为黑色
                .transparentStatusBar().statusBarDarkFont(true)
                // 指定导航栏背景颜色
                .navigationBarColor(com.lgfzd.base.R.color.white)
                // 状态栏字体和导航栏内容自动变色，必须指定状态栏颜色和导航栏颜色才可以自动变色
                .autoDarkModeEnable(true, 0.2f).init();
    }

    private void initViews() {
        mTvCydDz = findViewById(R.id.tv_cyd_dz);
        mTvCydJd = findViewById(R.id.tv_cyd_jd);
        mTvCydWd = findViewById(R.id.tv_cyd_wd);
        mBtCancel = findViewById(R.id.bt_cancel);
        mBtCancel.setOnClickListener(v -> {
            finish();
        });
    }
}