package com.linggan.jd831.ui.works.qiye.v2.lab.adapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.ui.works.qiye.v2.lab.entity.ListSysRyEntity;

/**
 * 类  名：宣教执行人
 * 作  者：ZXB
 * 说  明：
 * 时  间：2024/2/2 11:51
 * 版  权：LGKJ
 */
public class LabRegionListHolder extends IViewHolder {

    @Override
    public int getLayout() {
        return R.layout.item_lab_region_list;
    }

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    class ViewHolder extends XViewHolder<ListSysRyEntity.QyList> {

        ImageView mIvChoice;
        TextView mTvTitle;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mIvChoice = view.findViewById(R.id.iv_choice);
            mTvTitle = view.findViewById(R.id.tv_title);
//            mEtNum = view.findViewById(R.id.et_num);
//            mEtNum.setFilters(new InputFilter[]{new WuTenInputFilter()});
        }

        @Override
        protected void onBindData(final ListSysRyEntity.QyList itemData) {
            if (!TextUtils.isEmpty(itemData.getBh())) {
                mTvTitle.setText(itemData.getMc());
            }
            mIvChoice.setSelected(itemData.isChoice());
            mIvChoice.setSelected(itemData.isChoice());
            itemView.setOnClickListener(v -> {
                //选中
                itemData.setChoice(!itemData.isChoice());
                mIvChoice.setSelected(itemData.isChoice());
            });
        }
    }

}


