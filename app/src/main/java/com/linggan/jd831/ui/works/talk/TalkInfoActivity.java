package com.linggan.jd831.ui.works.talk;


import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.FileAllTypeAdapter;
import com.linggan.jd831.adapter.ImaVideoShowAdapter;
import com.linggan.jd831.adapter.PeoStatusInfoListAdapter;
import com.linggan.jd831.adapter.TalkNeiRongAdapter;
import com.linggan.jd831.bean.PeopleInfoEntity;
import com.linggan.jd831.bean.SignQzBean;
import com.linggan.jd831.bean.TalkInfoEntity;
import com.linggan.jd831.bean.TalkListEntity;
import com.linggan.jd831.databinding.ActivityTalkInfoBinding;
import com.linggan.jd831.ui.common.PhotoActivity;
import com.linggan.jd831.ui.common.SignatureActivity;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.TaskApprovalFactory;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.utils.XImageUtils;
import com.linggan.jd831.widget.CodeNameDialog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

/**
 * 类  名：谈话记录-详情
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class TalkInfoActivity extends XBaseActivity<ActivityTalkInfoBinding> implements View.OnClickListener {

    private String bh, peoId, tag, key, taskId, aid, pro, spls, ryyxEdit, qmdzUrl, xtxx;
    private TalkInfoEntity talkInfoEntity;

    @Override
    protected ActivityTalkInfoBinding getViewBinding() {
        return ActivityTalkInfoBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        bh = getIntent().getStringExtra("id");
        peoId = getIntent().getStringExtra("pid");
        ryyxEdit = getIntent().getStringExtra("ryyxEdit");
        xtxx = getIntent().getStringExtra("xtxx");
        //这里是标识从任务审批跳转过来--下面三个参数
        tag = getIntent().getStringExtra("tag");
        taskId = getIntent().getStringExtra("tid");
        key = getIntent().getStringExtra("key");
        pro = getIntent().getStringExtra("pro");
        aid = getIntent().getStringExtra("aid");
        spls = getIntent().getStringExtra("spls");//审批历史过来

        binding.top.recyclerPeoStatus.setLayoutManager(new GridLayoutManager(this, 3));
        binding.recycleNr.setLayoutManager(new LinearLayoutManager(this));
        EventBus.getDefault().register(this);

        //湖南不需要--电子签名
        String xzqhdm = UserInfoUtils.getUserInfo().getYhXzqhdm();
        if (!TextUtils.isEmpty(xzqhdm) && !xzqhdm.startsWith("43")) {
            binding.linDzqm.setVisibility(View.VISIBLE);
        }
        //系统消息进来
        if (!TextUtils.isEmpty(xtxx)) {
            binding.base.btRight.setVisibility(View.GONE);
            binding.top.linInfo.setVisibility(View.GONE);
            binding.peo.linInfo.setVisibility(View.GONE);
        }
    }

    @Override
    protected void initListener() {
        //非审批过来
        if (TextUtils.isEmpty(tag)) {
            if (TextUtils.isEmpty(ryyxEdit)) {
                binding.base.tvRight.setText(getString(R.string.edit));
                binding.base.tvRight.setTextColor(getResources().getColor(R.color.color_main));
                binding.base.ivRight.setImageResource(R.mipmap.ic_edit);
                binding.base.btRight.setOnClickListener(v -> {
                    //编辑
                    Bundle bundle = new Bundle();
                    bundle.putString("id", peoId);
                    bundle.putString("name", binding.top.tvName.getText().toString());
                    bundle.putSerializable("msg", talkInfoEntity);
                    XIntentUtil.redirectToNextActivity(this, TalkAddActivity.class, bundle);
                });
            }
            if (TextUtils.isEmpty(xtxx)) {
                getUserData();
            }
        } else {
            //审批数据
            binding.linPopup.linInfo.setVisibility(View.VISIBLE);
            TaskApprovalFactory.getSpData(this, binding.linPopup.ivOnDown, binding.linPopup.recycleSp, binding.linPopup.etSpRemark, binding.linPopup.btNo, binding.linPopup.btTg, binding.linPopup.linDspShow, binding.linPopup.linSpth, binding.linPopup.btFangQi, binding.linPopup.btTgEdit, key, pro, aid, taskId, tag, spls, code -> {
                switch (code) {
                    case "pass":
                        //审批提交完成
                        finish();
                        break;
                    case "fin":
                        //放弃审批
                        finish();
                        break;
                    case "edit":
                        //编辑
                        Bundle bundle = new Bundle();
                        bundle.putString("id", peoId);
                        bundle.putString("name", binding.top.tvName.getText().toString());
                        bundle.putSerializable("msg", talkInfoEntity);
                        bundle.putString("tag", tag);
                        bundle.putString("pro", pro);
                        XIntentUtil.redirectToNextActivity(this, TalkAddActivity.class, bundle);
                        break;
                }
            });
        }
        binding.ivLyrQz.setOnClickListener(this);
    }

    @Override
    protected void getData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.TALK_INFO + bh);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<TalkInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<TalkInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        talkInfoEntity = xResultData.getData();
                        if (!TextUtils.isEmpty(xResultData.getData().getZq())) {
                            binding.tvJfDate.setText(xResultData.getData().getZq());
                        } else {
                            binding.tvJfDate.setText(xResultData.getData().getZqKssj() + " 至 " + xResultData.getData().getZqJssj());
                        }
                        binding.tvTime.setText(xResultData.getData().getThsj());
                        if (xResultData.getData().getThlx() != null) {
                            binding.tvJfType.setText(xResultData.getData().getThlx().getName());
                        }
                        if (xResultData.getData().getThfs() != null) {
                            binding.tvJfFangShi.setText(xResultData.getData().getThfs().getName());
                        }
                        binding.tvWork.setText(xResultData.getData().getGzryxm());
                        binding.tvGaiYao.setText(xResultData.getData().getThgy());
                        binding.tvThAddress.setText(xResultData.getData().getShengQhmc() + xResultData.getData().getShiQhmc() + xResultData.getData().getQuQhmc() + xResultData.getData().getXzQhmc() + xResultData.getData().getThdd());
                        //谈话附件
                        binding.gridFj.setAdapter(new FileAllTypeAdapter(TalkInfoActivity.this, xResultData.getData().getThfj()));
                        //图片
                        binding.gridImg.setAdapter(new ImaVideoShowAdapter(xResultData.getData().getThzp()));
                        try {
                            binding.gridImgInsights.setAdapter(new ImaVideoShowAdapter(xResultData.getData().getXdth()));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        //谈话内容
                        binding.recycleNr.setAdapter(new TalkNeiRongAdapter(TalkInfoActivity.this, xResultData.getData().getNr(), "1"));
                        //审批中不能编辑
                        if (!TextUtils.isEmpty(xResultData.getData().getApproval()) && xResultData.getData().getApproval().equals("2")) {
                            binding.base.btRight.setVisibility(View.GONE);
                        }
                        if (!TextUtils.isEmpty(xResultData.getData().getBz())) {//金堂才有备注
                            binding.linBz.setVisibility(View.VISIBLE);
                            binding.tvRemark.setText(xResultData.getData().getBz());
                        }
                        //审批--获取获取详情
                        if (!TextUtils.isEmpty(tag)) {
                            peoId = xResultData.getData().getXyrbh();
                            getUserData();
                        }
                        //是否签名
                        String xzqhdm = UserInfoUtils.getUserInfo().getYhXzqhdm();
                        if (!TextUtils.isEmpty(xzqhdm) && !xzqhdm.startsWith("43")) {
                            if (xResultData.getData().getDzqm() != null && !TextUtils.isEmpty(xResultData.getData().getDzqm().getLj())) {//有签名
                                binding.tvLxr.setVisibility(View.GONE);
                                binding.ivLyrQz.setVisibility(View.VISIBLE);
                                qmdzUrl = xResultData.getData().getDzqm().getLj();
                                XImageUtils.loadFit(TalkInfoActivity.this, xResultData.getData().getDzqm().getLj(), binding.ivLyrQz);
                            } else {
                                //系统消息进来
                                if (!TextUtils.isEmpty(xtxx)) {
                                    binding.tvLxr.setText(getString(R.string.lx_xdry_sign));
                                    binding.tvLxr.setTextColor(Color.GRAY);
                                    binding.ivLyrQz.setEnabled(false);
                                    binding.tvLxr.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
                                }
                            }
                        }
                    }
                } else {
                    XToastUtil.showToast(TalkInfoActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 人员行信息
     */
    private void getUserData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "xyrbh=" + peoId);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PEOPLE_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<PeopleInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<PeopleInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    XImageUtils.load(mContext, xResultData.getData().getXp(), binding.top.ivHead, R.mipmap.ic_def_head);
                    binding.top.tvName.setText(xResultData.getData().getXm());
                    binding.top.recyclerPeoStatus.setAdapter(new PeoStatusInfoListAdapter(TalkInfoActivity.this, xResultData.getData().getRyejzt()));
                    if (xResultData.getData().getRyyjzt() != null) {
                        if (xResultData.getData().getXbdm() != null) {
                            binding.top.tvSex.setText(xResultData.getData().getXbdm().getName() + " " + xResultData.getData().getRyyjzt().getName());
                        } else {
                            binding.top.tvSex.setText(xResultData.getData().getRyyjzt().getName());
                        }
                    } else {
                        if (xResultData.getData().getXbdm() != null) {
                            binding.top.tvSex.setText(xResultData.getData().getXbdm().getName());
                        }
                    }
                    if (xResultData.getData().getMzdm() != null) {
                        binding.peo.tvMingZu.setText(xResultData.getData().getMzdm().getName());
                    }
                    binding.peo.tvIdCard.setText(StrUtils.hideIdCard(xResultData.getData().getZjhm()));
                    binding.peo.tvMobile.setText(xResultData.getData().getLxdh());
                    if (xResultData.getData().getZzmm() != null) {
                        binding.peo.tvZzmm.setText(xResultData.getData().getZzmm().getName());
                    }
                    binding.peo.tvBrith.setText(xResultData.getData().getCsrq());
                    if (xResultData.getData().getXldm() != null) {
                        binding.peo.tvEdu.setText(xResultData.getData().getXldm().getName());
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.iv_lyr_qz) {
            if (TextUtils.isEmpty(qmdzUrl)) {
                //去签名
                CodeNameDialog dianDialog = new CodeNameDialog(this, StrUtils.getSignTypeList());
                dianDialog.setOnClickDataListener(() -> {
                    if (!TextUtils.isEmpty(dianDialog.getCode().getCode())) {
                        if (dianDialog.getCode().getCode().equals("1")) {
                            //推送
                            setSignTiXing();
                        } else {
                            //签名
                            Bundle bundle = new Bundle();
                            bundle.putString("from", "talk");
                            bundle.putString("bh", bh);
                            XIntentUtil.redirectToNextActivity(TalkInfoActivity.this, SignatureActivity.class, bundle);
                        }
                    }
                });
                dianDialog.show();
            } else {
                //查看签名
                XIntentUtil.redirectToNextActivity(this, PhotoActivity.class, "path", qmdzUrl);
            }
        }
    }

    /**
     * 设置签字提醒
     */
    private void setSignTiXing() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "xyrbh=" + peoId + "&bh=" + bh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.TALK_DZQM_TX + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(TalkInfoActivity.this, getString(R.string.submit_scess));
                } else {
                    XToastUtil.showToast(TalkInfoActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(TalkListEntity event) {
        if (event != null) {
            finish();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(SignQzBean event) {
        if (event != null) {//更新签字数据
            getData();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }


}
