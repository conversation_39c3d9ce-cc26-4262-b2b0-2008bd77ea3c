package com.linggan.jd831.ui.works.task;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.linggan.jd831.utils.XHttpUtils;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.PunishTaskCodeListAdapter;
import com.linggan.jd831.bean.CodeNameEntity;
import com.linggan.jd831.bean.LveEntity;
import com.linggan.jd831.bean.PunishCodeBean;
import com.linggan.jd831.bean.TaskSpListEntity;
import com.linggan.jd831.databinding.ActivityPunishGkShengjiBinding;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.util.List;

/**
 * 类  名：惩戒管控升级
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/11/17 9:46
 * 版  权：凌感科技
 */
public class PunishGKActivity extends XBaseActivity<ActivityPunishGkShengjiBinding> implements View.OnClickListener {

    private String peoId, bh;
    private CodeNameEntity codeNameEntity;
    private String fxdjListString, todayLvl;
    private LveEntity lveEntity;

    @Override
    protected ActivityPunishGkShengjiBinding getViewBinding() {
        return ActivityPunishGkShengjiBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        binding.mRecycle.setLayoutManager(new LinearLayoutManager(this));
        bh = getIntent().getStringExtra("bh");
        peoId = getIntent().getStringExtra("id");
        codeNameEntity = (CodeNameEntity) getIntent().getSerializableExtra("info");
        if (codeNameEntity != null) {
            binding.peo.tvRylx.setText(codeNameEntity.getName());
        }
        FactoryUtils.getUserData(this, peoId, result -> {
            binding.peo.tvName.setText(result.getXm());
            binding.peo.tvIdCard.setText(StrUtils.hideIdCard(result.getZjhm()));
            binding.peo.tvMobile.setText(result.getLxdh());
            if (result.getFxdj() != null) {
                binding.peo.tvLvl.setText(result.getFxdj().getName());
                todayLvl = result.getFxdj().getCode();
            }
        });
        EventBus.getDefault().register(this);
    }

    @Override
    protected void initListener() {
        binding.btSubmit.setOnClickListener(this);
        binding.tvSjLvl.setOnClickListener(this);
    }

    @Override
    protected void getData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + bh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PUNISH_TASK_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<PunishCodeBean>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<PunishCodeBean>>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    binding.mRecycle.setAdapter(new PunishTaskCodeListAdapter(PunishGKActivity.this, xResultData.getData()));
                } else {
                    XToastUtil.showToast(PunishGKActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.bt_submit) {
            //确定
            if (ButtonUtils.isFastClick()) {
                RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PUNISH_TASK_INFO_EDIT);
                requestParams.addHeader("Content-Type", "application/json");
                requestParams.addHeader("Origin-Content-Type", "application/json");
                JSONObject objectMap = new JSONObject();
                try {
                    objectMap.put("xyrbh", peoId);
                    objectMap.put("rwbh", bh);
                    JSONArray jsonArray = new JSONArray();
                    if (lveEntity != null && lveEntity.getDm() != null && lveEntity.getDm().size() > 0) {
                        for (int i = 0; i < lveEntity.getDm().size(); i++) {
                            jsonArray.put(lveEntity.getDm().get(i).toString());
                        }
                    }
                    if (!TextUtils.isEmpty(lveEntity.getReason())) {
                        objectMap.put("bz", lveEntity.getReason());//定级原因，如果为特高必穿
                    }
                    if (jsonArray.length() <= 0) {
                        XToastUtil.showToast(this, "请选择风险等级");
                        return;
                    }
                    objectMap.put("fxgk", jsonArray);
                } catch (JSONException e) {
                }
                binding.btSubmit.setEnabled(false);
                String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
                XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), true, true, new XHttpResponseCallBack() {
                    @Override
                    public void onSuccess(String result) {
                        XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                        }.getType());
                        if (xResultData.getStatus() == 0) {
                            XToastUtil.showToast(PunishGKActivity.this, xResultData.getErrorInfo());
                            EventBus.getDefault().post(new TaskSpListEntity());
                            finish();
                        }
                    }

                    @Override
                    public void onFailed(int code, String failedMsg) {
                        binding.btSubmit.setEnabled(true);
                    }

                    @Override
                    public void onFinished() {
                        binding.btSubmit.setEnabled(true);
                    }
                });
            }
        } else if (v.getId() == R.id.tv_sj_lvl) {
            //等级
            Bundle bundle = new Bundle();
            bundle.putString("info", fxdjListString);
            bundle.putString("lvl", todayLvl);
            XIntentUtil.redirectToNextActivity(this, PunishFxLvlActivity.class, bundle);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(LveEntity item) {
        if (item != null) {
            lveEntity = item;
            binding.tvSjLvl.setText(StrUtils.listToString(item.getMc()));
            fxdjListString = StrUtils.listToString(item.getDm());
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
