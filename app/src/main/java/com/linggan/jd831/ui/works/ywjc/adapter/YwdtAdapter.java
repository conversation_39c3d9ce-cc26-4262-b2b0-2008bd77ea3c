package com.linggan.jd831.ui.works.ywjc.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.bean.YwzlItem;

import java.util.ArrayList;
import java.util.List;

public class YwdtAdapter extends RecyclerView.Adapter<YwdtAdapter.ViewHolder> {
    private final Context context;
    private List<YwzlItem> YwzlItems = new ArrayList<>();

    public YwdtAdapter(Context context, List<YwzlItem> YwzlItems) {
        this.context = context;
        this.YwzlItems = YwzlItems;
    }

    /**
     * 添加分隔线到RecyclerView
     * 在除最后一个item外的所有item底部添加下划线
     *
     * @param recyclerView 要添加分隔线的RecyclerView
     */
    public void addItemDecoration(RecyclerView recyclerView) {
        // 创建自定义分隔线装饰器
        YwdtItemDecoration itemDecoration = new YwdtItemDecoration(
                Color.parseColor("#EFEFEF"), // 浅灰色分隔线
                3f, // 3dp高度
                29,  // 左右2dp边距
                4
        );
        recyclerView.addItemDecoration(itemDecoration);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_ywdt_item, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        YwzlItem item = YwzlItems.get(position);
        holder.tvTime.setText(item.getDate());
        holder.tvTitle.setText(item.getDisplayText());
    }

    @Override
    public int getItemCount() {
        return YwzlItems.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvTitle;
        TextView tvTime;

        ViewHolder(View itemView) {
            super(itemView);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvTime = itemView.findViewById(R.id.tv_time);
        }
    }
}