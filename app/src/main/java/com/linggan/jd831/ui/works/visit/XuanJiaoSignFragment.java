package com.linggan.jd831.ui.works.visit;

import android.app.Dialog;
import android.os.Bundle;
import android.os.Environment;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseFragment;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.XuanJiaoInfoEntity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.utils.XImageUtils;

import org.xutils.common.Callback;
import org.xutils.http.RequestParams;
import org.xutils.view.annotation.ContentView;
import org.xutils.view.annotation.Event;
import org.xutils.view.annotation.ViewInject;
import org.xutils.x;

import java.io.File;

/**
 * 宣教-签到
 */
@ContentView(R.layout.fragment_xuan_jiao_sign)
public class XuanJiaoSignFragment extends XBaseFragment {

    @ViewInject(R.id.tv_title)
    private TextView tvTitle;
    @ViewInject(R.id.iv_erm)
    private ImageView ivErm;
    @ViewInject(R.id.tv_num)
    private TextView tvNum;
    @ViewInject(R.id.swipe_refresh)
    private SwipeRefreshLayout swipeRefreshLayout;
    @ViewInject(R.id.nested_scroll_view)
    private NestedScrollView nestedScrollView;
    private String title, ewm, num, bh;

    public static XuanJiaoSignFragment newInstance(String title, String ewm, String num, String bh) {
        XuanJiaoSignFragment benefitTabFragment = new XuanJiaoSignFragment();
        Bundle bundle = new Bundle();
        bundle.putString("title", title);
        bundle.putString("ewm", ewm);
        bundle.putString("num", num);
        bundle.putString("bh", bh);
        benefitTabFragment.setArguments(bundle);
        return benefitTabFragment;
    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            title = getArguments().getString("title");
            ewm = getArguments().getString("ewm");
            num = getArguments().getString("num");
            bh = getArguments().getString("bh");
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        tvTitle.setText(title);
        XImageUtils.loadFit(getActivity(), ewm, ivErm);
        tvNum.setText(StrUtils.getDev(num, "0"));
        
        // 设置刷新颜色
//        swipeRefreshLayout.setColorSchemeColors(Color.BLUE);
        // 实现滑倒顶端才能下拉刷新（即解决滑动冲突）
        nestedScrollView.getViewTreeObserver().addOnScrollChangedListener(() -> 
            swipeRefreshLayout.setEnabled(nestedScrollView.getScrollY() == 0));
        // 设置刷新监听器
        swipeRefreshLayout.setOnRefreshListener(() -> {
            ewm = ""; // 清空ewm
            swipeRefreshLayout.setRefreshing(false);
            lazyLoad();
        });
    }

    @Override
    protected void lazyLoad() {
        if (TextUtils.isEmpty(ewm)) {
            String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + bh);
            RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.XJHD_INFO + "?query=" + jiaMiString);
            requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
            requestParams.addHeader("Origin-Content-Type", "application/json");
            XHttpUtils.get(getActivity(), requestParams, DialogUtils.showLoadDialog(getActivity(), ""), new XHttpResponseCallBack() {
                @Override
                public void onSuccess(String result) {
                    XResultData<XuanJiaoInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XuanJiaoInfoEntity>>() {
                    }.getType());
                    if (xResultData.getStatus() == 0) {
                        if (xResultData.getData() != null) {
                            ewm = xResultData.getData().getEwmlj();
                            XImageUtils.loadFit(getActivity(), xResultData.getData().getEwmlj(), ivErm);
                            if (TextUtils.isEmpty(num)) {
                                tvNum.setText(StrUtils.getDev(xResultData.getData().getCyrs(), "0"));
                            }
                        }
                    }
                }

                @Override
                public void onFailed(int code, String failedMsg) {
                }

                @Override
                public void onFinished() {
                }
            });
        }
    }

    @Event(type = View.OnClickListener.class, value = {R.id.iv_erm})
    private void onClick(View view) {
        if (view.getId() == R.id.iv_erm) {
            if (ButtonUtils.isFastClick()) {
                if (!TextUtils.isEmpty(ewm)) {
                    Dialog dialog = DialogUtils.showLoadDialog(getActivity(), getString(R.string.saveing));
                    dialog.show();
                    RequestParams params = new RequestParams(ewm);
                    //设置不自动命名
                    params.setAutoRename(false);
                    //设置文件保存路径
                    params.setSaveFilePath(createPath(StrUtils.getFileNme(ewm)));
                    //开始下载
                    x.http().get(params, new Callback.ProgressCallback<File>() {
                        @Override
                        public void onSuccess(File result) {
                            dialog.dismiss();
                            XToastUtil.showToast(getActivity(), "二维码已保存到相册");
                            XFileUtil.insertMediaPic(getActivity(), result, true);
                        }

                        @Override
                        public void onError(Throwable ex, boolean isOnCallback) {
                            dialog.dismiss();
                        }

                        @Override
                        public void onCancelled(CancelledException cex) {
                        }

                        @Override
                        public void onFinished() {
                        }

                        @Override
                        public void onWaiting() {
                        }

                        @Override
                        public void onStarted() {
                        }

                        @Override
                        public void onLoading(long total, long current, boolean isDownloading) {
                        }
                    });
                }
            }
        }
    }

    /**
     * 获取上传的路径
     *
     * @return 路径
     */
    private String createPath(String fileName) {
        //先判断外设SD卡是不是可用
        if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            //判断是否存在同文件
            File file = new File(getActivity().getExternalCacheDir().getPath() + File.separator + fileName);
            if (file.exists()) {
                file.delete();
            }
            return getActivity().getExternalCacheDir().getPath() + File.separator + fileName;
        } else {
            File file = new File(getActivity().getFilesDir() + File.separator + fileName);
            if (file.exists()) {
                file.delete();
            }
            return getActivity().getFilesDir() + File.separator + fileName;
        }
    }

}
