package com.linggan.jd831.ui.works.weifa;


import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.GridLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseLocaActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.ImaVideoShowAdapter;
import com.linggan.jd831.adapter.PeoStatusInfoListAdapter;
import com.linggan.jd831.bean.PeopleInfoEntity;
import com.linggan.jd831.bean.PingGuListEntity;
import com.linggan.jd831.bean.SendFileEntity;
import com.linggan.jd831.bean.WFFZDetailsEntity;
import com.linggan.jd831.bean.UrineListEntity;
import com.linggan.jd831.databinding.ActivityWeifafanzuiDetailBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.utils.XImageUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

import java.util.ArrayList;

/**
 * 类  名：季度新增
 * 作  者：lqs
 * 说  明：
 * 时  间：2024/8/12 10:00
 */
public class WeiFaFanZuiDetailActivity extends XBaseLocaActivity<ActivityWeifafanzuiDetailBinding> implements View.OnClickListener {

    private String bh, peoId, tag, key, taskId, aid, pro, spls;
    private WFFZDetailsEntity WFFZDetailsEntity;

    @Override
    protected ActivityWeifafanzuiDetailBinding getViewBinding() {
        return ActivityWeifafanzuiDetailBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        peoId = getIntent().getStringExtra("id");
        bh = getIntent().getStringExtra("listBh");

        //这里是标识从任务审批跳转过来--下面三个参数
        binding.top.recyclerPeoStatus.setLayoutManager(new GridLayoutManager(this, 2));

        EventBus.getDefault().register(this);
    }

    @Override
    protected void initListener() {
        //非审批过来
        if (TextUtils.isEmpty(tag)) {
            getUserData();
            binding.base.tvRight.setText(getString(R.string.edit));
            binding.base.tvRight.setTextColor(getResources().getColor(R.color.color_main));
            binding.base.ivRight.setImageResource(R.mipmap.ic_edit);
            binding.base.btRight.setOnClickListener(v -> {
                //编辑
                Bundle bundle = new Bundle();
                bundle.putString("id", peoId);
                bundle.putString("listBh", bh);
                bundle.putString("name", binding.top.tvName.getText().toString());
                bundle.putSerializable("msg", WFFZDetailsEntity);
                XIntentUtil.redirectToNextActivity(this, WeiFaFanZuiAddActivity.class, bundle);
            });
        }
    }

    @Override
    protected void getData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + bh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.INFO_AY + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<WFFZDetailsEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<WFFZDetailsEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        WFFZDetailsEntity = xResultData.getData();
//                        if (!TextUtils.isEmpty(xResultData.getData().getZq())) {
//                            binding.tvZhouqi.setText(xResultData.getData().getZq());
//                        } else {
//                            if (TextUtils.isEmpty(xResultData.getData().getZqKssj())) {
//                                binding.tvZhouqi.setText("非周期检测");
//                            } else {
//                                binding.tvZhouqi.setText(xResultData.getData().getZqKssj() + "至" + xResultData.getData().getZqJssj());
//                            }
//                        }
                        binding.tvTime.setText(StrUtils.getDev(xResultData.getData().getFzsj(), "-"));
                        binding.tvType.setText(xResultData.getData().getAjLxEnum() != null && xResultData.getData().getAjLxEnum().getName() != null
                                ? xResultData.getData().getAjLxEnum().getName() : "-");

                        binding.tvLx.setText(StrUtils.getDev(xResultData.getData().getFzxq(), "-"));

                        // fzayList 拼接list /
                        if (xResultData.getData().getFzayList() != null && !xResultData.getData().getFzayList().isEmpty()) {
                            StringBuilder sb = new StringBuilder();
                            for (WFFZDetailsEntity.FzayDTO fzayDTO : xResultData.getData().getFzayList()) {
                                sb.append(fzayDTO.getText()).append("/");
                            }
                            binding.tvReason.setText(sb.substring(0, sb.length() - 1));
                        } else {
                            binding.tvReason.setText("-");
                        }

                        // 附件
                        if (xResultData.getData().getFjList() != null && !xResultData.getData().getFjList().isEmpty()) {
                            binding.tvFjNoTips.setVisibility(View.GONE);
                            binding.gridFj.setAdapter(new ImaVideoShowAdapter(xResultData.getData().getFjList()));
                            binding.gridFj.notifyAll();
                        } else {
                            binding.tvFjNoTips.setVisibility(View.VISIBLE);
                            binding.gridFj.setAdapter(new ImaVideoShowAdapter(new ArrayList<SendFileEntity>()));
                            binding.gridFj.notifyAll();
                        }
                        //审批中不能编辑
//                        if (!TextUtils.isEmpty(xResultData.getData().getApproval()) && xResultData.getData().getApproval().equals("2")) {
//                            binding.base.btRight.setVisibility(View.GONE);
//                        }
                        //审批--获取获取详情
//                        if (!TextUtils.isEmpty(tag)) {
//                            peoId = xResultData.getData().getXyrbh();
//                            getUserData();
//                        }
                    }
                } else {
                    XToastUtil.showToast(WeiFaFanZuiDetailActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 人员行信息
     */
    private void getUserData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "xyrbh=" + peoId);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PEOPLE_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<PeopleInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<PeopleInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    XImageUtils.load(mContext, xResultData.getData().getXp(), binding.top.ivHead, R.mipmap.ic_def_head);
                    binding.top.tvName.setText(xResultData.getData().getXm());
                    binding.top.recyclerPeoStatus.setLayoutManager(new GridLayoutManager(mContext, 2));
                    binding.top.recyclerPeoStatus.setAdapter(new PeoStatusInfoListAdapter(WeiFaFanZuiDetailActivity.this, xResultData.getData().getRyejzt()));
                    if (xResultData.getData().getRyyjzt() != null) {
                        if (xResultData.getData().getXbdm() != null) {
                            binding.top.tvSex.setText(xResultData.getData().getXbdm().getName() + " " + xResultData.getData().getRyyjzt().getName());
                        } else {
                            binding.top.tvSex.setText(xResultData.getData().getRyyjzt().getName());
                        }
                    } else {
                        if (xResultData.getData().getXbdm() != null) {
                            binding.top.tvSex.setText(xResultData.getData().getXbdm().getName());
                        }
                    }
//                    if (xResultData.getData().getMzdm() != null) {
//                        binding.peo.tvMingZu.setText(StrUtils.getDev(xResultData.getData().getMzdm().getName(), "无"));
//                    }
//                    binding.peo.tvIdCard.setText(StrUtils.hideIdCard(xResultData.getData().getZjhm()));
//                    binding.peo.tvMobile.setText(xResultData.getData().getLxdh());
//                    if (xResultData.getData().getZzmm() != null) {
//                        binding.peo.tvZzmm.setText(StrUtils.getDev(xResultData.getData().getZzmm().getName(), "无"));
//                    }
//                    binding.peo.tvBrith.setText(xResultData.getData().getCsrq());
//                    if (xResultData.getData().getXldm() != null) {
//                        binding.peo.tvEdu.setText(StrUtils.getDev(xResultData.getData().getXldm().getName(), "无"));
//                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(UrineListEntity event) {
        if (event != null) {
            finish();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PingGuListEntity event) {
        if (event != null) {
            getData();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void onClick(View v) {

    }
}