package com.linggan.jd831.ui.works.sign;


import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.BitmapFactory;
import android.location.Location;
import android.location.LocationManager;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.view.TimePickerView;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBase3Activity;
import com.lgfzd.base.device.OtherUtils;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.MapUtils;
import com.lgfzd.base.utils.XDateUtil;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XLocationMagUtil;
import com.lgfzd.base.utils.XPermissionUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.InputEntity;
import com.linggan.jd831.bean.MapChoiceEntity;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.PeopleInfoEntity;
import com.linggan.jd831.bean.SignListEntity;
import com.linggan.jd831.databinding.ActivitySignAddBinding;
import com.linggan.jd831.ui.common.InputInfoActivity;
import com.linggan.jd831.ui.common.VideoRecordNoActivity;
import com.linggan.jd831.ui.common.maps.MapPickerActivity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.MdFactoryUtils;
import com.linggan.jd831.utils.PhotoUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.TaskApprovalFactory;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.XHttpUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.List;


/**
 * 类  名： 签到-新增
 * 描   述：JD831_V20
 * 作  者：
 * 时  间：
 * 版  权：
 */
public class SignAddActivity extends XBase3Activity<ActivitySignAddBinding> implements View.OnClickListener {

    private String peoId, workID, yjBh, yjMc, renWu, areaCode, rwMs, pro, khl;
    private PeopleInfoEntity peopleListEntity;
    private ImageAddUtil imageAddImg, imageAddVideos;
    private int from = 1;
    private String mLongitude, mLatitude;
    private Uri photoUri;
    private File cameraPhoto;

    @Override
    protected ActivitySignAddBinding getViewBinding() {
        return ActivitySignAddBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        requestPermission(new String[]{Manifest.permission.ACCESS_COARSE_LOCATION, Manifest.permission.ACCESS_FINE_LOCATION});
        peoId = getIntent().getStringExtra("id");
        yjBh = getIntent().getStringExtra("yjztbh");
        khl = getIntent().getStringExtra("khl");
        peopleListEntity = (PeopleInfoEntity) getIntent().getSerializableExtra("info");
        if (peopleListEntity != null) {
            workID = UserInfoUtils.getUserInfo().getUserId();
            if (peopleListEntity.getYjztbhList() != null && peopleListEntity.getYjztbhList().size() > 0 && peopleListEntity.getYjztbhList().get(0) != null) {
                if (TextUtils.isEmpty(yjBh)) {
                    yjBh = peopleListEntity.getYjztbhList().get(0).getYjztbh();
                }
                yjMc = peopleListEntity.getYjztbhList().get(0).getRyyjzt().getName();
            }
            binding.tvName.setText(peopleListEntity.getXm());
            binding.tvWorkName.setText(UserInfoUtils.getUserInfo().getUserName());
        }
        StrUtils.getSignText(binding.tvImgInfo, binding.tvVideoInfo, getString(R.string.sign_zp_sm), getString(R.string.sign_video_sm));
        binding.tvSignType.setText(getString(R.string.daiqiandao));
        EventBus.getDefault().register(this);
        //任务过来
        renWu = getIntent().getStringExtra("tag");
        rwMs = getIntent().getStringExtra("rwMs");
        if (!TextUtils.isEmpty(rwMs)) {
            if (rwMs.equals("2") || rwMs.equals("3")) {
                //展示安保标签
                binding.tvTag.setText(getString(R.string.anbao));
                binding.tvTag.setVisibility(View.VISIBLE);
            }
        }
        if (!TextUtils.isEmpty(renWu)) {
            FactoryUtils.getUserData(this, peoId, result -> {
                workID = UserInfoUtils.getUserInfo().getUserId();
                if (result.getYjztbhList() != null && result.getYjztbhList().size() > 0 && result.getYjztbhList().get(0) != null) {
                    if (TextUtils.isEmpty(yjBh)) {
                        yjBh = result.getYjztbhList().get(0).getYjztbh();
                    }
                }
                if (result.getRyyjzt() != null) {
                    yjMc = result.getRyyjzt().getName();
                }
                binding.tvName.setText(result.getXm());
                binding.tvWorkName.setText(UserInfoUtils.getUserInfo().getUserName());
            });
        }
        //默认展示年月日时分秒-不选
        binding.tvSignTime.setText(XDateUtil.getCurrentDate(XDateUtil.dateFormatYMDHMS));

        if (!TextUtils.isEmpty(XShareCacheUtils.getInstance().getString("city"))) {
            binding.tvSignArea.setText(XShareCacheUtils.getInstance().getString("city"));
            mLongitude = XShareCacheUtils.getInstance().getString("lon");
            mLatitude = XShareCacheUtils.getInstance().getString("lat");
            areaCode = XShareCacheUtils.getInstance().getString("code");
        }
        location();
        //
        createFileMuLu();
    }

    @Override
    protected void initListener() {
        binding.btSure.setOnClickListener(this);
        binding.tvSignDate.setOnClickListener(this);
//        binding.tvSignTime.setOnClickListener(this);
//        binding.tvSignArea.setOnClickListener(this);
        binding.tvRemark.setOnClickListener(this);
    }

    @Override
    protected void getData() {

        imageAddImg = new ImageAddUtil(this, binding.gridImg);
        imageAddImg.setOnImageAddListener(() -> {
            PhotoUtil.openCameraAndToPermission(SignAddActivity.this, new PhotoUtil.OnOpenPermissionResultCallback() {
                @Override
                public void onRefuse() {
                    from = 0;
                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                }

                @Override
                public void onSuccess() {
                    from = 0;
                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                }
            });
        });
        imageAddVideos = new ImageAddUtil(this, binding.gridVideo);
        imageAddVideos.setOnImageAddListener(() -> {
            PhotoUtil.openVideoAndToPermission(SignAddActivity.this, new PhotoUtil.OnOpenPermissionResultCallback() {
                @Override
                public void onRefuse() {
                    from = 2;
                    requestPermission(new String[]{android.Manifest.permission.CAMERA, Manifest.permission.RECORD_AUDIO, android.Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                }

                @Override
                public void onSuccess() {
                    from = 2;
                    requestPermission(new String[]{android.Manifest.permission.CAMERA, Manifest.permission.RECORD_AUDIO, android.Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                }
            });
        });
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.bt_sure) {
            if (ButtonUtils.isFastClick()) {
                postData();
            }
        } else if (v.getId() == R.id.tv_sign_time) {
            //签到时间
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.tvSignTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_remark) {
            //备注
            Bundle bundle = new Bundle();
            bundle.putString("title", "备注");
            bundle.putString("hint", "请输入备注");
            bundle.putInt("tab", 1);
            XIntentUtil.redirectToNextActivity(this, InputInfoActivity.class, bundle);
        } else if (v.getId() == R.id.tv_sign_area) {
            //地址选择
            XIntentUtil.redirectToNextActivity(this, MapPickerActivity.class);
        }
    }

    /**
     * 创建文件
     */
    private void createFileMuLu() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
                && ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {
            cameraPhoto = new File(PhotoUtil.createPath(this));
            try {
                if (cameraPhoto.exists()) {
                    cameraPhoto.delete();
                }
                cameraPhoto.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            // 权限未授权，需要申请权限
            requestPermission(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        }
    }

    /**
     * 数据提交
     */
    private void postData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.SIGN_ADD);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        if (TextUtils.isEmpty(binding.tvSignArea.getText().toString())) {
            XToastUtil.showToast(this, "请选择签到地址");
            return;
        }
        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("xyrbh", peoId);
            objectMap.put("yjztbh", yjBh);
            objectMap.put("yjztmc", yjMc);
            objectMap.put("xyrxm", binding.tvName.getText().toString());
            objectMap.put("dqrBh", workID);
            objectMap.put("dqrXm", binding.tvWorkName.getText().toString());
            objectMap.put("lrsj", binding.tvSignTime.getText().toString());
            objectMap.put("dz", binding.tvSignArea.getText().toString());
            objectMap.put("lng", mLongitude);
            objectMap.put("lat", mLatitude);
            objectMap.put("xzqhdm", areaCode);
            objectMap.put("sjLy", "3");
            objectMap.put("bz", binding.tvRemark.getText().toString());
            objectMap.put("lx", "1");//0自主上传 1代上传
            //
            JSONArray jsonArray = new JSONArray();
            if (imageAddImg != null && imageAddImg.getPaths() != null && !imageAddImg.getPaths().isEmpty()) {
                for (int i = 0; i < imageAddImg.getPaths().size(); i++) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("lj", imageAddImg.getPaths().get(i).getSavePath());
                    jsonObject.put("mc", imageAddImg.getPaths().get(i).getOriginName());
                    jsonObject.put("hz", StrUtils.getFileType(imageAddImg.getPaths().get(i).getSavePath()));
                    jsonObject.put("dx", imageAddImg.getPaths().get(i).getFileSize());
                    jsonArray.put(jsonObject);
                }
            }
            if (jsonArray.length() <= 0) {
                XToastUtil.showToast(this, "请上传图片");
                return;
            }
            objectMap.put("qdZp", jsonArray);
            JSONArray jsonArraySP = new JSONArray();
            if (imageAddVideos != null && imageAddVideos.getPaths() != null && !imageAddVideos.getPaths().isEmpty()) {
                for (int i = 0; i < imageAddVideos.getPaths().size(); i++) {
                    JSONObject jsonObjectSP = new JSONObject();
                    jsonObjectSP.put("lj", imageAddVideos.getPaths().get(i).getSavePath());
                    jsonObjectSP.put("mc", imageAddVideos.getPaths().get(i).getOriginName());
                    jsonObjectSP.put("hz", StrUtils.getFileType(imageAddVideos.getPaths().get(i).getSavePath()));
                    jsonObjectSP.put("dx", imageAddVideos.getPaths().get(i).getFileSize());
                    jsonArraySP.put(jsonObjectSP);
                }
            }
            if (jsonArraySP.length() <= 0) {
                XToastUtil.showToast(this, "请上传签到视频");
                return;
            }
            objectMap.put("qdSp", jsonArraySP);
        } catch (JSONException e) {
        }
        binding.btSure.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
//        MdFactoryUtils.mdSave(ApiHostUtils.getHostUrl() + ApiUrlsUtils.SIGN_ADD, objectMap.toString(), StrUtils.getSbxxCj());
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), true, true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (!TextUtils.isEmpty(renWu) && renWu.equals("2")) {
                        TaskApprovalFactory.sendGiveUp(SignAddActivity.this, pro, code -> {
                            XToastUtil.showToast(SignAddActivity.this, getString(R.string.add_sucess));
                            EventBus.getDefault().post(new SignListEntity());
                            finish();
                        });
                    } else {
                        XToastUtil.showToast(SignAddActivity.this, getString(R.string.add_sucess));
                        EventBus.getDefault().post(new SignListEntity());
                        finish();
                    }
                } else {
                    XToastUtil.showToast(SignAddActivity.this, xResultData.getErrorInfo());
                    binding.btSure.setEnabled(true);
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSure.setEnabled(true);
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (from == 0) {
            createFileMuLu();
            if (cameraPhoto != null) {
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                photoUri = XFileUtil.getUriFromFile(this, cameraPhoto);
                intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                startActivityForResult(intent, 119);
            }
        } else if (from == 2) {
            startActivityForResult(new Intent(this, VideoRecordNoActivity.class), 211);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case 119:
                    //图片
                    try {
                        boolean isFinish = XFileUtil.bitmapToFilePath(SignAddActivity.this, BitmapFactory.decodeFile(cameraPhoto.getAbsolutePath()), photoUri, cameraPhoto);
                        if (isFinish) {
                            uploadFile(cameraPhoto.getAbsolutePath());
                        }
                    } catch (Exception e) {
                    }
                    break;
                case 211://录像返回
                    if (data != null) {
                        String mp4_path = data.getStringExtra("path");
                        if (!TextUtils.isEmpty(mp4_path)) {
                            long fileSize = XFileUtil.getFileSize(mp4_path);
                            long fileNewSize = fileSize / 1000000;
                            if (fileNewSize >= 20) {
                                DialogUtils.showVideoError(this, getString(R.string.tishi), getString(R.string.video_big_info), (code, id) -> {
                                    uploadFileVideo(mp4_path);
                                });
                            } else {
                                uploadFileVideo(mp4_path);
                            }
                        } else {
                            XToastUtil.showToast(this, "视频拍摄失败，请重新拍摄");
                        }
                    } else {
                        XToastUtil.showToast(this, "视频拍摄失败，请重新拍摄");
                    }
                    break;
            }
        }
    }

    /**
     * 定位初始化
     */
    private void location() {
        AMapLocationClient client;
        try {
            client = new AMapLocationClient(getApplicationContext());
        } catch (Exception e) {
            XToastUtil.showToast(this, "定位初始化失败");
            return;
        }
//        AMapLocationClient client = new AMapLocationClient(getApplicationContext());
        AMapLocationClientOption option = MapUtils.getDefaultOption();
        option.setMockEnable(true);
        client.setLocationOption(option);
        client.setLocationListener(aMapLocation -> {
            if (aMapLocation != null) {
                if (aMapLocation.getErrorCode() == 0) {
                    Log.i("sss", "aMapLocation: " + aMapLocation.toString());
                    if (!TextUtils.isEmpty(aMapLocation.getAddress())) {
                        binding.tvSignArea.setText(aMapLocation.getAddress());
                        mLongitude = aMapLocation.getLongitude() + "";
                        mLatitude = aMapLocation.getLatitude() + "";
                        areaCode = aMapLocation.getAdCode();
                        XShareCacheUtils.getInstance().putString("city", aMapLocation.getAddress());
                        XShareCacheUtils.getInstance().putString("lon", aMapLocation.getLongitude() + "");
                        XShareCacheUtils.getInstance().putString("lat", aMapLocation.getLatitude() + "");
                    } else {
                        gpsLocation();
                    }
                    client.stopLocation();
                } else {
                    binding.tvSignArea.setHint("定位失败,错误代码-" + aMapLocation.getErrorCode());
                    if (!TextUtils.isEmpty(XShareCacheUtils.getInstance().getString("city"))) {
                        binding.tvSignArea.setText(XShareCacheUtils.getInstance().getString("city"));
                        mLongitude = XShareCacheUtils.getInstance().getString("lon");
                        mLatitude = XShareCacheUtils.getInstance().getString("lat");
                        areaCode = XShareCacheUtils.getInstance().getString("code");
                    } else {
                        gpsLocation();
                    }
                    MdFactoryUtils.mdSave("工作人员端签到定位失败,错误代码:" + aMapLocation.getErrorCode(),
                            aMapLocation.getErrorInfo() + "--" +
                                    aMapLocation.getLocationDetail(), StrUtils.getSbxxCj() + "--" + OtherUtils.checkVPN());
                }
            } else {
                binding.tvSignArea.setHint("定位失败,请检查是否打开网络或同意定位权限，重新尝试登录");
                if (!TextUtils.isEmpty(XShareCacheUtils.getInstance().getString("city"))) {
                    binding.tvSignArea.setText(XShareCacheUtils.getInstance().getString("city"));
                    mLongitude = XShareCacheUtils.getInstance().getString("lon");
                    mLatitude = XShareCacheUtils.getInstance().getString("lat");
                    areaCode = XShareCacheUtils.getInstance().getString("code");
                } else {
                    gpsLocation();
                }
                MdFactoryUtils.mdSave("工作人员端签到定位失败,错误代码:" + aMapLocation.getErrorCode(),
                        aMapLocation.getErrorInfo() + "--" +
                                aMapLocation.getLocationDetail(), StrUtils.getSbxxCj() + "--" + OtherUtils.checkVPN());
            }
        });
        client.startLocation();


    }

    //上传
    private void uploadFile(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        imageAddImg.addImage(xResultData.getData().get(0));
                        imageAddImg.notifyData();
                    }
                } else {
                    XToastUtil.showToast(SignAddActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    //上传
    private void uploadFileVideo(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        imageAddVideos.addImage(xResultData.getData().get(0));
                        imageAddVideos.notifyData();
                    }
                } else {
                    XToastUtil.showToast(SignAddActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(InputEntity item) {
        if (item != null) {
            if (item.getTab() == 1) {
                binding.tvRemark.setText(item.getInfo());
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(MapChoiceEntity item) {
        if (item != null) {
            binding.tvSignArea.setText(item.getAddress());
            mLatitude = item.getLat() + "";
            mLongitude = item.getLon() + "";
            areaCode = item.getAreaCode();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        for (int i = 0; i < permissions.length; i++) {
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED || ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED || ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED || ActivityCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                XPermissionUtil.initPermission(this, new String[]{android.Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.CAMERA});
            }
        }
    }

    /**
     * 采用Gps定位
     */
    private void gpsLocation() {
        try {
            XLocationMagUtil.getInstance().startSingleLocation(new XLocationMagUtil.LocationManagerListener() {
                @Override
                public void onSuccessLocationListener(LocationManager manager, Location location, double longitude,
                                                      double latitude, String locationAddr, double longitudeGd, double latitudeGd, String locationAddrGd) {
                    if (!TextUtils.isEmpty(locationAddr)) {
                        Log.i("sss", "onSuccessLocationListener: " + locationAddr);
                        binding.tvSignArea.setText(locationAddr);
                        mLongitude = longitude + "";
                        mLatitude = latitude + "";
                        XShareCacheUtils.getInstance().putString("city", locationAddr);
                    } else {
                        binding.tvSignArea.setText(locationAddrGd);
                        mLongitude = longitudeGd + "";
                        mLatitude = latitudeGd + "";
                        XShareCacheUtils.getInstance().putString("city", locationAddrGd);
                    }
                    XShareCacheUtils.getInstance().putString("lon", mLongitude + "");
                    XShareCacheUtils.getInstance().putString("lat", mLatitude + "");
                }

                @Override
                public void onFailureLocationListener(String failureText) {
                    XToastUtil.showToast(SignAddActivity.this, failureText);
                }
            });
        } catch (Exception e) {
            MdFactoryUtils.mdSave("工作人员端签到定位失败,错误", e.getMessage(), StrUtils.getSbxxCj() + "--" + OtherUtils.checkVPN());
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
