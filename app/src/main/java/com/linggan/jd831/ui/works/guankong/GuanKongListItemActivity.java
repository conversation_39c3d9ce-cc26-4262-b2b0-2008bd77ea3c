package com.linggan.jd831.ui.works.guankong;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData;
import com.lgfzd.base.utils.XAppUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.adapter.GuanKongItemListHolder;
import com.linggan.jd831.adapter.GuanKongSJGKLItemListHolder;
import com.linggan.jd831.bean.GuanKongListItemEntity;
import com.linggan.jd831.bean.GuanKongListItemSJGKLEntity;
import com.linggan.jd831.bean.XuanJiaoListEntity;
import com.linggan.jd831.constants.CompletionRateType;
import com.linggan.jd831.databinding.ActivityGuanKongItemListBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.BaseStringSelectDialog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 类  名：宣教活动列表
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class GuanKongListItemActivity extends XBaseActivity<ActivityGuanKongItemListBinding> implements XRefreshLayout.PullLoadMoreListener, EditText.OnEditorActionListener {

    private int page = 1, totalPage = 0;
    private boolean isShow = true, icCheck = true;
    private String keywordsXm, name;
    private int selectedWcqk = -1;
    private CompletionRateType type;
    private Set<Integer> cachedUniqueWcqkSet;
    private Map<Integer, String> cachedWcqkToWcqkStrMap;

    @Override
    protected ActivityGuanKongItemListBinding getViewBinding() {
        return ActivityGuanKongItemListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        // 将上面这一行修改为GridLayoutManager,一排两个
        binding.etSearch.setHint("请输入人员姓名");
//        code = getIntent().getStringExtra("code");
        name = getIntent().getStringExtra("name");

        if (name == null) {
            XToastUtil.showToast(this, "服务器错误");
            return;
        }
        type = CompletionRateType.fromName(name);

        binding.recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
        if (type == CompletionRateType.COMMUNITY_REHAB_CONTROL_RATE) {
            binding.recycler.getAdapter().bindHolder(new GuanKongSJGKLItemListHolder(type));
        } else {
            binding.recycler.getAdapter().bindHolder(new GuanKongItemListHolder(type));
        }
        binding.recycler.setOnPullLoadMoreListener(this);
        EventBus.getDefault().register(this);
        if (!TextUtils.isEmpty(name)) {
            setTitle(name);
        }
    }


    private void initializeCache() {
        cachedUniqueWcqkSet = new HashSet<>();

        switch (type) {
            case DRUG_USER_CHECK_RATE: // 完成状态
            case DRUG_USER_ASSESSMENT_RATE: // 完成状态
            case DRUG_USER_TEST_RATE: // 完成状态
            case COMMUNITY_REHAB_TEST_RATE: // 完成状态
                cachedWcqkToWcqkStrMap = new HashMap<Integer, String>() {{
                    put(0, "未完成");
                    put(1, "已完成");
                }};
                break;
            case AID_APPLICATION_RATE: // 提交状态
                cachedWcqkToWcqkStrMap = new HashMap<Integer, String>() {{
//                    处理方式（0待关爱，1已完成关爱，2无法关爱，3已提交资料供审批，4已制定帮扶计划，5无法办理）
                    put(0, "待关爱");
                    put(1, "已完成关爱");
                    put(2, "无法关爱");
                    put(3, "已提交资料供审批");
                    put(4, "已制定帮扶计划");
//                    put(5, "无法办理");
                }};
                break;
            case COMMUNITY_REHAB_CONTROL_RATE: // 提交状态
                cachedWcqkToWcqkStrMap = new HashMap<Integer, String>() {{
                    put(1, "违反协议");
                    put(2, "超期未报到");
                    put(3, "脱失脱管");
                    put(4, "正常管控");
                }};
                break;
            default:
                cachedWcqkToWcqkStrMap = new HashMap<>();
                break;
        }

        cachedUniqueWcqkSet.addAll(cachedWcqkToWcqkStrMap.keySet());
    }


    @Override
    protected void initListener() {
        binding.etSearch.setOnEditorActionListener(this);
        binding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                String searchText = s.toString();
                keywordsXm = searchText;
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });
        if (Objects.equals(type.getName(), "信息完整率")) {
            binding.selectContainer.setVisibility(View.GONE);
            return;
        }

        binding.selectContainer.setOnClickListener(v -> {
            if (cachedUniqueWcqkSet == null || cachedWcqkToWcqkStrMap == null) {
                initializeCache();
            }

            ArrayList<String> uniqueWcqkStrList = new ArrayList<>();
            uniqueWcqkStrList.add("全部");
            uniqueWcqkStrList.addAll(cachedWcqkToWcqkStrMap.values());

            BaseStringSelectDialog dialog = new BaseStringSelectDialog(this, "确定", uniqueWcqkStrList);
            try {
                if (selectedWcqk != -1) {
                    for (Map.Entry<Integer, String> entry : cachedWcqkToWcqkStrMap.entrySet()) {
                        if (entry.getKey() == selectedWcqk) {
                            dialog.setDefaultSelectedIndex(uniqueWcqkStrList.indexOf(entry.getValue()));
                            break;
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            dialog.setOnClickDataListener(() -> {
                String selectedValue = dialog.getData();
                binding.tvSelect.setText(selectedValue);

                if (selectedValue == null || selectedValue.isEmpty()) {
                    binding.tvSelect.setText("全部");
                    selectedWcqk = -1;
                }
                if (selectedValue.equals("全部")) {
                    selectedWcqk = -1;
                } else {
                    for (Map.Entry<Integer, String> entry : cachedWcqkToWcqkStrMap.entrySet()) {
                        if (entry.getValue().equals(selectedValue)) {
                            selectedWcqk = entry.getKey();
                            break;
                        }
                    }
                }
                page = 1;
                isShow = true;
                binding.recycler.triggerRefresh();
                getData();
            });
            dialog.show();
        });

    }

    public <T> XResultData<XResultPageData<T>> parseJson(String result, String code) {
        Gson gson = new Gson();
        Type type;
        if ("社戒社康管控率".equals(code)) {
            type = TypeToken.getParameterized(XResultData.class,
                    TypeToken.getParameterized(XResultPageData.class, GuanKongListItemSJGKLEntity.class).getType()).getType();
        } else {
            type = TypeToken.getParameterized(XResultData.class,
                    TypeToken.getParameterized(XResultPageData.class, GuanKongListItemEntity.class).getType()).getType();
        }

        return gson.fromJson(result, type);
    }

    private void handleData(XResultData<?> xResultData, int page) {
        if (xResultData.getData() != null) {
            XResultPageData<?> pageData = (XResultPageData<?>) xResultData.getData();
            int totalPage = pageData.getTotalPage();
            List<?> records = pageData.getRecords();

            if (records != null && !records.isEmpty()) {
                binding.tvNoData.setVisibility(View.GONE);
                if (page == 1) {
                    binding.recycler.getAdapter().setData(0, records);
                } else {
                    binding.recycler.getAdapter().addDataAll(0, records);
                }
            } else {
                handleEmptyData(page);
            }
        } else {
            handleEmptyData(page);
        }
    }

    private void handleEmptyData(int page) {
        if (page == 1) {
            binding.tvNoData.setVisibility(View.VISIBLE);
            binding.recycler.getAdapter().setData(0, new ArrayList<>());
        }
    }

    @Override
    protected void getData() {

        String url = "";
        try {
            url = type.getUrl();
        } catch (Exception e) {
            XToastUtil.showToast(this, "服务器错误");
            e.printStackTrace();
            return;
        }

        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + url);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("page", page);
        objectMap.put("rows", XConstantUtils.PAGE_SIZE);

        Calendar cal = Calendar.getInstance();
        int currentYear = cal.get(Calendar.YEAR);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        cal.set(currentYear, Calendar.JANUARY, 1);
        String firstDay = sdf.format(cal.getTime());
        cal.set(currentYear, Calendar.DECEMBER, 31);
        String lastDay = sdf.format(cal.getTime());
        objectMap.put("startTime", firstDay);
        objectMap.put("endTime", lastDay);

        if (selectedWcqk != -1) {
            if (Objects.equals(type.getName(), "社戒社康管控率")) {
                objectMap.put("gkzt", selectedWcqk);
            } else if (Objects.equals(type.getName(), "救助申请办理率")) {
                objectMap.put("clfs", selectedWcqk);
            } else {
                objectMap.put("wcqk", selectedWcqk);
            }
        }

        if (keywordsXm != null) {
            final Set<String> SPECIAL_CODES = Set.of("社会面吸毒人员评估率", "信息完整率", "救助申请办理率");
            String key = SPECIAL_CODES.contains(type.getName()) ? "xm" : "keywordsXm";
            objectMap.put(key, keywordsXm);
        }

        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), isShow, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData test = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (test.getStatus() == 0) {
                    handleData(parseJson(result, type.getName()), page);
                } else {
                    XToastUtil.showToast(GuanKongListItemActivity.this, test.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                binding.recycler.setPullLoadMoreCompleted();
                icCheck = true;
            }
        });
    }


    @Override
    public void onRefresh() {
        page = 1;
        isShow = false;
        getData();
    }

    @Override
    public boolean onLoadMore() {
        if (page < totalPage) {
            page++;
            isShow = false;
            getData();
            return true;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(XuanJiaoListEntity event) {
        if (event != null) {
            page = 1;
            isShow = false;
            getData();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    @Override
    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
        //以下方法防止两次发送请求
        if (icCheck) {
            page = 1;
            isShow = true;
            if (v.getText() != null) {
                keywordsXm = v.getText().toString();
            }
            getData();
            icCheck = false;
        }
        XAppUtil.closeSoftInput(this);
        return false;
    }
}
