package com.linggan.jd831.ui.works.transport;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.RelativeLayout;
import android.widget.Toast;

import com.github.barteksc.pdfviewer.scroll.DefaultScrollHandle;
import com.lgfzd.base.base.XBaseActivity;
import com.linggan.jd831.R;
import com.linggan.jd831.databinding.ActivityRegulationDetailBinding;
import com.linggan.jd831.ui.works.transport.event.TransportStatusEvent;
import com.linggan.jd831.utils.DialogUtils;
import com.tencent.smtt.sdk.TbsReaderView;
import com.tencent.smtt.sdk.TbsReaderView.ReaderCallback;

import org.greenrobot.eventbus.EventBus;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class RegulationDetailActivity2 extends XBaseActivity<ActivityRegulationDetailBinding> implements ReaderCallback {

    private boolean hasReachedBottom = false;
    private static final String EXTRA_URL = "extra_url";
    private static final String EXTRA_TITLE = "extra_title";
    private static final String EXTRA_SHOW_CONFIRM = "extra_show_confirm";
    private static final String EXTRA_TRANSPORT_ID = "extra_transport_id";
    private static final long MIN_READ_TIME = 3000; // 最小加载等待时间3秒
    private static final int READER_CALLBACK_LOADED = 1;
    private static final int READER_CALLBACK_LOAD_FAILED = 2;
    private static final int READER_CALLBACK_RENDERED = 3;
    private static final int READER_CALLBACK_RENDER_FAILED = 4;

    private String transportId;
    private TbsReaderView mTbsReaderView;
    private boolean isUsingTbsReader = false;
    private android.app.ProgressDialog mLoadingDialog;

    @Override
    protected ActivityRegulationDetailBinding getViewBinding() {
        return ActivityRegulationDetailBinding.inflate(getLayoutInflater());
    }

    public static void start(Context context, String title, String url, boolean showConfirm, String transportId) {
        Intent intent = new Intent(context, RegulationDetailActivity2.class);
        intent.putExtra(EXTRA_TITLE, title);
        intent.putExtra(EXTRA_URL, url);
        intent.putExtra(EXTRA_SHOW_CONFIRM, showConfirm);
        intent.putExtra(EXTRA_TRANSPORT_ID, transportId);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        String title = getIntent().getStringExtra(EXTRA_TITLE);
        String url = getIntent().getStringExtra(EXTRA_URL);
        boolean showConfirm = getIntent().getBooleanExtra(EXTRA_SHOW_CONFIRM, false);
        transportId = getIntent().getStringExtra(EXTRA_TRANSPORT_ID);

        initViews(title, showConfirm);

        if (url != null) {
            if (url.toLowerCase().endsWith(".pdf")) {
                binding.pdfView.setVisibility(View.VISIBLE);
                downloadAndOpenPdf(url);
            } else if (url.toLowerCase().endsWith(".docx")) {
                // 对于docx文件使用在线预览
                String previewUrl = "https://view.officeapps.live.com/op/view.aspx?src=" + url;
                binding.webView.setVisibility(View.VISIBLE);
                setupWebView(showConfirm);
                loadUrl(previewUrl);
            } else {
                binding.webView.setVisibility(View.VISIBLE);
                setupWebView(showConfirm);
                loadUrl(url);
            }
        }
    }

    private void initViews(String title, boolean showConfirm) {
        setTitle(title);
//        binding.base.tvRight.setText("滚动底部");
        binding.base.tvRight.setOnClickListener(v -> {
            if (isUsingTbsReader) {
                // 对TBS阅读器，直接标记为已读
                hasReachedBottom = true;
                enableConfirmButton();
            } else {
                binding.webView.evaluateJavascript("window.scrollTo(0, document.body.scrollHeight)", null);
            }
            binding.btnConfirm.setEnabled(true);
        });

        binding.btnConfirm.setVisibility(showConfirm ? View.VISIBLE : View.GONE);
        if (showConfirm) {
            binding.btnConfirm.setEnabled(false);
            binding.btnConfirm.setOnClickListener(v -> {
                if (hasReachedBottom) {
                    DialogUtils.showReadDialog(this, new DialogUtils.OnResult() {
                        @Override
                        public void onSuccess(String code, String id) {
                            if ("1".equals(code)) {
                                startNextPage();
                            } else {
                                EventBus.getDefault().post(new TransportStatusEvent(
                                        transportId,
                                        TransportStatusEvent.STATUS_PENDING
                                ));
                            }
                            finish();
                        }
                    });
                }
            });
        }

        // 调整容器布局
        RelativeLayout.LayoutParams containerParams = (RelativeLayout.LayoutParams) binding.container.getLayoutParams();
        if (!showConfirm) {
            containerParams.removeRule(RelativeLayout.ABOVE);
            containerParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
            binding.container.setLayoutParams(containerParams);
        }
    }

    private void startNextPage() {
        if (transportId != null) {
            TransportInfoActivity.start(this, transportId, null, false);
        } else {
            TransportInfoActivity.startForNew(this);
        }
        finish();
    }

    private void setupWebView(boolean showConfirm) {
        WebSettings settings = binding.webView.getSettings();
        settings.setJavaScriptEnabled(true);
        settings.setSupportZoom(true);
        settings.setBuiltInZoomControls(true);
        settings.setDisplayZoomControls(false);
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);
        settings.setDomStorageEnabled(true);
        settings.setCacheMode(WebSettings.LOAD_DEFAULT);

        if (showConfirm) {
            binding.webView.addJavascriptInterface(new WebAppInterface(), "android");

            binding.webView.setWebViewClient(new WebViewClient() {
                @Override
                public void onPageFinished(WebView view, String url) {
                    super.onPageFinished(view, url);
                    injectScrollListener();
                }
            });

            binding.webView.setOnScrollChangeListener((v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
                // 移除原有的滚动检测逻辑，统一使用JavaScript检测
            });
        } else {
            binding.webView.setWebViewClient(new WebViewClient());
        }
    }

    private void injectScrollListener() {
        String javascript = "javascript:(function() { " +
                "var lastKnownScrollPosition = 0;" +
                "var ticking = false;" +
                "window.onscroll = function() {" +
                "   if (!ticking) {" +
                "       window.requestAnimationFrame(function() {" +
                "           var scrollHeight = Math.max(" +
                "               document.documentElement.scrollHeight," +
                "               document.body.scrollHeight" +
                "           );" +
                "           var scrollTop = window.pageYOffset ||" +
                "               document.documentElement.scrollTop ||" +
                "               document.body.scrollTop;" +
                "           var clientHeight = document.documentElement.clientHeight;" +
                "           var scrolledToBottom = scrollHeight - (scrollTop + clientHeight) <= 50;" + // 增加底部判定区域
                "           if(scrolledToBottom) {" +
                "               window.android.onScrollToBottom();" +
                "           }" +
                "           ticking = false;" +
                "       });" +
                "       ticking = true;" +
                "   }" +
                "};" +
                "})()";
        binding.webView.loadUrl(javascript);
    }

    private void downloadAndOpenPdf(String url) {
        showLoading();
        new Thread(() -> {
            try {
                OkHttpClient client = new OkHttpClient();
                Request request = new Request.Builder().url(url).build();
                Response response = client.newCall(request).execute();

                if (response.body() != null) {
                    File file = new File(getExternalCacheDir(), "temp.pdf");
                    FileOutputStream fos = new FileOutputStream(file);
                    InputStream is = response.body().byteStream();

                    byte[] buffer = new byte[2048];
                    int len;
                    while ((len = is.read(buffer)) != -1) {
                        fos.write(buffer, 0, len);
                    }

                    fos.close();
                    is.close();

                    runOnUiThread(() -> {
                        hideLoading();
                        openPdfFile(file);
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    hideLoading();
                    Toast.makeText(this, "文档加载失败", Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }

    private void openPdfFile(File file) {
        binding.pdfView.fromFile(file)
                .defaultPage(0)
                .enableSwipe(true)
                .swipeHorizontal(false)
                .enableDoubletap(true)
                .enableAnnotationRendering(true)
                .scrollHandle(new DefaultScrollHandle(this))
                .spacing(10)
                .onLoad(nbPages -> {
                    // PDF加载完成
                    Toast.makeText(this, "PDF加载完成，共" + nbPages + "页", Toast.LENGTH_SHORT).show();

                    // 添加滚动监听
                    binding.pdfView.setOnScrollChangeListener((v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
                        // 获取PDF View的高度信息
                        int viewHeight = binding.pdfView.getHeight();
                        int totalHeight = binding.pdfView.getHeight() * binding.pdfView.getPageCount();

                        // 打印调试信息
                        Toast.makeText(this,
                                "scrollY: " + scrollY +
                                        ", viewHeight: " + viewHeight +
                                        ", totalHeight: " + totalHeight,
                                Toast.LENGTH_SHORT).show();

                        // 如果是最后一页，并且滚动到接近底部
                        if (binding.pdfView.getCurrentPage() == binding.pdfView.getPageCount() - 1) {
                            // 如果滚动位置接近文档底部
                            if ((scrollY + viewHeight) >= (totalHeight - 100)) {  // 100px的容差
                                hasReachedBottom = true;
                                enableConfirmButton();
                            }
                        }
                    });
                })
                .onPageChange((page, pageCount) -> {
                    Toast.makeText(this,
                            "当前第" + (page + 1) + "页，共" + pageCount + "页",
                            Toast.LENGTH_SHORT).show();
                })
                .load();
    }

    @Override
    public void onCallBackAction(Integer integer, Object o, Object o1) {
        if (integer == READER_CALLBACK_LOADED) {
            new Handler().postDelayed(() -> {
                runOnUiThread(() -> {
                    hasReachedBottom = true;
                    enableConfirmButton();
                });
            }, MIN_READ_TIME);
        }
    }

    @Override
    protected void onDestroy() {
        if (mTbsReaderView != null) {
            mTbsReaderView.onStop();
            binding.container.removeView(mTbsReaderView);
        }
        if (mLoadingDialog != null) {
            mLoadingDialog.dismiss();
            mLoadingDialog = null;
        }
        super.onDestroy();
    }

    private void loadUrl(String url) {
        if (!isUsingTbsReader) {
            binding.webView.loadUrl(url);
        }
    }

    private void enableConfirmButton() {
        runOnUiThread(() -> {
            binding.btnConfirm.setEnabled(true);
            binding.btnConfirm.setText("确认");
            binding.btnConfirm.setBackgroundResource(R.drawable.bg_button_enabled);
        });
    }

    @Override
    protected void initView() {
    }

    @Override
    protected void initListener() {
    }

    @Override
    protected void getData() {
    }

    // 供JS��用的接口
    public class WebAppInterface {
        @android.webkit.JavascriptInterface
        public void onScrollToBottom() {
            // 添加二次验证
            runOnUiThread(() -> {
                if (!hasReachedBottom) {
                    // 再次验证是否真的到达底部
                    binding.webView.post(() -> {
                        binding.webView.evaluateJavascript(
                                "(function() {" +
                                        "   var scrollHeight = Math.max(" +
                                        "       document.documentElement.scrollHeight," +
                                        "       document.body.scrollHeight" +
                                        "   );" +
                                        "   var scrollTop = window.pageYOffset ||" +
                                        "       document.documentElement.scrollTop ||" +
                                        "       document.body.scrollTop;" +
                                        "   var clientHeight = document.documentElement.clientHeight;" +
                                        "   return scrollHeight - (scrollTop + clientHeight) <= 50;" +
                                        "})()",
                                result -> {
                                    if ("true".equals(result)) {
                                        hasReachedBottom = true;
                                        enableConfirmButton();
                                    }
                                }
                        );
                    });
                }
            });
        }
    }

    private void showLoading() {
        if (mLoadingDialog == null) {
            mLoadingDialog = new android.app.ProgressDialog(this);
            mLoadingDialog.setMessage("加载中...");
            mLoadingDialog.setCancelable(false);
        }
        if (!mLoadingDialog.isShowing()) {
            mLoadingDialog.show();
        }
    }

    private void hideLoading() {
        if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
            mLoadingDialog.dismiss();
        }
    }
}