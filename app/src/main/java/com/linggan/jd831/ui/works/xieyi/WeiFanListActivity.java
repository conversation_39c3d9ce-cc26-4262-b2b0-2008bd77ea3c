package com.linggan.jd831.ui.works.xieyi;

import android.os.Bundle;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.linggan.jd831.utils.XHttpUtils;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.adapter.AllCodeTopHolder;
import com.linggan.jd831.adapter.WeiFanXieYiListHolder;
import com.linggan.jd831.databinding.ActivityJiafangListBinding;
import com.linggan.jd831.bean.PeopleInfoEntity;
import com.linggan.jd831.bean.WeiFanListEntity;
import com.linggan.jd831.bean.WeiFanXieYiInfoEntity;
import com.lgfzd.base.XConstantUtils;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 类  名：违反协议
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class WeiFanListActivity extends XBaseActivity<ActivityJiafangListBinding> implements XRefreshLayout.PullLoadMoreListener {

    private String peoId;
    private int page = 1, totalPage = 0;
    private PeopleInfoEntity peopleListEntity;

    @Override
    protected ActivityJiafangListBinding getViewBinding() {
        return ActivityJiafangListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        peoId = getIntent().getStringExtra("id");
        binding.recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
        binding.recycler.getAdapter().bindHolder(new AllCodeTopHolder());
        binding.recycler.getAdapter().bindHolder(new WeiFanXieYiListHolder(peoId));
        binding.recycler.setOnPullLoadMoreListener(this);
        peopleListEntity = (PeopleInfoEntity) getIntent().getSerializableExtra("info");
        if (peopleListEntity != null) {
            //设置基础信息
            binding.recycler.getAdapter().setData(0, peopleListEntity);
        }
        EventBus.getDefault().register(this);
    }

    @Override
    protected void initListener() {
        binding.fab.setOnClickListener(v -> {
            if (ButtonUtils.isFastClick()) {
                Bundle bundle = new Bundle();
                bundle.putString("id", peoId);
                bundle.putSerializable("info", peopleListEntity);
                XIntentUtil.redirectToNextActivity(this, WeiFanAddActivity.class, bundle);
            }
        });
    }

    @Override
    protected void getData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.WEI_FAN_XIE_YI_LIST);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("xyrbh", peoId);
        objectMap.put("page", page);
        objectMap.put("rows", XConstantUtils.PAGE_SIZE);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<XResultPageData<WeiFanListEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XResultPageData<WeiFanListEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        totalPage = xResultData.getData().getTotalPage();

                        if (xResultData.getData().getRecords() != null && xResultData.getData().getRecords().size() > 0) {
                            binding.tvNoData.setVisibility(View.GONE);
                            if (page == 1) {
                                binding.recycler.getAdapter().setData(1, xResultData.getData().getRecords());
                            } else {
                                binding.recycler.getAdapter().addDataAll(1, xResultData.getData().getRecords());
                            }
                        } else {
                            if (page == 1) {
                                binding.tvNoData.setVisibility(View.VISIBLE);
                                binding.recycler.getAdapter().setData(1, new ArrayList());
                            }
                        }
                    }
                } else {
                    XToastUtil.showToast(WeiFanListActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                binding.recycler.setPullLoadMoreCompleted();
            }
        });
    }

    @Override
    public void onRefresh() {
        page = 1;
        getData();
    }

    @Override
    public boolean onLoadMore() {
        if (page < totalPage) {
            page++;
            getData();
            return true;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(WeiFanXieYiInfoEntity event) {
        if (event != null) {
            page = 1;
            getData();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
