package com.linggan.jd831.ui.works.sewage.sampledevice.entity;

import com.linggan.jd831.bean.YbbhZuEntity;
import com.linggan.jd831.bean.BottleEntity;

import java.io.Serializable;
import java.util.List;

/**
 * 类  名：字段数据回传
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/11/30 9:30
 * 版  权：凌感科技
 */
public class BottleEventEntity implements Serializable {

    private int from;
    private List<BottleEntity> BottleEntityList;

    private YbbhZuEntity ybbhZuEntity;

    public List<BottleEntity> getBottleEntityList() {
        return BottleEntityList;
    }

    public void setBottleEntityList(List<BottleEntity> BottleEntityList) {
        this.BottleEntityList = BottleEntityList;
    }
}
