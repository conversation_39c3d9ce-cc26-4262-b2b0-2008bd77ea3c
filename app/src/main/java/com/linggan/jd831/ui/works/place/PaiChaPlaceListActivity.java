package com.linggan.jd831.ui.works.place;

import androidx.fragment.app.Fragment;

import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.view.XTabFragmentPagerAdapter;
import com.linggan.jd831.databinding.ActivityBaseTabListBinding;

import java.util.ArrayList;
import java.util.List;

/**
 * 类  名： 排查场所-记录
 * 描   述：
 * 作  者：zxb
 * 时  间：
 * 版  权：LGKJ
 */
public class PaiChaPlaceListActivity extends XBaseActivity<ActivityBaseTabListBinding> {

    @Override
    protected ActivityBaseTabListBinding getViewBinding() {
        return ActivityBaseTabListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
    }

    @Override
    protected void initListener() {
    }

    @Override
    protected void getData() {
        List<Fragment> fragments = new ArrayList<>();
        fragments.add(PlacePerListFragment.newInstance());
        fragments.add(PlaceAllListFragment.newInstance());
        List<String> list = new ArrayList<>();
        list.add("个人");
        list.add("全部");
        binding.viewPager.setAdapter(new XTabFragmentPagerAdapter(getSupportFragmentManager(), fragments, list));
        binding.tabLayout.setupWithViewPager(binding.viewPager);
    }
}

