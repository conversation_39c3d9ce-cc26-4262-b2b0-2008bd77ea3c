package com.linggan.jd831.ui.works.car;

import android.text.TextUtils;
import android.view.View;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.KaKouListEntity;
import com.linggan.jd831.bean.LoginEntity;
import com.linggan.jd831.databinding.ActivityKakouAddBinding;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.AreaPickerView3Dialog;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

/**
 * 类  名：检测卡口新增
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class KaKouAddActivity extends XBaseActivity<ActivityKakouAddBinding> implements View.OnClickListener {

    private String xqqh, xqmc;

    @Override
    protected ActivityKakouAddBinding getViewBinding() {
        return ActivityKakouAddBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        xqqh = getIntent().getStringExtra("xqqh");
        xqmc = getIntent().getStringExtra("xqmc");
        if (!TextUtils.isEmpty(xqqh)) {//默认展示用户在检测上传页面选择的地区,且不可编辑
            binding.tvArea.setText(xqmc);
            binding.tvArea.setEnabled(false);
        }
    }

    @Override
    protected void initListener() {
        binding.btSure.setOnClickListener(this);
        binding.tvArea.setOnClickListener(this);
    }

    @Override
    protected void getData() {

    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_area) {
            //行政区域
            if (ButtonUtils.isFastClick()) {
                AreaPickerView3Dialog areaPickerView = new AreaPickerView3Dialog(this, UserInfoUtils.getUserInfo().getYhXzqhdm(), "1");
                areaPickerView.show();
                areaPickerView.setAreaPickerViewCallback((areaName, ids) -> {
                    xqqh = ids.get(ids.size() - 1);
                    String areas = StrUtils.listToStringText(areaName);
                    binding.tvArea.setText(areas);
                });
            }
        } else if (v.getId() == R.id.bt_sure) {
            if (ButtonUtils.isFastClick()) {
                postData();
            }
        }
    }

    /**
     * 数据提交
     */
    private void postData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.CAR_KK_ADD);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        if (TextUtils.isEmpty(binding.tvArea.getText().toString())) {
            XToastUtil.showToast(this, "请选择卡口所在地区");
            return;
        }
        if (TextUtils.isEmpty(binding.etKkmc.getText().toString())) {
            XToastUtil.showToast(this, "请输入卡口名称");
            return;
        }
        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("kkXzqh", xqqh);
            objectMap.put("kkMc", binding.etKkmc.getText().toString());
            objectMap.put("sj_ly", "3");
        } catch (JSONException e) {
        }
        binding.btSure.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJsonNoCan(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(KaKouAddActivity.this, "新增成功");
                    EventBus.getDefault().post(new KaKouListEntity());
                    finish();
                } else {
                    XToastUtil.showToast(KaKouAddActivity.this, xResultData.getErrorInfo());
                    binding.btSure.setEnabled(true);
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSure.setEnabled(true);
            }

            @Override
            public void onFinished() {
            }
        });
    }


}