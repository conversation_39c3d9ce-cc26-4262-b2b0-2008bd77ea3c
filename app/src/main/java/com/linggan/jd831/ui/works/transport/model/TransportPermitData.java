package com.linggan.jd831.ui.works.transport.model;

import java.io.Serializable;
import java.util.List;

/**
 * 运输许可证响应实体类
 */

/**
 * 运输许可证详细数据实体类
 */
public class TransportPermitData implements Serializable {
    private static final long serialVersionUID = 1L;

    private double bh;                // 编号
    private String fhdw;             // 发货单位
    private String fzjg;             // 发证机关
    private String shdw;             // 收货单位
    private String sqdw;             // 申请单位
    private TransportLicense xkz;     // 许可证信息
    private String ysdw;             // 运输单位
    private String yslxdh;           // 运输联系电话
    private String yssl;             // 运输数量
    private String yswp;             // 运输物品
    private Long yxcs;             // 有效次数

    private Long ysycs;             // 已使用次数
    private String yxjsrq;           // 有效结束日期
    private String yxksrq;           // 有效开始日期
    private String zsbh;             // 证书编号
    private List<RouteCity> balx; // 运输路线城市列表

    public Long getYsycs() {
        return ysycs;
    }

    public void setYsycs(Long ysycs) {
        this.ysycs = ysycs;
    }

    public void setYxcs(Long yxcs) {
        this.yxcs = yxcs;
    }

    public double getBh() {
        return bh;
    }

    public void setBh(double bh) {
        this.bh = bh;
    }

    public String getFhdw() {
        return fhdw;
    }

    public void setFhdw(String fhdw) {
        this.fhdw = fhdw;
    }

    public String getFzjg() {
        return fzjg;
    }

    public void setFzjg(String fzjg) {
        this.fzjg = fzjg;
    }

    public String getShdw() {
        return shdw;
    }

    public void setShdw(String shdw) {
        this.shdw = shdw;
    }

    public String getSqdw() {
        return sqdw;
    }

    public void setSqdw(String sqdw) {
        this.sqdw = sqdw;
    }

    public TransportLicense getXkz() {
        return xkz;
    }

    public void setXkz(TransportLicense xkz) {
        this.xkz = xkz;
    }

    public String getYsdw() {
        return ysdw;
    }

    public void setYsdw(String ysdw) {
        this.ysdw = ysdw;
    }

    public String getYslxdh() {
        return yslxdh;
    }

    public void setYslxdh(String yslxdh) {
        this.yslxdh = yslxdh;
    }

    public String getYssl() {
        return yssl;
    }

    public void setYssl(String yssl) {
        this.yssl = yssl;
    }

    public String getYswp() {
        return yswp;
    }

    public void setYswp(String yswp) {
        this.yswp = yswp;
    }

    public Long getYxcs() {
        return yxcs;
    }

    public String getYxjsrq() {
        return yxjsrq;
    }

    public void setYxjsrq(String yxjsrq) {
        this.yxjsrq = yxjsrq;
    }

    public String getYxksrq() {
        return yxksrq;
    }

    public void setYxksrq(String yxksrq) {
        this.yxksrq = yxksrq;
    }

    public String getZsbh() {
        return zsbh;
    }

    public void setZsbh(String zsbh) {
        this.zsbh = zsbh;
    }

    public List<RouteCity> getBalx() {
        return balx;
    }

    public void setBalx(List<RouteCity> balx) {
        this.balx = balx;
    }

    /**
     * 运输许可证信息实体类
     */
    public static class TransportLicense implements Serializable {
        private static final long serialVersionUID = 1L;

        private Object bh;       // 编号
        private String dx;       // 对象
        private String hz;       // 后缀
        private String lj;       // 路径
        private String mc;       // 名称

        // Getters and Setters
        public Object getBh() {
            return bh;
        }

        public void setBh(Object bh) {
            this.bh = bh;
        }

        public String getDx() {
            return dx;
        }

        public void setDx(String dx) {
            this.dx = dx;
        }

        public String getHz() {
            return hz;
        }

        public void setHz(String hz) {
            this.hz = hz;
        }

        public String getLj() {
            return lj;
        }

        public void setLj(String lj) {
            this.lj = lj;
        }

        public String getMc() {
            return mc;
        }

        public void setMc(String mc) {
            this.mc = mc;
        }
    }
}
