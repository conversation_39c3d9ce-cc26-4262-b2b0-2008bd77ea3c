package com.linggan.jd831.ui.works.visit;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.view.TimePickerView;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XDateUtil;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.CodeNameEntity;
import com.linggan.jd831.bean.InputEntity;
import com.linggan.jd831.bean.PeoStatusEntity;
import com.linggan.jd831.bean.XuanJiaoListEntity;
import com.linggan.jd831.bean.ZiDianEntity;
import com.linggan.jd831.bean.ZiDianEventEntity;
import com.linggan.jd831.databinding.ActivityXuanJiaoAddBinding;
import com.linggan.jd831.ui.common.InputInfoActivity;
import com.linggan.jd831.ui.common.MulChoiceList1Activity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.BaseZiDianDialog;
import com.linggan.jd831.widget.CodeNameDialog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.text.SimpleDateFormat;
import java.util.ArrayList;

/**
 * 类  名：宣教活动-新增
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/12/23 13:11
 */
public class XuanJiaoAddActivity extends XBaseActivity<ActivityXuanJiaoAddBinding> implements View.OnClickListener {

    private String lxCode, fangShiCode, xjBh, kssj, jssj, hdLbCode = "";
    private long timeDate1;
    private ZiDianEventEntity ziDianEvent;

    @Override
    protected ActivityXuanJiaoAddBinding getViewBinding() {
        return ActivityXuanJiaoAddBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        EventBus.getDefault().register(this);
        xjBh = getIntent().getStringExtra("bh");
        kssj = getIntent().getStringExtra("kssj");
        jssj = getIntent().getStringExtra("jssj");
        if (!TextUtils.isEmpty(xjBh)) {
            //任务中心过来
//            binding.tvKsTime.setText(kssj);
//            timeDate1 = XDateUtil.getDateByFormat(kssj, XConstantUtils.YMD).getTime();
//            binding.tvJsTime.setText(jssj);
        }
        //2024-1-31金堂定制--活动列表
        String xzqhdm = UserInfoUtils.getUserInfo().getYhXzqhdm();
        if (!TextUtils.isEmpty(xzqhdm) && xzqhdm.startsWith("510121")) {
            binding.linHdlb.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected void initListener() {
        binding.tvInfo.setOnClickListener(this);
        binding.tvHdFang.setOnClickListener(this);
        binding.tvHdType.setOnClickListener(this);
        binding.tvKsTime.setOnClickListener(this);
        binding.tvJsTime.setOnClickListener(this);
        binding.btSure.setOnClickListener(this);
        binding.tvZxrName.setOnClickListener(this);
        binding.tvHdLb.setOnClickListener(this);
    }

    @Override
    protected void getData() {
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_hd_type) {
            //活动类型--大同类型：lg_jdxj_xjhd_lx_dt  全国：lg_jdxj_xjhd_lx  StrUtils.qfXJLxFromQgOrDt()
            FactoryUtils.getBaseDataType(this, StrUtils.qfXJLxFromQgOrDt(), result -> {
                BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, (ArrayList<ZiDianEntity>) result);
                dianDialog.setOnClickDataListener(() -> {
                    binding.tvHdType.setText(dianDialog.getData().getMc());
                    lxCode = dianDialog.getData().getDm();
                });
                dianDialog.show();
            });
        } else if (v.getId() == R.id.tv_hd_fang) {
            //活动方式--（2023-04-19隐藏场馆参观）
            FactoryUtils.getBaseDataType(this, "lg_jdxj_xjhd_fs", result -> {
                ArrayList<ZiDianEntity> list = new ArrayList<>();
                for (int k = 0; k < result.size(); k++) {
//                    if (!result.get(k).getDm().equals("1")) {
                    list.add(result.get(k));
//                    }
                }
                BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, list);
                dianDialog.setOnClickDataListener(() -> {
                    binding.tvHdFang.setText(dianDialog.getData().getMc());
                    fangShiCode = dianDialog.getData().getDm();
                    if (dianDialog.getData().getDm().equals("2")) {//任务完成方式是 递推活动，则隐藏执行人选项
                        binding.linZxr.setVisibility(View.GONE);
                    } else {
                        binding.linZxr.setVisibility(View.VISIBLE);
                    }
                });
                dianDialog.show();
            });
        } else if (v.getId() == R.id.tv_ks_time) {
            //开始时间
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                timeDate1 = XDateUtil.getDateByFormat(timeDate, XConstantUtils.YMD).getTime();
                if (!TextUtils.isEmpty(xjBh) && !TextUtils.isEmpty(kssj) && jumpKssTime(kssj, timeDate1)) {
                    //任务中心过来--选择的时间必须比任务开始时间大于等于
                    XToastUtil.showToast(this, "开始日期须大于(或等于)任务开始日期");
                    return;
                }
                binding.tvKsTime.setText(timeDate);

            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_js_time) {
            //结束时间
            if (TextUtils.isEmpty(binding.tvKsTime.getText().toString())) {
                XToastUtil.showToast(this, "请先选择开始日期");
                return;
            }
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                long timeDate2 = XDateUtil.getDateByFormat(timeDate, XConstantUtils.YMD).getTime();
                if (timeDate2 < timeDate1) {
                    XToastUtil.showToast(this, "结束日期须大于(或等于)开始日期");
                    return;
                }
                if (!TextUtils.isEmpty(xjBh) && !TextUtils.isEmpty(jssj) && jumpJssTime(jssj, timeDate2)) {
                    //任务中心过来--选择的时间必须比任务开始时间小于等于
                    XToastUtil.showToast(this, "结束日期须小于(或等于)任务结束日期");
                    return;
                }
                binding.tvJsTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_info) {
            //活动内容
            Bundle bundle = new Bundle();
            bundle.putString("title", "活动内容");
            bundle.putString("info", binding.tvInfo.getText().toString());
            bundle.putInt("tab", 2);
            bundle.putString("yy", "yy");//判断是否有语音
            String xzqhdm = UserInfoUtils.getUserInfo().getYhXzqhdm();
            if (!TextUtils.isEmpty(xzqhdm) && xzqhdm.startsWith("510121")) {//金堂限制为200-500
                bundle.putString("len", "200-500");
                bundle.putString("hint", "请输入活动内容，字数要求200-500");
            } else {
                bundle.putString("len", "500");
                bundle.putString("hint", "请输入活动内容");
            }
            XIntentUtil.redirectToNextActivity(this, InputInfoActivity.class, bundle);
        } else if (v.getId() == R.id.bt_sure) {
            if (ButtonUtils.isFastClick()) {
                postData();
            }
        } else if (v.getId() == R.id.tv_zxr_name) {
            //选择执行人
            if (ButtonUtils.isFastClick()) {
                Bundle bundle = new Bundle();
                bundle.putInt("from", 1);
                bundle.putString("param", "xjzxr");
                bundle.putString("title", "指定执行人");
                if (ziDianEvent != null) {
                    bundle.putSerializable("val", ziDianEvent);
                }
                XIntentUtil.redirectToNextActivity(this, MulChoiceList1Activity.class, bundle);
            }
        } else if (v.getId() == R.id.tv_hd_lb) {
            //活动类别
            FactoryUtils.getXjhdList(this, result -> {
                CodeNameDialog dianDialog = new CodeNameDialog(this, (ArrayList<CodeNameEntity>) result);
                dianDialog.setOnClickDataListener(() -> {
                    binding.tvHdLb.setText(dianDialog.getCode().getName());
                    hdLbCode = dianDialog.getCode().getCode();
                });
                dianDialog.show();
            });
        }
    }

    /**
     * 语音输入
     */
    private void postData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.XJHD_ADD);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");

        if (TextUtils.isEmpty(binding.etZhuTi.getText().toString())) {
            XToastUtil.showToast(this, "请输入主题");
            return;
        }
        if (TextUtils.isEmpty(binding.tvHdType.getText().toString())) {
            XToastUtil.showToast(this, "请选择活动类型");
            return;
        }
        if (TextUtils.isEmpty(binding.tvKsTime.getText().toString())) {
            XToastUtil.showToast(this, "请选择活动开始日期");
            return;
        }
        if (TextUtils.isEmpty(binding.tvJsTime.getText().toString())) {
            XToastUtil.showToast(this, "请选择活动结束日期");
            return;
        }
        if (TextUtils.isEmpty(binding.tvInfo.getText().toString())) {
            XToastUtil.showToast(this, "请输入活动内容");
            return;
        }
        if (TextUtils.isEmpty(binding.tvHdFang.getText().toString())) {
            XToastUtil.showToast(this, "请选择活动方式");
            return;
        }
        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("kssj", binding.tvKsTime.getText().toString() + " 00:00:00");
            objectMap.put("jssj", binding.tvJsTime.getText().toString() + " 23:59:59");
            objectMap.put("zt", binding.etZhuTi.getText().toString());
            objectMap.put("nr", binding.tvInfo.getText().toString());
            objectMap.put("lx", lxCode);
            if (!TextUtils.isEmpty(fangShiCode)) {
                objectMap.put("fs", fangShiCode);
            }
            objectMap.put("sjLy", "3");
            objectMap.put("jhcyrs", binding.etCyrs.getText().toString());
            objectMap.put("hdlb", hdLbCode);
            if (!TextUtils.isEmpty(xjBh)) {
                objectMap.put("rwbh", xjBh);
            }

            if (!TextUtils.isEmpty(fangShiCode)) {
                if (fangShiCode.equals("0") && ziDianEvent != null && ziDianEvent.getZiDianEntityList() != null && ziDianEvent.getZiDianEntityList().size() > 0) {
                    JSONArray jsonArray = new JSONArray();
                    for (int i = 0; i < ziDianEvent.getZiDianEntityList().size(); i++) {
                        JSONObject object = new JSONObject();
                        object.put("zxrbh", ziDianEvent.getZiDianEntityList().get(i).getId());
                        object.put("zxrmc", ziDianEvent.getZiDianEntityList().get(i).getYhXm());
                        jsonArray.put(object);
                    }
                    objectMap.put("zxrList", jsonArray);
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        binding.btSure.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJsonNoCan(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(XuanJiaoAddActivity.this, getString(R.string.add_sucess));
                    EventBus.getDefault().post(new XuanJiaoListEntity());
                    EventBus.getDefault().post(new PeoStatusEntity());
                    finish();
                } else {
                    XToastUtil.showToast(XuanJiaoAddActivity.this, xResultData.getErrorInfo());
                    binding.btSure.setEnabled(true);
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSure.setEnabled(true);
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 判断开始时间
     *
     * @param kssj
     * @param timeDate1
     * @return
     */
    private boolean jumpKssTime(String kssj, long timeDate1) {
        long rwKssj = XDateUtil.getDateByFormat(kssj, XConstantUtils.YMD).getTime();
        if (timeDate1 < rwKssj) {
            return true;
        }
        return false;
    }

    /**
     * 判断开始时间
     *
     * @param jssj
     * @param timeDate2
     * @return
     */
    private boolean jumpJssTime(String jssj, long timeDate2) {
        long rwJssj = XDateUtil.getDateByFormat(jssj, XConstantUtils.YMD).getTime();
        if (timeDate2 > rwJssj) {
            return true;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(InputEntity event) {
        if (event != null) {
            binding.tvInfo.setText(event.getInfo());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(ZiDianEventEntity event) {
        if (event != null) {
            if (event.getFrom() == 1) {
                //执行人
                ziDianEvent = event;
                binding.tvZxrName.setText(StrUtils.listToZiDianText2(event.getZiDianEntityList()));
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

}
