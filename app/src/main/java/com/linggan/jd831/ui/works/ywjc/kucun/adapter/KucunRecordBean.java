package com.linggan.jd831.ui.works.ywjc.kucun.adapter;

import java.io.Serializable;

/**
 * 入库记录实体类
 */
public class KucunRecordBean implements Serializable {
    private String org;             // 检查机构
    private String result;          // 检查结果
    private String time;            // 检查时间
    private String submitter;       // 提交人
    private String phone;           // 联系电话
    private String avatarUrl;       // 头像URL
    private int statusType;         // 状态类型

    // 无参构造函数
    public KucunRecordBean() {
    }

    // 全参构造函数
    public KucunRecordBean(String org, String result, String time, String submitter,
                           String phone, String avatarUrl, int statusType) {
        this.org = org;
        this.result = result;
        this.time = time;
        this.submitter = submitter;
        this.phone = phone;
        this.avatarUrl = avatarUrl;
        this.statusType = statusType;
    }

    // Getter和Setter方法
    public String getOrg() {
        return org;
    }

    public void setOrg(String org) {
        this.org = org;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getSubmitter() {
        return submitter;
    }

    public void setSubmitter(String submitter) {
        this.submitter = submitter;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public int getStatusType() {
        return statusType;
    }

    public void setStatusType(int statusType) {
        this.statusType = statusType;
    }

    @Override
    public String toString() {
        return "KucunRecordBean{" +
                "org='" + org + '\'' +
                ", result='" + result + '\'' +
                ", time='" + time + '\'' +
                ", submitter='" + submitter + '\'' +
                ", phone='" + phone + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", statusType=" + statusType +
                '}';
    }
}