package com.linggan.jd831.ui.works.care;


import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.adapter.CareTuiListHolder;
import com.linggan.jd831.bean.CareTuiListEntity;
import com.linggan.jd831.bean.OnePeoCeListEntity;
import com.linggan.jd831.bean.TaskCenListEntity;
import com.linggan.jd831.databinding.ActivityBaseListBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.XHttpUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

import java.util.ArrayList;

/**
 * 类  名：平安关爱推荐
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/12/6 14:58
 * 版  权：凌感科技
 */
public class CareTuiListActivity extends XBaseActivity<ActivityBaseListBinding> implements XRefreshLayout.PullLoadMoreListener {

    private int page = 1, totalPage = 0;
    private boolean isShowDialog = true;

    @Override
    protected ActivityBaseListBinding getViewBinding() {
        return ActivityBaseListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        binding.recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
        binding.recycler.getAdapter().bindHolder(new CareTuiListHolder());
        binding.recycler.setOnPullLoadMoreListener(this);
        EventBus.getDefault().register(this);
    }

    @Override
    protected void initListener() {
    }

    @Override
    protected void getData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "page=" + page + "&rows=" + XConstantUtils.PAGE_SIZE);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.CARE_TUI_LIST + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), isShowDialog, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<XResultPageData<CareTuiListEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XResultPageData<CareTuiListEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        totalPage = xResultData.getData().getTotalPage();
                        if (xResultData.getData().getRecords() != null && xResultData.getData().getRecords().size() > 0) {
                            if (page == 1) {
                                binding.recycler.getAdapter().setData(0, xResultData.getData().getRecords());
                            } else {
                                binding.recycler.getAdapter().addDataAll(0, xResultData.getData().getRecords());
                            }
                        } else {
                            if (page == 1) {
                                binding.tvNoData.setVisibility(View.VISIBLE);
                                binding.recycler.getAdapter().setData(0, new ArrayList());
                            }
                        }
                    } else {
                        if (page == 1) {
                            binding.tvNoData.setVisibility(View.VISIBLE);
                            binding.recycler.getAdapter().setData(0, new ArrayList());
                        }
                    }
                } else {
                    if (page == 1) {
                        binding.tvNoData.setVisibility(View.VISIBLE);
                        binding.recycler.getAdapter().setData(0, new ArrayList());
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                binding.recycler.setPullLoadMoreCompleted();
            }
        });
    }

    @Override
    public void onRefresh() {
        page = 1;
        isShowDialog = false;
        getData();
    }

    @Override
    public boolean onLoadMore() {
        if (page < totalPage) {
            page++;
            isShowDialog = false;
            getData();
            return true;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(CareTuiListEntity event) {
        if (event != null) {
            page = 1;
            isShowDialog = false;
            getData();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(OnePeoCeListEntity event) {
        if (event != null) {
            page = 1;
            isShowDialog = false;
            getData();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(TaskCenListEntity event) {
        if (event != null) {
            page = 1;
            isShowDialog = false;
            getData();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

}
