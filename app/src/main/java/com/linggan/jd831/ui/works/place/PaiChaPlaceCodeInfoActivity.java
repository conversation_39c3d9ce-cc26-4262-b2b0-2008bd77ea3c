package com.linggan.jd831.ui.works.place;

import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.utils.MapUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.ImaVideoShowStringAdapter;
import com.linggan.jd831.adapter.PaiChaCodeXmInfoAdapter;
import com.linggan.jd831.adapter.PlaceUserMsgShowListAdapter;
import com.linggan.jd831.bean.PlaceCodeInfoEntity;
import com.linggan.jd831.databinding.ActivityPaiChaPlaceCodeInfoBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;

import org.xutils.http.RequestParams;

import java.util.Arrays;

/**
 * 类  名： 新增排查记录-详情
 * 描   述：
 * 作  者：zxb
 * 时  间：
 * 版  权：凌感科技
 */
public class PaiChaPlaceCodeInfoActivity extends XBaseActivity<ActivityPaiChaPlaceCodeInfoBinding> {

    private String id;

    @Override
    protected ActivityPaiChaPlaceCodeInfoBinding getViewBinding() {
        return ActivityPaiChaPlaceCodeInfoBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        id = getIntent().getStringExtra("id");
        binding.recycle.setLayoutManager(new LinearLayoutManager(this));
        binding.mRecycle.setLayoutManager(new LinearLayoutManager(this));
    }

    @Override
    protected void initListener() {
        binding.tvCsdz.setOnClickListener(v -> {
            DialogUtils.mapDialog(this, (code1, va) -> {
                if (code1.equals("1")) {
                    MapUtils.openGaoDe(mContext, binding.tvCsdz.getText().toString());
                } else {
                    MapUtils.goToBaiduMap(mContext, binding.tvCsdz.getText().toString());
                }
            });
        });
    }

    @Override
    protected void getData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "jlId=" + id);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PLACE_PC_CODE_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<PlaceCodeInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<PlaceCodeInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    if (xResultData.getData().getSdpcSdcsVO() != null) {
                        binding.tvCsmc.setText(xResultData.getData().getSdpcSdcsVO().getCsmc());
                        binding.tvCsdz.setText(xResultData.getData().getSdpcSdcsVO().getCsdz());
                        binding.tvPlaceType.setText(xResultData.getData().getSdpcSdcsVO().getCslxLxmc());
                        binding.tvKsTime.setText(StrUtils.getDev(xResultData.getData().getSdpcSdcsVO().getXcrwKsrq(), getString(R.string.wu)));
                        binding.tvJsTime.setText(StrUtils.getDev(xResultData.getData().getSdpcSdcsVO().getXcrwJsrq(), getString(R.string.wu)));
                        binding.tvPlaceName.setText(StrUtils.getDev(xResultData.getData().getSdpcSdcsVO().getRwmc(), getString(R.string.chang_suo_msg)));
                    }
                    if (xResultData.getData().getPcxmVOs() != null && xResultData.getData().getPcxmVOs().size() > 0) {
                        binding.recycle.setAdapter(new PaiChaCodeXmInfoAdapter(PaiChaPlaceCodeInfoActivity.this, xResultData.getData().getPcxmVOs()));
                    }
                    if (xResultData.getData().getPcjlVO() != null) {
                        if (!TextUtils.isEmpty(xResultData.getData().getPcjlVO().getSfpcrw())) {
                            //排查任务--是否是
                            binding.linPcTask.setVisibility(View.VISIBLE);
                            binding.tvMjName.setText(xResultData.getData().getPcjlVO().getMjXm());
                            binding.tvMjTime.setText(xResultData.getData().getPcjlVO().getMjQrQrsj());
                            binding.tvMjNr.setText(StrUtils.getIsYesNoText(xResultData.getData().getPcjlVO().getMjQrSfss()));
                            binding.tvMjResult.setText(xResultData.getData().getPcjlVO().getMjQrJgmc());
                            binding.tvMjRemark.setText(xResultData.getData().getPcjlVO().getMjQrBz());
                        }
                        // 图片
                        if (!TextUtils.isEmpty(xResultData.getData().getPcjlVO().getTp())) {
                            binding.gridImg.setAdapter(new ImaVideoShowStringAdapter(Arrays.asList(xResultData.getData().getPcjlVO().getTp().split(","))));
                        }
                        //视频
                        if (!TextUtils.isEmpty(xResultData.getData().getPcjlVO().getSp())) {
                            binding.gridVideo.setAdapter(new ImaVideoShowStringAdapter(Arrays.asList(xResultData.getData().getPcjlVO().getSp().split(","))));
                        }
                        binding.tvResult.setText(xResultData.getData().getPcjlVO().getPcjgJgmc());
                        binding.tvPcr.setText(xResultData.getData().getPcjlVO().getPcrXm());
                        binding.tvPcTime.setText(xResultData.getData().getPcjlVO().getPcsj());
                    }
                    //使用人信息
                    if (xResultData.getData().getCssyrVOs() != null && xResultData.getData().getCssyrVOs().size() > 0) {
                        binding.tvNoUserMsg.setVisibility(View.GONE);
                        binding.mRecycle.setVisibility(View.VISIBLE);
                        binding.mRecycle.setAdapter(new PlaceUserMsgShowListAdapter(PaiChaPlaceCodeInfoActivity.this, xResultData.getData().getCssyrVOs()));
                    } else {
                        binding.mRecycle.setVisibility(View.GONE);
                        binding.tvNoUserMsg.setVisibility(View.VISIBLE);
                    }

                    if (xResultData.getData().getBz() != null) {
                        binding.tvBz.setText(StrUtils.getDev(xResultData.getData().getBz(), getString(R.string.wu)));
                    } else {
                        binding.tvBz.setText(StrUtils.getDev(xResultData.getData().getPcjlVO().getBz(), getString(R.string.wu)));
                    }

                } else {
                    XToastUtil.showToast(PaiChaPlaceCodeInfoActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }
}
