package com.linggan.jd831.ui.works.place;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.PaiPlaceCodeListHolder;
import com.linggan.jd831.adapter.PlaceTopHolder;
import com.linggan.jd831.bean.PlacceAreaListEntity;
import com.linggan.jd831.bean.PlaceCodeListEntity;
import com.linggan.jd831.bean.PlaceTaskListEntity;
import com.linggan.jd831.databinding.ActivityPaiChaCodePlaceListBinding;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.XHttpUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 类  名： 排查场所-记录
 * 描   述：
 * 作  者：zxb
 * 时  间：
 * 版  权：LGKJ
 */
public class PaiChaPlaceCodeListActivity extends XBaseActivity<ActivityPaiChaCodePlaceListBinding> implements XRefreshLayout.PullLoadMoreListener {

    private String sdpcId;
    private int page = 1, totalPage = 0;
    private PlacceAreaListEntity placceAreaList;
    private boolean isShow = true;

    @Override
    protected ActivityPaiChaCodePlaceListBinding getViewBinding() {
        return ActivityPaiChaCodePlaceListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        sdpcId = getIntent().getStringExtra("id");
        binding.base.tvRight.setText(getString(R.string.edit));
        binding.base.tvRight.setTextColor(getResources().getColor(R.color.color_main));
        binding.recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
        binding.recycler.getAdapter().bindHolder(new PlaceTopHolder());
        binding.recycler.getAdapter().bindHolder(new PaiPlaceCodeListHolder());
        binding.recycler.setOnPullLoadMoreListener(this);
        placceAreaList = (PlacceAreaListEntity) getIntent().getSerializableExtra("info");
        if (placceAreaList != null) {
            binding.recycler.getAdapter().setData(0, placceAreaList);
        }
        EventBus.getDefault().register(this);
    }

    @Override
    protected void initListener() {
        binding.fab.setOnClickListener(v -> {
            if (ButtonUtils.isFastClick()) {
                if (placceAreaList != null) {
                    Bundle bundle = new Bundle();
                    bundle.putString("id", sdpcId);
                    bundle.putSerializable("info", placceAreaList);
                    if (placceAreaList.getJwd() == null
                            || placceAreaList.getJwd().isEmpty()
                            || TextUtils.isEmpty(placceAreaList.getJwd())
                            || placceAreaList.getJwd().equals("0.0,0.0")
                            || placceAreaList.getJwd().equals("null")
                            || placceAreaList.getJwd().equals("0,0")
                    ) {

                        XToastUtil.showToast(this, "场所地址异常,请点击上方\"编辑\"重新设置");
                    } else {
                        XIntentUtil.redirectToNextActivity(this, PaiChaPlaceCodeAddActivity.class, bundle);
                    }
                } else {
                    XToastUtil.showToast(this, "数据错误，请联系工作人员");
                }
            }
        });
        binding.base.btRight.setOnClickListener(v -> {
            //编辑
            Bundle bundle = new Bundle();
            bundle.putString("id", sdpcId);
            XIntentUtil.redirectToNextActivity(this, PaiChaPlaceAddActivity.class, bundle);
        });

    }

    @Override
    protected void getData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PLACE_PC_CODE_LIST);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("sdpcId", sdpcId);
        objectMap.put("page", page);
        objectMap.put("rows", XConstantUtils.PAGE_SIZE);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), isShow, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<XResultPageData<PlaceCodeListEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XResultPageData<PlaceCodeListEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        totalPage = xResultData.getData().getTotalPage();
                        if (xResultData.getData().getRecords() != null && xResultData.getData().getRecords().size() > 0) {
                            binding.tvNoData.setVisibility(View.GONE);
                            if (page == 1) {
                                binding.recycler.getAdapter().setData(1, new ArrayList());
                                binding.recycler.getAdapter().setData(1, xResultData.getData().getRecords());
                            } else {
                                binding.recycler.getAdapter().addDataAll(1, xResultData.getData().getRecords());
                            }
                        } else {
                            if (page == 1) {
                                binding.recycler.getAdapter().setData(1, new ArrayList());
                                binding.tvNoData.setVisibility(View.VISIBLE);
                            }
                        }
                    }
                } else {
                    XToastUtil.showToast(PaiChaPlaceCodeListActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                binding.recycler.setPullLoadMoreCompleted();
            }
        });
    }

    @Override
    public void onRefresh() {
        page = 1;
        isShow = false;
        getData();
    }

    @Override
    public boolean onLoadMore() {
        if (page < totalPage) {
            page++;
            isShow = false;
            getData();
            return true;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PlaceTaskListEntity event) {
        if (event != null) {
            page = 1;
            isShow = false;
            getData();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PlacceAreaListEntity event) {
        if (event != null) {
//                      returnData.setJwd(mLongitude + "," + mLatitude); // 经纬度
//                    returnData.setCsmc(binding.etPlaceName.getText().toString());   //场所名称
//                    returnData.setCsdz(binding.tvAddressInfo.getText().toString());  //场所地址
//                    returnData.setCslxLxmc(binding.tvPlaceType.getText().toString());  //场所类型
            // 设置经纬度
            if (placceAreaList != null) {
                if (event.getJwd() != null) {
                    placceAreaList.setJwd(event.getJwd());
                }
                if (event.getCsmc() != null) {
                    placceAreaList.setCsmc(event.getCsmc());
                }
                if (event.getCsdz() != null) {
                    placceAreaList.setCsdz(event.getCsdz());
                }
                if (event.getCslxLxmc() != null) {
                    placceAreaList.setCslxLxmc(event.getCslxLxmc());
                }
                binding.recycler.getAdapter().setData(0, placceAreaList);
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}

