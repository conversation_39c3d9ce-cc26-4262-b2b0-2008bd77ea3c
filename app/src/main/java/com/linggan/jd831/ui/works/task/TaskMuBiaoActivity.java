package com.linggan.jd831.ui.works.task;


import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.RadioGroup;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XDateUtil;
import com.lgfzd.base.utils.XGlideEngine;
import com.lgfzd.base.utils.XImageFileCompressEngine;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.FileAllTypeAdapter;
import com.linggan.jd831.adapter.TaskMuBiaoAddImgListAdapter;
import com.linggan.jd831.adapter.TaskMuBiaoAddListAdapter;
import com.linggan.jd831.bean.MbrwInfoEntity;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.SjsjNrBean;
import com.linggan.jd831.bean.TaskSpListEntity;
import com.linggan.jd831.bean.ZiDianEventEntity;
import com.linggan.jd831.databinding.ActivityTaskMuBiaoBinding;
import com.linggan.jd831.ui.common.MulChoiceList1Activity;
import com.linggan.jd831.ui.common.VideoRecordNoActivity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.PhotoUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.XHttpUtils;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 类  名：任务中心--目标任务（待完成/逾期）
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/11/22  9:37
 * 版  权：凌感科技
 */
public class TaskMuBiaoActivity extends XBaseActivity<ActivityTaskMuBiaoBinding> implements View.OnClickListener, RadioGroup.OnCheckedChangeListener, TaskMuBiaoAddImgListAdapter.OnItemClickListener {

    private String bh, zjwc = "1", rwlxCode, isTijiao = "";
    private int from = 0, nowPosition = -1;
    private ZiDianEventEntity ziDianEvent;
    private TaskMuBiaoAddListAdapter muBiaoAddListAdapter;
    private TaskMuBiaoAddImgListAdapter muBiaoAddImgListAdapter;
    private ImageAddUtil imageAddHdImg, imageAddHdVideo;
    private SjsjNrBean sjsjNrBean;
    private Uri photoUri;
    private File cameraPhoto;

    @Override
    protected ActivityTaskMuBiaoBinding getViewBinding() {
        return ActivityTaskMuBiaoBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        bh = getIntent().getStringExtra("bh");
        binding.recycleLv.setLayoutManager(new LinearLayoutManager(this));

        StrUtils.getPhotoVideoText(binding.tvHdHdImgInfo, binding.tvHdVideoInfo);

        imageAddHdImg = new ImageAddUtil(this, binding.gridHdImg);
        imageAddHdImg.setMax(4);
        imageAddHdImg.setOnImageAddListener(() -> {
            from = 3;
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                if (code.equals("1")) {
                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setMaxSelectNum(4 - imageAddHdImg.getSize()).setCompressEngine(new XImageFileCompressEngine()).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        });
        imageAddHdVideo = new ImageAddUtil(this, binding.gridHdVideo);
        imageAddHdVideo.setMax(4);
        imageAddHdVideo.setOnImageAddListener(() -> {
            from = 4;
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                if (code.equals("1")) {
                    requestPermission(new String[]{android.Manifest.permission.CAMERA, Manifest.permission.RECORD_AUDIO, android.Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofVideo()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setMaxSelectNum(1).setMinSelectNum(1).setCompressEngine(new XImageFileCompressEngine()).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        });
        EventBus.getDefault().register(this);
    }

    @Override
    protected void getData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + bh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.MBRW_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<MbrwInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<MbrwInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    if (xResultData.getData().getRwlx() != null) {
                        binding.info.tvRwlx.setText(xResultData.getData().getRwlx().getName());
                        //1资料提交、2数据收集、3活动完成
                        rwlxCode = xResultData.getData().getRwlx().getCode();
                        if (xResultData.getData().getRwlx().getCode().equals("1")) {
                            binding.linYd.setVisibility(View.GONE);
                            binding.recycleLv.setVisibility(View.VISIBLE);
                            binding.linHd.setVisibility(View.GONE);

//                            muBiaoAddImgListAdapter = new TaskMuBiaoAddImgListAdapter(TaskMuBiaoActivity.this, xResultData.getData().getSjsjnr());
//                            binding.recycleLv.setAdapter(muBiaoAddImgListAdapter);
//                            muBiaoAddImgListAdapter.setOnItemClickListener(TaskMuBiaoActivity.this);
                            //手机端取消完成资料提交类任务的功能，增加提示引导用户前往电脑端操作（2024-01-16需求）
                            binding.tvMbwZjwcTs.setVisibility(View.VISIBLE);
                            binding.linRemark.setVisibility(View.GONE);
                            binding.btSubmit.setText("返回");
                        } else if (xResultData.getData().getRwlx().getCode().equals("2")) {
                            //数据采集
                            binding.linYd.setVisibility(View.VISIBLE);
                            binding.recycleLv.setVisibility(View.VISIBLE);
                            binding.linHd.setVisibility(View.GONE);
                            muBiaoAddListAdapter = new TaskMuBiaoAddListAdapter(TaskMuBiaoActivity.this, xResultData.getData().getSjsjnr());
                            binding.recycleLv.setAdapter(muBiaoAddListAdapter);
                            binding.tvMbwZjwcTs.setVisibility(View.GONE);
                        } else if (xResultData.getData().getRwlx().getCode().equals("3")) {
                            binding.linYd.setVisibility(View.VISIBLE);
                            binding.linHd.setVisibility(View.VISIBLE);
                            binding.recycleLv.setVisibility(View.GONE);
                            binding.tvMbwZjwcTs.setVisibility(View.GONE);
                        }
                    }
                    binding.info.tvRwmc.setText(xResultData.getData().getRwmc());
                    binding.info.tvRwyq.setText(xResultData.getData().getRwyq());
                    binding.info.tvZrdw.setText(xResultData.getData().getZrdwmc());
                    if (xResultData.getData().getRwms() != null) {
                        if (!TextUtils.isEmpty(xResultData.getData().getRwms().getCode())) {
                            if (xResultData.getData().getRwms().getCode().equals("1")) {
                                if (xResultData.getData().getZqpl() != null) {
                                    binding.info.tvRwms.setText(xResultData.getData().getRwms().getName() + ":" + xResultData.getData().getZqpl().getName());
                                } else {
                                    binding.info.tvRwms.setText(xResultData.getData().getRwms().getName());
                                }
                            } else if (xResultData.getData().getRwms().getCode().equals("0")) {
                                binding.info.tvRwms.setText(xResultData.getData().getRwms().getName());
                            }
                        }
                    }
                    binding.info.tvRwzq.setText(xResultData.getData().getKssj() + " 至 " + xResultData.getData().getJssj());
                    binding.info.gridImg.setAdapter(new FileAllTypeAdapter(TaskMuBiaoActivity.this, xResultData.getData().getXgwj()));
                    if (xResultData.getData().getPfms() != null) {
                        binding.info.tvPfms.setText(xResultData.getData().getPfms().getName());
                    }
                    binding.info.tvRwfz.setText(xResultData.getData().getRwfz());
                    //是否有转发
                    if (!TextUtils.isEmpty(xResultData.getData().getZfdwmc())) {
                        binding.info.linZf.setVisibility(View.VISIBLE);
                        binding.info.tvZfdw.setText(xResultData.getData().getZfdwmc());
                        binding.info.tvZfr.setText(xResultData.getData().getZfrxm());
                        binding.info.tvZfrdh.setText(xResultData.getData().getZfrdh());
                    }
                    long rwTime = XDateUtil.getDateByFormat(xResultData.getData().getKssj(), XDateUtil.dateFormatYMD).getTime();
                    long nowTime = XDateUtil.getDateByFormat(XDateUtil.getStringByFormat(System.currentTimeMillis(), XDateUtil.dateFormatYMD), XDateUtil.dateFormatYMD).getTime();
                    if (rwTime > nowTime) {
                        isTijiao = "请在任务周期内提交";

                    } else {
                        isTijiao = "";
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void initListener() {
        binding.radioGroup.setOnCheckedChangeListener(this);
        binding.btSubmit.setOnClickListener(this);
        binding.tvZbXjDw.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.bt_submit) {
            //提交
            if (ButtonUtils.isFastClick()) {
                if (binding.btSubmit.getText().toString().equals("返回")) {
                    finish();
                } else {
                    if (TextUtils.isEmpty(isTijiao)) {
                        postData();
                    } else {
                        XToastUtil.showToast(this, isTijiao);
                    }
                }
            }
        } else if (v.getId() == R.id.tv_zb_xj_dw) {
            //下级单位
            Bundle bundle = new Bundle();
            bundle.putInt("from", 1);
            bundle.putString("param", "zfxjdw");
            bundle.putString("title", "转发下级单位");
            if (ziDianEvent != null) {
                bundle.putSerializable("val", ziDianEvent);
            }
            XIntentUtil.redirectToNextActivity(this, MulChoiceList1Activity.class, bundle);
        }
    }

    /**
     * 数据提交
     */
    private void postData() {
        JSONObject objectMap = new JSONObject();
        String url = "";
        if (zjwc.equals("2")) {
            //转发下级
            url = ApiUrlsUtils.MBRW_ZF;
            try {
                objectMap.put("bh", bh);
                if (ziDianEvent != null && ziDianEvent.getZiDianEntityList() != null && ziDianEvent.getZiDianEntityList().size() > 0) {
                    JSONArray jsonArray = new JSONArray();
                    for (int i = 0; i < ziDianEvent.getZiDianEntityList().size(); i++) {
                        jsonArray.put(ziDianEvent.getZiDianEntityList().get(i).getDwdm());
                    }
                    objectMap.put("dwdm", jsonArray);
                } else {
                    XToastUtil.showToast(this, "请选择转办的下级单位");
                    return;
                }
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        } else {
            url = ApiUrlsUtils.MBRW_WCRW;
            try {
                objectMap.put("bh", bh);
                ///1资料提交、2数据收集、3活动完成
                if (rwlxCode.equals("1")) {
                    JSONArray jsonArray = new JSONArray();
                    String input = "";
                    if (muBiaoAddImgListAdapter != null && muBiaoAddImgListAdapter.getList().size() > 0) {
                        for (int i = 0; i < muBiaoAddImgListAdapter.getList().size(); i++) {
                            SjsjNrBean sjsjNrBeanX = muBiaoAddImgListAdapter.getList().get(i);
                            if (sjsjNrBeanX != null && sjsjNrBeanX.getUrls() != null && sjsjNrBeanX.getUrls().size() > 0) {
                                for (int k = 0; k < sjsjNrBeanX.getUrls().size(); k++) {
                                    JSONObject jsonObject = new JSONObject();
                                    if (!TextUtils.isEmpty(sjsjNrBeanX.getUrls().get(k).getSavePath())) {
                                        jsonObject.put("lj", sjsjNrBeanX.getUrls().get(k).getSavePath());
                                        jsonObject.put("mc", sjsjNrBeanX.getZlmc());
                                        jsonObject.put("hz", StrUtils.getFileType(sjsjNrBeanX.getUrls().get(k).getOriginName()));
                                        jsonObject.put("dx", sjsjNrBeanX.getUrls().get(k).getFileSize());
                                        jsonObject.put("sjLy", "3");
                                        jsonArray.put(jsonObject);
                                    } else {
                                        input = sjsjNrBeanX.getZlmc();
                                        break;
                                    }
                                }
                            } else {
                                input = sjsjNrBeanX.getZlmc();
                                break;
                            }
                        }
                    }
                    if (muBiaoAddImgListAdapter != null && muBiaoAddImgListAdapter.getList().size() > 0 && jsonArray.length() <= 0 || muBiaoAddImgListAdapter != null && muBiaoAddImgListAdapter.getList().size() > 0 && !TextUtils.isEmpty(input)) {
                        XToastUtil.showToast(this, "请上传" + input);
                        return;
                    }
                    objectMap.put("xgzl", jsonArray);
                } else if (rwlxCode.equals("2")) {
                    //数据采集
                    JSONArray jsonArray = new JSONArray();
                    String input = "";
                    if (muBiaoAddListAdapter != null && muBiaoAddListAdapter.getList() != null && muBiaoAddListAdapter.getList().size() > 0) {
                        for (int i = 0; i < muBiaoAddListAdapter.getList().size(); i++) {
                            JSONObject jsonObject = new JSONObject();
                            if (!TextUtils.isEmpty(muBiaoAddListAdapter.getList().get(i).getSjsjnr())) {
                                jsonObject.put("bh", muBiaoAddListAdapter.getList().get(i).getBh());
                                jsonObject.put("zlmc", muBiaoAddListAdapter.getList().get(i).getZlmc());
                                jsonObject.put("sjsjnr", muBiaoAddListAdapter.getList().get(i).getSjsjnr());
                                jsonObject.put("sjLy", "3");
                                jsonArray.put(jsonObject);
                            } else {
                                input = muBiaoAddListAdapter.getList().get(i).getZlmc();
                                break;
                            }
                        }
                    }
                    if (muBiaoAddListAdapter != null && muBiaoAddListAdapter.getList() != null && muBiaoAddListAdapter.getList().size() > 0 && jsonArray.length() <= 0 || muBiaoAddListAdapter != null && muBiaoAddListAdapter.getList() != null && muBiaoAddListAdapter.getList().size() > 0 && !TextUtils.isEmpty(input)) {
                        XToastUtil.showToast(this, "请输入" + input);
                        return;
                    }
                    objectMap.put("sjsjnr", jsonArray);
                } else if (rwlxCode.equals("3")) {
                    //3活动完成
                    JSONArray jsonArray = new JSONArray();
                    if (imageAddHdImg != null && imageAddHdImg.getPaths() != null && imageAddHdImg.getPaths().size() > 0) {
                        for (int i = 0; i < imageAddHdImg.getPaths().size(); i++) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("lj", imageAddHdImg.getPaths().get(i).getSavePath());
                            jsonObject.put("mc", imageAddHdImg.getPaths().get(i).getOriginName());
                            jsonObject.put("hz", StrUtils.getFileType(imageAddHdImg.getPaths().get(i).getOriginName()));
                            jsonObject.put("dx", imageAddHdImg.getPaths().get(i).getFileSize());
                            jsonObject.put("sjLy", "3");
                            jsonArray.put(jsonObject);
                        }
                    }
                    if (jsonArray.length() <= 0) {
                        XToastUtil.showToast(this, "请上传活动照片");
                        return;
                    }
                    objectMap.put("hdzp", jsonArray);
                    //
                    JSONArray jsonArrayVideo = new JSONArray();
                    if (imageAddHdVideo != null && imageAddHdVideo.getPaths() != null && imageAddHdVideo.getPaths().size() > 0) {
                        for (int i = 0; i < imageAddHdVideo.getPaths().size(); i++) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("lj", imageAddHdVideo.getPaths().get(i).getSavePath());
                            jsonObject.put("mc", imageAddHdVideo.getPaths().get(i).getOriginName());
                            jsonObject.put("hz", StrUtils.getFileType(imageAddHdVideo.getPaths().get(i).getOriginName()));
                            jsonObject.put("dx", imageAddHdVideo.getPaths().get(i).getFileSize());
                            jsonObject.put("sjLy", "3");
                            jsonArrayVideo.put(jsonObject);
                        }
                    }
                    if (jsonArrayVideo.length() <= 0) {
                        XToastUtil.showToast(this, "请上传活动视频");
                        return;
                    }
                    objectMap.put("hdsp", jsonArrayVideo);
                }
                objectMap.put("bz", binding.etRemark.getText().toString());
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        }
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + url);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        binding.btSubmit.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), true, true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(TaskMuBiaoActivity.this, getString(R.string.sub_sucess));
                    EventBus.getDefault().post(new TaskSpListEntity());
                    finish();
                } else {
                    XToastUtil.showToast(TaskMuBiaoActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSubmit.setEnabled(true);
            }

            @Override
            public void onFinished() {
                binding.btSubmit.setEnabled(true);
            }
        });
    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        switch (checkedId) {
            case R.id.rb_yes:
                //直接完成 ----(2021-01-15优化修改直接完成--提示到pc端完成)
                if (!TextUtils.isEmpty(rwlxCode) && rwlxCode.equals("1")) {
                    binding.tvMbwZjwcTs.setVisibility(View.VISIBLE);
                    binding.linYd.setVisibility(View.GONE);
                    binding.btSubmit.setText("返回");
                } else {
                    binding.linYd.setVisibility(View.VISIBLE);
                    binding.tvMbwZjwcTs.setVisibility(View.GONE);
                    binding.btSubmit.setText("提交");
                }
                binding.linNoDx.setVisibility(View.GONE);
                zjwc = "1";
                break;
            case R.id.rb_no:
                //转发下级
                binding.tvMbwZjwcTs.setVisibility(View.GONE);
                binding.linYd.setVisibility(View.GONE);
                binding.linNoDx.setVisibility(View.VISIBLE);
                binding.btSubmit.setText("提交");
                zjwc = "2";
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.CHOOSE_REQUEST:
                    List<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                    if (null != selectList) {
                        for (LocalMedia media : selectList) {
                            if (!TextUtils.isEmpty(media.getCompressPath())) {
                                uploadFile(media.getCompressPath());
                            } else if (!TextUtils.isEmpty(media.getRealPath())) {
                                long fileSize = XFileUtil.getFileSize(media.getRealPath());
                                long fileNewSize = fileSize / 1000000;
                                if (fileNewSize >= 20) {
                                    DialogUtils.showVideoError(this, getString(R.string.tishi), getString(R.string.video_big_info), (code, id) -> {
                                        uploadFile(media.getRealPath());
                                    });
                                    break;
                                } else {
                                    uploadFile(media.getRealPath());
                                }
                            } else if (!TextUtils.isEmpty(media.getPath())) {
                                long fileSize = XFileUtil.getFileSize(media.getPath());
                                long fileNewSize = fileSize / 1000000;
                                if (fileNewSize >= 20) {
                                    DialogUtils.showVideoError(this, getString(R.string.tishi), getString(R.string.video_big_info), (code, id) -> {
                                        uploadFile(media.getPath());
                                    });
                                    break;
                                } else {
                                    uploadFile(media.getPath());
                                }
                            }
                        }
                    }
                    break;
                case 1:
                    //图片
//                    uploadFile(data.getStringExtra("path"));
                    try {
                        boolean isFinish = XFileUtil.bitmapToFilePath(this, BitmapFactory.decodeFile(cameraPhoto.getAbsolutePath()), photoUri, cameraPhoto);
                        if (isFinish) {
                            uploadFile(cameraPhoto.getAbsolutePath());
                        }
                    } catch (Exception e) {
                    }
                    break;
                case 211://录像返回
                    String mp4_path = data.getStringExtra("path");
                    if (!TextUtils.isEmpty(mp4_path)) {
                        long fileSize = XFileUtil.getFileSize(mp4_path);
                        long fileNewSize = fileSize / 1000000;
                        if (fileNewSize >= 20) {
                            DialogUtils.showVideoError(this, getString(R.string.tishi), getString(R.string.video_big_info), (code, id) -> {
                                uploadFile(mp4_path);
                            });
                            break;
                        } else {
                            uploadFile(mp4_path);
                        }

                    }
                    break;
            }
        }
    }

    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (from == 2 || from == 3) {
//            Intent intent = new Intent(this, CameraActivity.class);//调用系统相机
//            startActivityForResult(intent, 1);
            createFileMuLu();
            if (cameraPhoto != null) {
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                photoUri = XFileUtil.getUriFromFile(this, cameraPhoto);
                intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                startActivityForResult(intent, 1);
            }
        } else if (from == 4) {
            startActivityForResult(new Intent(this, VideoRecordNoActivity.class), 211);
        }
    }

    /**
     * 创建文件
     */
    private void createFileMuLu() {
        if ((ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED)
                && (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED)) {
            cameraPhoto = new File(PhotoUtil.createPath(this));
            try {
                if (cameraPhoto.exists()) {
                    cameraPhoto.delete();
                }
                cameraPhoto.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            // 权限未授权，需要申请权限
            requestPermission(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        }
    }

    //上传
    private void uploadFile(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        if (from == 2) {
                            Log.i("fff", "onSuccess:11 ");
                            if (nowPosition > -1) {
                                Log.i("fff", "onSuccess:22 ");
                                sjsjNrBean.getUrls().add(xResultData.getData().get(0));
                                muBiaoAddImgListAdapter.getList().set(nowPosition, sjsjNrBean);
                                Log.i("fff", "onSuccess: " + new Gson().toJson(muBiaoAddImgListAdapter.getList()));
                                muBiaoAddImgListAdapter.notifyDataSetChanged();
                            }
                        } else if (from == 3) {
                            imageAddHdImg.addImage(xResultData.getData().get(0));
                            imageAddHdImg.notifyData();
                        } else if (from == 4) {
                            imageAddHdVideo.addImage(xResultData.getData().get(0));
                            imageAddHdVideo.notifyData();
                        }
                    }
                } else {
                    XToastUtil.showToast(TaskMuBiaoActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(ZiDianEventEntity event) {
        if (event != null) {
            if (event.getFrom() == 1) {
                //仓库数据
                ziDianEvent = event;
                binding.tvZbXjDw.setText("当前转办单位" + event.getZiDianEntityList().size() + "个");
            }
        }
    }

    @Override
    public void onItemClick(SjsjNrBean data, int position) {
        nowPosition = position;
        sjsjNrBean = data;
        DialogUtils.showPhotoDialog(this, (code, id) -> {
            from = 2;
            if (code.equals("1")) {
                requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
            } else {
                PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setMaxSelectNum(4 - imageAddHdImg.getSize()).setCompressEngine(new XImageFileCompressEngine()).forResult(PictureConfig.CHOOSE_REQUEST);
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
