package com.linggan.jd831.ui.works.aiAssistant;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Base64;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XToastUtil;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.AIAssistantResponse;
import com.linggan.jd831.databinding.ActivityAiAssistantListBinding;
import com.linggan.jd831.net.AIAssistantService;

import org.xutils.common.Callback;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class AIAssistantListActivity extends XBaseActivity<ActivityAiAssistantListBinding> implements XRefreshLayout.PullLoadMoreListener {

    private static final Set<String> ALLOWED_IDS = new HashSet<>(Arrays.asList(
            "4dc06aaeff0b11efbc35024264640706", // 涉毒法律法规智能体
            "88b4621cfe4a11efaf7e024264640706", // 禁毒社工工作助理
            "2fe4ebccfd4e11efbae3024264640706", // 系统智能客服
            "b7a787b8f99811efb4c3024264640706", // 莫光耀教授
            "ec04aabef36b11ef9ff3024264640706", // 涉毒案例小助手
            "ccce2056f36311ef8544024264640706"  // 毒品预防教育小助手
    ));

    private AIAssistantAdapter adapter;
    private int currentPage = 1;
    private static final int PAGE_SIZE = 20;
    private List<AIAssistantResponse.AIAssistant> dataList = new ArrayList<>();
    private View loadingView;
    private View emptyView;
    private View errorView;
    private TextView tvErrorMessage;
    private Button btnRetry;

    private boolean isFirstLoad = true;

    @Override
    protected ActivityAiAssistantListBinding getViewBinding() {
        return ActivityAiAssistantListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        setTitle("智能体选择");

        // 获取状态栏高度并设置给title
        int statusBarHeight = 0;
        int resourceId = getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            statusBarHeight = getResources().getDimensionPixelSize(resourceId);
            View titleLayout = binding.title;
            if (titleLayout != null) {
                ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) titleLayout.getLayoutParams();
                params.topMargin = statusBarHeight;
                titleLayout.setLayoutParams(params);
            }
        }

        binding.mRecycle.setLayoutManager(new LinearLayoutManager(this));
        adapter = new AIAssistantAdapter();
        binding.mRecycle.setAdapter(adapter);

        // 初始化状态视图
        loadingView = binding.layoutLoading.getRoot();
        emptyView = binding.layoutEmpty.getRoot();
        errorView = binding.layoutError.getRoot();
        tvErrorMessage = errorView.findViewById(R.id.tv_error_message);
        btnRetry = errorView.findViewById(R.id.btn_retry);
    }

    @Override
    protected void initListener() {
        binding.mSwipe.setOnRefreshListener(() -> {
            isFirstLoad = false;
            onRefresh();
        });

        btnRetry.setOnClickListener(v -> {
            loadData(true);
        });
        binding.back.setOnClickListener(v -> {
            finish();
        });
    }

    @Override
    protected void getData() {
        loadData(true);
    }

    @Override
    public void onRefresh() {
        loadData(true);
    }

    @Override
    public boolean onLoadMore() {
        if (currentPage > 1) {
            loadData(false);
            return true;
        }
        return false;
    }

    /**
     * 显示加载中视图
     */
    private void showLoadingView() {
        if (!isFirstLoad) {
            loadingView.findViewById(R.id.progressBar).setVisibility(View.GONE);
        } else {
            loadingView.findViewById(R.id.progressBar).setVisibility(View.VISIBLE);
        }
        binding.mRecycle.setVisibility(View.GONE);
        loadingView.setVisibility(View.VISIBLE);
        emptyView.setVisibility(View.GONE);
        errorView.setVisibility(View.GONE);
    }

    /**
     * 显示内容视图
     */
    private void showContentView() {
        binding.mRecycle.setVisibility(View.VISIBLE);
        loadingView.setVisibility(View.GONE);
        emptyView.setVisibility(View.GONE);
        errorView.setVisibility(View.GONE);
    }

    /**
     * 显示空数据视图
     */
    private void showEmptyView() {
        binding.mRecycle.setVisibility(View.GONE);
        loadingView.setVisibility(View.GONE);
        emptyView.setVisibility(View.VISIBLE);
        errorView.setVisibility(View.GONE);
    }

    /**
     * 显示错误视图
     *
     * @param errorMsg 错误信息
     */
    private void showErrorView(String errorMsg) {
        binding.mRecycle.setVisibility(View.GONE);
        loadingView.setVisibility(View.GONE);
        emptyView.setVisibility(View.GONE);
        errorView.setVisibility(View.VISIBLE);
        tvErrorMessage.setText("加载失败，请重试");

//        if (!TextUtils.isEmpty(errorMsg)) {
//            tvErrorMessage.setText(errorMsg);
//        } else {
//        }
    }

    private void loadData(boolean refresh) {
        if (refresh) {
            currentPage = 1;
            dataList.clear();
            showLoadingView();
        }

        AIAssistantService.getAssistants(
                currentPage,
                PAGE_SIZE,
                "update_time",
                true,
                null,
                null,
                new Callback.CommonCallback<String>() {
                    @Override
                    public void onSuccess(String result) {
                        if (binding.mSwipe.isRefreshing()) {
                            binding.mSwipe.setRefreshing(false);
                        }

                        if (!TextUtils.isEmpty(result)) {
                            try {
                                AIAssistantResponse response = new Gson().fromJson(result, AIAssistantResponse.class);
                                if (response != null && response.getData() != null) {
                                    dataList.addAll(response.getData());

                                    if (dataList.isEmpty()) {
                                        showEmptyView();
                                    } else {
                                        showContentView();
                                        adapter.updateFilteredList();
                                        adapter.notifyDataSetChanged();
                                        currentPage++;
                                    }
                                } else {
                                    showErrorView("加载失败");
                                    XToastUtil.showToast(AIAssistantListActivity.this, "加载失败");
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                                showErrorView("数据解析失败");
                                XToastUtil.showToast(AIAssistantListActivity.this, "数据解析失败");
                            }
                        } else {
                            showEmptyView();
                        }
                    }

                    @Override
                    public void onError(Throwable ex, boolean isOnCallback) {
                        if (binding.mSwipe.isRefreshing()) {
                            binding.mSwipe.setRefreshing(false);
                        }

                        showErrorView("加载失败：" + ex.getMessage());
                        XToastUtil.showToast(AIAssistantListActivity.this, "加载失败：" + ex.getMessage());
                    }

                    @Override
                    public void onCancelled(CancelledException cex) {
                        if (binding.mSwipe.isRefreshing()) {
                            binding.mSwipe.setRefreshing(false);
                        }
                    }

                    @Override
                    public void onFinished() {
                        if (binding.mSwipe.isRefreshing()) {
                            binding.mSwipe.setRefreshing(false);
                        }
                    }
                }
        );
    }

    private class AIAssistantAdapter extends RecyclerView.Adapter<AIAssistantAdapter.ViewHolder> {

        private List<AIAssistantResponse.AIAssistant> filteredList = new ArrayList<>();

        private void updateFilteredList() {
            filteredList.clear();
            for (AIAssistantResponse.AIAssistant item : dataList) {
                if (ALLOWED_IDS.contains(item.getId())) {
                    filteredList.add(item);
                }
            }
        }

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_ai_assistant, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            AIAssistantResponse.AIAssistant item = filteredList.get(position);

            // 处理base64格式的avatar
            if (!TextUtils.isEmpty(item.getAvatar())) {
                try {
                    String base64Image = item.getAvatar();
                    // 处理Base64前缀
                    if (base64Image.contains(",")) {
                        base64Image = base64Image.split(",")[1];
                    }
                    byte[] decodedString = Base64.decode(base64Image, Base64.DEFAULT);
                    Bitmap bitmap = BitmapFactory.decodeByteArray(decodedString, 0, decodedString.length);
                    if (bitmap != null) {
                        holder.ivAvatar.setImageResource(R.drawable.ic_default_avatar);
                        holder.ivAvatar.setScaleType(ImageView.ScaleType.FIT_CENTER);
                    } else {
                        holder.ivAvatar.setImageResource(R.drawable.ic_default_avatar);
                        holder.ivAvatar.setScaleType(ImageView.ScaleType.FIT_CENTER);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    holder.ivAvatar.setImageResource(R.drawable.ic_default_avatar);
                    holder.ivAvatar.setScaleType(ImageView.ScaleType.FIT_CENTER);
                }
            } else {
                holder.ivAvatar.setImageResource(R.drawable.ic_default_avatar);
                holder.ivAvatar.setScaleType(ImageView.ScaleType.FIT_CENTER);
            }

            holder.tvTitle.setText(item.getName());
            holder.tvDescription.setText(item.getDescription());

            holder.itemView.setOnClickListener(v -> {
                Bundle bundle = new Bundle();
                bundle.putString("id", item.getId());
                bundle.putString("name", item.getName());
                bundle.putString("prompt", item.getPrompt().getOpener());
                XIntentUtil.redirectToNextActivity(AIAssistantListActivity.this, AIChatAIAssistantActivity.class, bundle);
            });
        }

        @Override
        public int getItemCount() {
            return filteredList.size();
        }

        class ViewHolder extends RecyclerView.ViewHolder {
            ImageView ivAvatar;
            TextView tvTitle;
            TextView tvDescription;

            ViewHolder(View itemView) {
                super(itemView);
                ivAvatar = itemView.findViewById(R.id.iv_avatar);
                tvTitle = itemView.findViewById(R.id.tv_title);
                tvDescription = itemView.findViewById(R.id.tv_description);
            }
        }
    }
} 