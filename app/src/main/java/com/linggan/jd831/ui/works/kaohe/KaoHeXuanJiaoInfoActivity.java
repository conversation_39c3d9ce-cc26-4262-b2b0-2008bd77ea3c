package com.linggan.jd831.ui.works.kaohe;


import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.GridLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.BaoBeiZpListAdapter;
import com.linggan.jd831.bean.BaiBeiListEntity;
import com.linggan.jd831.bean.BaoBeiZpBean;
import com.linggan.jd831.databinding.ActivityKaoheXuanjiaoInfoBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XHttpUtils;

import org.greenrobot.eventbus.EventBus;
import org.xutils.http.RequestParams;

import java.util.List;

/**
 * 类  名：考核宣教任务详情
 * 作  者：zxb
 * 说  明：
 * 时  间：2024/7/10 13:11
 */
public class KaoHeXuanJiaoInfoActivity extends XBaseActivity<ActivityKaoheXuanjiaoInfoBinding> {

    private String bh, from;

    @Override
    protected ActivityKaoheXuanjiaoInfoBinding getViewBinding() {
        return ActivityKaoheXuanjiaoInfoBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        binding.mRecycle.setLayoutManager(new GridLayoutManager(this, 3));
        bh = getIntent().getStringExtra("bh");
        from = getIntent().getStringExtra("from");//标识走系统消息过来
        BaiBeiListEntity beiListEntity = (BaiBeiListEntity) getIntent().getSerializableExtra("info");
        if (beiListEntity != null) {
            showUIData(beiListEntity);
        }
    }

    @Override
    protected void initListener() {
        binding.btCx.setOnClickListener(v -> {
            //撤销
            String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + bh);
            RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.YZD_BB_CX + "?query=" + jiaMiString);
            requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
            requestParams.addHeader("Origin-Content-Type", "application/json");
            binding.btCx.setEnabled(false);
            XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), true, new XHttpResponseCallBack() {
                @Override
                public void onSuccess(String result) {
                    XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                    }.getType());
                    if (xResultData.getStatus() == 0) {
                        XToastUtil.showToast(KaoHeXuanJiaoInfoActivity.this, getString(R.string.chehui_sucess));
                        EventBus.getDefault().post(new BaiBeiListEntity());
                        finish();
                    } else {
                        XToastUtil.showToast(KaoHeXuanJiaoInfoActivity.this, xResultData.getErrorInfo());
                        binding.btCx.setEnabled(true);
                    }
                }

                @Override
                public void onFailed(int code, String failedMsg) {
                    binding.btCx.setEnabled(true);
                }

                @Override
                public void onFinished() {
                }
            });
        });
    }

    @Override
    protected void getData() {
        if (!TextUtils.isEmpty(from)) {
            String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bbbh=" + bh);
            RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.YZD_BB_INFO + "?query=" + jiaMiString);
            requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
            requestParams.addHeader("Origin-Content-Type", "application/json");
            XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
                @Override
                public void onSuccess(String result) {
                    XResultData<BaiBeiListEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<BaiBeiListEntity>>() {
                    }.getType());
                    if (xResultData.getStatus() == 0) {
                        showUIData(xResultData.getData());
                    } else {
                        XToastUtil.showToast(KaoHeXuanJiaoInfoActivity.this, xResultData.getErrorInfo());
                    }
                }

                @Override
                public void onFailed(int code, String failedMsg) {
                }

                @Override
                public void onFinished() {
                }
            });
        }
    }

    /**
     * 数据展示
     */
    private void showUIData(BaiBeiListEntity beiListEntity) {
        if (beiListEntity != null) {
            binding.sure.tvCkmz.setText(beiListEntity.getCkmc());
            binding.sure.tvJrsj.setText(beiListEntity.getJrsj());
            binding.sure.tvLksj.setText(beiListEntity.getLksj());
            binding.sure.tvBz.setText(StrUtils.getDev(beiListEntity.getBz(), "无"));
            binding.sure.tvBbr.setText(beiListEntity.getBbrmc());
            binding.sure.tvBbsj.setText(beiListEntity.getBbsj());
            //
            binding.msg.tvBbr.setText(beiListEntity.getQrrmc());
            binding.msg.tvBbsj.setText(beiListEntity.getQrsj());
            binding.msg.tvBz.setText(StrUtils.getDev(beiListEntity.getBhbz(), "无"));
            if (beiListEntity.isSfrztg()) {//人证通过
                binding.sure.ivRz.setVisibility(View.VISIBLE);
            } else {
                binding.sure.ivRz.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(beiListEntity.getZt().getCode())) {
                //2已确认；3已驳回；1未确认 4已逾期
                switch (beiListEntity.getZt().getCode()) {
                    case "2":
                        binding.msg.tvZt.setText("已确认");
                        binding.msg.linQrr.setVisibility(View.VISIBLE);
                        binding.msg.linQrSj.setVisibility(View.VISIBLE);
                        binding.view1.setVisibility(View.VISIBLE);
                        binding.tvZpbt.setVisibility(View.VISIBLE);

                        loadData();
                        break;
                    case "3":
                        binding.msg.tvZt.setText("已驳回");
                        binding.msg.linQrr.setVisibility(View.VISIBLE);
                        binding.msg.linQrSj.setVisibility(View.VISIBLE);
                        binding.msg.linBhBz.setVisibility(View.VISIBLE);
                        break;
                    case "1":
                        binding.msg.tvZt.setText("待确认");
                        //报备人是自己就撤销，不是自己就确认
                        if (TextUtils.equals(beiListEntity.getBbrbh(), XShareCacheUtils.getInstance().getString(XConstantUtils.USER_ID))) {
                            binding.btCx.setVisibility(View.VISIBLE);
                        }
                        break;
                    case "4":
                        binding.msg.tvZt.setText("已逾期");
                        break;
                    case "5":
                        binding.msg.tvZt.setText("已撤销");
                        binding.msg.linCxsj.setVisibility(View.VISIBLE);
                        binding.msg.tvCxsj.setText(beiListEntity.getQrsj());
                        break;
                    default:
                        break;
                }
            }
        }
    }

    /**
     * 抓拍照片
     */
    protected void loadData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + bh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.YZD_BB_ZP_LIST + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.getN(this, requestParams, DialogUtils.showLoadDialog(this, ""), true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<BaoBeiZpBean>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<BaoBeiZpBean>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    binding.mRecycle.setAdapter(new BaoBeiZpListAdapter(KaoHeXuanJiaoInfoActivity.this, xResultData.getData()));
                } else {
                    XToastUtil.showToast(KaoHeXuanJiaoInfoActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

}
