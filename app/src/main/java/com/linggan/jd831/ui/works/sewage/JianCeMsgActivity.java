package com.linggan.jd831.ui.works.sewage;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.linggan.jd831.utils.XHttpUtils;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.WsJcProInfoAdapter;
import com.linggan.jd831.bean.TalkListEntity;
import com.linggan.jd831.bean.WsYbInfoEntity;
import com.linggan.jd831.bean.XieYiShiEventEntity;
import com.linggan.jd831.databinding.ActivityWuShuiJcMsgBinding;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

/**
 * 类  名：检测信息-上传
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class JianCeMsgActivity extends XBaseActivity<ActivityWuShuiJcMsgBinding> implements View.OnClickListener {


    private XieYiShiEventEntity xieYiShiEvent;
    private String bh, rwzBh, cydBh, jcjgBh, ybBh, rwBh, scJg, ybhLb;

    @Override
    protected ActivityWuShuiJcMsgBinding getViewBinding() {
        return ActivityWuShuiJcMsgBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        bh = getIntent().getStringExtra("bh");
        cydBh = getIntent().getStringExtra("cydBh");
        rwzBh = getIntent().getStringExtra("rwzBh");
        rwBh = getIntent().getStringExtra("rwBh");
        scJg = getIntent().getStringExtra("scJg");
        ybhLb = getIntent().getStringExtra("ybBh");
        EventBus.getDefault().register(this);
        binding.recycler.setLayoutManager(new LinearLayoutManager(this));
    }

    @Override
    protected void initListener() {
        binding.btSubmit.setOnClickListener(this);
        binding.tvJcw.setOnClickListener(this);
    }

    @Override
    protected void getData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + bh + "&cydBh=" + cydBh + "&rwzBh=" + rwzBh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.SEWAGE_WS_YB_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<WsYbInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<WsYbInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    binding.top.tvCyTime.setText(xResultData.getData().getCySj());
                    binding.top.tvCyr.setText(xResultData.getData().getCyr());
                    binding.top.tvCyBh.setText(xResultData.getData().getYbBh());
                    binding.top.tvCyDw.setText(xResultData.getData().getMc());
                    binding.top.tvCyAddress.setText(xResultData.getData().getDz());
                    jcjgBh = xResultData.getData().getJcjgBh();
                    ybBh = xResultData.getData().getYbZj();
                    //回显数据
                    if (!TextUtils.isEmpty(xResultData.getData().getZsZl())) {
                        binding.etWeight.setText(StrUtils.getPoint(xResultData.getData().getZsZl()));
                    }
                    if (xResultData.getData().getXqList() != null && xResultData.getData().getXqList().size() > 0) {
                        binding.recycler.setAdapter(new WsJcProInfoAdapter(JianCeMsgActivity.this, xResultData.getData().getXqList()));
                        binding.recycler.setVisibility(View.VISIBLE);
                        binding.tvJcw.setVisibility(View.GONE);
                        binding.view1.setVisibility(View.GONE);
                        if (xieYiShiEvent == null) {
                            xieYiShiEvent = new XieYiShiEventEntity();
                            xieYiShiEvent.setWsJcProBeanList(xResultData.getData().getXqList());
                        }
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.bt_submit) {
            //数据提交
            if (ButtonUtils.isFastClick()) {
                postData();
            }
        } else if (v.getId() == R.id.tv_jcw) {
            //检测物
            Bundle bundle = new Bundle();
            bundle.putString("ybBh", ybhLb);
            bundle.putString("scJg", scJg);
            XIntentUtil.redirectToNextActivity(this, JianCeProAddActivity.class, bundle);
        }
    }

    /**
     * 数据提交
     */
    private void postData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.SEWAGE_WS_YB_JCW_SAVE);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");

        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("jcjgBh", jcjgBh);
            objectMap.put("cySj", binding.top.tvCyTime.getText().toString());
            objectMap.put("zsZl", binding.etWeight.getText().toString());
            objectMap.put("sjLy", "3");
            objectMap.put("ybBh", ybBh);
            objectMap.put("cydBh", cydBh);
            objectMap.put("rwBh", rwBh);
            //
            JSONArray jsonArray = new JSONArray();
            if (xieYiShiEvent != null && xieYiShiEvent.getWsJcProBeanList() != null && xieYiShiEvent.getWsJcProBeanList().size() > 0) {
                for (int i = 0; i < xieYiShiEvent.getWsJcProBeanList().size(); i++) {
                    JSONObject jsonObject = new JSONObject();
                    if (!TextUtils.isEmpty(xieYiShiEvent.getWsJcProBeanList().get(i).getBh())) {
                        jsonObject.put("bh", xieYiShiEvent.getWsJcProBeanList().get(i).getBh());
                    }
                    jsonObject.put("mbwBh", xieYiShiEvent.getWsJcProBeanList().get(i).getMbwBh());
                    jsonObject.put("mbwMc", xieYiShiEvent.getWsJcProBeanList().get(i).getMbwMc());
                    jsonObject.put("ndz", xieYiShiEvent.getWsJcProBeanList().get(i).getNdz());
                    jsonObject.put("qrjxfl", xieYiShiEvent.getWsJcProBeanList().get(i).getQrjxfl());
                    jsonArray.put(jsonObject);
                }
            }
            if (jsonArray.length() <= 0) {
                XToastUtil.showToast(this, "请选择检测物");
                return;
            }
            objectMap.put("xqList", jsonArray);
        } catch (JSONException e) {
        }
        binding.btSubmit.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), true, true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(JianCeMsgActivity.this, getString(R.string.sub_sucess));
                    EventBus.getDefault().post(new TalkListEntity());
                    finish();
                } else {
                    XToastUtil.showToast(JianCeMsgActivity.this, xResultData.getErrorInfo());
                    binding.btSubmit.setEnabled(true);
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSubmit.setEnabled(true);
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(XieYiShiEventEntity item) {
        if (item != null) {
            xieYiShiEvent = item;
            binding.recycler.setAdapter(new WsJcProInfoAdapter(this, item.getWsJcProBeanList()));
            binding.recycler.setVisibility(View.VISIBLE);
            binding.tvJcw.setVisibility(View.GONE);
            binding.view1.setVisibility(View.GONE);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

}