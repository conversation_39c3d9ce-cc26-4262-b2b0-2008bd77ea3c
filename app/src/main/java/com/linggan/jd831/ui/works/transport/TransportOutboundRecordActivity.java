package com.linggan.jd831.ui.works.transport;

import android.Manifest;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.databinding.ActivityTransportOutboundRecordBinding;
import com.linggan.jd831.ui.works.transport.model.TransportDetail;
import com.linggan.jd831.ui.works.transport.model.TransportOutboundRecord;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.GpsLocationService;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.ReverseGeocoder;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.XHttpUtils;
import com.tencent.bugly.crashreport.CrashReport;

import org.xutils.http.RequestParams;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class TransportOutboundRecordActivity extends XBaseActivity<ActivityTransportOutboundRecordBinding> {

    private ImageAddUtil imageAddImg, imageAddManifest;
    private Uri photoUri;
    private File cameraPhoto;
    private int from = 0;
    private String longitude, latitude, address;
    private TransportOutboundRecord originalRecord;

    @Override
    protected ActivityTransportOutboundRecordBinding getViewBinding() {
        return ActivityTransportOutboundRecordBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setTitle("出库记录");
        initViews();
        initLocation();

        // 获取传入的记录数据
        TransportOutboundRecord record = (TransportOutboundRecord) getIntent().getSerializableExtra("record");
        if (record != null) {
            // 恢复之前的数据
            restoreRecordData(record);
        }

        requestPermission(new String[]{
                Manifest.permission.ACCESS_COARSE_LOCATION,
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.READ_PHONE_STATE
        });

    }

    // 修改恢复数据的方法
    private void restoreRecordData(TransportOutboundRecord record) {
        try {
            // 保存原始记录
            this.originalRecord = record;

            // 恢复出库图片
            if (record.getOutboundPaths() != null) {
                imageAddImg = new ImageAddUtil(this, binding.gridImg);
                imageAddImg.setMax(9);
                imageAddImg.setOnImageAddListener(() -> {
                    from = 1;
                    requestPermission(new String[]{Manifest.permission.CAMERA,
                            Manifest.permission.WRITE_EXTERNAL_STORAGE,
                            Manifest.permission.READ_EXTERNAL_STORAGE});
                });

                // 将 PhotoInfo 转换为 OssFileEntity 用于显示
                for (TransportDetail.PhotoInfo entity : record.getOutboundPaths()) {
                    OssFileEntity ossFile = new OssFileEntity();
                    ossFile.setMc(entity.getMc());
                    ossFile.setSavePath(entity.getLj());
                    ossFile.setFileSize(entity.getDx());
                    ossFile.setOriginName(entity.getMc());
                    imageAddImg.addImage(ossFile);
                }
                imageAddImg.notifyData();
            }

            // 恢复清单图片
            if (record.getManifestPaths() != null) {
                imageAddManifest = new ImageAddUtil(this, binding.gridVideo);
                imageAddManifest.setMax(9);
                imageAddManifest.setOnImageAddListener(() -> {
                    from = 2;
                    requestPermission(new String[]{Manifest.permission.CAMERA,
                            Manifest.permission.WRITE_EXTERNAL_STORAGE,
                            Manifest.permission.READ_EXTERNAL_STORAGE});
                });

                // 将 PhotoInfo 转换为 OssFileEntity 用于显示
                for (TransportDetail.PhotoInfo entity : record.getManifestPaths()) {
                    OssFileEntity ossFile = new OssFileEntity();
                    ossFile.setMc(entity.getMc());
                    ossFile.setSavePath(entity.getLj());
                    ossFile.setFileSize(entity.getDx());
                    ossFile.setOriginName(entity.getMc());
                    imageAddManifest.addImage(ossFile);
                }
                imageAddManifest.notifyData();
            }

            // 恢复定位信息
            longitude = record.getLongitude();
            latitude = record.getLatitude();
            address = record.getAddress();
            if (!TextUtils.isEmpty(address)) {
                binding.tvSignArea.setText(address);
            }
        } catch (Exception e) {
            e.printStackTrace();
            XToastUtil.showToast(this, "恢复数据失败");
        }
    }

    private void initViews() {
        createFileMuLu();

        // 初始化出库图片上传控件
        imageAddImg = new ImageAddUtil(this, binding.gridImg);
        imageAddImg.setMax(9);
        imageAddImg.setOnImageAddListener(() -> {
            from = 1;
            requestPermission(new String[]{Manifest.permission.CAMERA,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE,
                    Manifest.permission.READ_EXTERNAL_STORAGE});
        });

        // 初始化清单图片上传控件
        imageAddManifest = new ImageAddUtil(this, binding.gridVideo);
        imageAddManifest.setMax(9);
        imageAddManifest.setOnImageAddListener(() -> {
            from = 2;
            requestPermission(new String[]{Manifest.permission.CAMERA,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE,
                    Manifest.permission.READ_EXTERNAL_STORAGE});
        });

        // 确定按钮点击事件
        binding.btSure.setOnClickListener(v -> {
            if (ButtonUtils.isFastClick()) {
                if (address == null || address.isEmpty()) {
                    XToastUtil.showToast(this, "请点击定位图标，重新获取出库地址");
                    return;
                }

                if (imageAddImg == null || imageAddImg.getPaths() == null || imageAddImg.getPaths().isEmpty()) {
                    XToastUtil.showToast(this, "请上传出库图片");
                    return;
                }
                if (imageAddManifest == null || imageAddManifest.getPaths() == null || imageAddManifest.getPaths().isEmpty()) {
                    XToastUtil.showToast(this, "请上传清单图片");
                    return;
                }

                if (latitude == null || longitude == null) {
                    initLocation();
                    XToastUtil.showToast(this, "你尚未开启手机中的《位置信息》功能，请打开位置服务功能");
                    return;
                }

                if (TextUtils.isEmpty(latitude) || TextUtils.isEmpty(longitude)) {
                    initLocation();
                    XToastUtil.showToast(this, "你尚未开启手机中的《位置信息》功能，请打开位置服务功能");
                    return;
                }
                submitData();
            }
        });
        binding.tvSignArea.setOnClickListener(v -> {
            if (ButtonUtils.isFastClick()) {
                initLocation();
            }
        });
    }

    private void createFileMuLu() {
        try {
            // 使用更可靠的方式创建临时文件
            String fileName = "camera_photo_" + System.currentTimeMillis() + ".jpg";
            File storageDir = getExternalFilesDir(android.os.Environment.DIRECTORY_PICTURES);
            cameraPhoto = new File(storageDir, fileName);

            if (cameraPhoto.exists()) {
                cameraPhoto.delete();
            }
            cameraPhoto.createNewFile();
        } catch (IOException e) {
            e.printStackTrace();
            XToastUtil.showToast(this, "创建临时件失败");
        }
    }

    private void submitData() {
        try {
            TransportOutboundRecord record = new TransportOutboundRecord();

            // 处理出库图片
            if (imageAddImg != null && imageAddImg.getPaths() != null) {
                List<TransportDetail.PhotoInfo> outboundPaths = new ArrayList<>();
                List<String> deletedOutboundBhs = new ArrayList<>();

                // 先检查哪些原始图片被删除了
                if (originalRecord != null && originalRecord.getOutboundPaths() != null) {
                    for (TransportDetail.PhotoInfo originalPhoto : originalRecord.getOutboundPaths()) {
                        boolean found = false;
                        // 检查原图片是否还在当前列表中
                        for (OssFileEntity currentFile : imageAddImg.getPaths()) {
                            if (currentFile.getSavePath().equals(originalPhoto.getLj())) {
                                found = true;
                                break;
                            }
                        }
                        // 如果原图片不在当前列表中，说明被删除了
                        if (!found && originalPhoto.getBh() != null) {
                            deletedOutboundBhs.add(originalPhoto.getBh());
                        }
                    }
                }

                // 遍历当前显示的图片
                for (OssFileEntity ossFile : imageAddImg.getPaths()) {
                    TransportDetail.PhotoInfo photoInfo = new TransportDetail.PhotoInfo();

                    // 设置新图片的内容
                    photoInfo.setMc(ossFile.getOriginName());
                    photoInfo.setLj(ossFile.getSavePath());
                    photoInfo.setDx(ossFile.getFileSize());
                    String fileName = ossFile.getOriginName();
                    if (fileName != null && fileName.contains(".")) {
                        photoInfo.setHz(fileName.substring(fileName.lastIndexOf(".") + 1));
                    }

                    // 如果是原有图片，保留其bh
                    if (originalRecord != null && originalRecord.getOutboundPaths() != null) {
                        for (TransportDetail.PhotoInfo originalPhoto : originalRecord.getOutboundPaths()) {
                            if (ossFile.getSavePath().equals(originalPhoto.getLj())) {
                                photoInfo.setBh(originalPhoto.getBh());
                                break;
                            }
                        }
                    }

                    outboundPaths.add(photoInfo);
                }
                record.setOutboundPaths(outboundPaths);
                record.setDeletedOutboundBhs(deletedOutboundBhs);
            }

            // 处理清单图片
            if (imageAddManifest != null && imageAddManifest.getPaths() != null) {
                List<TransportDetail.PhotoInfo> manifestPaths = new ArrayList<>();
                List<String> deletedManifestBhs = new ArrayList<>();

                // 先检查哪些原始图片被删除了
                if (originalRecord != null && originalRecord.getManifestPaths() != null) {
                    for (TransportDetail.PhotoInfo originalPhoto : originalRecord.getManifestPaths()) {
                        boolean found = false;
                        // 检查原图片是否还在当前列表中
                        for (OssFileEntity currentFile : imageAddManifest.getPaths()) {
                            if (currentFile.getSavePath().equals(originalPhoto.getLj())) {
                                found = true;
                                break;
                            }
                        }
                        // 如果原图片不在当前列表中，说明被删除了
                        if (!found && originalPhoto.getBh() != null) {
                            deletedManifestBhs.add(originalPhoto.getBh());
                        }
                    }
                }

                // 遍历当前显示的图片
                for (OssFileEntity ossFile : imageAddManifest.getPaths()) {
                    TransportDetail.PhotoInfo photoInfo = new TransportDetail.PhotoInfo();

                    // 设置新图片的内容
                    photoInfo.setMc(ossFile.getOriginName());
                    photoInfo.setLj(ossFile.getSavePath());
                    photoInfo.setDx(ossFile.getFileSize());
                    String fileName = ossFile.getOriginName();
                    if (fileName != null && fileName.contains(".")) {
                        photoInfo.setHz(fileName.substring(fileName.lastIndexOf(".") + 1));
                    }

                    // 如果是原有图片，保留其bh
                    if (originalRecord != null && originalRecord.getManifestPaths() != null) {
                        for (TransportDetail.PhotoInfo originalPhoto : originalRecord.getManifestPaths()) {
                            if (ossFile.getSavePath().equals(originalPhoto.getLj())) {
                                photoInfo.setBh(originalPhoto.getBh());
                                break;
                            }
                        }
                    }

                    manifestPaths.add(photoInfo);
                }
                record.setManifestPaths(manifestPaths);
                record.setDeletedManifestBhs(deletedManifestBhs);
            }

            // 添加定位信息
            record.setLongitude(longitude);
            record.setLatitude(latitude);
            record.setAddress(address);

            // 返回数据给上一个页面
            Intent resultIntent = new Intent();
            resultIntent.putExtra("record", record);
            setResult(RESULT_OK, resultIntent);
            finish();

        } catch (Exception e) {
            e.printStackTrace();
            XToastUtil.showToast(this, "保存数据失败");
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            if (requestCode == 1 || requestCode == 2) {
                try {
                    boolean isFinish = XFileUtil.bitmapToFilePath(this,
                            BitmapFactory.decodeFile(cameraPhoto.getAbsolutePath()),
                            photoUri, cameraPhoto);
                    if (isFinish) {
                        uploadFile(cameraPhoto.getAbsolutePath(), requestCode);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void uploadFile(String path, int requestCode) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));

        XHttpUtils.uploadFile(this, requestParams,
                DialogUtils.showLoadDialog(this, getString(R.string.uploading)),
                new XHttpResponseCallBack() {
                    @Override
                    public void onSuccess(String result) {
                        XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result,
                                new TypeToken<XResultData<List<OssFileEntity>>>() {
                                }.getType());
                        if (xResultData.getStatus() == 0) {
                            if (xResultData.getData() != null && !xResultData.getData().isEmpty()) {
                                OssFileEntity fileEntity = xResultData.getData().get(0);
                                // 直接使用 OssFileEntity
                                if (requestCode == 1) {
                                    imageAddImg.addImage(fileEntity);
                                    imageAddImg.notifyData();
                                } else {
                                    imageAddManifest.addImage(fileEntity);
                                    imageAddManifest.notifyData();
                                }
                            }
                        } else {
                            XToastUtil.showToast(TransportOutboundRecordActivity.this,
                                    xResultData.getErrorInfo());
                        }
                    }

                    @Override
                    public void onFailed(int code, String failedMsg) {
                    }

                    @Override
                    public void onFinished() {
                    }
                });
    }

    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (from == 1 || from == 2) {
            createFileMuLu();
            if (cameraPhoto != null && cameraPhoto.exists()) {
                try {
                    Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                    photoUri = XFileUtil.getUriFromFile(this, cameraPhoto);
                    if (photoUri != null) {
                        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                        intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                        startActivityForResult(intent, from);
                    } else {
                        XToastUtil.showToast(this, "创建文件 URI 失败");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    XToastUtil.showToast(this, "启动相机失败");
                }
            }
        }
    }

    @Override
    protected void initView() {
    }

    @Override
    protected void initListener() {
    }

    @Override
    protected void getData() {
    }

    private void initLocation() {
        gpsLocation();

//        AMapLocationClient client;
//        try {
//            client = new AMapLocationClient(getApplicationContext());
//        } catch (Exception e){
//            XToastUtil.showToast(this, "定位初始化失败");
//            return;
//        }
////        AMapLocationClient client = new AMapLocationClient(XBaseApp.instance());
//        AMapLocationClientOption option = MapUtils.getDefaultOption();
//        option.setMockEnable(true);
//        client.setLocationOption(option);
//        client.setLocationListener(aMapLocation -> {
//            if (aMapLocation != null && aMapLocation.getErrorCode() == 0) {
//                longitude = String.valueOf(aMapLocation.getLongitude());
//                latitude = String.valueOf(aMapLocation.getLatitude());
//                // 如果经纬度不为空则设置 调用
//                if (!TextUtils.isEmpty(aMapLocation.getAddress())) {
//                    address = aMapLocation.getAddress();
//                    binding.tvSignArea.setText(address);
//                } else {
//                    if (aMapLocation.getLongitude() > 0 && aMapLocation.getLatitude() > 0) {
//                        try {
//                            getLocation(aMapLocation.getLongitude(), aMapLocation.getLatitude());
//                        } catch (Exception e) {
////                            binding.tvLocation.setText("当前位置无地图地址");
//                            throw new RuntimeException(e);
//                        }
//                    } else {
////                        binding.tvLocation.setText("当前位置无地图地址");
//                    }
//                }
//
//                // 更新UI显示
//
//                client.stopLocation();
//            } else {
//                // 定位失败时使用GPS定位
//                gpsLocation();
//            }
//        });
//        client.startLocation();
    }

    private void getLocation(double amapLongitude, double amapLatitude) throws Exception {
        ReverseGeocoder geocoder = new ReverseGeocoder(this);
        String jwd = amapLongitude + "," + amapLatitude;
        geocoder.getLocation(jwd, new ReverseGeocoder.OnReverseGeocodeListener() {
            @Override
            public void onSuccess(String addressResult) {
                // 定位信息
                address = addressResult;
                binding.tvSignArea.setText(addressResult);
            }

            @Override
            public void onError(String errorMessage) {
                // 处理错误情况
                binding.tvSignArea.setText(errorMessage);
            }
        });
    }

    private void gpsLocation() {
        GpsLocationService.getInstance(this).requestSingleLocation(new GpsLocationService.LocationCallback() {
            @Override
            public void onLocationReceived(String[] convertedLocation) {
                // convertedLocation[0]是经度, convertedLocation[1]是纬度
                longitude = convertedLocation[0];
                latitude = convertedLocation[1];
                if (longitude != null && latitude != null) {
                    try {
                        getLocation(Double.parseDouble(longitude), Double.parseDouble(latitude));
                    } catch (Exception e) {
                        XToastUtil.showToast(TransportOutboundRecordActivity.this, "定位失败");
                    }
                }

                // 保持原有的UI显示
//                binding.tvSignArea.setText(address);
            }

            @Override
            public void onLocationFailed(String error) {
                if (!TextUtils.isEmpty(longitude)) {
                    try {
                        CrashReport.postCatchedException(new IllegalArgumentException("运输定位异常：" +error));
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                    XToastUtil.showToast(TransportOutboundRecordActivity.this, "GPS信号弱，定位失败");
                    return;
                }
                XToastUtil.showToast(TransportOutboundRecordActivity.this, error);
            }
        });
    }
}
