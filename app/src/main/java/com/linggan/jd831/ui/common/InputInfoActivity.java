package com.linggan.jd831.ui.common;

import android.text.InputFilter;
import android.text.TextUtils;

import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.utils.XAppUtil;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.InputEntity;
import com.linggan.jd831.databinding.ActivityInputInfoBinding;
import com.linggan.jd831.utils.EditLengthFilter;

import org.greenrobot.eventbus.EventBus;

/**
 * 类  名：录入通用页面
 * 作  者：zxb
 * 说  明：标题，输入内容
 * 时  间：2022/9/13  10:17
 * 版  权：凌感科技
 */
public class InputInfoActivity extends XBaseActivity<ActivityInputInfoBinding> {//implements INativeNuiCallback
    private String hint, info, len, info1;
    private int tabFrom;
    //    //语音转文字
//    private NativeNui nui_instance = new NativeNui();
//    private AudioRecord mAudioRecorder;
//    private int WAVE_FRAM_SIZE = 20 * 2 * 1 * 16000 / 1000;
    private int SAMPLE_RATE = 16000, retYyCode = -1, pos = -1;
//    private HandlerThread mHanderThread;
//    private Handler mHandler;
//    private String TAG = "gpt", yy;
//    private String[] permissions = {Manifest.permission.RECORD_AUDIO};
//    private StringBuffer stringBuffer = new StringBuffer();

    @Override
    protected ActivityInputInfoBinding getViewBinding() {
        return ActivityInputInfoBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        binding.baseTop.tvRight.setText(getString(R.string.save));
        binding.baseTop.tvRight.setTextColor(getResources().getColor(R.color.color_main));

        //判断是否有语音
//        yy = getIntent().getStringExtra("yy");
        pos = getIntent().getIntExtra("pos", -1);
//        if (!TextUtils.isEmpty(yy)) {
////            授权语音
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
//                int i = ContextCompat.checkSelfPermission(this, permissions[0]);
//                if (i != PackageManager.PERMISSION_GRANTED) {
//                    this.requestPermissions(permissions, 321);
//                }
//                while (true) {
//                    i = ContextCompat.checkSelfPermission(this, permissions[0]);
//                    if (i == PackageManager.PERMISSION_GRANTED) break;
//                }
//            }
//            if (CommonUtils.copyAssetsData(this)) {
//                Log.i(TAG, "copy assets data done");
//            } else {
//                Log.i(TAG, "copy assets failed");
//                return;
//            }
//            //
//            mHanderThread = new HandlerThread("process_thread");
//            mHanderThread.start();
//            mHandler = new Handler(mHanderThread.getLooper());
//            //
//            binding.ivYy.setVisibility(View.VISIBLE);
//        }
    }

    @Override
    protected void onStart() {
        super.onStart();
//        if (!TextUtils.isEmpty(yy)) {
//            initYuYin();
//        }
    }

    @Override
    protected void getData() {
        String title = getIntent().getStringExtra("title");
        hint = getIntent().getStringExtra("hint");
        info = getIntent().getStringExtra("info");
        len = getIntent().getStringExtra("len");
        info1 = getIntent().getStringExtra("info1");
        tabFrom = getIntent().getIntExtra("tab", 0);
        binding.tvTitle.setText(title);
        binding.etInfo.setHint(hint);
        if (!TextUtils.isEmpty(info)) {
            binding.etInfo.setText(info);
            binding.etInfo.setSelection(info.length());//光标移动到最后一位
        }
        if (!TextUtils.isEmpty(len)) {
            if (len.equals("50")) {
                binding.etInfo.setFilters(new InputFilter[]{new EditLengthFilter(50, this)});
            } else if (len.equals("100")) {
                binding.etInfo.setFilters(new InputFilter[]{new EditLengthFilter(100, this)});
            } else if (len.equals("500")) {
                binding.etInfo.setFilters(new InputFilter[]{new EditLengthFilter(500, this)});
            } else if (len.equals("1000")) {
                binding.etInfo.setFilters(new InputFilter[]{new EditLengthFilter(1000, this)});
            } else if (len.equals("200-500")) {
                binding.etInfo.setFilters(new InputFilter[]{new EditLengthFilter(500, this)});
            } else if (len.equals("100-200")) {
                binding.etInfo.setFilters(new InputFilter[]{new EditLengthFilter(200, this)});
            } else if (len.equals("10-max")) {

            } else {
                binding.etInfo.setFilters(new InputFilter[]{new EditLengthFilter(200, this)});
            }
        } else {
            binding.etInfo.setFilters(new InputFilter[]{new EditLengthFilter(200, this)});
        }
    }


    @Override
    protected void initListener() {
        binding.baseTop.tvRight.setOnClickListener(v -> {
//        binding.btSave.setOnClickListener(v -> {
            XAppUtil.closeSoftInput(this);
            //保存
            if (TextUtils.isEmpty(binding.etInfo.getText().toString()) && TextUtils.isEmpty(info)) {
                XToastUtil.showToast(this, hint);
                return;
            }
            if (TextUtils.equals(len, "10-max") && binding.etInfo.getText().toString().length() < 10) {
                XToastUtil.showToast(this, "最少需要输入10个字");
                return;
            }
            if (TextUtils.equals(len, "200-500") && binding.etInfo.getText().toString().length() < 200) {
                XToastUtil.showToast(this, "最少需要输入200个字");
                return;
            }
            if (TextUtils.equals(len, "100-200") && binding.etInfo.getText().toString().length() < 100) {
                XToastUtil.showToast(this, "最少需要输入100个字");
                return;
            }
            InputEntity inputEntity = new InputEntity(binding.etInfo.getText().toString(), info1, tabFrom, pos);
            EventBus.getDefault().post(inputEntity);
            XAppUtil.closeSoftInput(this);
            finish();
        });
//        binding.btYy.setOnClickListener(v -> {
//            XAppUtil.closeSoftInput(this);
//            //开启语音转文字
//            if (retYyCode != Constants.NuiResultCode.SUCCESS) {
//                XToastUtil.showToast(this, YuYinUtils.getMsgWithErrorCode(retYyCode, "init"));
//            } else {
//                startDialog();
//                DialogUtils.showYuYinDialog1(InputInfoActivity.this, (code, id) -> {
//                    Log.i(TAG, "停止录音");
//                    cancelDialog();
//                });
//            }
//        });
    }

    /**
     * 初始化语音
     */
    private void initYuYin() {
        //获取工作路径
//        String assets_path = CommonUtils.getModelPath(this);
//        String yuYinPath = getExternalCacheDir().getAbsolutePath() + "/debug_" + System.currentTimeMillis();
//        YuYinUtils.createDir(yuYinPath);
//        mAudioRecorder = new AudioRecord(MediaRecorder.AudioSource.DEFAULT, SAMPLE_RATE, AudioFormat.CHANNEL_IN_MONO, AudioFormat.ENCODING_PCM_16BIT, WAVE_FRAM_SIZE * 4);
//        retYyCode = nui_instance.initialize(this, genInitParams(assets_path, yuYinPath), Constants.LogLevel.LOG_LEVEL_VERBOSE, true);
//        Log.i("gpt", "result = " + retYyCode);
//        if (retYyCode != Constants.NuiResultCode.SUCCESS) {
//            MdFactoryUtils.mdSave("语音初始化报错", "错误码：" + retYyCode, "");
//        }
//        nui_instance.setParams(genParams());
    }

//    @Override
//    public void onNuiEventCallback(Constants.NuiEvent nuiEvent, int resultCode, int arg2, KwsResult kwsResult, AsrResult asrResultS) {
//        if (nuiEvent == Constants.NuiEvent.EVENT_SENTENCE_START) {
//        } else if (nuiEvent == Constants.NuiEvent.EVENT_SENTENCE_END) {
//            Log.i(TAG, "EVENT_SENTENCE_END: " + asrResultS.asrResult);//输出结果
//            runOnUiThread(() -> {//不能再主线程执行
//                YuYinResultBean yinResultBean = new Gson().fromJson(asrResultS.asrResult, new TypeToken<YuYinResultBean>() {
//                }.getType());
//                if (yinResultBean != null && yinResultBean.getPayload() != null) {
//                    if (!TextUtils.isEmpty(yinResultBean.getPayload().getResult())) {
//                        stringBuffer.append(yinResultBean.getPayload().getResult());
//                        stringBuffer.append("，");
//                    }
//                }
//            });
//        } else if (nuiEvent == Constants.NuiEvent.EVENT_ASR_ERROR) {
//            Log.i(TAG, "EVENT_ASR_ERROR: " + asrResultS.asrResult);
//        } else if (nuiEvent == Constants.NuiEvent.EVENT_TRANSCRIBER_COMPLETE) {
//            runOnUiThread(() -> {//不能再主线程执行
//                if (!TextUtils.isEmpty(stringBuffer.toString())) {
//                    if (TextUtils.isEmpty(binding.etInfo.getText().toString())) {
//                        int la = stringBuffer.toString().lastIndexOf("，");
//                        String last = stringBuffer.toString().substring(0, la) + "。";
//                        binding.etInfo.setText(last);
//                        binding.etInfo.setSelection(last.length());//光标移动到最后
//                    } else {//有数据时候插入到具体位置
//                        int la = stringBuffer.toString().lastIndexOf("，");
//                        String last = stringBuffer.toString().substring(0, la) + "。";
//                        int cursorPosition = binding.etInfo.getSelectionStart();
//                        Editable editableText = binding.etInfo.getText();
//                        editableText.insert(cursorPosition, last);
//                    }
//                    stringBuffer.setLength(0);
//                } else {
//                    XToastUtil.showToast(InputInfoActivity.this, "说话时间太短或未检测到语音");
//                }
//            });
//        }
//    }
//
//    @Override
//    public int onNuiNeedAudioData(byte[] bytes, int len) {
//        int ret = 0;
//        if (mAudioRecorder.getState() != AudioRecord.STATE_INITIALIZED) {
//            return -1;
//        }
//        ret = mAudioRecorder.read(bytes, 0, len);
//        return ret;
//    }
//
//    @Override
//    public void onNuiAudioStateChanged(Constants.AudioState state) {
//        Log.i(TAG, "onNuiAudioStateChanged" + state);
//        if (state == Constants.AudioState.STATE_OPEN) {
//            mAudioRecorder.startRecording();
//        } else if (state == Constants.AudioState.STATE_CLOSE) {
//            mAudioRecorder.release();
//        } else if (state == Constants.AudioState.STATE_PAUSE) {
//            mAudioRecorder.stop();
//        }
//    }
//
//    @Override
//    public void onNuiAudioRMSChanged(float v) {
//    }
//
//    @Override
//    public void onNuiVprEventCallback(Constants.NuiVprEvent nuiVprEvent) {
//    }
//
//    /**
//     * 授权
//     *
//     * @param workpath
//     * @param debugpath
//     * @return
//     */
//    private String genInitParams(String workpath, String debugpath) {
//        String str = "";
//        try {
//            //获取token方式：
//            JSONObject object = new JSONObject();
//            object.put("app_key", XConstantUtils.ALY_YY_KEY); // 必填
//            object.put("token", XShareCacheUtils.getInstance().getString(XConstantUtils.ALY_YY_TOKEN)); // 必填
//            object.put("device_id", YuYinUtils.getDeviceId()); // 必填, 推荐填入具有唯一性的id, 方便定位问题
//            object.put("url", "wss://nls-gateway.cn-shanghai.aliyuncs.com:443/ws/v1"); // 默认
//            object.put("workspace", workpath);
//            object.put("debug_path", debugpath);
//            object.put("service_mode", Constants.ModeFullCloud); // 必填
//            str = object.toString();
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//        return str;
//    }
//
//    /**
//     * 语音配置参数
//     *
//     * @return
//     */
//    private String genParams() {
//        String params = "";
//        try {
//            JSONObject nls_config = new JSONObject();
//            nls_config.put("enable_intermediate_result", true);
//            JSONObject tmp = new JSONObject();
//            tmp.put("nls_config", nls_config);
//            tmp.put("service_type", Constants.kServiceTypeSpeechTranscriber); // 必填
//            params = tmp.toString();
//        } catch (JSONException e) {
//        }
//        return params;
//    }
//
//    private String genDialogParams() {
//        String params = "";
//        try {
//            JSONObject dialog_param = new JSONObject();
//            params = dialog_param.toString();
//        } catch (Exception e) {
//        }
//        return params;
//    }
//
//    /**
//     * 开始识别
//     */
//    private void startDialog() {
//        mHandler.post(() -> {
//            int ret = nui_instance.startDialog(Constants.VadMode.TYPE_P2T, genDialogParams());
//            Log.i("fff", "startDialog: " + ret);
//        });
//    }
//
//    /**
//     * 取消识别
//     */
//    private void cancelDialog() {
//        mHandler.post(() -> {
//            if (nui_instance != null) {
//                nui_instance.stopDialog();
//            }
//        });
//    }
//
//    @Override
//    protected void onStop() {
//        super.onStop();
//        if (nui_instance != null) {
//            nui_instance.release();
//        }
//    }
}
