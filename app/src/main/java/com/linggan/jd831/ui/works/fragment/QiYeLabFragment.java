package com.linggan.jd831.ui.works.fragment;


import android.app.Activity;
import android.app.Dialog;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewpager.widget.ViewPager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseFragment;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XViewUtil;
import com.lgfzd.base.view.XViewPagerAdapter;
import com.lgfzd.base.view.recycle.XRecyclerView;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.PingJiListHolder;
import com.linggan.jd831.adapter.QiYeIndexListHolder;
import com.linggan.jd831.bean.BaiBeiListEntity;
import com.linggan.jd831.bean.BaoBeiTsBean;
import com.linggan.jd831.bean.CksBean;
import com.linggan.jd831.bean.JieTuBean;
import com.linggan.jd831.bean.QiYeLabInfoEntity;
import com.linggan.jd831.bean.SxtBean;
import com.linggan.jd831.ui.works.qiye.v2.JkCenterActivity;
import com.linggan.jd831.ui.works.qiye.v2.ZhuaPaiScActivity;
import com.linggan.jd831.ui.works.qiye.v2.alert.AlertListActivity;
import com.linggan.jd831.ui.works.qiye.v2.lab.PersonListActivity;
import com.linggan.jd831.ui.works.qiye.v2.region.RegionListActivity;
import com.linggan.jd831.ui.works.qiye.v2.warehouse.WareHouseListActivity;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.utils.XImageUtils;
import com.makeramen.roundedimageview.RoundedImageView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONObject;
import org.xutils.http.RequestParams;
import org.xutils.view.annotation.ContentView;
import org.xutils.view.annotation.ViewInject;

import java.io.Serializable;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 类  名：易制毒企业主页面
 * 作  者：LGKJ
 * 说  明： //仓管员：出库登记、入库登记、报损申请
 * //信息员：登记确认、报损确认、注销申请
 * 时  间：2024/3/27 15:28
 * 版  权：凌感科技
 */
@ContentView(R.layout.fragment_manger)
public class QiYeLabFragment extends XBaseFragment {

    @ViewInject(R.id.recycle_mag)
    private XRecyclerView mRecycleMag;
    @ViewInject(R.id.tv_manager_unit)
    private TextView mTvManagerUnit;
    @ViewInject(R.id.tv_dj)
    private TextView tvDj;
    @ViewInject(R.id.tv_dj_df)
    private TextView tvDjDf;
    @ViewInject(R.id.mSwipe)
    private SwipeRefreshLayout swipeRefreshLayout;
    @ViewInject(R.id.mNested)
    private NestedScrollView nestedScrollView;
    private Dialog mDialog;
    private String sysBh;
    @ViewInject(R.id.recycle_dj)
    private XRecyclerView mRecycleDj;
    @ViewInject(R.id.tv_bb_bt)
    private TextView tvBbBt;
    @ViewInject(R.id.tv_pl_bt)
    private TextView tvPlBt;
    @ViewInject(R.id.iv_sxt)
    private ImageView ivSxt;

    private TextView tvKssj, tvjssj, btnSure, tvTitle, tvZpTime, btSx, tvCkmc;
    private ImageView btCha;
    private RoundedImageView dialogImg;
    private int curIndex = 0;
    private String ckbh;

    @ViewInject(R.id.ll_lab_sxt)
    private LinearLayout llLabSxt;
    private TextView tvInstalled, tvUninstalled;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        init(view);
        mRecycleMag.getRecyclerView().setLayoutManager(new LinearLayoutManager(getActivity()));
        mRecycleMag.getAdapter().bindHolder(new QiYeIndexListHolder());
        mRecycleDj.getRecyclerView().setLayoutManager(new LinearLayoutManager(getActivity()));
        mRecycleDj.getAdapter().bindHolder(new PingJiListHolder());
        swipeRefreshLayout.setColorSchemeColors(Color.BLUE);
        tvBbBt.setVisibility(View.GONE);
        tvPlBt.setVisibility(View.GONE);
        //实现滑倒顶端才能下拉刷新（即解决滑动冲突）
        nestedScrollView.getViewTreeObserver().addOnScrollChangedListener(() -> swipeRefreshLayout.setEnabled(nestedScrollView.getScrollY() == 0));
        swipeRefreshLayout.setOnRefreshListener(() -> {
            swipeRefreshLayout.setRefreshing(false);
            lazyLoad();
        });
    }

    public void init(View view) {
        tvDj.setVisibility(View.GONE);

        llLabSxt.setVisibility(View.VISIBLE);

        tvInstalled = new TextView(getContext());
        tvInstalled.setTextSize(25);
        Typeface typeface = Typeface.create(Typeface.DEFAULT, Typeface.BOLD);
        tvInstalled.setTypeface(typeface);  //         加粗
        tvInstalled.setTextColor(Color.parseColor("#FFFFFF"));
        LinearLayout.LayoutParams paramsInstalled = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        llLabSxt.addView(tvInstalled, paramsInstalled);

        tvUninstalled = new TextView(getContext());
        tvUninstalled.setTextSize(14);
        tvUninstalled.setTextColor(Color.parseColor("#FFFFFF"));
        LinearLayout.LayoutParams paramsUninstalled = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        paramsUninstalled.topMargin = 8;
        llLabSxt.addView(tvUninstalled, paramsUninstalled);

        // 实验室
        View laboratoryMenu = LayoutInflater.from(getContext()).inflate(R.layout.layout_ren_yuan_menu, null);
        LinearLayout container = getView().findViewById(R.id.menu_container);
        container.removeAllViews();
        container.addView(laboratoryMenu);
        setupRenYuanButtonListeners(view);
    }

    // 消息提醒事件
    private void setupMessageButtonListeners(View view) {
        // 1. 基础检查
        if (view == null || getActivity() == null || TextUtils.isEmpty(sysBh)) {
            return;
        }

        // 2. 获取View引用
        View rygkRemind = view.findViewById(R.id.rygk_remind);
        View yjRemind = view.findViewById(R.id.yj_remind);
        View regionRemind = view.findViewById(R.id.region_remind);

        if (rygkRemind == null || yjRemind == null || regionRemind == null) {
            return;
        }

        // 3. 创建弱引用
        final WeakReference<View> rygkRemindRef = new WeakReference<>(rygkRemind);
        final WeakReference<View> yjRemindRef = new WeakReference<>(yjRemind);
        final WeakReference<View> regionRemindRef = new WeakReference<>(regionRemind);

        // 4. 发送请求
        try {
            // 人员管理提醒
//            sendRemindRequest(ApiUrlsUtils.GET_SYS_RY, rygkRemindRef);

            // 预警处置提醒
            sendRemindRequest(ApiUrlsUtils.GET_SYS_YJ, yjRemindRef);

            // 区域管理提醒  
//            sendRemindRequest(ApiUrlsUtils.GET_SYS_QY, regionRemindRef);

        } catch (Exception e) {
            e.printStackTrace();
            // 发生异常时隐藏所有提醒
            hideAllReminders(rygkRemind, yjRemind, regionRemind);
        }
    }

    // 5. 抽取公共请求方法
    private void sendRemindRequest(String apiUrl, WeakReference<View> remindViewRef) {
        RequestParams params = new RequestParams(ApiHostUtils.getHostUrl() + apiUrl);
        params.addHeader("Content-Type", "application/json");

        try {
            JSONObject jsonParams = new JSONObject();
            jsonParams.put("page", 1);
            jsonParams.put("rows", 10);
            jsonParams.put("sysBh", sysBh);

            String jiaMiString = SM2Utils.encrypt(
                    XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY),
                    new Gson().toJson(jsonParams)
            );

            XHttpUtils.postJsonN(getActivity(), params, jiaMiString, new XHttpResponseCallBack() {
                @Override
                public void onSuccess(String result) {
                    if (getActivity() == null || getActivity().isFinishing() || !isAdded()) {
                        return;
                    }

                    try {
                        XResultData<ListResponse> xResultData = new Gson().fromJson(result,
                                new TypeToken<XResultData<ListResponse>>() {
                                }.getType());

                        View remindView = remindViewRef.get();
                        if (remindView != null && xResultData != null && xResultData.getStatus() == 0) {
                            List<?> records = xResultData.getData() != null ? xResultData.getData().getRecords() : null;
                            remindView.setVisibility(records != null && !records.isEmpty() ? View.VISIBLE : View.INVISIBLE);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onFailed(int code, String failedMsg) {
                    View remindView = remindViewRef.get();
                    if (remindView != null) {
                        remindView.setVisibility(View.INVISIBLE);
                    }
                }

                @Override
                public void onFinished() {
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            View remindView = remindViewRef.get();
            if (remindView != null) {
                remindView.setVisibility(View.INVISIBLE);
            }
        }
    }

    // 6. 隐藏所有提醒的辅助方法
    private void hideAllReminders(View... reminders) {
        for (View reminder : reminders) {
            if (reminder != null) {
                reminder.setVisibility(View.INVISIBLE);
            }
        }
    }

    // 添加ListResponse类用于解析返回数据
    private static class ListResponse {
        private List<?> records;

        public List<?> getRecords() {
            return records;
        }

        public void setRecords(List<?> records) {
            this.records = records;
        }
    }

    // 实验室点击事件绑定
    private void setupRenYuanButtonListeners(View view) {
        View btCkdj = view.findViewById(R.id.bt_ckdj);
        View btRkdj = view.findViewById(R.id.bt_rkdj);
        View btBssq = view.findViewById(R.id.bt_bssq);
        View ivSxt = view.findViewById(R.id.iv_sxt);
        View btWarehouse = view.findViewById(R.id.bt_warehouse);

        btCkdj.setOnClickListener(v -> {
            // 人员管理
            XIntentUtil.redirectToNextActivity(getActivity(), PersonListActivity.class);
        });

        btRkdj.setOnClickListener(v -> {
            // 预警管理
            XIntentUtil.redirectToNextActivity(getActivity(), AlertListActivity.class, "title", "确认列表");
        });

        btBssq.setOnClickListener(v -> {
            // 区域管理
            XIntentUtil.redirectToNextActivity(getActivity(), RegionListActivity.class, "sysBh", sysBh);
        });

        ivSxt.setOnClickListener(v -> {
            // 监控中心
            XIntentUtil.redirectToNextActivity(getActivity(), JkCenterActivity.class);
        });

        if (btWarehouse != null) {
            btWarehouse.setOnClickListener(v -> {
                //仓库管理
                XIntentUtil.redirectToNextActivity(getActivity(), WareHouseListActivity.class);
            });
        }
    }

    @Override
    protected void lazyLoad() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.GET_SYS_BH);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(getActivity(), requestParams, DialogUtils.showLoadDialog(getActivity(), ""), true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<QiYeLabInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<QiYeLabInfoEntity>>() {
                }.getType());

                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    sysBh = xResultData.getData().getSysBh();
                    if  (TextUtils.isEmpty(sysBh)) {
                        sysBh = "0";
                    }
                    XShareCacheUtils.getInstance().put("sysBh", sysBh);
                    mTvManagerUnit.setText(xResultData.getData().getSysMc());
                    setupMessageButtonListeners(requireView());
                    getSys();
//                    tvDj.setText(StrUtils.getDev(xResultData.getData().getQyzxdj(), "暂未评级"));
//                    tvDjDf.setText(xResultData.getData().getPszb());
//                    getPingJiList(qyBh);
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
//        getBaoBeibTiShi();
    }

    @Override
    public void onResume() {
        super.onResume();
        setupMessageButtonListeners(requireView());
    }


    /**
     * 获取报备提示
     */
    private void getSys() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "sysbh=" + sysBh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.GET_SYS_QYSXT + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.getN(getActivity(), requestParams, DialogUtils.showLoadDialog(getActivity(), ""), true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<Map<String, String>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<Map<String, String>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        String uninstalled = xResultData.getData().get("0"); // 未安装摄像头区域数量
                        String installed = xResultData.getData().get("1"); // 已安装摄像头区域数量
                        // 总数 uninstalled+ installed
                        String total = "";
                        try {
                            total = String.valueOf(Integer.parseInt(uninstalled) + Integer.parseInt(installed));
                        } catch (Exception e) {

                        }

                        tvInstalled.setText(installed + "/" + total);
                        tvUninstalled.setText("已安装摄像头");
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });

        //更新摄像头是否在线
        String url = ApiUrlsUtils.YZD_SXT_LIST;
        String sfLxDm = XShareCacheUtils.getInstance().getString(XConstantUtils.SF_LX);
        if (Objects.equals(sfLxDm, "yzdsysglry")) {
            url = ApiUrlsUtils.SYS_SXT;
        }
        RequestParams requestParams1 = new RequestParams(ApiHostUtils.getHostUrl() + url);
        requestParams1.addHeader("Content-Type", "application/json");
        requestParams1.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(getActivity(), requestParams1, null, false, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<SxtBean> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<SxtBean>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null && xResultData.getData().getCks() != null && !xResultData.getData().getCks().isEmpty()) {
                    int num = -1;
                    for (int i = 0; i < xResultData.getData().getCks().size(); i++) {
                        CksBean cksBean = xResultData.getData().getCks().get(i);
                        if (cksBean != null) {
                            List<CksBean.SxtsBean> cksBeanList = cksBean.getSxts();
                            if (cksBeanList != null && cksBeanList.size() > 0) {
                                for (int k = 0; k < cksBeanList.size(); k++) {
                                    CksBean.SxtsBean sxtsBean = cksBeanList.get(k);
                                    try {
                                        if (!sxtsBean.isSfzx()) {
                                            num = 1;
                                            break;
                                        }
                                    } catch (Exception e) {

                                    }

                                }
                            }
                        }
                    }
                    if (num == 1) {
                        ivSxt.setImageResource(R.mipmap.ic_sxt);
                    } else {
                        ivSxt.setImageResource(R.mipmap.ic_sxt_f);
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 出入仓库抓拍提示
     */
    private Dialog showCrkZp(Activity
                                     context, List<BaoBeiTsBean> beanList, DialogUtils.OnResult onResult) {
        mDialog = new Dialog(context, com.lgfzd.base.R.style.dialogStyle);
        View viewDiaolg = LayoutInflater.from(context).inflate(R.layout.dialog_crc_zp, null);
        mDialog.getWindow().setBackgroundDrawable(new ColorDrawable(0));
        mDialog.setContentView(viewDiaolg);
        mDialog.setCanceledOnTouchOutside(false);
        mDialog.getWindow().setGravity(Gravity.CENTER);
        mDialog.setCancelable(false);
        //设置铺满屏幕
        DisplayMetrics dm = new DisplayMetrics();
        WindowManager m = ((Activity) context).getWindowManager();
        m.getDefaultDisplay().getMetrics(dm);
        // 为获取屏幕宽、高
        WindowManager.LayoutParams p = mDialog.getWindow().getAttributes(); // 获取对话框当前的参数值
        p.width = (int) (dm.widthPixels * 0.85); // 宽度设置为屏幕的0.85
        mDialog.getWindow().setAttributes(p);
        ViewPager viewPager = viewDiaolg.findViewById(R.id.view_pager);
        LinearLayout llDot = viewDiaolg.findViewById(R.id.lin_point);
        btCha = viewDiaolg.findViewById(R.id.bt_cha);
        //
        ArrayList<View> viewsList = new ArrayList<View>();
        llDot.removeAllViews();
        curIndex = 0;
        for (int i = 0; i < beanList.size(); i++) {
            View viewNotice = LayoutInflater.from(context).inflate(R.layout.layout_crk_zp_nr, null);
            //
            BaoBeiTsBean bean = beanList.get(i);
            //
            btnSure = viewNotice.findViewById(R.id.bt_sure);
            tvKssj = viewNotice.findViewById(R.id.tv_kksj);
            tvTitle = viewNotice.findViewById(R.id.tv_title);
            tvjssj = viewNotice.findViewById(R.id.tv_jssj);
            tvCkmc = viewNotice.findViewById(R.id.tv_ckmc);
            dialogImg = viewNotice.findViewById(R.id.image);
            tvZpTime = viewNotice.findViewById(R.id.tv_zp_time);
            btSx = viewNotice.findViewById(R.id.bt_sx);
            if (i == 0) {
                ckbh = bean.getCkbh();
            }
            tvKssj.setText("计划进入时间：" + bean.getJrsj());
            tvjssj.setText("计划离开时间：" + bean.getLksj());
            tvCkmc.setText(bean.getCkmc());
            String sj = bean.getZp() != null ? bean.getZp().getZpRlUrl() : "";
            if (TextUtils.isEmpty(sj)) {    //无数据
                tvTitle.setText("出入仓库认证提示");
                dialogImg.setImageResource(R.mipmap.ic_wu_crk);
                tvZpTime.setText("暂未识别到您本人，请将人脸正对仓库监控摄像头3~5s，或点击手动截屏添加");
                tvZpTime.setTextColor(Color.parseColor("#333333"));
                btSx.setVisibility(View.VISIBLE);
                if (bean.getJts() != null && bean.getJts().size() > 0) {
                    btnSure.setText("更新截屏照片");
                } else {
                    btnSure.setText("截屏认证");
                }
                btnSure.setOnClickListener(view -> {
                    Bundle bundle = new Bundle();
                    bundle.putString("bh", bean.getBh());
                    if (bean.getJts() != null && bean.getJts().size() > 0) {
                        bundle.putSerializable("info", (Serializable) bean.getJts());
                    }
                    XIntentUtil.redirectToNextActivity(context, ZhuaPaiScActivity.class, bundle);
                });
                btSx.setOnClickListener(v -> {
                    onResult.onSuccess("1", "1");
                });
            } else {
                //抓拍到数据--正常流程
                if (bean.getZp() != null) {
                    tvZpTime.setText("抓拍时间：" + bean.getZp().getZpSj());
                }
                XImageUtils.loadFit(context, sj, dialogImg);
                btSx.setVisibility(View.GONE);
                btnSure.setOnClickListener(view -> {
                    mDialog.dismiss();
                    mDialog = null;
                });
            }
            //设置圆点
            View viewPoint = LayoutInflater.from(context).inflate(R.layout.layout_dot, null);
            // 默认显示第一页
            View viewPoint1 = viewPoint.findViewById(R.id.v_dot);
            if (i == 0) {
                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(XViewUtil.px2dip(context, 80), XViewUtil.px2dip(context, 30));
                viewPoint1.setLayoutParams(params);
                viewPoint1.setBackgroundResource(R.drawable.bg_blue_dot);
            } else {
                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(XViewUtil.px2dip(context, 30), XViewUtil.px2dip(context, 30));
                params.leftMargin = 10;
                viewPoint1.setLayoutParams(params);
                viewPoint1.setBackgroundResource(R.drawable.bg_blue_low_yuan);
            }
            llDot.addView(viewPoint);
            //
            viewsList.add(viewNotice);
        }
        viewPager.setAdapter(new XViewPagerAdapter(context, viewsList));

        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            @Override
            public void onPageSelected(int position) {
                // 取消圆点选中
                View viewNoPoint = llDot.getChildAt(curIndex).findViewById(R.id.v_dot);
                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(XViewUtil.px2dip(context, 30), XViewUtil.px2dip(context, 30));
                params.leftMargin = 10;
                viewNoPoint.setLayoutParams(params);
                viewNoPoint.setBackgroundResource(R.drawable.bg_blue_low_yuan);
                // 圆点选中
                View viewPoint = llDot.getChildAt(position).findViewById(R.id.v_dot);
                LinearLayout.LayoutParams paramsP = new LinearLayout.LayoutParams(XViewUtil.px2dip(context, 80), XViewUtil.px2dip(context, 30));
                paramsP.leftMargin = 10;
                viewPoint.setLayoutParams(paramsP);
                viewPoint.setBackgroundResource(R.drawable.bg_blue_dot);
                //
                curIndex = position;
                if (beanList != null && beanList.get(position) != null) {
                    ckbh = beanList.get(position).getCkbh();
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });
        btCha.setOnClickListener(v -> {
            if (mDialog != null) {
                mDialog.dismiss();
                mDialog = null;
            }
        });
        return mDialog;
    }

    /**
     * 弹窗数据更新
     */
    private void dialogUpdateData(List<BaoBeiTsBean> beanList) {
        for (int i = 0; i < beanList.size(); i++) {
            BaoBeiTsBean bean = beanList.get(i);
            if (bean != null) {
                if (TextUtils.equals(bean.getCkbh(), ckbh)) {
                    updateBbTsData(bean);
                } else {//之前的数据被认证了直接获取第一条数据
                    updateBbTsData(beanList.get(0));
                }
            }
        }
    }

    /**
     * 弹窗数据更新2
     */
    private void updateBbTsData(BaoBeiTsBean bean) {
        tvKssj.setText("计划进入时间：" + bean.getJrsj());
        tvjssj.setText("计划离开时间：" + bean.getLksj());
        String sj = bean.getZp() != null ? bean.getZp().getZpRlUrl() : "";
        if (TextUtils.isEmpty(sj)) {    //无数据
            tvTitle.setText("出入仓库认证提示");
            dialogImg.setImageResource(R.mipmap.ic_wu_crk);
            tvZpTime.setText("暂未识别到您本人，请将人脸正对仓库监控摄像头3~5s，或点击手动截屏添加");
            tvZpTime.setTextColor(Color.parseColor("#333333"));
            btSx.setVisibility(View.VISIBLE);
            if (bean.getJts() != null && bean.getJts().size() > 0) {
                btnSure.setText("更新截屏照片");
            } else {
                btnSure.setText("截屏认证");
            }
        } else {
            if (bean.getZp() != null) {
                tvZpTime.setText("抓拍时间：" + bean.getZp().getZpSj());
            }
            XImageUtils.loadFit(getActivity(), sj, dialogImg);
            btSx.setVisibility(View.GONE);
            btnSure.setText("确定");
            btnSure.setOnClickListener(view -> {
                closeDialog();
            });
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(JieTuBean event) {
        if (event != null) {//认证之后刷新页面--看是否认证成功
            if (mDialog != null) {
                mDialog.dismiss();
                mDialog = null;
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaiBeiListEntity event) {
        if (event != null) {
            setupMessageButtonListeners(requireView());
        }
    }

    private void closeDialog() {
        if (mDialog != null) {
            mDialog.dismiss();
            mDialog = null;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
