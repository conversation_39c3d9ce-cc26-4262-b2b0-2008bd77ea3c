package com.linggan.jd831.ui.works.sewage.sampledevice;

import android.text.InputFilter;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XAppUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.bean.InputEntity;
import com.linggan.jd831.databinding.ActivitySampleDeviceDoorBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.EditLengthFilter;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.XHttpUtils;

import org.greenrobot.eventbus.EventBus;
import org.xutils.http.RequestParams;

import java.util.HashMap;
import java.util.Map;

/**
 * 类  名：录入通用页面
 * 作  者：zxb
 * 说  明：标题，输入内容
 * 时  间：2022/9/13  10:17
 * 版  权：凌感科技
 */
public class SampleDeviceDoorActivity extends XBaseActivity<ActivitySampleDeviceDoorBinding> {
    private static final String EXTRA_DOOR_STATE = "doorState";
    private static final String EXTRA_REMOTE_URL = "remoteUrl";
    private static final String EXTRA_IMEI_ID = "imeiId";
    private static final String EXTRA_TITLE = "title";
    private static final String EXTRA_HINT = "hint";
    private static final String EXTRA_INFO = "info";
    private static final String EXTRA_LEN = "len";
    private static final String EXTRA_INFO1 = "info1";
    private static final String EXTRA_TAB = "tab";

    private String hint, info, len, info1, token;
    private int tabFrom;
    private int doorState = -1;
    private String remoteUrl, imei;

    @Override
    protected ActivitySampleDeviceDoorBinding getViewBinding() {
        return ActivitySampleDeviceDoorBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        doorState = getIntent().getIntExtra(EXTRA_DOOR_STATE, -1);
        remoteUrl = getIntent().getStringExtra(EXTRA_REMOTE_URL);
        imei = getIntent().getStringExtra(EXTRA_IMEI_ID);
    }

    @Override
    protected void getData() {
        String title = getIntent().getStringExtra(EXTRA_TITLE);
        hint = getIntent().getStringExtra(EXTRA_HINT);
        info = getIntent().getStringExtra(EXTRA_INFO);
        len = getIntent().getStringExtra(EXTRA_LEN);
        info1 = getIntent().getStringExtra(EXTRA_INFO1);
        tabFrom = getIntent().getIntExtra(EXTRA_TAB, 0);

        binding.etInfo.setHint(hint);
        if (!TextUtils.isEmpty(info)) {
            binding.etInfo.setText(info);
            binding.etInfo.setSelection(info.length());
        }
        setInputFilter();
    }

    private void setInputFilter() {
        InputFilter lengthFilter;
        if (TextUtils.isEmpty(len)) {
            lengthFilter = new EditLengthFilter(200, this);
        } else {
            switch (len) {
                case "50":
                case "100":
                case "500":
                case "1000":
                    lengthFilter = new EditLengthFilter(Integer.parseInt(len), this);
                    break;
                case "200-500":
                    lengthFilter = new EditLengthFilter(500, this);
                    break;
                case "100-200":
                    lengthFilter = new EditLengthFilter(200, this);
                    break;
                case "10-max":
                    // 处理特殊情况
                    return;
                default:
                    lengthFilter = new EditLengthFilter(200, this);
            }
        }
        binding.etInfo.setFilters(new InputFilter[]{lengthFilter});
    }

    @Override
    protected void initListener() {
        binding.baseTop.tvRight.setOnClickListener(v -> saveAndFinish());
        binding.btSave.setOnClickListener(v -> postData());
    }

    private void saveAndFinish() {
        XAppUtil.closeSoftInput(this);
        String inputText = binding.etInfo.getText().toString();
        if (TextUtils.isEmpty(inputText) && TextUtils.isEmpty(info)) {
            XToastUtil.showToast(this, hint);
            return;
        }
        if (isInputLengthValid(inputText)) {
            InputEntity inputEntity = new InputEntity(inputText, info1, tabFrom, doorState);
            EventBus.getDefault().post(inputEntity);
            finish();
        }
    }

    private boolean isInputLengthValid(String inputText) {
        int inputLength = inputText.length();
        if (TextUtils.equals(len, "10-max") && inputLength < 10) {
            XToastUtil.showToast(this, "最少需要输入10个字");
            return false;
        }
        if (TextUtils.equals(len, "200-500") && inputLength < 200) {
            XToastUtil.showToast(this, "最少需要输入200个字");
            return false;
        }
        if (TextUtils.equals(len, "100-200") && inputLength < 100) {
            XToastUtil.showToast(this, "最少需要输入100个字");
            return false;
        }
        return true;
    }

    private void postData() {
        if (TextUtils.isEmpty(binding.etInfo.getText())) {
            XToastUtil.showToast(this, "请输入开关门原因");
            return;
        }
        if (doorState == -1) {
            XToastUtil.showToast(this, "数据错误，请返回后重新进入。");
            return;
        }

        int postDoorState = (doorState == 1) ? 0 : 1;

        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.CYSB_DOOR_GATE);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("doorState", postDoorState);
        objectMap.put("imeiId", imei);
        objectMap.put("label", binding.etInfo.getText().toString());

        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));

        XHttpUtils.postJson(this, requestParams, jiaMiString,
                DialogUtils.showLoadDialog(this, ""), true, new XHttpResponseCallBack() {
                    @Override
                    public void onSuccess(String result) {
                        XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                        }.getType());
                        if (xResultData.getStatus() == 0) {
                            XToastUtil.showToast(SampleDeviceDoorActivity.this, "操作成功");
                            finish();
                        }

                    }

                    @Override
                    public void onFailed(int code, String failedMsg) {
                        XToastUtil.showToast(SampleDeviceDoorActivity.this, "操作失败：" + failedMsg);
                    }

                    @Override
                    public void onFinished() {
                        // 可以在这里处理一些完成后的操作，比如关闭加载对话框
                    }
                });
    }
}