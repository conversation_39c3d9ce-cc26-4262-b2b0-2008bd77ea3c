package com.linggan.jd831.ui.works.transport.adapter;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.lgfzd.base.utils.XIntentUtil;
import com.linggan.jd831.databinding.ItemTransportPermitBinding;
import com.linggan.jd831.ui.common.PhotoActivity;
import com.linggan.jd831.ui.works.transport.model.TransportPermitData;
import com.linggan.jd831.utils.StrUtils;

import java.util.List;

public class TransportPermitAdapter extends RecyclerView.Adapter<TransportPermitAdapter.ViewHolder> {

    private List<TransportPermitData> permits;
    private OnEditClickListener onEditClickListener;
    private OnUseClickListener onUseClickListener;

    public TransportPermitAdapter(List<TransportPermitData> permits) {
        this.permits = permits;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ItemTransportPermitBinding binding = ItemTransportPermitBinding.inflate(
                LayoutInflater.from(parent.getContext()), parent, false
        );
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        TransportPermitData permit = permits.get(position);
        if (permit == null) {
            return;
        }

        holder.binding.tvPermitNumber.setText(StrUtils.getDev("证书号：" + permit.getZsbh(), "-"));

        if (permit.getYxksrq() != null && permit.getYxjsrq() != null) {
            holder.binding.tvValidDate.setText(StrUtils.getDev("有效期：" + permit.getYxksrq() + " 至 " + permit.getYxjsrq(), "-"));
        } else {
            holder.binding.tvValidDate.setText("-");
        }

//        holder.binding.tvValidDate.setText("有效期：" + permit.getValidDate());
        // yxcs	number
        //必须
        //有效次数
        // 如果有效次数为10000 则显示为多次有效
        try {
            if (permit.getYxcs() == 10000) {
                holder.binding.tvValidTimes.setText("有效次数：多次有效");
            } else {
                holder.binding.tvValidTimes.setText(StrUtils.getDev("有效次数：" + permit.getYxcs() + "次", "-"));

            }
        } catch (Exception e) {
            holder.binding.tvValidTimes.setText("有效次数：-");
        }

        holder.binding.tvUsedTimes.setText(StrUtils.getDev("已使用次数：" + permit.getYsycs() + "次", "-"));


        holder.binding.tvIssuingAuthority.setText(StrUtils.getDev("发证机关：" + permit.getFzjg(), "-"));

        if (permit.getXkz() != null && permit.getXkz().getLj() != null) {
            // 这个是url glide加载 且可以点击放大查看
            Glide.with(holder.binding.ivPermitPhoto.getContext()).load(permit.getXkz().getLj()).into(holder.binding.ivPermitPhoto);
            holder.binding.ivPermitPhoto.setOnClickListener(v -> {
                XIntentUtil.redirectToNextActivity(holder.binding.getRoot().getContext(), PhotoActivity.class, "path", permit.getXkz().getLj());
            });
        }

        // 编辑按钮点击事件
        holder.binding.btnEdit.setOnClickListener(v -> {
            if (onEditClickListener != null) {
                onEditClickListener.onEditClick(permit, position);
            }
        });

        // 使用按钮点击事件
        holder.binding.btnUse.setOnClickListener(v -> {
            if (onUseClickListener != null) {
                onUseClickListener.onUseClick(permit, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return permits.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ItemTransportPermitBinding binding;

        public ViewHolder(ItemTransportPermitBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

    public interface OnEditClickListener {
        void onEditClick(TransportPermitData permit, int position);
    }

    public interface OnUseClickListener {
        void onUseClick(TransportPermitData permit, int position);
    }

    public void setOnEditClickListener(OnEditClickListener listener) {
        this.onEditClickListener = listener;
    }

    public void setOnUseClickListener(OnUseClickListener listener) {
        this.onUseClickListener = listener;
    }
} 