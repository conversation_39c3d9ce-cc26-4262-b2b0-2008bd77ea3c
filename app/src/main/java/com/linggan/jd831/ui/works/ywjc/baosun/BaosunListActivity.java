package com.linggan.jd831.ui.works.ywjc.baosun;

import static com.lgfzd.base.utils.XAppUtil.hideKeyboard;

import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData;
import com.lgfzd.base.utils.XAppUtil;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.bean.InventoryStatsBean;
import com.linggan.jd831.bean.XuanJiaoListEntity;
import com.linggan.jd831.databinding.ActivityBsjlListBinding;
import com.linggan.jd831.ui.works.ywjc.baosun.adapter.BaoSunRecordAdapter;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XHttpUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

import java.util.ArrayList;

/**
 * 类  名：宣教活动列表
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
//activity_ywry_list
//    activity_bsjl_list.xml
public class BaosunListActivity extends XBaseActivity<ActivityBsjlListBinding> implements XRefreshLayout.PullLoadMoreListener, EditText.OnEditorActionListener {

    private int page = 1, totalPage = 0;
    private boolean isShow = true, icCheck = true;
    private String zkcy;
    private String startTime = ""; // 开始时间
    private String endTime = ""; // 结束时间

    @Override
    protected ActivityBsjlListBinding getViewBinding() {
        return ActivityBsjlListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        binding.recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
        binding.recycler.getAdapter().bindHolder(new BaoSunRecordAdapter());
        binding.recycler.setOnPullLoadMoreListener(this);
        zkcy = getIntent().getStringExtra("zkcy");
        EventBus.getDefault().register(this);
        setTitle("报损记录");
    }

    @Override
    protected void initListener() {
        binding.etSearch.setOnEditorActionListener(this);
        binding.fab.setOnClickListener(v -> {
            if (ButtonUtils.isFastClick()) {
                XIntentUtil.redirectToNextActivity(this, BaosunAddActivity.class);
            }
        });

        // 添加时间段选择点击事件
        binding.dateContainer.setOnClickListener(v -> {
            // 使用DialogUtils.showBaoBeiTime实现时间段选择
            DialogUtils.showBaoBeiTime(this, startTime, endTime, (ksTime, jsTime) -> {
                startTime = ksTime;
                endTime = jsTime;
                if (!android.text.TextUtils.isEmpty(startTime)) {
                    binding.tvDate.setText(startTime + "至" + endTime);
                } else {
                    binding.tvDate.setText("请选择时间段");
                }
                // 重置页码并刷新数据
                page = 1;
                isShow = true;
                getData();
            });
        });
        binding.etSearch.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                    actionId == EditorInfo.IME_ACTION_DONE ||
                    (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {

                // 隐藏软键盘
                hideKeyboard(this);
                // 触发搜索
                page = 1;
                isShow = true;
                getData();


                return true;
            }
            return false;
        });

        // 添加搜索按钮点击事件
//        binding.btnSearch.setOnClickListener(v -> {
//            if (ButtonUtils.isFastClick()) {
//                page = 1;
//                isShow = true;
//                getData();
//            }
//        });
    }

    @Override
    protected void getData() {
        getBaosunStats(); // 添加获取报损统计

        String xzqhdm = UserInfoUtils.getUserInfo() != null ? UserInfoUtils.getUserInfo().getYhDwdm() : null;
        String queryParams = "page=" + page + "&rows=" + XConstantUtils.PAGE_SIZE;
        if (!TextUtils.isEmpty(xzqhdm)) {
            queryParams += "&dwdm=" + xzqhdm;
        }
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), queryParams);

        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FYJL_BS_LIST + "?query=" + jiaMiString);

        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, null, false, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<XResultPageData<BaosunRecordBean>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XResultPageData<BaosunRecordBean>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        totalPage = xResultData.getData().getTotalPage();
                        if (xResultData.getData().getRecords() != null && xResultData.getData().getRecords().size() > 0) {
                            binding.tvNoData.setVisibility(View.GONE);
                            if (page == 1) {
                                binding.recycler.getAdapter().setData(0, xResultData.getData().getRecords());
                            } else {
                                binding.recycler.getAdapter().addDataAll(0, xResultData.getData().getRecords());
                            }
                        } else {
                            if (page == 1) {
                                binding.tvNoData.setVisibility(View.VISIBLE);
                                binding.recycler.getAdapter().setData(0, new ArrayList());
                            }
                        }
                    } else {
                        if (page == 1) {
                            binding.tvNoData.setVisibility(View.VISIBLE);
                            binding.recycler.getAdapter().setData(0, new ArrayList());
                        }
                    }
                } else {
                    XToastUtil.showToast(BaosunListActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                binding.recycler.setPullLoadMoreCompleted();
                icCheck = true;
            }
        });
    }

    @Override
    public void onRefresh() {
        page = 1;
        isShow = false;
        getData();
    }

    @Override
    public boolean onLoadMore() {
        if (page < totalPage) {
            page++;
            isShow = false;
            getData();
            return true;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(XuanJiaoListEntity event) {
        if (event != null) {
            page = 1;
            isShow = false;
            getData();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    /**
     * 格式化日期显示，将YYYY-MM-DD格式简化为MM-DD格式
     *
     * @param dateStr 原始日期字符串（格式如：2023-01-01）
     * @return 简化后的日期字符串（格式如：01-01）
     */
    private String formatDateForDisplay(String dateStr) {
        if (android.text.TextUtils.isEmpty(dateStr) || dateStr.length() < 10) {
            return dateStr;
        }
        // 取出月份和日期部分（MM-DD）
        return dateStr.substring(5, 10);
    }

    @Override
    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
        //以下方法防止两次发送请求
        if (icCheck) {
            page = 1;
            isShow = true;
            getData();
            icCheck = false;
        }
        XAppUtil.closeSoftInput(this);
        return false;
    }

    private void getBaosunStats() {
        @Nullable
        String xzqhdm = UserInfoUtils.getUserInfo() != null ? UserInfoUtils.getUserInfo().getYhDwdm() : null;
        String queryParams = "";
        if (!TextUtils.isEmpty(xzqhdm)) {
            queryParams += "dwdm=" + xzqhdm;
        }

        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), queryParams);

        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FYJL_RK_KC + "?query=" + jiaMiString);

        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.getN(this, requestParams, null, false, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(@Nullable String result) {
                if (result == null) {
                    XToastUtil.showToast(BaosunListActivity.this, "数据返回为空");
                    return;
                }
                XResultData<InventoryStatsBean> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<InventoryStatsBean>>() {
                }.getType());
                if (xResultData != null && xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        updateStatsUI(xResultData.getData());
                    }
                } else {
                    XToastUtil.showToast(BaosunListActivity.this, xResultData != null ? xResultData.getErrorInfo() : "未知错误");
                }
            }

            @Override
            public void onFailed(int code, @Nullable String failedMsg) {
                XToastUtil.showToast(BaosunListActivity.this, failedMsg != null ? failedMsg : "请求失败");
            }

            @Override
            public void onFinished() {
            }
        });
    }

    private void updateStatsUI(InventoryStatsBean stats) {
        if (stats == null) return;

        // 更新累计报损
        try {
            binding.tvLjbs.setText(String.format("%.1fml", stats.getBs()));
        } catch (Exception e) {
            binding.tvLjbs.setText("-");
        }
    }
}
