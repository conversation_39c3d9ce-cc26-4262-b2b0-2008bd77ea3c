package com.linggan.jd831.ui.works.urine;


import android.text.TextUtils;
import android.view.View;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.UrineListEntity;
import com.linggan.jd831.bean.WorksEntity;
import com.linggan.jd831.databinding.ActivityChoujianAddBinding;
import com.linggan.jd831.ui.user.WorkChoiceListActivity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.BaseWorkDialog;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.util.ArrayList;

/**
 * 类  名： 抽检
 * 描   述：
 * 作  者：zxb
 * 说  明：
 * 时  间：2024/2/1 15:06
 */
public class UrineChouAddActivity extends XBaseActivity<ActivityChoujianAddBinding> implements View.OnClickListener {

    private String peoId;

    @Override
    protected ActivityChoujianAddBinding getViewBinding() {
        return ActivityChoujianAddBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        peoId = getIntent().getStringExtra("id");
    }

    @Override
    protected void initListener() {
        binding.tvTwlx.setOnClickListener(this);
        binding.tvKssj.setOnClickListener(this);
        binding.tvJssj.setOnClickListener(this);
        binding.btSure.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.bt_sure) {
            if (ButtonUtils.isFastClick()) {
                postData();
            }
        } else if (v.getId() == R.id.tv_twlx) {
            //工作人员--2023-11-09修改为可查询同时支持平级选择
            String areaIds = UserInfoUtils.getUserInfo().getYhXzqhdm();
            if (!TextUtils.isEmpty(areaIds) && areaIds.startsWith("43")) {
                FactoryUtils.getWorkList(this, result -> {
                    BaseWorkDialog dianDialog = new BaseWorkDialog(this, (ArrayList<WorksEntity>) result);
                    dianDialog.setOnClickDataListener(() -> {
                        binding.tvTwlx.setText(dianDialog.getData().getYhXm());
//                        workID = dianDialog.getData().getId() + "";
                    });
                    dianDialog.show();
                });
            } else {
                XIntentUtil.redirectToNextActivity(this, WorkChoiceListActivity.class);
            }
        } else if (v.getId() == R.id.tv_kssj) {
            //检测类型
//            FactoryUtils.getBaseDataType(this, "lg_xdry_jc_lx", result -> {
//                BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, (ArrayList<ZiDianEntity>) result);
//                dianDialog.setOnClickDataListener(() -> {
//                    binding.tvJcLx.setText(dianDialog.getData().getMc());
//                    lxTypeCode = dianDialog.getData().getDm();
//                });
//                dianDialog.show();
//            });
        } else if (v.getId() == R.id.tv_jssj) {
            //检测类型
//            FactoryUtils.getBaseDataType(this, "lg_xdry_jc_lx", result -> {
//                BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, (ArrayList<ZiDianEntity>) result);
//                dianDialog.setOnClickDataListener(() -> {
//                    binding.tvJcLx.setText(dianDialog.getData().getMc());
//                    lxTypeCode = dianDialog.getData().getDm();
//                });
//                dianDialog.show();
//            });
        }
    }

    @Override
    protected void getData() {

    }

    /**
     * 数据提交
     */
    private void postData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.URINE_SAVE);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");

//        if (TextUtils.isEmpty(binding.tvWorkName.getText().toString())) {
//            XToastUtil.showToast(this, "请选择工作人员");
//            return;
//        }
//        if (TextUtils.isEmpty(binding.tvJcType.getText().toString())) {
//            XToastUtil.showToast(this, "请选择检测方式");
//            return;
//        }
//
//        if (TextUtils.isEmpty(binding.btJcTime.getText().toString())) {
//            XToastUtil.showToast(this, "请选择检测时间");
//            return;
//        }

        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("sjLy", "3");
//            objectMap.put("bz", binding.tvRemark.getText().toString());
//            objectMap.put("approval", approval);
//            objectMap.put("lng", mLongitude);
//            objectMap.put("lat", mLatitude);
//            objectMap.put("phdPj", peHeDuCode);
//            objectMap.put("bphBx", binding.tvBuPeiHe.getText().toString());
        } catch (JSONException e) {
        }
        binding.btSure.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), true, true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(UrineChouAddActivity.this, getString(R.string.add_sucess));
                    EventBus.getDefault().post(new UrineListEntity());
                    finish();
                } else {
                    XToastUtil.showToast(UrineChouAddActivity.this, xResultData.getErrorInfo());
                    binding.btSure.setEnabled(true);
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSure.setEnabled(true);
            }

            @Override
            public void onFinished() {

            }
        });

    }


}
