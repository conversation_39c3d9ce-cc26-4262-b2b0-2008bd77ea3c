package com.linggan.jd831.ui.common;


import android.os.Build;
import android.webkit.WebSettings;

import com.lgfzd.base.base.XBaseActivity;
import com.linggan.jd831.databinding.ActivityWebBinding;

/**
 * * 类  名：禁毒活动--审批--活动详情
 * * 作  者：LGKJ
 * * 说  明：
 * * 时  间：2024/4/15 13:11
 */
public class WebStringActivity extends XBaseActivity<ActivityWebBinding> {

    private String html;

    @Override
    protected ActivityWebBinding getViewBinding() {
        return ActivityWebBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        html = getIntent().getStringExtra("html");
        String title = getIntent().getStringExtra("title");
        setTitle(title);
    }

    @Override
    protected void initListener() {
    }

    @Override
    protected void getData() {
        setWeb(html);
    }

    private void setWeb(String htmlString) {
        WebSettings webseting = binding.mWebView.getSettings();
        webseting.setJavaScriptEnabled(true);
        webseting.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.SINGLE_COLUMN);
        webseting.setLoadWithOverviewMode(true);
        webseting.setDefaultTextEncodingName("UTF-8");
        webseting.setLoadsImagesAutomatically(true);    //设置自动加载图片
        webseting.setDefaultFontSize(25);
        webseting.setTextSize(WebSettings.TextSize.LARGER);
        webseting.setUseWideViewPort(true);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webseting.setMixedContentMode(0);
        }
        binding.mWebView.loadDataWithBaseURL(null, htmlString, "text/html", "UTF-8", null);
    }

}
