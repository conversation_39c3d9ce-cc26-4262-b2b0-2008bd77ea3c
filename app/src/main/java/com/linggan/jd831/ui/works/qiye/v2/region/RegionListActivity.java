package com.linggan.jd831.ui.works.qiye.v2.region;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData1;
import com.lgfzd.base.utils.XAppUtil;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.databinding.ActivityRegionManngerListBinding;
import com.linggan.jd831.ui.works.qiye.v2.lab.entity.ListSysQyEntity;
import com.linggan.jd831.ui.works.qiye.v2.region.adapter.RegionManageListHolder;
import com.linggan.jd831.ui.works.qiye.v2.region.adapter.SwipeToDeleteCallback;
import com.linggan.jd831.ui.works.qiye.v2.warehouse.entity.WareHouseInventoryEntity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.XHttpUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 类  名：宣教活动列表
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class RegionListActivity extends XBaseActivity<ActivityRegionManngerListBinding> implements XRefreshLayout.PullLoadMoreListener, EditText.OnEditorActionListener {

    private int page = 1, totalPage = 0;
    private boolean isShow = true, icCheck = true;
    private String zkcy;

    @Override
    protected ActivityRegionManngerListBinding getViewBinding() {
        return ActivityRegionManngerListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        setTitle("区域管理");
        binding.recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
        RegionManageListHolder holder = new RegionManageListHolder();
        binding.recycler.getAdapter().bindHolder(holder);
        binding.recycler.setOnPullLoadMoreListener(this);
        zkcy = getIntent().getStringExtra("zkcy");
        EventBus.getDefault().register(this);

        // 添加左滑删除功能
        holder.setOnItemDeleteListener((position, item) -> {
            // 处理删除逻辑
            deleteItem(position, item);
        });

        ItemTouchHelper itemTouchHelper = new ItemTouchHelper(new SwipeToDeleteCallback());
        itemTouchHelper.attachToRecyclerView(binding.recycler.getRecyclerView());
    }

    @Override
    protected void initListener() {
        binding.fab.setOnClickListener(v -> {
            if (ButtonUtils.isFastClick()) {
                Bundle bundle = new Bundle();
                bundle.putString("from", "add");
                XIntentUtil.redirectToNextActivity(this, RegionManagementAddActivity.class);
            }
        });
    }

    @Override
    protected void getData() {
        String url = "";
        url = ApiUrlsUtils.GET_SYS_QY;
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + url);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("page", page);
        objectMap.put("rows", XConstantUtils.PAGE_SIZE);
        objectMap.put("sysBh", XShareCacheUtils.getInstance().getString("sysBh"));
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), isShow, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<XResultPageData1<ListSysQyEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XResultPageData1<ListSysQyEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        totalPage = xResultData.getData().getTotalPage();
                        if (xResultData.getData().getRecords() != null && xResultData.getData().getRecords().size() > 0) {
                            binding.tvNoData.setVisibility(View.GONE);
                            if (page == 1) {
                                binding.recycler.getAdapter().setData(0, xResultData.getData().getRecords());
                            } else {
                                binding.recycler.getAdapter().addDataAll(0, xResultData.getData().getRecords());
                            }
                        } else {
                            if (page == 1) {
                                binding.tvNoData.setVisibility(View.VISIBLE);
                                binding.recycler.getAdapter().setData(0, new ArrayList());
                            }
                        }
                    } else {
                        if (page == 1) {
                            binding.tvNoData.setVisibility(View.VISIBLE);
                            binding.recycler.getAdapter().setData(0, new ArrayList());
                        }
                    }
                } else {
                    if (page == 1) {
                        binding.tvNoData.setVisibility(View.VISIBLE);
                        binding.recycler.getAdapter().setData(0, new ArrayList());
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                binding.recycler.setPullLoadMoreCompleted();
            }
        });
    }

    @Override
    public void onRefresh() {
        page = 1;
        isShow = false;
        getData();
    }

    @Override
    public boolean onLoadMore() {
        if (page < totalPage) {
            page++;
            isShow = false;
            getData();
            return true;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(WareHouseInventoryEntity event) {
        if (event != null) {
            page = 1;
            isShow = false;
            getData();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    @Override
    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
        //以下方法防止两次发送请求
        if (icCheck) {
            page = 1;
            isShow = true;
            getData();
            icCheck = false;
        }
        XAppUtil.closeSoftInput(this);
        return false;
    }

    private void deleteItem(int position, ListSysQyEntity item) {
        // 调用删除接口
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "qybh=" + item.getBh());
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.DEL_SYS_QY + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");

        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, "删除中..."), true,
                new XHttpResponseCallBack() {
                    @Override
                    public void onSuccess(String result) {
                        XResultData xResultData = new Gson().fromJson(result, XResultData.class);
                        if (xResultData.getStatus() == 0) {
                            // 删除成功后更新UI
                            binding.recycler.getAdapter().setData(position, null);
                            binding.recycler.getAdapter().notifyItemRemoved(position);
                            binding.recycler.getAdapter().notifyItemRangeChanged(position,
                                    binding.recycler.getAdapter().getItemCount());
                            getData();
                            // 如果删除后列表为空，显示空数据视图
//                        if (binding.recycler.getAdapter().getItemCount() == 0) {
//                            binding.tvNoData.setVisibility(View.VISIBLE);
//                        }
                        } else {
                            // 删除失败提示
                            XToastUtil.showToast(RegionListActivity.this, "删除失败：" + xResultData.getErrorInfo());
                        }
                    }

                    @Override
                    public void onFailed(int code, String failedMsg) {
                        XToastUtil.showToast(RegionListActivity.this, "删除失败：" + failedMsg);
                    }

                    @Override
                    public void onFinished() {
                        // 可以在这里处理加载对话框的关闭
                    }
                });
    }
}
