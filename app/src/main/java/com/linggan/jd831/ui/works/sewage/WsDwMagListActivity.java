package com.linggan.jd831.ui.works.sewage;

import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.linggan.jd831.utils.XHttpUtils;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData;
import com.lgfzd.base.utils.XAppUtil;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.WSDwMagListHolder;
import com.linggan.jd831.bean.LoginEntity;
import com.linggan.jd831.bean.TalkListEntity;
import com.linggan.jd831.bean.WsDwMagListEntity;
import com.linggan.jd831.databinding.ActivityPeopleListBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.widget.AreaPickerViewDialog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 类  名：污水点位管理--社工/禁毒办
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class WsDwMagListActivity extends XBaseActivity<ActivityPeopleListBinding> implements XRefreshLayout.PullLoadMoreListener, View.OnClickListener {

    private int page = 1, totalPage = 0;
    private boolean isShow = true;
    private String xzqhdm;

    @Override
    protected ActivityPeopleListBinding getViewBinding() {
        return ActivityPeopleListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        binding.recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
        binding.recycler.getAdapter().bindHolder(new WSDwMagListHolder());
        binding.recycler.setOnPullLoadMoreListener(this);
        EventBus.getDefault().register(this);
        xzqhdm = UserInfoUtils.getUserInfo().getYhXzqhdm();
        binding.top.btRight.setVisibility(View.VISIBLE);
        binding.top.btRight.setText(getText(R.string.add));
        binding.top.btBack.setOnClickListener(this);
        binding.linTypeTop.setVisibility(View.GONE);
    }

    @Override
    protected void initListener() {
        //获取当前所在区域
        FactoryUtils.getCodeAreaName(this, UserInfoUtils.getUserInfo().getYhXzqhdm(), result -> {
            String areas = StrUtils.getShowAreaListText(result);
            Log.i("lll", "initListener: " + areas);
            if (areas.length() > 10) {
                if (result.get(0).getXzqhmc().length() > 3) {
                    binding.tvArea.setText("当前辖区:" + result.get(0).getXzqhmc().substring(0, 3) + "..." + result.get(result.size() - 1).getXzqhmc());
                } else {
                    binding.tvArea.setText("当前辖区:" + result.get(0).getXzqhmc() + "..." + result.get(result.size() - 1).getXzqhmc());
                }
            } else {
                binding.tvArea.setText("当前辖区:" + areas);
            }
        });
        binding.top.etSearch.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_SEND || (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                switch (event.getAction()) {
                    case KeyEvent.ACTION_UP:
                        //发送请求
                        page = 1;
                        getData();
                        XAppUtil.closeSoftInput(WsDwMagListActivity.this);
                        return true;
                    default:
                        return true;
                }
            }
            return false;
        });
        binding.tvArea.setOnClickListener(this);
        binding.top.btRight.setOnClickListener(this);
    }

    @Override
    protected void getData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.SEWAGE_WS_DW_LIST);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("page", page);
        objectMap.put("rows", XConstantUtils.PAGE_SIZE);
        if (!TextUtils.isEmpty(xzqhdm)) {
            objectMap.put("xzqhdm", xzqhdm);
        }
        if (!TextUtils.isEmpty(binding.top.etSearch.getText().toString())) {
            objectMap.put("keywords", binding.top.etSearch.getText().toString());
        }
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), isShow, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<XResultPageData<WsDwMagListEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XResultPageData<WsDwMagListEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        totalPage = xResultData.getData().getTotalPage();
                        binding.tvNum.setText("当前地区总点位:" + xResultData.getData().getTotal() + "个");
                        if (xResultData.getData().getRecords() != null && xResultData.getData().getRecords().size() > 0) {
                            binding.ivNoData.setVisibility(View.GONE);
                            if (page == 1) {
                                binding.recycler.getAdapter().setData(0, xResultData.getData().getRecords());
                            } else {
                                binding.recycler.getAdapter().addDataAll(0, xResultData.getData().getRecords());
                            }
                        } else {
                            if (page == 1) {
                                binding.recycler.getAdapter().setData(0, new ArrayList());
                                binding.ivNoData.setVisibility(View.VISIBLE);
                            }
                        }
                    } else {
                        binding.tvNum.setText("当前地区总点位:0个");
                        if (page == 1) {
                            binding.recycler.getAdapter().setData(0, new ArrayList());
                            binding.ivNoData.setVisibility(View.VISIBLE);
                        }
                    }
                } else {
                    if (page == 1) {
                        binding.recycler.getAdapter().setData(0, new ArrayList());
                        binding.ivNoData.setVisibility(View.VISIBLE);
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                binding.recycler.setPullLoadMoreCompleted();
            }
        });
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_area) {
            //地区选择
            LoginEntity loginEntity = UserInfoUtils.getUserInfo();
            if (loginEntity != null && !TextUtils.isEmpty(loginEntity.getXzqhdj()) && !loginEntity.getXzqhdj().equals("5")) {
                AreaPickerViewDialog areaPickerView = new AreaPickerViewDialog(this, UserInfoUtils.getUserInfo().getYhXzqhdm(), "1");
                areaPickerView.show();
                areaPickerView.setAreaPickerViewCallback((areaName, ids) -> {
                    xzqhdm = ids.get(ids.size() - 1);
                    String areas = StrUtils.listToStringText(areaName);
                    if (areas.length() > 10) {
                        binding.tvArea.setText("当前辖区:" + areaName.get(0).substring(0, 2) + "..." + areaName.get(areaName.size() - 1));
                    } else {
                        binding.tvArea.setText("当前辖区:" + areas);
                    }
                    page = 1;
                    getData();
                });
            } else {
                XToastUtil.showToast(this, "您只能查看当前辖区内容");
            }
        } else if (v.getId() == R.id.bt_right) {
            //新增
            XIntentUtil.redirectToNextActivity(this, WsDwAddActivity.class);
        } else if (v.getId() == R.id.bt_back) {
            finish();
        }
    }

    @Override
    public void onRefresh() {
        page = 1;
        isShow = false;
        getData();
    }

    @Override
    public boolean onLoadMore() {
        if (page < totalPage) {
            page++;
            isShow = false;
            getData();
            return true;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(TalkListEntity event) {
        if (event != null) {
            page = 1;
            isShow = false;
            getData();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }


}
