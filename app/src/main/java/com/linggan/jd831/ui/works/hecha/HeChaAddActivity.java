package com.linggan.jd831.ui.works.hecha;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseLocaActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XGlideEngine;
import com.lgfzd.base.utils.XImageFileCompressEngine;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.InputEntity;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.PeopleInfoEntity;
import com.linggan.jd831.bean.TalkListEntity;
import com.linggan.jd831.bean.ZiDianEntity;
import com.linggan.jd831.bean.ZiDianEventEntity;
import com.linggan.jd831.databinding.ActivityHechaAddBinding;
import com.linggan.jd831.ui.common.InputInfoActivity;
import com.linggan.jd831.ui.common.MulChoiceList1Activity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.PhotoUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.CodeNameDialog;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 类  名：核查请求
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class HeChaAddActivity extends XBaseLocaActivity<ActivityHechaAddBinding> implements View.OnClickListener {
    private String peoId, yjBh, renWu, approval, pro, xclxType;
    private PeopleInfoEntity peopleListEntity;
    private ImageAddUtil imageAddFJ;
    private int from = 1;
    private ZiDianEventEntity ziDianEvent;
    private Uri photoUri;
    private File cameraPhoto;

    @Override
    protected ActivityHechaAddBinding getViewBinding() {
        return ActivityHechaAddBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        peoId = getIntent().getStringExtra("id");
        renWu = getIntent().getStringExtra("tag");
        pro = getIntent().getStringExtra("pro");
        yjBh = getIntent().getStringExtra("yjztbh");
        peopleListEntity = (PeopleInfoEntity) getIntent().getSerializableExtra("info");
        if (peopleListEntity != null) {
            if (peopleListEntity.getYjztbhList() != null && peopleListEntity.getYjztbhList().size() > 0 && peopleListEntity.getYjztbhList().get(0) != null) {
                yjBh = peopleListEntity.getYjztbhList().get(0).getYjztbh();
                if (peopleListEntity.getYjztbhList().get(0).getRyyjzt() != null) {
                    binding.tvYrType.setText(peopleListEntity.getYjztbhList().get(0).getRyyjzt().getName());
                }
            }
            binding.tvName.setText(peopleListEntity.getXm());
            binding.tvIdCard.setText(StrUtils.hideIdCard(peopleListEntity.getZjhm()));
            binding.tvLxMobile.setText(peopleListEntity.getLxdh());
        }

        imageAddFJ = new ImageAddUtil(this, binding.gridVideo);
        imageAddFJ.setMax(8);
        imageAddFJ.setOnImageAddListener(() -> {
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                if (code.equals("1")) {
                    from = 0;
                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setCompressEngine(new XImageFileCompressEngine()).setMinSelectNum(1).setMaxSelectNum(8 - imageAddFJ.getSize()).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        });
        /**
         *这里是任务中心跳转过来
         */
        if (!TextUtils.isEmpty(renWu)) {
            FactoryUtils.getUserData(this, peoId, result -> {
                if (result.getYjztbhList() != null && result.getYjztbhList().size() > 0 && result.getYjztbhList().get(0) != null) {
                    if (TextUtils.isEmpty(yjBh)) {
                        yjBh = result.getYjztbhList().get(0).getYjztbh();
                    }
                }
                if (result.getRyyjzt() != null) {
                    binding.tvYrType.setText(result.getRyyjzt().getName());
                }
                binding.tvName.setText(result.getXm());
                binding.tvIdCard.setText(StrUtils.hideIdCard(result.getZjhm()));
                binding.tvLxMobile.setText(result.getLxdh());
            });
        }
        EventBus.getDefault().register(this);
        StrUtils.getPhotoText3(binding.tvVideoInfo);
    }

    @Override
    protected void initListener() {
        binding.btSure.setOnClickListener(this);
        binding.tvXclxType.setOnClickListener(this);
        binding.tvXxbc.setOnClickListener(this);
        binding.tvHcMs.setOnClickListener(this);
    }

    @Override
    protected void getData() {
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.bt_sure) {
            //数据提交
            if (ButtonUtils.isFastClick()) {
                postData();
            }
        } else if (v.getId() == R.id.tv_xclx_type) {
            //协查类型
            CodeNameDialog dianDialog = new CodeNameDialog(this, StrUtils.getXclxList());
            dianDialog.setOnClickDataListener(() -> {
                binding.tvXclxType.setText(dianDialog.getCode().getName());
                xclxType = dianDialog.getCode().getCode();
                if (dianDialog.getCode().getCode().equals("0")) {//人员动向确认
                    binding.linXxbc.setVisibility(View.GONE);
                    binding.linDxqr.setVisibility(View.VISIBLE);
                    binding.tvXclxInfo.setText(getString(R.string.dxqrts_info));
                    binding.tvXclxInfo.setVisibility(View.VISIBLE);
                } else {//人员信息补充
                    binding.linXxbc.setVisibility(View.VISIBLE);
                    binding.linDxqr.setVisibility(View.GONE);
                    binding.tvXclxInfo.setText(getString(R.string.cpws_info));
                    binding.tvXclxInfo.setVisibility(View.VISIBLE);
                }
            });
            dianDialog.show();
        } else if (v.getId() == R.id.tv_xxbc) {
            //补充信息
            Bundle bundle = new Bundle();
            bundle.putInt("from", 1);
            bundle.putString("param", "xyxc");
            if (ziDianEvent != null) {
                bundle.putSerializable("val", ziDianEvent);
            }
            bundle.putString("title", "资料名称");
            XIntentUtil.redirectToNextActivity(this, MulChoiceList1Activity.class, bundle);
        } else if (v.getId() == R.id.tv_hc_ms) {
            //协查背景描述
            Bundle bundle = new Bundle();
            bundle.putString("title", "协查背景描述");
            bundle.putString("hint", getString(R.string.xcbjms));
            bundle.putString("info", binding.tvHcMs.getText().toString());
            bundle.putString("len", "100");
            bundle.putInt("tab", 1);
            XIntentUtil.redirectToNextActivity(this, InputInfoActivity.class, bundle);
        }
    }

    /**
     * 数据提交
     */
    private void postData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.HE_CHA_ADD);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        if (TextUtils.isEmpty(binding.tvXclxType.getText().toString())) {
            XToastUtil.showToast(this, "请选择协查类型");
            return;
        }
        if (xclxType.equals("0") && TextUtils.isEmpty(binding.tvHcMs.getText().toString())) {
            XToastUtil.showToast(this, "请输入核查请求描述");
            return;
        }
        if (xclxType.equals("1") && TextUtils.isEmpty(binding.tvXxbc.getText().toString())) {
            XToastUtil.showToast(this, "请选择需要民警补充的资料名称");
            return;
        }
        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("xyrbh", peoId);
            objectMap.put("yjztbh", yjBh);
            objectMap.put("hcMs", binding.tvHcMs.getText().toString());
            objectMap.put("sjLy", "3");
            objectMap.put("approval", approval);
            objectMap.put("xcLx", xclxType);
            //
            JSONArray jsonArray = new JSONArray();
            if (imageAddFJ != null && imageAddFJ.getPaths() != null && imageAddFJ.getPaths().size() > 0) {
                for (int i = 0; i < imageAddFJ.getPaths().size(); i++) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("lj", imageAddFJ.getPaths().get(i).getSavePath());
                    jsonObject.put("mc", imageAddFJ.getPaths().get(i).getOriginName());
                    jsonObject.put("hz", StrUtils.getFileType(imageAddFJ.getPaths().get(i).getOriginName()));
                    jsonObject.put("dx", imageAddFJ.getPaths().get(i).getFileSize());
                    jsonArray.put(jsonObject);
                }
            }
            objectMap.put("fjList", jsonArray);
            if (ziDianEvent != null && ziDianEvent.getZiDianEntityList() != null && ziDianEvent.getZiDianEntityList().size() > 0) {
                for (int i = 0; i < ziDianEvent.getZiDianEntityList().size(); i++) {
                    if (ziDianEvent.getZiDianEntityList().get(i).getDwdm().equals("zp")) {
                        objectMap.put("sfRyZp", ziDianEvent.getZiDianEntityList().get(i).isChoice() ? 0 : 1);
                    } else if (ziDianEvent.getZiDianEntityList().get(i).getDwdm().equals("sj")) {
                        objectMap.put("sfCcSj", ziDianEvent.getZiDianEntityList().get(i).isChoice() ? 0 : 1);
                    } else if (ziDianEvent.getZiDianEntityList().get(i).getDwdm().equals("jds")) {
                        objectMap.put("sfKfJd", ziDianEvent.getZiDianEntityList().get(i).isChoice() ? 0 : 1);
                    } else if (ziDianEvent.getZiDianEntityList().get(i).getDwdm().equals("xys")) {
                        objectMap.put("sfKfXy", ziDianEvent.getZiDianEntityList().get(i).isChoice() ? 0 : 1);
                    }
                }
            }
        } catch (JSONException e) {
        }
        binding.btSure.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJsonNoCan(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(HeChaAddActivity.this, getString(R.string.add_sucess));
                    EventBus.getDefault().post(new TalkListEntity());
                    finish();
                } else {
                    XToastUtil.showToast(HeChaAddActivity.this, xResultData.getErrorInfo());
                    binding.btSure.setEnabled(true);
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSure.setEnabled(true);
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.REQUEST_CAMERA:
                case PictureConfig.CHOOSE_REQUEST:
                    List<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                    if (null != selectList) {
                        for (LocalMedia media : selectList) {
                            if (!TextUtils.isEmpty(media.getCompressPath())) {
                                uploadFile(media.getCompressPath());
                            } else if (!TextUtils.isEmpty(media.getRealPath())) {
                                uploadFile(media.getRealPath());
                            } else if (!TextUtils.isEmpty(media.getPath())) {
                                uploadFile(media.getPath());
                            }
                        }
                    }
                    break;
                case 1:
                    //图片
//                    uploadFile(data.getStringExtra("path"));
                    try {
                        boolean isFinish = XFileUtil.bitmapToFilePath(this, BitmapFactory.decodeFile(cameraPhoto.getAbsolutePath()), photoUri, cameraPhoto);
                        if (isFinish) {
                            uploadFile(cameraPhoto.getAbsolutePath());
                        }
                    } catch (Exception e) {
                    }
                    break;
            }
        }
    }

    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (from == 0) {
            createFileMuLu();
            if (cameraPhoto != null) {
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                photoUri = XFileUtil.getUriFromFile(this, cameraPhoto);
                intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                startActivityForResult(intent, 1);
            }
        }
    }

    //上传
    private void uploadFile(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        imageAddFJ.addImage(xResultData.getData().get(0));
                        imageAddFJ.notifyData();
                    }
                } else {
                    XToastUtil.showToast(HeChaAddActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }


    /**
     * 创建文件
     */
    private void createFileMuLu() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
                && ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {
            cameraPhoto = new File(PhotoUtil.createPath(this));
            try {
                if (cameraPhoto.exists()) {
                    cameraPhoto.delete();
                }
                cameraPhoto.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            // 权限未授权，需要申请权限
            requestPermission(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(ZiDianEventEntity event) {
        if (event != null) {
            if (event.getFrom() == 1) {
                //仓库数据
                if (event.getZiDianEntityList() != null && event.getZiDianEntityList().size() > 0) {
                    List<ZiDianEntity> entityList = new ArrayList<>();
                    for (int i = 0; i < event.getZiDianEntityList().size(); i++) {
                        if (event.getZiDianEntityList().get(i).isChoice()) {
                            entityList.add(event.getZiDianEntityList().get(i));
                        }
                    }
                    ZiDianEventEntity entity = new ZiDianEventEntity();
                    entity.setZiDianEntityList(entityList);
                    ziDianEvent = entity;
                    binding.tvXxbc.setText(StrUtils.listToZiDianTextSelect(event.getZiDianEntityList()));
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(InputEntity event) {
        if (event != null) {
            binding.tvHcMs.setText(event.getInfo());
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

}