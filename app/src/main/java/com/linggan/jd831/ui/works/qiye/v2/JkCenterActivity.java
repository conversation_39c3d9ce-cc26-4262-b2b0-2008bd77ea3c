package com.linggan.jd831.ui.works.qiye.v2;


import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.adapter.JkCenterListHolder;
import com.linggan.jd831.bean.CksBean;
import com.linggan.jd831.bean.SxtBean;
import com.linggan.jd831.databinding.ActivityBaseListBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.XHttpUtils;

import org.xutils.http.RequestParams;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 类  名:监控中心
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2024/3/25 10:00
 * 版  权：凌感科技
 */
public class JkCenterActivity extends XBaseActivity<ActivityBaseListBinding> implements XRefreshLayout.PullLoadMoreListener {

    private boolean isShow = true;

    @Override
    protected ActivityBaseListBinding getViewBinding() {
        return ActivityBaseListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        binding.recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
        binding.recycler.getAdapter().bindHolder(new JkCenterListHolder());
        binding.recycler.setOnPullLoadMoreListener(this);
    }

    @Override
    protected void initListener() {
    }

    @Override
    protected void getData() {
        String url = ApiUrlsUtils.YZD_SXT_LIST;
        String sfLxDm = XShareCacheUtils.getInstance().getString(XConstantUtils.SF_LX);
        if (Objects.equals(sfLxDm, "yzdsysglry")) {
            url = ApiUrlsUtils.SYS_SXT;
        }
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + url);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), isShow, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<SxtBean> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<SxtBean>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        binding.tvNoData.setVisibility(View.GONE);
                        if (xResultData.getData().getCks() != null && xResultData.getData().getCks().size() > 0) {
                            List<CksBean.SxtsBean> listAll = new ArrayList<>();
                            for (int i = 0; i < xResultData.getData().getCks().size(); i++) {
                                if (xResultData.getData().getCks().get(i) != null) {
                                    CksBean cksBean = xResultData.getData().getCks().get(i);
                                    List<CksBean.SxtsBean> cksBeanList = cksBean.getSxts();
                                    if (cksBeanList != null && cksBeanList.size() > 0) {
                                        for (int k = 0; k < cksBeanList.size(); k++) {
                                            CksBean.SxtsBean sxtsBean = cksBeanList.get(k);
                                            sxtsBean.setSxtMc(cksBean.getCkmc() + "-" + sxtsBean.getSxtMc());
                                            listAll.add(sxtsBean);
                                        }
                                    }
                                }
                            }
                            if (listAll != null && listAll.size() > 0) {
                                binding.tvNoData.setVisibility(View.GONE);
                                binding.recycler.getAdapter().setData(0, listAll);
                            } else {
                                binding.tvNoData.setVisibility(View.VISIBLE);
                            }
                        }
                    } else {
                        binding.tvNoData.setVisibility(View.VISIBLE);
                    }
                } else {
                    binding.tvNoData.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                binding.recycler.setPullLoadMoreCompleted();
            }
        });
    }

    @Override
    public void onRefresh() {
        isShow = true;
        getData();
    }

    @Override
    public boolean onLoadMore() {
        return false;
    }
}
