package com.linggan.jd831.ui.works.visit;


import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.GridLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.ImaVideoShowAdapter;
import com.linggan.jd831.adapter.PeoStatusInfoListAdapter;
import com.linggan.jd831.bean.SignQzBean;
import com.linggan.jd831.bean.TaskSpListEntity;
import com.linggan.jd831.bean.VisitInfoEntity;
import com.linggan.jd831.bean.VisitListEntity;
import com.linggan.jd831.databinding.ActivityZoufangInfoBinding;
import com.linggan.jd831.ui.common.PhotoActivity;
import com.linggan.jd831.ui.common.SignatureActivity;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.TaskApprovalFactory;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.utils.XImageUtils;
import com.linggan.jd831.widget.CodeNameDialog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

/**
 * 类  名：走访记录-详情
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class VisitEduInfoActivity extends XBaseActivity<ActivityZoufangInfoBinding> implements View.OnClickListener {

    private String peoId, vBh, tag, key, taskId, aid, pro, ryyxEdit, qmdzUrl, xtxx;
    private VisitInfoEntity visitInfoEntity;

    @Override
    protected ActivityZoufangInfoBinding getViewBinding() {
        return ActivityZoufangInfoBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        vBh = getIntent().getStringExtra("id");
        peoId = getIntent().getStringExtra("pid");
        ryyxEdit = getIntent().getStringExtra("ryyxEdit");
        xtxx = getIntent().getStringExtra("xtxx");
        //这里是标识从任务审批跳转过来--下面三个参数
        tag = getIntent().getStringExtra("tag");
        taskId = getIntent().getStringExtra("tid");
        key = getIntent().getStringExtra("key");
        pro = getIntent().getStringExtra("pro");
        aid = getIntent().getStringExtra("aid");
        binding.top.recyclerPeoStatus.setLayoutManager(new GridLayoutManager(this, 3));
        EventBus.getDefault().register(this);

        //湖南不需要--电子签名
        String xzqhdm = UserInfoUtils.getUserInfo().getYhXzqhdm();
        if (!TextUtils.isEmpty(xzqhdm) && !xzqhdm.startsWith("43")) {
            binding.linDzqm.setVisibility(View.VISIBLE);
        }
        //系统消息进来
        if (!TextUtils.isEmpty(xtxx)) {
            binding.base.btRight.setVisibility(View.GONE);
            binding.top.linInfo.setVisibility(View.GONE);
            binding.peo.linInfo.setVisibility(View.GONE);
        }
    }

    @Override
    protected void initListener() {
        if (TextUtils.isEmpty(tag)) {
            if (TextUtils.isEmpty(ryyxEdit)) {
                binding.base.tvRight.setText(getString(R.string.edit));
                binding.base.tvRight.setTextColor(getResources().getColor(R.color.color_main));
                binding.base.ivRight.setImageResource(R.mipmap.ic_edit);
                binding.base.btRight.setOnClickListener(v -> {
                    //编辑
                    Bundle bundle = new Bundle();
                    bundle.putString("id", peoId);
                    bundle.putString("name", binding.top.tvName.getText().toString());
                    bundle.putSerializable("msg", visitInfoEntity);
                    XIntentUtil.redirectToNextActivity(this, VisitEduAddActivity.class, bundle);
                });
            }
            if (TextUtils.isEmpty(xtxx)) {
                getUserData();
            }
        } else {
            //审批
            binding.linPopup.linInfo.setVisibility(View.VISIBLE);
            TaskApprovalFactory.getSpData(this, binding.linPopup.ivOnDown, binding.linPopup.recycleSp, binding.linPopup.etSpRemark, binding.linPopup.btNo, binding.linPopup.btTg, binding.linPopup.linDspShow, binding.linPopup.linSpth, binding.linPopup.btFangQi, binding.linPopup.btTgEdit, key, pro, aid, taskId, tag, "", code -> {
                switch (code) {
                    case "pass":
                        //审批提交完成
                        EventBus.getDefault().post(new TaskSpListEntity());
                        finish();
                        break;
                    case "fin":
                        //放弃审批
                        finish();
                        break;
                    case "edit":
                        //编辑
                        Bundle bundle = new Bundle();
                        bundle.putString("id", peoId);
                        bundle.putString("tag", tag);
                        bundle.putString("pro", pro);
                        bundle.putString("name", binding.top.tvName.getText().toString());
                        bundle.putSerializable("msg", visitInfoEntity);
                        XIntentUtil.redirectToNextActivity(this, VisitEduAddActivity.class, bundle);
                        break;
                }
            });
        }
        binding.ivLyrQz.setOnClickListener(this);
    }

    @Override
    protected void getData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.ZOU_VISIT_INFO + vBh);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<VisitInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<VisitInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        visitInfoEntity = xResultData.getData();
                        binding.tvJfDate.setText(xResultData.getData().getZq());
                        binding.tvTime.setText(xResultData.getData().getZfsj());
                        if (xResultData.getData().getLx() != null) {
                            binding.tvType.setText(xResultData.getData().getLx().getName());
                        }
                        binding.tvWork.setText(xResultData.getData().getGzryxm());
                        binding.tvInfo.setText(xResultData.getData().getZfnr());
                        binding.tvAddress.setText(xResultData.getData().getShengQhmc() + xResultData.getData().getShiQhmc() + xResultData.getData().getQuQhmc() + xResultData.getData().getXzQhmc() + xResultData.getData().getDz());
                        //视频
                        binding.gridVideo.setAdapter(new ImaVideoShowAdapter(xResultData.getData().getSp()));
                        // 图片
                        binding.gridImg.setAdapter(new ImaVideoShowAdapter(xResultData.getData().getZp()));
                        //审批中不能编辑
                        if (!TextUtils.isEmpty(xResultData.getData().getApproval()) && xResultData.getData().getApproval().equals("2")) {
                            binding.base.btRight.setVisibility(View.GONE);
                        }
                        //审批--获取获取详情
                        if (!TextUtils.isEmpty(tag)) {
                            peoId = xResultData.getData().getXyrbh();
                            getUserData();
                        }
                        //是否签名
                        if (xResultData.getData().getDzqm() != null && !TextUtils.isEmpty(xResultData.getData().getDzqm().getLj())) {//有签名
                            binding.tvLxr.setVisibility(View.GONE);
                            binding.ivLyrQz.setVisibility(View.VISIBLE);
                            qmdzUrl = xResultData.getData().getDzqm().getLj();
                            XImageUtils.loadFit(VisitEduInfoActivity.this, xResultData.getData().getDzqm().getLj(), binding.ivLyrQz);
                        } else {
                            //系统消息进来
                            if (!TextUtils.isEmpty(xtxx)) {
                                binding.tvLxr.setText(getString(R.string.lx_xdry_sign));
                                binding.ivLyrQz.setEnabled(false);
                                binding.tvLxr.setTextColor(Color.GRAY);
                                binding.tvLxr.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
                            }
                        }
                    }
                } else {
                    XToastUtil.showToast(VisitEduInfoActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 获取用户数据
     */
    private void getUserData() {
        FactoryUtils.getUserData(this, peoId, result -> {
            XImageUtils.load(mContext, result.getXp(), binding.top.ivHead, R.mipmap.ic_def_head);
            binding.top.tvName.setText(result.getXm());
            binding.top.recyclerPeoStatus.setAdapter(new PeoStatusInfoListAdapter(VisitEduInfoActivity.this, result.getRyejzt()));
            if (result.getRyyjzt() != null) {
                if (result.getXbdm() != null) {
                    binding.top.tvSex.setText(result.getXbdm().getName() + " " + result.getRyyjzt().getName());
                } else {
                    binding.top.tvSex.setText(result.getRyyjzt().getName());
                }
            } else {
                if (result.getXbdm() != null) {
                    binding.top.tvSex.setText(result.getXbdm().getName());
                }
            }
            if (result.getMzdm() != null) {
                binding.peo.tvMingZu.setText(result.getMzdm().getName());
            }
            binding.peo.tvIdCard.setText(StrUtils.hideIdCard(result.getZjhm()));
            binding.peo.tvMobile.setText(result.getLxdh());
            if (result.getZzmm() != null) {
                binding.peo.tvZzmm.setText(result.getZzmm().getName());
            }
            binding.peo.tvBrith.setText(result.getCsrq());
            if (result.getXldm() != null) {
                binding.peo.tvEdu.setText(result.getXldm().getName());
            }
        });
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.iv_lyr_qz) {
            //去签名
            if (TextUtils.isEmpty(qmdzUrl)) {
                CodeNameDialog dianDialog = new CodeNameDialog(this, StrUtils.getSignTypeList());
                dianDialog.setOnClickDataListener(() -> {
                    if (!TextUtils.isEmpty(dianDialog.getCode().getCode())) {
                        if (dianDialog.getCode().getCode().equals("1")) {
                            //推送
                            setSignTiXing();
                        } else {
                            //签名
                            Bundle bundle = new Bundle();
                            bundle.putString("from", "edu");
                            bundle.putString("bh", vBh);
                            XIntentUtil.redirectToNextActivity(VisitEduInfoActivity.this, SignatureActivity.class, bundle);
                        }
                    }
                });
                dianDialog.show();
            } else {
                //查看签名
                XIntentUtil.redirectToNextActivity(this, PhotoActivity.class, "path", qmdzUrl);
            }
        }
    }

    /**
     * 设置签字提醒
     */
    private void setSignTiXing() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "xyrbh=" + peoId + "&bh=" + vBh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.ZF_EDU_DZQM_TX + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(VisitEduInfoActivity.this, getString(R.string.submit_scess));
                } else {
                    XToastUtil.showToast(VisitEduInfoActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(VisitListEntity event) {
        if (event != null) {
            finish();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(SignQzBean event) {
        if (event != null) {//更新签字数据
            getData();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
