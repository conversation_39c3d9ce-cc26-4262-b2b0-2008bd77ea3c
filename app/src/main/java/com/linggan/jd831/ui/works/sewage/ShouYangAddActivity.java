package com.linggan.jd831.ui.works.sewage;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.YbBhAddListAdapter;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.TalkListEntity;
import com.linggan.jd831.bean.WyYbBhBean;
import com.linggan.jd831.databinding.ActivityWsSyJgAddBinding;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.PhotoUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XHttpUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类  名：收样添加
 * 作  者：zxb
 * 说  明：
 * 时  间：2024/3/5 13:11
 */
public class ShouYangAddActivity extends XBaseActivity<ActivityWsSyJgAddBinding> {

    private String ybBh;
    private YbBhAddListAdapter listAdapter;
    private List<String> bfqyBeanList = new ArrayList<>();
    private ImageAddUtil imageAddImg;
    private int from = 0;
    private Uri photoUri;
    private File cameraPhoto;

    @Override
    protected ActivityWsSyJgAddBinding getViewBinding() {
        return ActivityWsSyJgAddBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        ybBh = getIntent().getStringExtra("bh");
        binding.mRecycle.setLayoutManager(new LinearLayoutManager(this));

        listAdapter = new YbBhAddListAdapter(this, bfqyBeanList);
        binding.mRecycle.setAdapter(listAdapter);
        //
        imageAddImg = new ImageAddUtil(this, binding.gridImg);
        imageAddImg.setMax(10);
        imageAddImg.setOnImageAddListener(() -> {
            from = 1;
            requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        });
        //回显列表数据样本
        if (!TextUtils.isEmpty(ybBh)) {
            bfqyBeanList.add(ybBh);
            listAdapter.notifyDataSetChanged();
        } else {
            bfqyBeanList.add("");
            listAdapter.notifyDataSetChanged();
        }
        //
        EventBus.getDefault().register(this);
        //
        createFileMuLu();
    }

    @Override
    protected void initListener() {
        binding.btAdd.setOnClickListener(v -> {
            //新增
            if (ButtonUtils.isFastClick()) {
                bfqyBeanList.add("");
                listAdapter.notifyDataSetChanged();
            }
        });
        binding.btSubmit.setOnClickListener(v -> {
            if (ButtonUtils.isFastClick()) {
                postData();
            }
        });
    }

    @Override
    protected void getData() {
    }

    /**
     * 创建文件
     */
    private void createFileMuLu() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
                && ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {
            cameraPhoto = new File(PhotoUtil.createPath(this));
            try {
                if (cameraPhoto.exists()) {
                    cameraPhoto.delete();
                }
                cameraPhoto.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            // 权限未授权，需要申请权限
            requestPermission(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        }
    }

    /**
     * 提交数据
     */
    private void postData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.sy_bh);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");

        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("sjLy", "3");
            //
            JSONArray jsonArraySP = new JSONArray();
            if (bfqyBeanList != null && bfqyBeanList.size() > 0) {
                for (int i = 0; i < bfqyBeanList.size(); i++) {
                    if (!TextUtils.isEmpty(bfqyBeanList.get(i))) {
                        jsonArraySP.put(bfqyBeanList.get(i));
                    }
                }
            }
            if (jsonArraySP.length() <= 0) {
                XToastUtil.showToast(this, "请新增样本编号");
                return;
            }
            objectMap.put("ybCodeList", jsonArraySP);
            //
            JSONArray jsonArray = new JSONArray();
            if (imageAddImg != null && imageAddImg.getPaths() != null && imageAddImg.getPaths().size() > 0) {
                for (int i = 0; i < imageAddImg.getPaths().size(); i++) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("lj", imageAddImg.getPaths().get(i).getSavePath());
                    jsonObject.put("mc", imageAddImg.getPaths().get(i).getOriginName());
                    jsonObject.put("hz", StrUtils.getFileType(imageAddImg.getPaths().get(i).getOriginName()));
                    jsonObject.put("dx", imageAddImg.getPaths().get(i).getFileSize());
                    jsonArray.put(jsonObject);
                }
            }
            if (jsonArray.length() <= 0) {
                XToastUtil.showToast(this, "请上传样本照片");
                return;
            }
            objectMap.put("fjList", jsonArray);
        } catch (JSONException e) {
        }
        binding.btSubmit.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJsonNoCan(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(ShouYangAddActivity.this, getString(R.string.sub_sucess));
                    EventBus.getDefault().post(new TalkListEntity());
                    finish();
                } else {
                    XToastUtil.showToast(ShouYangAddActivity.this, xResultData.getErrorInfo());
                    binding.btSubmit.setEnabled(true);
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSubmit.setEnabled(true);
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case 1:
                    //图片
//                    uploadFile(data.getStringExtra("path"));
                    boolean isFinish = XFileUtil.bitmapToFilePath(ShouYangAddActivity.this, BitmapFactory.decodeFile(cameraPhoto.getAbsolutePath()), photoUri, cameraPhoto);
                    if (isFinish) {
                        uploadFile(cameraPhoto.getAbsolutePath());
                    }
                    break;
            }
        }
    }

    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (from == 1) {
//            Intent intent = new Intent(this, CameraActivity.class);//调用系统相机
//            startActivityForResult(intent, 1);
            createFileMuLu();
            Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
            photoUri = XFileUtil.getUriFromFile(this, cameraPhoto);
            intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
            startActivityForResult(intent, 1);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(WyYbBhBean event) {
        if (event != null) {
//            Log.i("yby", "onEvent: " + new Gson().toJson(event));
            if (bfqyBeanList.size() >= event.getPos()) {
                bfqyBeanList.set(event.getPos(), event.getNum());
            } else {
                bfqyBeanList.add(event.getNum());
            }
            listAdapter.notifyDataSetChanged();

            //检验编号--是否合格
            jiaoYanBh(event.getNum(), (code, id) -> {
                if (!TextUtils.isEmpty(code)) {
                    bfqyBeanList.remove(event.getNum());
                    listAdapter.notifyDataSetChanged();
                }
            });
        }
    }

    /**
     * 校验编号
     */
    private void jiaoYanBh(String bh, DialogUtils.OnResult onResult) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.syJy_bh);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("ybCode", bh);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJsonNoCan(this, requestParams, jiaMiString, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    onResult.onSuccess("", "");
                } else {
                    XToastUtil.showToast(ShouYangAddActivity.this, xResultData.getErrorInfo());
                    onResult.onSuccess("1", "1");
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    //上传
    private void uploadFile(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        imageAddImg.addImage(xResultData.getData().get(0));
                        imageAddImg.notifyData();
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}