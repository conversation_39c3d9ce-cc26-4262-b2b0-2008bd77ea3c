package com.linggan.jd831.ui.works.qiye.v2;


import android.text.TextUtils;
import android.view.View;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.BaiBeiListEntity;
import com.linggan.jd831.databinding.ActivityBaoBeiSureBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XHttpUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

/**
 * 类  名：出入库报备确认
 * 作  者：zxb
 * 说  明：
 * 时  间：2024/3/26 13:11
 */
public class BaoBeiSureActivity extends XBaseActivity<ActivityBaoBeiSureBinding> implements View.OnClickListener {

    private String bh, from;

    @Override
    protected ActivityBaoBeiSureBinding getViewBinding() {
        return ActivityBaoBeiSureBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        bh = getIntent().getStringExtra("bh");
        from = getIntent().getStringExtra("from");//标识走系统消息过来
        BaiBeiListEntity beiListEntity = (BaiBeiListEntity) getIntent().getSerializableExtra("info");
        if (beiListEntity != null) {
            binding.linBottom.setVisibility(View.VISIBLE);
            binding.sure.tvCkmz.setText(beiListEntity.getCkmc());
            binding.sure.tvJrsj.setText(beiListEntity.getJrsj());
            binding.sure.tvLksj.setText(beiListEntity.getLksj());
            binding.sure.tvBz.setText(beiListEntity.getBz());
            binding.sure.tvBbr.setText(beiListEntity.getBbrmc());
            binding.sure.tvBbsj.setText(beiListEntity.getBbsj());
        }
        EventBus.getDefault().register(this);
    }

    @Override
    protected void initListener() {
        binding.btFangQi.setOnClickListener(this);
        binding.btSure.setOnClickListener(this);
    }

    @Override
    protected void getData() {
        if (!TextUtils.isEmpty(from)) {
            String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bbbh=" + bh);
            RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.YZD_BB_INFO + "?query=" + jiaMiString);
            requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
            requestParams.addHeader("Origin-Content-Type", "application/json");
            XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
                @Override
                public void onSuccess(String result) {
                    XResultData<BaiBeiListEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<BaiBeiListEntity>>() {
                    }.getType());
                    if (xResultData.getStatus() == 0) {
                        if (xResultData.getData() != null) {
                            binding.sure.tvCkmz.setText(xResultData.getData().getCkmc());
                            binding.sure.tvJrsj.setText(xResultData.getData().getJrsj());
                            binding.sure.tvLksj.setText(xResultData.getData().getLksj());
                            binding.sure.tvBz.setText(xResultData.getData().getBz());
                            binding.sure.tvBbr.setText(xResultData.getData().getBbrmc());
                            binding.sure.tvBbsj.setText(xResultData.getData().getBbsj());
                            // 报备原因
                            binding.sure.tvBbyy.setText(StrUtils.getDev(xResultData.getData().getBbyys(), "-"));

                            // 如果是化学品相关的报备,显示化学品信息
                            if ("化学品出库".equals(xResultData.getData().getBbyys()) || "化学品入库".equals(xResultData.getData().getBbyys())) {
                                binding.sure.llChemicalInfo.setVisibility(View.VISIBLE);

                                binding.sure.llChemicalInfo.setVisibility(View.VISIBLE);
                                if (TextUtils.isEmpty(xResultData.getData().getCrkhxpmc())) {
                                    binding.sure.llTvChemicalName.setVisibility(View.GONE);
                                } else {
                                    binding.sure.llTvChemicalName.setVisibility(View.VISIBLE);
                                    binding.sure.tvChemicalName.setText(StrUtils.getDev(xResultData.getData().getCrkhxpmc(), "-"));
                                }
                                if (TextUtils.isEmpty(xResultData.getData().getCrksl())) {
                                    binding.sure.llChemicalQuantity.setVisibility(View.GONE);
                                } else {
                                    binding.sure.llChemicalQuantity.setVisibility(View.VISIBLE);
                                    binding.sure.tvChemicalQuantity.setText(StrUtils.getDev(xResultData.getData().getCrksl(), "-"));
                                }
                                if (TextUtils.isEmpty(xResultData.getData().getCrkdws())) {
                                    binding.sure.llChemicalUnit.setVisibility(View.GONE);
                                } else {
                                    binding.sure.llChemicalUnit.setVisibility(View.VISIBLE);
                                    binding.sure.tvChemicalUnit.setText(StrUtils.getDev(xResultData.getData().getCrkdws(), "-"));
                                }
//                                binding.sure.tvChemicalName.setText(StrUtils.getDev(xResultData.getData().getCrkhxpmc(), "-"));
//                                binding.sure.tvChemicalQuantity.setText(StrUtils.getDev(xResultData.getData().getCrksl(), "-"));
//                                binding.sure.tvChemicalUnit.setText(StrUtils.getDev(xResultData.getData().getCrkdws(), "-"));
                            } else {
                                binding.sure.llChemicalInfo.setVisibility(View.GONE);
                            }
                            //
                            binding.msg.tvBbr.setText(xResultData.getData().getQrrmc());
                            binding.msg.tvBbsj.setText(xResultData.getData().getQrsj());
                            binding.msg.tvBz.setText(StrUtils.getDev(xResultData.getData().getBhbz(), "无"));
                            if (!TextUtils.isEmpty(xResultData.getData().getZt().getCode())) {
                                //2已确认；3已驳回；1未确认 4已逾期
                                switch (xResultData.getData().getZt().getCode()) {
                                    case "2":
                                        binding.msg.tvZt.setText("已确认");
                                        binding.msg.linQrr.setVisibility(View.VISIBLE);
                                        binding.msg.linQrSj.setVisibility(View.VISIBLE);
                                        binding.linBottom.setVisibility(View.GONE);
                                        binding.linSure.setVisibility(View.VISIBLE);
                                        break;
                                    case "3":
                                        binding.msg.tvZt.setText("已驳回");
                                        binding.msg.linQrr.setVisibility(View.VISIBLE);
                                        binding.msg.linQrSj.setVisibility(View.VISIBLE);
                                        binding.msg.linBhBz.setVisibility(View.VISIBLE);
                                        binding.linBottom.setVisibility(View.GONE);
                                        binding.linSure.setVisibility(View.VISIBLE);
                                        break;
                                    case "1":
                                        binding.msg.tvZt.setText("待确认");
                                        binding.linBottom.setVisibility(View.VISIBLE);
                                        break;
                                    case "4":
                                        binding.msg.tvZt.setText("已逾期");
                                        binding.linBottom.setVisibility(View.GONE);
                                        binding.linSure.setVisibility(View.VISIBLE);
                                        break;
                                    case "5":
                                        binding.msg.tvZt.setText("已撤销");
                                        binding.msg.linCxsj.setVisibility(View.VISIBLE);
                                        binding.msg.tvCxsj.setText(xResultData.getData().getQrsj());
                                        binding.linBottom.setVisibility(View.GONE);
                                        binding.linSure.setVisibility(View.VISIBLE);
                                        break;
                                }
                            }
                        }
                    } else {
                        XToastUtil.showToast(BaoBeiSureActivity.this, xResultData.getErrorInfo());
                    }
                }

                @Override
                public void onFailed(int code, String failedMsg) {
                }

                @Override
                public void onFinished() {
                }
            });
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.bt_fang_qi) {
            //驳回
            XIntentUtil.redirectToNextActivity(this, BaoBeiRejectActivity.class, "bh", bh);
        } else if (v.getId() == R.id.bt_sure) {
            //确定
            String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + bh);
            RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.YZD_BB_QR + "?query=" + jiaMiString);
            requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
            requestParams.addHeader("Origin-Content-Type", "application/json");
            XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), new XHttpResponseCallBack() {
                @Override
                public void onSuccess(String result) {
                    XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                    }.getType());
                    if (xResultData.getStatus() == 0) {
                        XToastUtil.showToast(BaoBeiSureActivity.this, getString(R.string.submit_scess));
                        EventBus.getDefault().post(new BaiBeiListEntity());
                    } else {
                        XToastUtil.showToast(BaoBeiSureActivity.this, xResultData.getErrorInfo());
                    }
                }

                @Override
                public void onFailed(int code, String failedMsg) {
                }

                @Override
                public void onFinished() {
                }
            });
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaiBeiListEntity event) {
        if (event != null) {
            finish();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
