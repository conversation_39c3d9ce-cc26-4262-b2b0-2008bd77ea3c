package com.linggan.jd831.ui.works.sewage.sampledevice;

import com.linggan.jd831.bean.CyDeviceDetailEntity;

public class SamplingLogEvent {
    private CyDeviceDetailEntity.Cyrz[] cyrzs;

    public SamplingLogEvent() {
    }

    public SamplingLogEvent(CyDeviceDetailEntity.Cyrz[] cyrzs) {
        this.cyrzs = cyrzs;
    }

    public CyDeviceDetailEntity.Cyrz[] getCyrzs() {
        return cyrzs;
    }

    public void setCyrzs(CyDeviceDetailEntity.Cyrz[] cyrzs) {
        this.cyrzs = cyrzs;
    }
}