package com.linggan.jd831.ui.works.transport;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.lgfzd.base.base.XBaseActivity;
import com.linggan.jd831.R;
import com.linggan.jd831.databinding.ActivityTransportDriverBinding;
import com.linggan.jd831.ui.works.transport.model.TransportDetail;

import java.util.ArrayList;
import java.util.List;

public class TransportDriverActivity extends XBaseActivity<ActivityTransportDriverBinding> {

    private List<View> driverViews = new ArrayList<>();
    private ArrayList<TransportDetail.Transporter> driverInfos = new ArrayList<>();
    private ArrayList<TransportDetail.Transporter> originalDrivers = new ArrayList<>();

    public static final String EXTRA_DRIVERS = "drivers";
    public static final String EXTRA_DELETED_DRIVER_IDS = "deleted_driver_ids";

    public static void start(Activity activity, int requestCode, ArrayList<TransportDetail.Transporter> drivers) {
        Intent intent = new Intent(activity, TransportDriverActivity.class);
        if (drivers != null) {
            intent.putExtra(EXTRA_DRIVERS, drivers);
        }
        activity.startActivityForResult(intent, requestCode);
    }

    @Override
    protected ActivityTransportDriverBinding getViewBinding() {
        return ActivityTransportDriverBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setTitle("运输司机信息");

        // 获取传入的司机数据进行回显
        Intent intent = getIntent();
        if (intent != null && intent.hasExtra(EXTRA_DRIVERS)) {
            ArrayList<TransportDetail.Transporter> drivers =
                    (ArrayList<TransportDetail.Transporter>) intent.getSerializableExtra(EXTRA_DRIVERS);
            if (drivers != null) {
                // 保存原始司机列表
                originalDrivers.addAll(drivers);
                for (TransportDetail.Transporter driver : drivers) {
                    addDriverView(driver);
                }
            }
        }

        initViews();
    }

    private void initViews() {
        // 添加司机按钮点击事件
        binding.tvAddDriver.setOnClickListener(v -> addDriverView());

        // 保存按钮点击事件
        binding.btnSave.setOnClickListener(v -> {
            if (validateInput()) {
                saveDriverInfo();
            }
        });
    }

    private void addDriverView(TransportDetail.Transporter driverInfo) {
        View driverView = LayoutInflater.from(this).inflate(R.layout.item_driver, null);
        TextView tvDelete = driverView.findViewById(R.id.tv_delete);
        TextView tvDriverTitle = driverView.findViewById(R.id.tv_driver_title);
        EditText etName = driverView.findViewById(R.id.et_name);
        EditText etIdNumber = driverView.findViewById(R.id.et_id_number);
        EditText etPhone = driverView.findViewById(R.id.et_phone);

        // 设置司机标题
        int driverNum = driverViews.size() + 1;
        tvDriverTitle.setText("运输司机" + driverNum);

        // 回显数据
        if (driverInfo != null) {
            etName.setText(driverInfo.getYsrXm());
            etIdNumber.setText(driverInfo.getYsrZjhm());
            etPhone.setText(driverInfo.getYsrLxdh());
            // 将driverInfo对象保存到View的tag中，用于后续获取id
            driverView.setTag(driverInfo);
        }

        // 删除按钮点击事件 - 简化为只移除视图
        tvDelete.setOnClickListener(v -> {
            binding.llDrivers.removeView(driverView);
            driverViews.remove(driverView);

            // 重新设置所有司机标题
            for (int i = 0; i < driverViews.size(); i++) {
                TextView title = driverViews.get(i).findViewById(R.id.tv_driver_title);
                title.setText("运输司机" + (i + 1));
            }
        });

        binding.llDrivers.addView(driverView);
        driverViews.add(driverView);
    }

    private void addDriverView() {
        addDriverView(null);
    }

    private boolean validateInput() {
        for (View driverView : driverViews) {
            EditText etName = driverView.findViewById(R.id.et_name);
            EditText etIdNumber = driverView.findViewById(R.id.et_id_number);
            EditText etPhone = driverView.findViewById(R.id.et_phone);

            if (TextUtils.isEmpty(etName.getText()) ||
                    TextUtils.isEmpty(etIdNumber.getText()) ||
                    TextUtils.isEmpty(etPhone.getText())) {
                Toast.makeText(this, "请填写完整司机信息", Toast.LENGTH_SHORT).show();
                return false;
            }
        }
        return true;
    }

    private void saveDriverInfo() {
        driverInfos.clear();
        ArrayList<String> deletedDriverIds = new ArrayList<>();

        // 1. 收集当前所有司机信息
        for (View driverView : driverViews) {
            EditText etName = driverView.findViewById(R.id.et_name);
            EditText etIdNumber = driverView.findViewById(R.id.et_id_number);
            EditText etPhone = driverView.findViewById(R.id.et_phone);

            TransportDetail.Transporter info = new TransportDetail.Transporter();
            // 如果存在原有数据,获取其id
            if (driverView.getTag() instanceof TransportDetail.Transporter) {
                TransportDetail.Transporter oldInfo = (TransportDetail.Transporter) driverView.getTag();
                info.setId(oldInfo.getId());
            }

            info.setYsrXm(etName.getText().toString());
            info.setYsrZjhm(etIdNumber.getText().toString());
            info.setYsrLxdh(etPhone.getText().toString());

            driverInfos.add(info);
        }

        // 2. 对比原始列表,找出被删除的司机ID
        for (TransportDetail.Transporter originalDriver : originalDrivers) {
            boolean isDeleted = true;
            for (TransportDetail.Transporter currentDriver : driverInfos) {
                if (originalDriver.getId() != null &&
                        originalDriver.getId().equals(currentDriver.getId())) {
                    isDeleted = false;
                    break;
                }
            }
            if (isDeleted && originalDriver.getId() != null) {
                deletedDriverIds.add(originalDriver.getId());
            }
        }

        Intent resultIntent = new Intent();
        resultIntent.putExtra(EXTRA_DRIVERS, driverInfos);
        // 只在保存时添加已删除的司机ID列表
        resultIntent.putStringArrayListExtra(EXTRA_DELETED_DRIVER_IDS, deletedDriverIds);
        setResult(RESULT_OK, resultIntent);
        finish();
    }

    @Override
    protected void initView() {
    }

    @Override
    protected void initListener() {
    }

    @Override
    protected void getData() {
    }
} 