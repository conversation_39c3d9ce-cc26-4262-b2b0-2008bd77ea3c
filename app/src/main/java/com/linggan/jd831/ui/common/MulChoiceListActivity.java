package com.linggan.jd831.ui.common;

import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.MulCHoiceLIst1Holder;
import com.linggan.jd831.adapter.MulCHoiceLIstHolder;
import com.linggan.jd831.bean.ZiDianEntity;
import com.linggan.jd831.bean.ZiDianEventEntity;
import com.linggan.jd831.databinding.ActivityChoiceListBinding;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.UserInfoUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

/**
 * 类  名：多选通用页面
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/13  10:17
 * 版  权：凌感科技
 */
public class MulChoiceListActivity extends XBaseActivity<ActivityChoiceListBinding> {

    private int fromPage;//来源于那一页面
    private String param;//字典类型
    private String title, val;
    private ZiDianEventEntity ziDianEvent;

    @Override
    protected ActivityChoiceListBinding getViewBinding() {
        return ActivityChoiceListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        binding.base.tvRight.setText(getString(R.string.sure));
        binding.base.tvRight.setTextColor(getResources().getColor(R.color.color_main));
        binding.recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
        fromPage = getIntent().getIntExtra("from", 0);
        param = getIntent().getStringExtra("param");
        val = getIntent().getStringExtra("val");
        title = getIntent().getStringExtra("title");
        setTitle(title);

        ziDianEvent = (ZiDianEventEntity) getIntent().getSerializableExtra("data");//回显数据
        if (ziDianEvent != null && ziDianEvent.getZiDianEntityList() != null && ziDianEvent.getZiDianEntityList().size() > 0) {
            binding.recycler.getAdapter().bindHolder(new MulCHoiceLIst1Holder(ziDianEvent.getZiDianEntityList()));
        } else {
            binding.recycler.getAdapter().bindHolder(new MulCHoiceLIstHolder());
        }
    }

    @Override
    protected void getData() {
        if (!TextUtils.isEmpty(param) && param.equals("kdx")) {
            //关爱类别
            FactoryUtils.getCareHelpType(this, val, result -> {
                binding.recycler.getAdapter().setData(0, result);
            });
        } else if (!TextUtils.isEmpty(param) && param.equals("cklb")) {
            //仓库列表
            FactoryUtils.getCangKuList(this, val, result -> {
                if (result != null && result.size() > 0) {
                    binding.tvNoData.setVisibility(View.GONE);
                    binding.recycler.getAdapter().setData(0, result);
                } else {
                    binding.tvNoData.setVisibility(View.VISIBLE);
                }
            });
        } else if (!TextUtils.isEmpty(param) && param.equals("zfxjdw")) {
            //转发下级单位
            FactoryUtils.getUnit1(this, UserInfoUtils.getUserInfo().getYhDwdm(), arrayList -> {
                if (arrayList != null && arrayList.size() > 0) {
                    binding.tvNoData.setVisibility(View.GONE);
                    binding.recycler.getAdapter().setData(0, arrayList);
                } else {
                    binding.tvNoData.setVisibility(View.VISIBLE);
                }
            });
        } else {
            FactoryUtils.getBaseDataType(this, param, result -> {
                binding.recycler.getAdapter().setData(0, result);
            });
        }
    }

    @Override
    protected void initListener() {
        binding.base.btRight.setOnClickListener(v -> {
            //保存
            if (binding.recycler.getAdapter().getData(0) != null) {
                List<ZiDianEntity> list = binding.recycler.getAdapter().getData(0);
                if (list != null && list.size() > 0) {
                    List<ZiDianEntity> entityList = new ArrayList<>();
                    for (int i = 0; i < list.size(); i++) {
                        if (list.get(i).isChoice()) {
                            entityList.add(list.get(i));
                        }
                    }
                    if (entityList != null && entityList.size() > 0) {
                        ZiDianEventEntity entity = new ZiDianEventEntity();
                        entity.setZiDianEntityList(entityList);
                        entity.setFrom(fromPage);
                        EventBus.getDefault().post(entity);
                        finish();
                    } else {
                        XToastUtil.showToast(this, "请选择内容");
                    }
                }
            }
        });
    }
}
