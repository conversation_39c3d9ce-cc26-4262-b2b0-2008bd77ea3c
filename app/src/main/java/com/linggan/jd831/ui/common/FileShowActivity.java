package com.linggan.jd831.ui.common;

import android.app.Dialog;
import android.graphics.Canvas;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.utils.XToastUtil;
import com.lidong.pdf.listener.OnDrawListener;
import com.lidong.pdf.listener.OnLoadCompleteListener;
import com.lidong.pdf.listener.OnPageChangeListener;
import com.linggan.jd831.R;
import com.linggan.jd831.databinding.ActivityFileShowBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.StrUtils;

import org.xutils.common.Callback;
import org.xutils.http.RequestParams;
import org.xutils.x;

import java.io.File;

import xyz.doikki.videocontroller.StandardVideoController;
import xyz.doikki.videocontroller.component.CompleteView;
import xyz.doikki.videocontroller.component.ErrorView;
import xyz.doikki.videocontroller.component.GestureView;
import xyz.doikki.videocontroller.component.TitleView;
import xyz.doikki.videocontroller.component.VodControlView;


/**
 * 类  名： 文件查看类
 * 描   述：
 * 作  者：ZXB
 * 时  间：2022/2/18 14:05
 * 版  权：LGKJ
 */
public class FileShowActivity extends XBaseActivity<ActivityFileShowBinding> implements OnPageChangeListener, OnLoadCompleteListener, OnDrawListener {

    private String fileUrl, upload;
    private boolean isPdfUploadFinish = false, isPdfUploadLoad = false;
    // 视频播放-控件
    protected StandardVideoController mController;
    protected ErrorView mErrorView;
    protected CompleteView mCompleteView;
    protected TitleView mTitleView;

    @Override
    protected ActivityFileShowBinding getViewBinding() {
        return ActivityFileShowBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        String title = getIntent().getStringExtra("title");
        if (!TextUtils.isEmpty(title)) {
            setTitle(getIntent().getStringExtra("title"));
        }
        fileUrl = getIntent().getStringExtra("url");
        upload = getIntent().getStringExtra("upload");
        //播放器设置
        mController = new StandardVideoController(this);
        mErrorView = new ErrorView(this);
        mController.addControlComponent(mErrorView);
        mCompleteView = new CompleteView(this);
        mController.addControlComponent(mCompleteView);
        mTitleView = new TitleView(this);
        mController.addControlComponent(mTitleView);
        mController.addControlComponent(new VodControlView(this));
        mController.addControlComponent(new GestureView(this));
        mController.setEnableOrientation(true);
        binding.player.setVideoController(mController);
        //
        getData();
    }

    @Override
    protected void initListener() {
        if (!TextUtils.isEmpty(upload)) {
//            binding.top.tvRight.setText("下载");
//            binding.top.btRight.setOnClickListener(v -> {
//                if (fileUrl.endsWith(".pdf")) {
//                    if (isPdfUploadFinish = true) {
//                        XToastUtil.showToast(this, "文件下载完成");
//                    } else if (isPdfUploadFinish == false && isPdfUploadLoad == true) {
//                        XToastUtil.showToast(this, "文件下正在载中，请稍后");
//                    }
//                } else {
//                    //下载其他文件（word/excel/ppt/txt）
//                    uploadAllFile();
//                }
//            });
        }
    }

    public void getData() {
        if (!TextUtils.isEmpty(fileUrl)) {
            if (fileUrl.endsWith(".doc") || fileUrl.endsWith(".docx") || fileUrl.endsWith(".txt") || fileUrl.endsWith(".xls") || fileUrl.endsWith(".xlsx")) {
                binding.mWebView.setVisibility(View.VISIBLE);
                setWeb(fileUrl);
//                uploadFile();
            } else if (fileUrl.endsWith(".pdf")) {
                binding.pdfViewNetNew.setVisibility(View.VISIBLE);
                displayFromFile(fileUrl, StrUtils.getFileNme(fileUrl));
            } else if (fileUrl.endsWith(".mp4") || fileUrl.endsWith(".wmv") || fileUrl.endsWith(".avi") || fileUrl.endsWith(".rm") || fileUrl.endsWith(".ram")) {
                binding.player.setVisibility(View.VISIBLE);
                binding.player.setUrl(fileUrl);
                binding.player.start();
            } else {
                XToastUtil.showToast(this, "暂不支持文件预览");
            }
        } else {
            XToastUtil.showToast(this, "暂无可以预览的文件");
        }
    }

    private void displayFromFile(String fileUrl, String fileName) {
        binding.pdfViewNetNew.fileFromLocalStorage(this, this, this, fileUrl, fileName);   //设置pdf文件地址
    }

    /**
     * 下载文件
     */
    private void uploadFile() {
        if (XFileUtil.isExist(XFileUtil.SD_BASE + StrUtils.getFileNme(fileUrl)) == false) {
            Log.i("sss", "onSuccess: ");
            Dialog dialog = DialogUtils.showLoadDialog(this, getString(R.string.loading));
            dialog.show();
            Log.i("sss", "onSuccess1: ");
            RequestParams params = new RequestParams(fileUrl);
            //设置不自动命名
            params.setAutoRename(false);
            //设置文件保存路径
            params.setSaveFilePath(XFileUtil.SD_BASE + StrUtils.getFileNme(fileUrl));
            //开始下载
            x.http().get(params, new Callback.ProgressCallback<File>() {
                @Override
                public void onSuccess(File result) {
                    dialog.dismiss();
                    Log.i("sss", "onSuccess2: ");
                    isPdfUploadFinish = true;
                }

                @Override
                public void onError(Throwable ex, boolean isOnCallback) {
                    dialog.dismiss();
                }

                @Override
                public void onCancelled(CancelledException cex) {
                }

                @Override
                public void onFinished() {
                }

                @Override
                public void onWaiting() {
                }

                @Override
                public void onStarted() {
                }

                @Override
                public void onLoading(long total, long current, boolean isDownloading) {
                    isPdfUploadLoad = true;
                }
            });
        } else {
            File file = new File(XFileUtil.SD_BASE + StrUtils.getFileNme(fileUrl));
        }
    }

    /**
     * 获取文件后缀
     *
     * @param paramString
     * @return
     */
    private String getFileType(String paramString) {
        String str = "";
        if (TextUtils.isEmpty(paramString)) {
            return str;
        }
        int i = paramString.lastIndexOf('.');
        if (i <= -1) {
            return str;
        }
        str = paramString.substring(i + 1);
        return str;
    }

    @Override
    public void onLayerDrawn(Canvas canvas, float pageWidth, float pageHeight, int displayedPage) {
    }

    @Override
    public void loadComplete(int nbPages) {
    }

    @Override
    public void onPageChanged(int page, int pageCount) {

    }

    private void setWeb(String wordExcelFile) {
        WebSettings webseting = binding.mWebView.getSettings();
        webseting.setJavaScriptEnabled(true);
        webseting.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.SINGLE_COLUMN);
        webseting.setLoadWithOverviewMode(true);
        webseting.setDefaultTextEncodingName("UTF-8");
        webseting.setLoadsImagesAutomatically(true);    //设置自动加载图片
        webseting.setDefaultFontSize(30);
        webseting.setTextSize(WebSettings.TextSize.LARGEST);
        webseting.setUseWideViewPort(true);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webseting.setMixedContentMode(0);
        }
        binding.mWebView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }
        });
        binding.mWebView.loadUrl("https://view.officeapps.live.com/op/embed.aspx?src=" + wordExcelFile);
    }

    /**
     * 下载文件
     */
    private void uploadAllFile() {
        if (XFileUtil.isExist(XFileUtil.SD_BASE + StrUtils.getFileNme(fileUrl)) == false) {
            Log.i("sss", "onSuccess: ");
            Dialog dialog = DialogUtils.showLoadDialog(this, getString(R.string.loading));
            dialog.show();
            Log.i("sss", "onSuccess1: ");
            RequestParams params = new RequestParams(fileUrl);
            //设置不自动命名
            params.setAutoRename(false);
            //设置文件保存路径
            params.setSaveFilePath(XFileUtil.SD_BASE + StrUtils.getFileNme(fileUrl));
            //开始下载
            x.http().get(params, new Callback.ProgressCallback<File>() {
                @Override
                public void onSuccess(File result) {
                    dialog.dismiss();
                    XToastUtil.showToast(FileShowActivity.this, "文件保存成功：" + result.getAbsolutePath());
                    Log.i("sss", "onSuccess2: ");
                }

                @Override
                public void onError(Throwable ex, boolean isOnCallback) {
                    dialog.dismiss();
                }

                @Override
                public void onCancelled(CancelledException cex) {
                }

                @Override
                public void onFinished() {
                }

                @Override
                public void onWaiting() {
                }

                @Override
                public void onStarted() {
                }

                @Override
                public void onLoading(long total, long current, boolean isDownloading) {
                }
            });
        } else {
            XToastUtil.showToast(FileShowActivity.this, "文件保存成功：" + XFileUtil.SD_BASE + StrUtils.getFileNme(fileUrl));
        }
    }


    @Override
    protected void onPause() {
        super.onPause();
        binding.player.pause();
    }

    @Override
    protected void onResume() {
        super.onResume();
        binding.player.resume();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding.player.release();
    }

    @Override
    public void onBackPressed() {
        if (!binding.player.onBackPressed()) {
            super.onBackPressed();
        }
    }

}
