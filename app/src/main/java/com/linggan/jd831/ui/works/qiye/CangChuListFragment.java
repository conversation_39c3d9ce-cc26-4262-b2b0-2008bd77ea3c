package com.linggan.jd831.ui.works.qiye;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseFragment;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.linggan.jd831.utils.XHttpUtils;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.view.recycle.XRecyclerView;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.CangLsCodeListHolder;
import com.linggan.jd831.bean.CangLsjlListEntity;
import com.linggan.jd831.bean.QiYeSxBean;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;
import org.xutils.view.annotation.ContentView;
import org.xutils.view.annotation.ViewInject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 出库
 */
@ContentView(R.layout.fragment_list)
public class CangChuListFragment extends XBaseFragment implements XRefreshLayout.PullLoadMoreListener {

    @ViewInject(R.id.recycler)
    private XRecyclerView recycler;
    private String type;
    @ViewInject(R.id.iv_no_data)
    private ImageView ivNoData;
    private int page = 1, totalPage = 0;
    private boolean isShowDialog = true;


    public static CangChuListFragment newInstance() {
        CangChuListFragment benefitTabFragment = new CangChuListFragment();
        return benefitTabFragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(getActivity()));
        recycler.getAdapter().bindHolder(new CangLsCodeListHolder());
        recycler.setOnPullLoadMoreListener(this);
    }

    @Override
    protected void lazyLoad() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.YZDQY_LS_LIST);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("queryType", "0");//（0出库；1入库；2报损）
        objectMap.put("page", page);
        objectMap.put("rows", XConstantUtils.PAGE_SIZE);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJson(getActivity(), requestParams, jiaMiString, DialogUtils.showLoadDialog(getActivity(), ""), isShowDialog, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<XResultPageData<CangLsjlListEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XResultPageData<CangLsjlListEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    totalPage = xResultData.getData().getTotalPage();
                    if (xResultData.getData().getRecords() != null && xResultData.getData().getRecords().size() > 0) {
                        ivNoData.setVisibility(View.GONE);
                        if (page == 1) {
                            recycler.getAdapter().setData(0, xResultData.getData().getRecords());
                        } else {
                            recycler.getAdapter().addDataAll(0, xResultData.getData().getRecords());
                        }
                    } else {
                        if (page == 1) {
                            recycler.getAdapter().setData(0, new ArrayList<>());
                            ivNoData.setVisibility(View.VISIBLE);
                        }
                    }
                } else {
                    if (page == 1) {
                        recycler.getAdapter().setData(0, new ArrayList<>());
                        ivNoData.setVisibility(View.VISIBLE);
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                recycler.setPullLoadMoreCompleted();
            }
        });
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(QiYeSxBean chemicalBean) {
        if (chemicalBean != null && chemicalBean.getType().equals("0")) {
            page = 1;
            isShowDialog = false;
            lazyLoad();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void onRefresh() {
        page = 1;
        isShowDialog = false;
        lazyLoad();
    }

    @Override
    public boolean onLoadMore() {
        if (page < totalPage) {
            page++;
            isShowDialog = false;
            lazyLoad();
            return true;
        }
        return false;
    }
}
