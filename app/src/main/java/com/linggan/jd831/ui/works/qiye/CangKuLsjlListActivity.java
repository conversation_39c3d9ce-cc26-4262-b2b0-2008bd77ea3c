package com.linggan.jd831.ui.works.qiye;

import androidx.fragment.app.Fragment;

import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.view.XTabFragmentPagerAdapter;
import com.linggan.jd831.databinding.ActivityBaseTabListBinding;

import java.util.ArrayList;
import java.util.List;

/**
 * 类  名：首页-易制毒企业--历史记录列表
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class CangKuLsjlListActivity extends XBaseActivity<ActivityBaseTabListBinding> {

    private int from;

    @Override
    protected ActivityBaseTabListBinding getViewBinding() {
        return ActivityBaseTabListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        from = getIntent().getIntExtra("from", 0);
        List<Fragment> fragments = new ArrayList<>();
        fragments.add(CangChuListFragment.newInstance());
        fragments.add(CangRuListFragment.newInstance());
        fragments.add(CangBaoSuListFragment.newInstance());
        List<String> list = new ArrayList<>();
        list.add("出库");
        list.add("入库");
        list.add("报损");
        binding.viewPager.setAdapter(new XTabFragmentPagerAdapter(getSupportFragmentManager(), fragments, list));
        binding.tabLayout.setupWithViewPager(binding.viewPager);

        //默认设置选中那个具体的选项卡
        binding.viewPager.setCurrentItem(from, false);
        binding.tabLayout.getTabAt(from).select();
    }

    @Override
    protected void initListener() {
    }

    @Override
    protected void getData() {
    }

}
