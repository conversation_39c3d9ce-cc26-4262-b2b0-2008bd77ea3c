package com.linggan.jd831.ui.works.transport;


import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.InputEntity;
import com.linggan.jd831.databinding.ActivityTransportPermitListBinding;
import com.linggan.jd831.ui.works.transport.adapter.TransportPermitAdapter;
import com.linggan.jd831.ui.works.transport.add.TransportPermitEditActivity;
import com.linggan.jd831.ui.works.transport.constants.TransportConstants;
import com.linggan.jd831.ui.works.transport.model.TransportPermitData;
import com.linggan.jd831.utils.XHttpUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

import java.util.ArrayList;
import java.util.List;

public class TransportPermitListActivity extends XBaseActivity<ActivityTransportPermitListBinding> {

    private TransportPermitAdapter adapter;
    private List<TransportPermitData> permits = new ArrayList<>();
    private static final int REQUEST_ADD_PERMIT = 102; // 添加请求码常量
    private SwipeRefreshLayout swipeRefresh;

    @Override
    protected ActivityTransportPermitListBinding getViewBinding() {
        return ActivityTransportPermitListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EventBus.getDefault().register(this);
        setTitle("运输许可证");
        initViews();
        loadData();
    }

    private void initViews() {
        binding.base.tvRight.setText("新增");
        binding.base.tvRight.setTextColor(getResources().getColor(R.color.color_main));
        binding.base.tvRight.setOnClickListener(v -> {
            TransportPermitEditActivity.startForAdd(this, REQUEST_ADD_PERMIT);
        });
        binding.rvPermits.setLayoutManager(new LinearLayoutManager(this));
        adapter = new TransportPermitAdapter(permits);
        binding.rvPermits.setAdapter(adapter);

        adapter.setOnEditClickListener((permit, position) -> {
            TransportPermitEditActivity.startForEdit(this, REQUEST_ADD_PERMIT, permit);
        });

        adapter.setOnUseClickListener((permit, position) -> {
            Intent resultIntent = new Intent();
            resultIntent.putExtra("permit_number", permit.getZsbh());
            resultIntent.putExtra("permit_item", permit);
            setResult(RESULT_OK, resultIntent);
            finish();
        });

        // 初始化下拉刷新
        swipeRefresh = binding.swipeRefresh;
        swipeRefresh.setColorSchemeResources(R.color.color_main);
        swipeRefresh.setOnRefreshListener(() -> {
            loadData();
        });
    }

    private void loadData() {
        getTransportPermitList();
    }

    private void getTransportPermitList() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + "/precursor/ysgl/baxx/sj/lb");
        requestParams.addHeader("Origin-Content-Type", "application/json");

        XHttpUtils.get(this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                try {
                    XResultData<List<TransportPermitData>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<TransportPermitData>>>() {
                    }.getType());

                    if (result != null && xResultData.getStatus() == 0) {
                        handlePermitList(xResultData.getData());
                    } else {
                        XToastUtil.showToast(TransportPermitListActivity.this,
                                xResultData != null ? xResultData.getErrorInfo() : "获取运输许可证列表失败");
                    }
                } catch (Exception e) {
                    XToastUtil.showToast(TransportPermitListActivity.this, "数据解析失败");
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                // 结束下拉刷新动画
                if (swipeRefresh != null) {
                    swipeRefresh.setRefreshing(false);
                }
                XToastUtil.showToast(TransportPermitListActivity.this, "获取运输许可证列表失败: " + failedMsg);
            }

            @Override
            public void onFinished() {
                // 可以在这里关闭加载框
            }
        });
    }

    private void handlePermitList(List<TransportPermitData> permitDataList) {
        // 结束下拉刷新动画
        if (swipeRefresh != null) {
            swipeRefresh.setRefreshing(false);
        }

        if (permitDataList == null || permitDataList.isEmpty()) {
            permits.clear();
            adapter.notifyDataSetChanged();
            // 显示空数据提示
            binding.tvNoData.setVisibility(View.VISIBLE);
            binding.rvPermits.setVisibility(View.GONE);
            return;
        }

        // 有数据时隐藏空提示
        binding.tvNoData.setVisibility(View.GONE);
        binding.rvPermits.setVisibility(View.VISIBLE);

        permits.clear();
        permits.addAll(permitDataList);
        adapter.notifyDataSetChanged();
    }

    @Override
    protected void initView() {
    }

    @Override
    protected void initListener() {
    }

    @Override
    protected void getData() {
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_ADD_PERMIT && resultCode == RESULT_OK) {
            if (data != null && data.hasExtra(TransportConstants.EXTRA_PERMIT)) {

            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(InputEntity item) {
        if (item != null) {
            loadData();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
} 