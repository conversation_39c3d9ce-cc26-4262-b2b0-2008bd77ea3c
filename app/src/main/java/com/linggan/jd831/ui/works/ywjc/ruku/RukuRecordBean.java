package com.linggan.jd831.ui.works.ywjc.ruku;

import com.google.gson.annotations.SerializedName;
import java.io.Serializable;

/**
 * 入库记录实体类
 */
public class RukuRecordBean implements Serializable {
    /** 入库记录ID */
    @SerializedName("id")
    private Long id;
    
    /** 经办人姓名 */
    @SerializedName("name")
    private String name;
    
    /** 经办人姓名 */
    @SerializedName("jdbXm")
    private String jdbXm;
    
    /** 入库时间 */
    @SerializedName("time")
    private String time;
    
    /** 入库时间 */
    @SerializedName("bbsj")
    private String bbsj;
    
    /** 身份证号 */
    @SerializedName("idNumber")
    private String idNumber;
    
    /** 服药量 */
    @SerializedName("dose")
    private String dose;
    
    /** 入库量 */
    @SerializedName("storageAmount")
    private String storageAmount;
    
    /** 剩余库存 */
    @SerializedName("remainingStock")
    private String remainingStock;
    
    /** 头像URL */
    @SerializedName("avatarUrl")
    private String avatarUrl;
    
    /** 状态类型：1-长期未服药(红色)，2-正常(绿色)，3-出组审批中(橙色)，4-已出组(灰色) */
    @SerializedName("statusType")
    private int statusType;
    
    /** 数量(ml) */
    @SerializedName("sl")
    private Float sl;

    public String getZp() {
        return zp;
    }

    public void setZp(String zp) {
        this.zp = zp;
    }

    @SerializedName("zp")
    private String zp;

    // 无参构造函数
    public RukuRecordBean() {
    }

    // 全参构造函数
    public RukuRecordBean(Long id, String name, String jdbXm, String time, String bbsj, String idNumber, String storageAmount,
                          String remainingStock, String avatarUrl, int statusType, Float sl) {
        this.id = id;
        this.name = name;
        this.jdbXm = jdbXm;
        this.time = time;
        this.bbsj = bbsj;
        this.idNumber = idNumber;
        this.storageAmount = storageAmount;
        this.remainingStock = remainingStock;
        this.avatarUrl = avatarUrl;
        this.statusType = statusType;
        this.sl = sl;
    }

    // 带服药量的构造函数
    public RukuRecordBean(Long id, String name, String jdbXm, String time, String bbsj, String idNumber, String dose, String storageAmount,
                          String remainingStock, String avatarUrl, int statusType, Float sl) {
        this.id = id;
        this.name = name;
        this.jdbXm = jdbXm;
        this.time = time;
        this.bbsj = bbsj;
        this.idNumber = idNumber;
        this.dose = dose;
        this.storageAmount = storageAmount;
        this.remainingStock = remainingStock;
        this.avatarUrl = avatarUrl;
        this.statusType = statusType;
        this.sl = sl;
    }

    // 用于RkjlListActivity中的构造函数
    public RukuRecordBean(String name, String time, String idNumber, String dose, String storageAmount,
                          String remainingStock, String avatarUrl, int statusType) {
        this.name = name;
        this.time = time;
        this.idNumber = idNumber;
        this.dose = dose;
        this.storageAmount = storageAmount;
        this.remainingStock = remainingStock;
        this.avatarUrl = avatarUrl;
        this.statusType = statusType;
    }

    // Getter和Setter方法
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }

    public String getDose() {
        return dose;
    }

    public void setDose(String dose) {
        this.dose = dose;
    }

    public String getStorageAmount() {
        return storageAmount;
    }

    public void setStorageAmount(String storageAmount) {
        this.storageAmount = storageAmount;
    }

    public String getRemainingStock() {
        return remainingStock;
    }

    public void setRemainingStock(String remainingStock) {
        this.remainingStock = remainingStock;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public int getStatusType() {
        return statusType;
    }

    public void setStatusType(int statusType) {
        this.statusType = statusType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getJdbXm() {
        return jdbXm;
    }

    public void setJdbXm(String jdbXm) {
        this.jdbXm = jdbXm;
    }

    public String getBbsj() {
        return bbsj;
    }

    public void setBbsj(String bbsj) {
        this.bbsj = bbsj;
    }

    public Float getSl() {
        return sl;
    }

    public void setSl(Float sl) {
        this.sl = sl;
    }

    @Override
    public String toString() {
        return "RukuRecordBean{" +
                "id=" + id +
                ", jdbXm='" + jdbXm + '\'' +
                ", sl=" + sl +
                ", bbsj='" + bbsj + '\'' +
                '}';
    }
}