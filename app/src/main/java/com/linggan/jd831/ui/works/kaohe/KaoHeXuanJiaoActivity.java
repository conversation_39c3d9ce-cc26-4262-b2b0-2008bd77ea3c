package com.linggan.jd831.ui.works.kaohe;


import android.text.TextUtils;

import androidx.fragment.app.Fragment;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.view.XTabFragmentPagerAdapter;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.bean.KhNumBean;
import com.linggan.jd831.bean.KhlvPlaceEntity;
import com.linggan.jd831.databinding.ActivityKaoHeRyxxListBinding;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XHttpUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类  名：宣教--周期排查完成率
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/9/19 9:21
 * 版  权：凌感科技
 */
public class KaoHeXuanJiaoActivity extends XBaseActivity<ActivityKaoHeRyxxListBinding> {

    private String xzqhdm, startDate, endDate, xzqhmc, wcl, type, fz, fm;

    @Override
    protected ActivityKaoHeRyxxListBinding getViewBinding() {
        return ActivityKaoHeRyxxListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        startDate = getIntent().getStringExtra("kssj");
        endDate = getIntent().getStringExtra("jssj");
        xzqhdm = getIntent().getStringExtra("xzqhdm");
        xzqhmc = getIntent().getStringExtra("xzqhmc");
        wcl = getIntent().getStringExtra("wcl");
        type = getIntent().getStringExtra("type");
        fz = getIntent().getStringExtra("fz");
        fm = getIntent().getStringExtra("fm");
        binding.tvArea.setText("当前辖区：" + xzqhmc);
        binding.tvWcl.setText("完成率：" + wcl);
        EventBus.getDefault().register(this);
        if (!TextUtils.isEmpty(type) && type.equals("xjkh_ls")) {
            setTitle("临时性任务完成率");
        }
    }

    @Override
    protected void initListener() {
    }

    @Override
    protected void getData() {
        List<Fragment> fragments = new ArrayList<>();
        fragments.add(KhXuanJiaoWwcListFragment.newInstance(startDate, endDate, xzqhdm, type));
        fragments.add(KhXuanJiaoYwcListFragment.newInstance(startDate, endDate, xzqhdm, type));
        List<String> list = new ArrayList<>();
        list.add("未完成(" + StrUtils.subtract(fm, fz) + ")");
        list.add("已完成(" + fz + ")");
        binding.viewPager.setAdapter(new XTabFragmentPagerAdapter(getSupportFragmentManager(), fragments, list));
        binding.tabLayout.setupWithViewPager(binding.viewPager);
    }

    private void getAppUserLv(int num) {
        binding.tabLayout.getTabAt(0).setText("未完成(" + num + ")");
        wwcNum();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(KhNumBean event) {
        if (event != null) {
            getAppUserLv(event.getNum());
        }
    }

    private void wwcNum() {
        String rwlx = "";
        if (type.equals("xjkh_zq")) {
            rwlx = "zqrw";
        } else {
            rwlx = "lsrw";
        }
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.KH_XJ_LIST_WCQK);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("rwlx", rwlx);
        objectMap.put("xzqhdm", xzqhdm);
        objectMap.put("startDate", startDate + " 00:00:00");
        objectMap.put("endDate", endDate + " 23:59:59");
        objectMap.put("wcqk", "1");
        objectMap.put("page", 1);
        objectMap.put("rows", XConstantUtils.PAGE_SIZE);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJson(this, requestParams, jiaMiString, null, false, false, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<XResultPageData<KhlvPlaceEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XResultPageData<KhlvPlaceEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        binding.tabLayout.getTabAt(1).setText("已完成(" + xResultData.getData().getTotal() + ")");
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

}
