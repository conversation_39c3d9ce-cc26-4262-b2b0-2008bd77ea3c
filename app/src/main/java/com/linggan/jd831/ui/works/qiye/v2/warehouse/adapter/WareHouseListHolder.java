package com.linggan.jd831.ui.works.qiye.v2.warehouse.adapter;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XToastUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.ui.works.qiye.v2.warehouse.InventoryEditActivity;
import com.linggan.jd831.ui.works.qiye.v2.warehouse.entity.WareHouseListEntity;

/**
 * 类  名：宣教列表
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class WareHouseListHolder extends IViewHolder {

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_ware_house_list;
    }


    public class ViewHolder extends XViewHolder<WareHouseListEntity> {

        private TextView mTvTitle;
        private TextView mtvChemicals;
        private TextView mtvCamera;


        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mTvTitle = view.findViewById(R.id.tv_title);
            mtvChemicals = view.findViewById(R.id.tv_chemicals);
            mtvCamera = view.findViewById(R.id.tv_camera);
        }

        @Override
        protected void onBindData(final WareHouseListEntity itemData) {
            mTvTitle.setText(itemData.getMc());

            if (itemData.getKcmcs() != null) {
                mtvChemicals.setText(itemData.getKcmcs());
            } else {
                mtvChemicals.setText("-");
            }
            //
            if ("0".equals(itemData.getSxtSl())) {
                mtvCamera.setText("否");
            } else {
                mtvCamera.setText("是");
            }

            itemView.setOnClickListener(v -> {
                if (TextUtils.isEmpty(itemData.getBh())) {
                    XToastUtil.showToast(mContext, "仓库数据丢失，请联系管理员");
                    return;
                }
                Bundle bundle = new Bundle();
//                List<Chemical> chemicalList = new ArrayList<>();
//                Chemical chemical = new Chemical();
//                chemical.setChemicalName("硫酸");
//                chemical.setStockQuantity("125");
//                chemical.setUnit("升(体积单位)");
//                chemicalList.add(chemical);
//
//                XieYiShiEventEntity xieYiShiEventEntity = new XieYiShiEventEntity();
//                xieYiShiEventEntity.setInventoryInitListAll(new ArrayList<>(chemicalList));
//                bundle.putSerializable("info", xieYiShiEventEntity);

                bundle.putSerializable("ckbh", itemData.getBh());
                bundle.putSerializable("title", itemData.getMc());
                bundle.putSerializable("gbxlh", itemData.getGbXlh());
                bundle.putSerializable("gbtdh", itemData.getGbTdh());
                bundle.putSerializable("gbBfdz", itemData.getGbBfdz());
                XIntentUtil.redirectToNextActivity(mContext, InventoryEditActivity.class, bundle);
            });
        }
    }
}


