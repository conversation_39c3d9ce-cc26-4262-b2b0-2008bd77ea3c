package com.linggan.jd831.ui.works.chat;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.net.NetworkInfo;
import android.os.Build;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.farsunset.cim.sdk.android.CIMEventListener;
import com.farsunset.cim.sdk.android.CIMListenerManager;
import com.farsunset.cim.sdk.android.CIMPushManager;
import com.farsunset.cim.sdk.android.model.Message;
import com.farsunset.cim.sdk.android.model.ReplyBody;
import com.farsunset.cim.sdk.android.model.SentBody;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XDateUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.MessageEntity;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.utils.XImageUtils;
import com.makeramen.roundedimageview.RoundedImageView;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;
import org.webrtc.AudioSource;
import org.webrtc.AudioTrack;
import org.webrtc.Camera2Capturer;
import org.webrtc.Camera2Enumerator;
import org.webrtc.CameraEnumerator;
import org.webrtc.DataChannel;
import org.webrtc.DefaultVideoDecoderFactory;
import org.webrtc.DefaultVideoEncoderFactory;
import org.webrtc.EglBase;
import org.webrtc.IceCandidate;
import org.webrtc.MediaConstraints;
import org.webrtc.MediaStream;
import org.webrtc.PeerConnection;
import org.webrtc.PeerConnectionFactory;
import org.webrtc.RtpReceiver;
import org.webrtc.SessionDescription;
import org.webrtc.SurfaceViewRenderer;
import org.webrtc.VideoCapturer;
import org.webrtc.VideoDecoderFactory;
import org.webrtc.VideoEncoderFactory;
import org.webrtc.VideoSource;
import org.webrtc.VideoTrack;
import org.xutils.http.RequestParams;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Flowable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Observer;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * 类  名：语音通话
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2023/9/6 14:13
 * 版  权：LGKJ
 */
public class VoiceChatActivity extends AppCompatActivity implements CIMEventListener, View.OnClickListener {

    private static final String AUDIO_TRACK_ID = "ARDAMSa0";
    private static final String VIDEO_TRACK_ID = "ARDAMSv0";
    private static final List<String> STREAM_IDS = new ArrayList<String>() {{
        add("ARDAMS");
    }};
    private static final String SURFACE_TEXTURE_HELPER_THREAD_NAME = "SurfaceTextureHelperThread";
    private static final int WIDTH = 1280;
    private static final int HEIGHT = 720;
    private static final int FPS = 30;
    private EglBase mEglBase;
    private SurfaceViewRenderer svrRemote, svrLocal;
    private PeerConnectionFactory mPeerConnectionFactory;
    private VideoCapturer mVideoCapturer;
    private AudioTrack mAudioTrack;
    //    private VideoTrack mVideoTrack;
    private PeerConnection mPeerConnection;
    private String bh, receiverName, receiverImg, pageFrom, dfSessionId, push;
    private boolean isSwappedFeeds, isVideoZhong = false, isOpenSond = true;
    private RoundedImageView mIvHead;
    private TextView mTvUserName;
    private TextView mTvJtWz, mTvJtWz1, tvDwxx;
    private TextView mBtnFz;
    private TextView mBtnHangUps, mBtnHangUps1;
    private TextView mBtnJieTing;
    private TextView mTvPicName, mTvPoint;
    private LinearLayout mLinHead, mLinHeads;
    private MediaPlayer mediaPlayerJS, mediaPlayerBd, mediaPlayerGd;
    private Disposable mDisposable, mDisposable1, countdownDisposable;
    private Vibrator vibrator;
    public static boolean isOpenPage = false;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_voice_chat_view);
        CIMListenerManager.registerMessageListener(this);
        //
        bh = getIntent().getStringExtra("bh");
        receiverName = getIntent().getStringExtra("name");
        receiverImg = getIntent().getStringExtra("img");
        push = getIntent().getStringExtra("push");//推送过来
        pageFrom = getIntent().getStringExtra("action");//表示接收者打开页面
        if (!TextUtils.isEmpty(push)) {
            CIMPushManager.connect(this, ApiHostUtils.getIM().get(0).getIm(), ApiHostUtils.getIM().get(0).getImPort());
        }
        //
        initViews();
        // 初始化 PeerConnectionFactory
        initPeerConnectionFactory(this);
        // 创建 EglBase
        mEglBase = EglBase.create();
        // 创建 PeerConnectionFactory
        mPeerConnectionFactory = createPeerConnectionFactory(mEglBase);
        // 创建音轨
        mAudioTrack = createAudioTrack(mPeerConnectionFactory);
        // 初始化本地视频渲染控件，这个方法非常重要，不初始化会黑屏
        svrLocal = findViewById(R.id.svr_local);
        svrLocal.init(mEglBase.getEglBaseContext(), null);
        // 初始化远端视频渲染控件，这个方法非常重要，不初始化会黑屏
        svrRemote = findViewById(R.id.svr_remote);
        svrRemote.init(mEglBase.getEglBaseContext(), null);
        svrRemote.setOnClickListener(v -> {
            setSwappedFeeds(!isSwappedFeeds);
        });
        setSpeakerphoneOn(true);
        //推送过来
        if (!TextUtils.isEmpty(push) && !CIMPushManager.isConnected(this)) {
            CIMPushManager.connect(this, ApiHostUtils.getIM().get(0).getIm(), ApiHostUtils.getIM().get(0).getImPort());
        }
        isOpenPage = true;
    }

    private void initViews() {
        mIvHead = findViewById(R.id.iv_head);
        mLinHead = findViewById(R.id.lin_head);
        mLinHeads = findViewById(R.id.lin_heads);
        mTvUserName = findViewById(R.id.tv_user_name);
        mTvJtWz = findViewById(R.id.tv_jt_wz);
        mTvJtWz1 = findViewById(R.id.tv_jt_wz1);
        mTvPoint = findViewById(R.id.tv_point);
        mBtnFz = findViewById(R.id.btn_fz);
        mBtnHangUps = findViewById(R.id.btn_hang_ups);
        mBtnHangUps1 = findViewById(R.id.btn_hang_ups1);
        mBtnJieTing = findViewById(R.id.btn_jie_ting);
        mTvPicName = findViewById(R.id.tv_pic_name);
        tvDwxx = findViewById(R.id.tv_dwxx);
        mBtnFz.setOnClickListener(this);
        mBtnHangUps.setOnClickListener(this);
        mBtnJieTing.setOnClickListener(this);
        mBtnHangUps1.setOnClickListener(this);
        if (!TextUtils.isEmpty(receiverImg)) {
            mIvHead.setVisibility(View.VISIBLE);
            mLinHead.setVisibility(View.GONE);
            XImageUtils.load(this, receiverImg, mIvHead);
        } else {
            mIvHead.setVisibility(View.GONE);
            mLinHead.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(receiverName)) {
                if (receiverName.length() > 2) {
                    mTvPicName.setText(receiverName.substring(0, 2));
                } else {
                    mTvPicName.setText(receiverName);
                }
            }
        }
        mTvUserName.setText(receiverName);
        if (!TextUtils.isEmpty(pageFrom)) {
            if (pageFrom.equals("900")) {
                mBtnJieTing.setVisibility(View.VISIBLE);
                mBtnHangUps.setVisibility(View.GONE);
                mBtnHangUps1.setVisibility(View.VISIBLE);
                mBtnFz.setVisibility(View.GONE);
                mTvJtWz1.setText("邀请你接听语音");
                mediaPlayerJS = MediaPlayer.create(this, R.raw.laidian);
                if (mediaPlayerJS != null) {
                    mediaPlayerJS.setLooping(true);
                    mediaPlayerJS.start();
                }
                vibrator = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);
                openCloseVibrator();
            }
        } else {
            sendVoice();
            mediaPlayerBd = MediaPlayer.create(this, R.raw.boda);
            if (mediaPlayerBd != null) {
                mediaPlayerBd.setLooping(true);
                mediaPlayerBd.start();
            }
//            djsTwoMinHungUp();
        }
        loadPointAnim();
    }

    /**
     * 获取房间号
     */
    private void sendVoice() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.CHAT_YP_FJH);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("targetId", bh);
            jsonObject.put("sessionId", XShareCacheUtils.getInstance().getString(XConstantUtils.SESSION));
            jsonObject.put("sender", UserInfoUtils.getUserInfo().getUserId());
            jsonObject.put("senderName", UserInfoUtils.getUserInfo().getUserName());
            jsonObject.put("senderImg", "");
            jsonObject.put("receiver", bh);
            jsonObject.put("receiverName", receiverName);
            jsonObject.put("receiverImg", receiverImg);
        } catch (JSONException e) {
        }
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), jsonObject.toString());
        XHttpUtils.postJson(this, requestParams, jiaMiString, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    mTvJtWz1.setText("连接中");
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 挂断/取消/拒绝/正忙
     */
    private void delContent(String url) {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "targetId=" + bh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + url + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 发送消息
     */
    private void sendContent(String messageType, String gd) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.CHAT_SEND);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("action", messageType);
            if (messageType.equals("5")) {
                if (isVideoZhong) {
                    jsonObject.put("content", "通话时长" + mTvJtWz.getText().toString());
                } else {
                    if (TextUtils.isEmpty(gd)) {
                        jsonObject.put("content", "已取消");
                    } else {
                        jsonObject.put("content", "对方已拒绝");
                    }
                }
            }
            jsonObject.put("sender", UserInfoUtils.getUserInfo().getUserId());
            jsonObject.put("senderName", UserInfoUtils.getUserInfo().getUserName());
            jsonObject.put("senderImg", "");
            jsonObject.put("receiver", bh);
            jsonObject.put("receiverName", receiverName);
            jsonObject.put("receiverImg", receiverImg);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), jsonObject.toString());
        XHttpUtils.postJson(this, requestParams, jiaMiString, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.btn_fz) {
            //关闭扬声器
            if (isOpenSond) {
                setSpeakerphoneOn(false);
                Drawable drawable = getResources().getDrawable(R.mipmap.ic_close_voice);
                mBtnFz.setCompoundDrawablesWithIntrinsicBounds(null, drawable, null, null);
                mBtnFz.setText("扬声器已关");
            } else {
                setSpeakerphoneOn(true);
                Drawable drawable = getResources().getDrawable(R.mipmap.ic_open_voice);
                mBtnFz.setCompoundDrawablesWithIntrinsicBounds(null, drawable, null, null);
                mBtnFz.setText("扬声器已开");
            }
            isOpenSond = !isOpenSond;
        } else if (v.getId() == R.id.btn_hang_ups) {
            //自己挂断挂断
            if (isVideoZhong) {//挂断
                delContent(ApiUrlsUtils.CHAT_GD);
            } else {//取消
                delContent(ApiUrlsUtils.CHAT_QX);
                MessageEntity messageEntity = new MessageEntity();
                messageEntity.setContentType("5");
                messageEntity.setDate(XDateUtil.getCurrentDate(XDateUtil.dateFormatYMDHMS));
                messageEntity.setReceiver(bh);
                messageEntity.setSender(UserInfoUtils.getUserInfo().getUserId());
                messageEntity.setContent("已取消");
                EventBus.getDefault().post(messageEntity);
            }
            hangUp();
        } else if (v.getId() == R.id.btn_hang_ups1) {
            //对方挂断
            if (isVideoZhong) {//挂断
                delContent(ApiUrlsUtils.CHAT_GD);
            } else {//拒绝接听
                delContent(ApiUrlsUtils.CHAT_JJ);
            }
            hangUp();
        } else if (v.getId() == R.id.btn_jie_ting) {
            //接听
            Log.i("vvv", "onClick接听: ");
            String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "targetId=" + bh + "&sessionId=" + XShareCacheUtils.getInstance().getString(XConstantUtils.SESSION));
            RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.CHAT_ACCEPT + "?query=" + jiaMiString);
            requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
            requestParams.addHeader("Origin-Content-Type", "application/json");
            XHttpUtils.get(this, requestParams, null, new XHttpResponseCallBack() {
                @Override
                public void onSuccess(String result) {
                    XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                    }.getType());
                    if (xResultData.getStatus() == 0) {
                        mTvJtWz.setText("连接中");
                    }
                }

                @Override
                public void onFailed(int code, String failedMsg) {
                }

                @Override
                public void onFinished() {
                }
            });
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mEglBase != null) {
            mEglBase.release();
            mEglBase = null;
        }
        isVideoZhong = false;
        if (mVideoCapturer != null) {
            try {
                mVideoCapturer.stopCapture();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            mVideoCapturer.dispose();
            mVideoCapturer = null;
        }
        if (mAudioTrack != null) {
            mAudioTrack.dispose();
            mAudioTrack = null;
        }
        if (mPeerConnection != null) {
            mPeerConnection.close();
            mPeerConnection = null;
        }
        if (svrLocal != null) {
            svrLocal.release();
            svrLocal = null;
        }
        if (svrRemote != null) {
            svrRemote.release();
            svrRemote = null;
        }
        if (mediaPlayerGd != null) {
            mediaPlayerGd.stop();
            mediaPlayerGd.release();
            mediaPlayerGd = null;
        }
        if (mediaPlayerJS != null) {
            mediaPlayerJS.stop();
            mediaPlayerJS.release();
            mediaPlayerJS = null;
        }
        if (mediaPlayerBd != null) {
            mediaPlayerBd.stop();
            mediaPlayerBd.release();
            mediaPlayerBd = null;
        }
        if (mDisposable1 != null) {
            mDisposable1.dispose();
            mDisposable1 = null;
        }
        if (mDisposable != null) {
            mDisposable.dispose();
            mDisposable = null;
        }
        if (vibrator != null) {
            vibrator.cancel();
        }
        isOpenPage = false;
        CIMListenerManager.removeMessageListener(this);
        EventBus.getDefault().post(new Message());
    }

    private void initPeerConnectionFactory(Context context) {
        PeerConnectionFactory.initialize(PeerConnectionFactory.InitializationOptions.builder(context).createInitializationOptions());
    }

    private PeerConnectionFactory createPeerConnectionFactory(EglBase eglBase) {
        VideoEncoderFactory videoEncoderFactory = new DefaultVideoEncoderFactory(eglBase.getEglBaseContext(), true, true);
        VideoDecoderFactory videoDecoderFactory = new DefaultVideoDecoderFactory(eglBase.getEglBaseContext());
        return PeerConnectionFactory.builder().setVideoEncoderFactory(videoEncoderFactory).setVideoDecoderFactory(videoDecoderFactory).createPeerConnectionFactory();
    }

    private AudioTrack createAudioTrack(PeerConnectionFactory peerConnectionFactory) {
        AudioSource audioSource = peerConnectionFactory.createAudioSource(new MediaConstraints());
        AudioTrack audioTrack = peerConnectionFactory.createAudioTrack(AUDIO_TRACK_ID, audioSource);
        audioTrack.setEnabled(true);
        return audioTrack;
    }

    private VideoCapturer createVideoCapturer() {
        VideoCapturer videoCapturer = null;
        CameraEnumerator cameraEnumerator = new Camera2Enumerator(this);
        for (String deviceName : cameraEnumerator.getDeviceNames()) {
            // 前摄像头
            if (cameraEnumerator.isFrontFacing(deviceName)) {
                videoCapturer = new Camera2Capturer(this, deviceName, null);
            }
        }
        return videoCapturer;
    }

    private VideoSource createVideoSource(PeerConnectionFactory peerConnectionFactory, VideoCapturer videoCapturer) {
        // 创建视频源
        VideoSource videoSource = peerConnectionFactory.createVideoSource(videoCapturer.isScreencast());
        return videoSource;
    }

    private VideoTrack createVideoTrack(PeerConnectionFactory peerConnectionFactory, VideoSource videoSource) {
        // 创建视轨
        VideoTrack videoTrack = peerConnectionFactory.createVideoTrack(VIDEO_TRACK_ID, videoSource);
        videoTrack.setEnabled(true);
        return videoTrack;
    }

    private PeerConnection createPeerConnection() {
        List<PeerConnection.IceServer> iceServers = new ArrayList<>();
        PeerConnection.IceServer server = new PeerConnection.IceServer("turn:stun.lgfzd.com:3478", "admin", "8ec71ccfca9d4942023");
        iceServers.add(server);
        PeerConnection.RTCConfiguration rtcConfiguration = new PeerConnection.RTCConfiguration(iceServers);
        PeerConnection peerConnection = mPeerConnectionFactory.createPeerConnection(rtcConfiguration, new PeerConnection.Observer() {
            @Override
            public void onSignalingChange(PeerConnection.SignalingState signalingState) {
            }

            @Override
            public void onIceConnectionChange(PeerConnection.IceConnectionState iceConnectionState) {
                Log.i("kkk", "onIceConnectionChange: ");
                if (iceConnectionState == PeerConnection.IceConnectionState.DISCONNECTED) {
                    runOnUiThread(() -> {
                        hangUp();
                    });
                } else if (iceConnectionState == PeerConnection.IceConnectionState.CONNECTED) {
                    //隐藏页面控件
                    runOnUiThread(() -> {
                        isVideoZhong = true;
                        mTvPoint.setVisibility(View.GONE);
                        mTvJtWz.setVisibility(View.VISIBLE);
                        mTvJtWz1.setVisibility(View.GONE);
                        countTimeByTimer();
                        if (!TextUtils.isEmpty(pageFrom)) {
                            mBtnJieTing.setVisibility(View.GONE);
                            mBtnHangUps.setVisibility(View.VISIBLE);
                            mBtnFz.setVisibility(View.VISIBLE);
                            mBtnHangUps1.setVisibility(View.GONE);
                            if (mediaPlayerJS != null) {
                                mediaPlayerJS.stop();
                                mediaPlayerJS.release();
                                mediaPlayerJS = null;
                            }
                        }
                        if (mediaPlayerBd != null) {
                            mediaPlayerBd.stop();
                            mediaPlayerBd.release();
                            mediaPlayerBd = null;
                        }
                        if (mDisposable1 != null) {
                            mDisposable1.dispose();
                            mDisposable1 = null;
                        }
                        if (vibrator != null) {
                            vibrator.cancel();
                        }
                        if (countdownDisposable != null) {
                            countdownDisposable.dispose();
                            countdownDisposable = null;
                        }

                    });
                }
            }

            @Override
            public void onIceConnectionReceivingChange(boolean b) {
            }

            @Override
            public void onIceGatheringChange(PeerConnection.IceGatheringState iceGatheringState) {
            }

            @Override
            public void onIceCandidate(IceCandidate iceCandidate) {
                sendIceCandidate(iceCandidate);
            }

            @Override
            public void onIceCandidatesRemoved(IceCandidate[] iceCandidates) {
            }

            @Override
            public void onAddStream(MediaStream mediaStream) {
                if (mediaStream == null || mediaStream.videoTracks == null || mediaStream.videoTracks.isEmpty()) {
                    return;
                }
                runOnUiThread(() -> {
                    SurfaceViewRenderer svrRemote = findViewById(R.id.svr_remote);
                    mediaStream.videoTracks.get(0).addSink(svrRemote);
                });
            }

            @Override
            public void onRemoveStream(MediaStream mediaStream) {
            }

            @Override
            public void onDataChannel(DataChannel dataChannel) {
            }

            @Override
            public void onRenegotiationNeeded() {
            }

            @Override
            public void onAddTrack(RtpReceiver rtpReceiver, MediaStream[] mediaStreams) {
            }
        });
        return peerConnection;
    }

    /**
     * 前后切换视频
     *
     * @param isSwappedFeeds
     */
    private void setSwappedFeeds(boolean isSwappedFeeds) {
        this.isSwappedFeeds = isSwappedFeeds;
        svrLocal.setMirror(isSwappedFeeds);
        svrRemote.setMirror(!isSwappedFeeds);
    }

    private void call() {
        // 创建 PeerConnection
        mPeerConnection = createPeerConnection();
        // 为 PeerConnection 添加音轨、视轨
        mPeerConnection.addTrack(mAudioTrack, STREAM_IDS);
//        mPeerConnection.addTrack(mVideoTrack, STREAM_IDS);
        // 通过 PeerConnection 创建 offer，获取 sdp
        MediaConstraints mediaConstraints = new MediaConstraints();
        mPeerConnection.createOffer(new MySdpObserver() {
            @Override
            public void onCreateSuccess(SessionDescription sessionDescription) {
                // 将 offer sdp 作为参数 setLocalDescription
                mPeerConnection.setLocalDescription(new MySdpObserver() {
                    @Override
                    public void onCreateSuccess(SessionDescription sessionDescription) {
                    }

                    @Override
                    public void onSetSuccess() {
                        // 发送 offer sdp
                        sendOffer(sessionDescription);
                    }
                }, sessionDescription);
            }

            @Override
            public void onSetSuccess() {
            }
        }, mediaConstraints);
    }

    private void sendOffer(SessionDescription offer) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.CHAT_OFFER);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("targetId", bh);
            jsonObject.put("sessionId", dfSessionId);
            jsonObject.put("content", offer.description);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), jsonObject.toString());
        XHttpUtils.postJson(this, requestParams, jiaMiString, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    private void receivedOffer(SessionDescription offer) {
        // 创建 PeerConnection
        mPeerConnection = createPeerConnection();
        // 为 PeerConnection 添加音轨、视轨
        mPeerConnection.addTrack(mAudioTrack, STREAM_IDS);
//        mPeerConnection.addTrack(mVideoTrack, STREAM_IDS);
        // 将 offer sdp 作为参数 setRemoteDescription
        mPeerConnection.setRemoteDescription(new MySdpObserver() {
            @Override
            public void onCreateSuccess(SessionDescription sessionDescription) {
            }

            @Override
            public void onSetSuccess() {
                // 通过 PeerConnection 创建 answer，获取 sdp
                MediaConstraints mediaConstraints = new MediaConstraints();
                mPeerConnection.createAnswer(new MySdpObserver() {
                    @Override
                    public void onCreateSuccess(SessionDescription sessionDescription) {
                        // 将 answer sdp 作为参数 setLocalDescription
                        mPeerConnection.setLocalDescription(new MySdpObserver() {
                            @Override
                            public void onCreateSuccess(SessionDescription sessionDescription) {
                            }

                            @Override
                            public void onSetSuccess() {
                                // 发送 answer sdp
                                sendAnswer(sessionDescription);
                            }
                        }, sessionDescription);
                    }

                    @Override
                    public void onSetSuccess() {
                    }
                }, mediaConstraints);
            }
        }, offer);
    }

    private void sendAnswer(SessionDescription answer) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.CHAT_ANSWER);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("targetId", bh);
            jsonObject.put("content", answer.description);
            jsonObject.put("sessionId", dfSessionId);
        } catch (JSONException e) {
        }
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), jsonObject.toString());
        XHttpUtils.postJson(this, requestParams, jiaMiString, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    private void receivedAnswer(SessionDescription answer) {
        // 收到 answer sdp，将 answer sdp 作为参数 setRemoteDescription
        mPeerConnection.setRemoteDescription(new MySdpObserver() {
            @Override
            public void onCreateSuccess(SessionDescription sessionDescription) {
            }

            @Override
            public void onSetSuccess() {
            }
        }, answer);
    }

    private void sendIceCandidate(IceCandidate iceCandidate) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.CHAT_ICE);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("targetId", bh);
            jsonObject.put("sessionId", XShareCacheUtils.getInstance().getString(XConstantUtils.SESSION));
            JSONObject jsonObject3 = new JSONObject();
            jsonObject3.put("id", iceCandidate.sdpMid);
            jsonObject3.put("label", iceCandidate.sdpMLineIndex);
            jsonObject3.put("candidate", iceCandidate.sdp);
            jsonObject.put("content", jsonObject3);
        } catch (JSONException e) {
        }
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), jsonObject.toString());
        XHttpUtils.postJson(this, requestParams, jiaMiString, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    private void receivedCandidate(IceCandidate iceCandidate) {
        mPeerConnection.addIceCandidate(iceCandidate);
    }


    private void hangUp() {
        mediaPlayerGd = MediaPlayer.create(this, R.raw.guaduan);
        if (mediaPlayerGd != null) {
            mediaPlayerGd.start();
        }
        if (mediaPlayerBd != null) {
            mediaPlayerBd.stop();
            mediaPlayerBd.release();
            mediaPlayerBd = null;
        }
        if (mVideoCapturer != null) {
            try {
                mVideoCapturer.stopCapture();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            mVideoCapturer.dispose();
            mVideoCapturer = null;
        }
        if (mAudioTrack != null) {
            mAudioTrack.dispose();
            mAudioTrack = null;
        }

        // 关闭 PeerConnection
        if (mPeerConnection != null) {
            mPeerConnection.close();
            mPeerConnection.dispose();
            mPeerConnection = null;
        }
        if (TextUtils.isEmpty(pageFrom)) {
            sendContent("5", "");
        }
        isVideoZhong = false;
        if (mDisposable1 != null) {
            mDisposable1.dispose();
            mDisposable1 = null;
        }
        if (mDisposable != null) {
            mDisposable.dispose();
            mDisposable = null;
        }
        if (svrLocal != null) {
            svrLocal.release();
            svrLocal = null;
        }
        if (svrRemote != null) {
            svrRemote.release();
            svrRemote = null;
        }
        if (vibrator != null) {
            vibrator.cancel();
        }
        isOpenPage = false;
        finish();
    }

    private void hangUpGd() {
        mediaPlayerGd = MediaPlayer.create(this, R.raw.guaduan);
        if (mediaPlayerGd != null) {
            mediaPlayerGd.start();
        }
        if (mediaPlayerBd != null) {
            mediaPlayerBd.stop();
            mediaPlayerBd.release();
            mediaPlayerBd = null;
        }
        if (mVideoCapturer != null) {
            try {
                mVideoCapturer.stopCapture();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            mVideoCapturer.dispose();
            mVideoCapturer = null;
        }
        if (mAudioTrack != null) {
            mAudioTrack.dispose();
            mAudioTrack = null;
        }
        // 关闭 PeerConnection
        if (mPeerConnection != null) {
            mPeerConnection.close();
            mPeerConnection.dispose();
            mPeerConnection = null;
        }
        sendContent("5", "1");
        isVideoZhong = false;
        if (mDisposable1 != null) {
            mDisposable1.dispose();
            mDisposable1 = null;
        }
        if (mDisposable != null) {
            mDisposable.dispose();
            mDisposable = null;
        }
        if (svrLocal != null) {
            svrLocal.release();
            svrLocal = null;
        }
        if (svrRemote != null) {
            svrRemote.release();
            svrRemote = null;
        }
        if (vibrator != null) {
            vibrator.cancel();
        }
        isOpenPage = false;
        finish();
    }

    @Override
    public void onMessageReceived(Message message) {
        if (message != null) {
            if (!TextUtils.isEmpty(message.getAction())) {
                if (message.getAction().equals("903")) {
                    //对方已拒绝
                    XToastUtil.showToast(VoiceChatActivity.this, "对方已拒绝");
                    hangUpGd();
                } else if (message.getAction().equals("905")) {
                    //对方挂断挂断
                    XToastUtil.showToast(VoiceChatActivity.this, "已挂断");
                    hangUp();
                } else if (message.getAction().equals("902")) {
                    //加入
                    try {
                        JSONObject jsonObject = new JSONObject(message.getExtra());
                        dfSessionId = jsonObject.getString("sessionId");
                    } catch (JSONException e) {
                    }
                    call();
                } else if (message.getAction().equals("904")) {
                    //当用户正在通话时别人给他拨打电话服务器会推送收到消息后走挂断流程
                    XToastUtil.showToast(VoiceChatActivity.this, "对方正忙");
                    hangUp();
                } else if (message.getAction().equals("906")) {
                    //取消呼叫
                    XToastUtil.showToast(VoiceChatActivity.this, "已挂断");
                    hangUp();
                } else if (message.getAction().equals("907")) {
                    //同步ICE SPD
                    try {
                        JSONObject jsonObject = new JSONObject(message.getContent());
                        IceCandidate iceCandidate = new IceCandidate(jsonObject.optString("id"), jsonObject.optInt("label"), jsonObject.optString("candidate"));
                        receivedCandidate(iceCandidate);
                    } catch (JSONException e) {
                        throw new RuntimeException(e);
                    }
                } else if (message.getAction().equals("908")) {
                    //同步Offer
                    String sdp = message.getContent();
                    try {
                        JSONObject jsonObject = new JSONObject(message.getExtra());
                        String session = jsonObject.getString("sessionId");
                        if (TextUtils.equals(session, XShareCacheUtils.getInstance().getString(XConstantUtils.SESSION))) {
                            SessionDescription offer = new SessionDescription(SessionDescription.Type.OFFER, sdp);
                            receivedOffer(offer);
                        } else {
                            XToastUtil.showToast(VoiceChatActivity.this, "其他设备已接听");
                            hangUp();
                        }
                    } catch (JSONException e) {
                    }
                } else if (message.getAction().equals("909")) {
                    //同步Answer
                    String sdp = message.getContent();
                    SessionDescription answer2 = new SessionDescription(SessionDescription.Type.ANSWER, sdp);
                    receivedAnswer(answer2);
                } else if (message.getAction().equals("10909")) {
                    //工作人员接受地址信息
                    try {
                        JSONObject jsonObject = new JSONObject(message.getContent());
                        tvDwxx.setText("对方地址：" + jsonObject.getString("address") + "\n使用网络：" + jsonObject.getString("network"));
                        tvDwxx.setVisibility(View.VISIBLE);
                    } catch (JSONException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
    }

    @Override
    public void onReplyReceived(ReplyBody replyBody) {
    }

    @Override
    public void onSendFinished(SentBody sentBody) {
    }

    @Override
    public void onNetworkChanged(NetworkInfo networkInfo) {
    }

    @Override
    public void onConnectFinished(boolean b) {
        if (!b && !TextUtils.isEmpty(push)) {
            CIMPushManager.bind(this, XShareCacheUtils.getInstance().getString(XConstantUtils.USER_ID));
        }
    }

    @Override
    public void onConnectionClosed() {
    }

    @Override
    public void onConnectFailed() {
    }

    @Override
    public int getEventDispatchOrder() {
        return 0;
    }

    /**
     * 扬声器与听筒切换
     *
     * @param isSpeakerphoneOn
     */
    public void setSpeakerphoneOn(boolean isSpeakerphoneOn) {
        AudioManager audioManager = (AudioManager) this.getSystemService(Context.AUDIO_SERVICE);
        audioManager.setSpeakerphoneOn(isSpeakerphoneOn);
        if (!isSpeakerphoneOn) {
            audioManager.setMode(AudioManager.MODE_NORMAL);
        }
    }

    /**
     * 计时器
     */
    private void countTimeByTimer() {
        Observable.interval(1, TimeUnit.SECONDS).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(new Observer<Long>() {
            @Override
            public void onSubscribe(Disposable d) {
                mDisposable = d;
            }

            @Override
            public void onNext(Long time) {
                mTvJtWz.setText(XDateUtil.getMins(time) + ":" + XDateUtil.getSeconds(time));
            }

            @Override
            public void onError(Throwable e) {
            }

            @Override
            public void onComplete() {
            }
        });
    }

    /**
     * 计时方式处理等待效果
     */
    private void loadPointAnim() {
        Observable.interval(1, TimeUnit.SECONDS).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(new Observer<Long>() {
            @Override
            public void onSubscribe(Disposable d) {
                mDisposable1 = d;
            }

            @Override
            public void onNext(Long time) {
                if (time % 3 == 0) {
                    mTvPoint.setText(".");
                } else if (time % 3 == 1) {
                    mTvPoint.setText("..");
                } else if (time % 3 == 2) {
                    mTvPoint.setText("...");
                }
            }

            @Override
            public void onError(Throwable e) {
            }

            @Override
            public void onComplete() {
            }
        });
    }

    /**
     * 开启振动
     */
    private void openCloseVibrator() {
        if (vibrator != null) {
            if (vibrator.hasVibrator()) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    vibrator.vibrate(VibrationEffect.createWaveform(new long[]{1000, 2000, 1000, 2000}, 0));
                } else {
                    vibrator.vibrate(new long[]{1000, 2000, 1000, 2000}, 0);
                }
            }
        }
    }

    /**
     * 1分钟内没有接听自动挂断
     */
    private void djsTwoMinHungUp() {
        countdownDisposable = Flowable.intervalRange(0, 60, 0, 1, TimeUnit.SECONDS).observeOn(AndroidSchedulers.mainThread()).doOnNext(new Consumer<Long>() {
            @Override
            public void accept(Long aLong) throws Exception {
            }
        }).doOnComplete(() -> {
            //自己挂断
            if (!isVideoZhong) {
                delContent(ApiUrlsUtils.CHAT_QX);
                MessageEntity messageEntity = new MessageEntity();
                messageEntity.setContentType("5");
                messageEntity.setDate(XDateUtil.getCurrentDate(XDateUtil.dateFormatYMDHMS));
                messageEntity.setReceiver(bh);
                messageEntity.setSender(UserInfoUtils.getUserInfo().getUserId());
                messageEntity.setContent("已取消");
                EventBus.getDefault().post(messageEntity);
                hangUp();
            }
        }).subscribe();
    }
}