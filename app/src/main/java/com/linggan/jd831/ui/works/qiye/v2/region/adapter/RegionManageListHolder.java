package com.linggan.jd831.ui.works.qiye.v2.region.adapter;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.ui.works.qiye.v2.lab.entity.ListSysQyEntity;
import com.linggan.jd831.ui.works.qiye.v2.region.RegionDetailActivity;
import com.linggan.jd831.utils.StrUtils;

/**
 * 类  名：宣教列表
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class RegionManageListHolder extends IViewHolder {
    private OnItemDeleteListener deleteListener;

    public interface OnItemDeleteListener {
        void onDelete(int position, ListSysQyEntity item);
    }

    public void setOnItemDeleteListener(OnItemDeleteListener listener) {
        this.deleteListener = listener;
    }

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_region_mannge_list;
    }


    public class ViewHolder extends XViewHolder<ListSysQyEntity> {

        private TextView mTvTitle;
        private TextView mtvIdCard;
        private TextView mtvRegional;

        private ListSysQyEntity mCurrentItem;  // 添加一个字段保存当前项

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mTvTitle = view.findViewById(R.id.tv_title);
            mtvIdCard = view.findViewById(R.id.tv_id_card);
            mtvRegional = view.findViewById(R.id.tv_regional);
        }

        @Override
        protected void onBindData(final ListSysQyEntity itemData) {
            mCurrentItem = itemData;  // 在绑定数据时保存当前项
            mTvTitle.setText(StrUtils.getDev(itemData.getMc(), "-"));
            mtvIdCard.setText(StrUtils.getDev(itemData.getKqKssj(), "-") + "-" + StrUtils.getDev(itemData.getKqJssj(), "-"));
            mtvRegional.setText(StrUtils.getDev(itemData.getSfJrSxj(), "-"));
            itemView.setOnClickListener(v -> {
                Bundle bundle = new Bundle();
                bundle.putSerializable("data", itemData);
                XIntentUtil.redirectToNextActivity(mContext, RegionDetailActivity.class, bundle);
            });
        }

        public void onSwiped(int position) {
            if (deleteListener != null && mCurrentItem != null) {
                deleteListener.onDelete(position, mCurrentItem);
            }
        }
    }
}



