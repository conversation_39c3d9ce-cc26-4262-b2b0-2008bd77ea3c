package com.linggan.jd831.ui.works.ywjc.gzrygl;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XDateUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.CodeNameEntity;
import com.linggan.jd831.bean.IdCardInfo;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.PeoStatusEntity;
import com.linggan.jd831.bean.XuanJiaoListEntity;
import com.linggan.jd831.bean.ZiDianEventEntity;
import com.linggan.jd831.databinding.ActivityGongzuoAddBinding;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.PhotoUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.CodeNameDialog;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.entity.LocalMedia;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * 类  名：宣教活动-新增
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/12/23 13:11
 */
public class GzryAddActivity extends XBaseActivity<ActivityGongzuoAddBinding> implements View.OnClickListener {

    private String lxCode;
    private String fangShiCode;
    private String xjBh;
    private String kssj;
    private String jssj;
    private final String hdLbCode = "";
    private long timeDate1;
    private ZiDianEventEntity ziDianEvent;
    private String headUrl;
    private Uri photoUri;
    private File cameraPhoto;
    private IdCardInfo idCardInfo;
    private int from = 0;
    private String fsTypeCode = "";
    private TextView tvIdCardCount;

    // 添加证件照片上传工具
    private ImageAddUtil imageAddZYZS;

    @Override
    protected ActivityGongzuoAddBinding getViewBinding() {
        return ActivityGongzuoAddBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        EventBus.getDefault().register(this);
        xjBh = getIntent().getStringExtra("bh");
        kssj = getIntent().getStringExtra("kssj");
        jssj = getIntent().getStringExtra("jssj");

        setTitle("工作人员新增");
        StrUtils.getPhotoVideoText2(binding.tvSjskInfo, null);
        
        // 初始化身份证字数统计
        tvIdCardCount = findViewById(R.id.tv_idcard_count);
        binding.etIdCard.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                tvIdCardCount.setText(s.length() + "/18");
            }
        });

        // 初始化证件照片上传
        imageAddZYZS = new ImageAddUtil(this, binding.gridZyzs);
        imageAddZYZS.setMax(1);
        imageAddZYZS.setOnImageAddListener(() -> {
            from = 2;
            requestPermission(new String[]{android.Manifest.permission.CAMERA, Manifest.permission.RECORD_AUDIO, android.Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        });

        // 默认隐藏执业证书
        binding.linSjsk.setVisibility(View.GONE);
    }

    @Override
    protected void initListener() {
        binding.btSure.setOnClickListener(this);
        binding.btnIdcardScan.setOnClickListener(this);
    }

    private ArrayList<CodeNameEntity> generateMockTimeRangeData() {
        ArrayList<CodeNameEntity> timeRangeList = new ArrayList<>();
        timeRangeList.add(new CodeNameEntity("联络员", "0"));
        timeRangeList.add(new CodeNameEntity("单位领导", "1"));
        timeRangeList.add(new CodeNameEntity("医师", "2"));
        timeRangeList.add(new CodeNameEntity("护士", "3"));
        timeRangeList.add(new CodeNameEntity("药品管理员", "4"));
        timeRangeList.add(new CodeNameEntity("安保人员", "5"));
        return timeRangeList;
    }

    @Override
    protected void getData() {
        binding.zhiwu.setOnClickListener(v -> {
            // 使用模拟数据
            CodeNameDialog codeNameDialog = new CodeNameDialog(this, generateMockTimeRangeData());
            codeNameDialog.setOnClickDataListener(() -> {
                binding.zhiwu.setText(codeNameDialog.getCode().getName());
                fsTypeCode = codeNameDialog.getCode().getCode();

                // 根据职务类型显示/隐藏执业证书
                if ("2".equals(fsTypeCode) || "3".equals(fsTypeCode)) {
                    binding.linSjsk.setVisibility(View.VISIBLE);
                    // 设置执业证书文字
                    if ("2".equals(fsTypeCode)) {
                        binding.tvZyzs.setText("医师执业证书");
                    } else {
                        binding.tvZyzs.setText("护士执业证书");
                    }
                } else {
                    binding.linSjsk.setVisibility(View.GONE);
                }
            });
            codeNameDialog.show();
        });
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.btn_idcard_scan) {
            from = 1;
            requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        } else if (v.getId() == R.id.bt_sure) {
            if (ButtonUtils.isFastClick()) {
                postData();
            }
        }
    }

    /**
     * 语音输入
     */
    private void postData() {
        // 验证姓名
        if (TextUtils.isEmpty(binding.etName.getText().toString().trim())) {
            XToastUtil.showToast(this, "请输入姓名");
            return;
        }

        // 验证身份证
        String idCard = binding.etIdCard.getText().toString().trim();
        if (TextUtils.isEmpty(idCard)) {
            XToastUtil.showToast(this, "请输入身份证号码");
            return;
        }
        if (idCard.length() != 18) {
            XToastUtil.showToast(this, "请输入18位身份证号码");
            return;
        }

        // 验证职务
        if (TextUtils.isEmpty(fsTypeCode)) {
            XToastUtil.showToast(this, "请选择职务");
            return;
        }

//        // 验证电话
        String phone = binding.etPhone.getText().toString().trim();
        if (TextUtils.isEmpty(phone)) {
            XToastUtil.showToast(this, "请输入联系电话");
            return;
        }

        // 验证执业证书
        String zzzs = "";
        if ("2".equals(fsTypeCode) || "3".equals(fsTypeCode)) {
            if (imageAddZYZS == null || imageAddZYZS.getPaths() == null || imageAddZYZS.getPaths().size() == 0) {
                XToastUtil.showToast(this, "请上传执业证书");
                return;
            }
            zzzs = imageAddZYZS.getPaths().get(0).getSavePath();
        }

        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.SAVE_LXR);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");

        JSONObject objectMap = new JSONObject();
        try {
            // 添加必填字段

            objectMap.put("lx", Integer.parseInt(fsTypeCode)); // 联系人类型
            objectMap.put("xm", binding.etName.getText().toString().trim()); // 联系人姓名
            if  (!TextUtils.isEmpty(idCard)) {
                objectMap.put("dh", binding.etPhone.getText().toString().trim()); // 联系人电话
            }

            if (!TextUtils.isEmpty(fsTypeCode) && ("2".equals(fsTypeCode) || "3".equals(fsTypeCode))) {
                objectMap.put("zzzs", zzzs); // 资质证书照片url
            }

            // 添加非必填字段
            String dwmc = UserInfoUtils.getUserInfo() != null ? UserInfoUtils.getUserInfo().getYhssdw() : null;
            if (!TextUtils.isEmpty(dwmc)) {
                objectMap.put("dwmc", dwmc); // 单位名称
            }

            String dwdm = UserInfoUtils.getUserInfo() != null ? UserInfoUtils.getUserInfo().getYhDwdm() : null;
            if (!TextUtils.isEmpty(dwdm)) {
                objectMap.put("dwdm", dwdm); // 单位代码
            }


        } catch (JSONException e) {
            e.printStackTrace();
        }
        binding.btSure.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJsonNoCan(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(GzryAddActivity.this, getString(R.string.add_sucess));
                    EventBus.getDefault().post(new XuanJiaoListEntity());
                    EventBus.getDefault().post(new PeoStatusEntity());
                    finish();
                } else {
                    XToastUtil.showToast(GzryAddActivity.this, xResultData.getErrorInfo());
                    binding.btSure.setEnabled(true);
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSure.setEnabled(true);
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 判断开始时间
     *
     * @param kssj
     * @param timeDate1
     * @return
     */
    private boolean jumpKssTime(String kssj, long timeDate1) {
        long rwKssj = XDateUtil.getDateByFormat(kssj, XConstantUtils.YMD).getTime();
        return timeDate1 < rwKssj;
    }



    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(ZiDianEventEntity event) {
        if (event != null) {
            if (event.getFrom() == 1) {
                //执行人
                ziDianEvent = event;
//                binding.tvZxrName.setText(StrUtils.listToZiDianText2(event.getZiDianEntityList()));
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.REQUEST_CAMERA:
                case PictureConfig.CHOOSE_REQUEST:
                    ArrayList<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                    if (null != selectList) {
                        for (LocalMedia media : selectList) {
                            if (!TextUtils.isEmpty(media.getCompressPath())) {
                                boolean isFinish = XFileUtil.bitmapToFilePathAlbum(GzryAddActivity.this, BitmapFactory.decodeFile(media.getCompressPath()), new File(media.getCompressPath()));
                                if (isFinish) {
                                    uploadFile(media.getCompressPath());
                                }
                            } else if (!TextUtils.isEmpty(media.getRealPath())) {
                                boolean isFinish = XFileUtil.bitmapToFilePathAlbum(GzryAddActivity.this, BitmapFactory.decodeFile(media.getRealPath()), new File(media.getRealPath()));
                                if (isFinish) {
                                    uploadFile(media.getRealPath());
                                }
                            } else if (!TextUtils.isEmpty(media.getPath())) {
                                boolean isFinish = XFileUtil.bitmapToFilePathAlbum(GzryAddActivity.this, BitmapFactory.decodeFile(media.getPath()), new File(media.getPath()));
                                if (isFinish) {
                                    uploadFile(media.getPath());
                                }
                            }
                        }
                    }
                    break;
                case 1:
                    //图片
                    try {
                        boolean isFinish = XFileUtil.bitmapToFilePathNo(this, BitmapFactory.decodeFile(cameraPhoto.getAbsolutePath()), photoUri, cameraPhoto);
                        if (isFinish) {
                            uploadFile(cameraPhoto.getAbsolutePath());
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
            }
        }
    }

    //上传
    private void uploadFile(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        if (from == 1) {
                            headUrl = xResultData.getData().get(0).getSavePath();
                            // 调用身份证识别接口
                            recognizeIdCard(headUrl);
                        } else {
                            imageAddZYZS.addImage(xResultData.getData().get(0));
                            imageAddZYZS.notifyData();
                        }
                        XToastUtil.showToast(GzryAddActivity.this, getString(R.string.upload_sucess));
                    }
                } else {
                    XToastUtil.showToast(GzryAddActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 身份证识别
     *
     * @param imageUrl 图片URL
     */
    private void recognizeIdCard(String imageUrl) {
        try {
            String encodedUrl = URLEncoder.encode(imageUrl, "UTF-8");
            String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "url=" + encodedUrl);
            RequestParams idCardParams = new RequestParams(ApiHostUtils.getHostUrl() + "/personinfo/xdryJbxx/zjsb" + "?query=" + jiaMiString);
            XHttpUtils.get(this, idCardParams, DialogUtils.showLoadDialog(GzryAddActivity.this, "正在识别身份证..."), new XHttpResponseCallBack() {
                @Override
                public void onSuccess(String result) {
                    XResultData<IdCardInfo> idCardResult = new Gson().fromJson(result, new TypeToken<XResultData<IdCardInfo>>() {
                    }.getType());
                    if (idCardResult.getStatus() == 0 && idCardResult.getData() != null) {
                        idCardInfo = idCardResult.getData();
                        // 填充识别结果
                        if (!TextUtils.isEmpty(idCardInfo.getName())) {
                            binding.etName.setText(idCardInfo.getName());
                        }
                        if (!TextUtils.isEmpty(idCardInfo.getIdCardNumber())) {
                            binding.etIdCard.setText(idCardInfo.getIdCardNumber());
                        }
                        // if (!TextUtils.isEmpty(idCardInfo.getBirthDate())) {
                        //     binding.tvBrith.setText(idCardInfo.getBirthDate());
                        // }
                        // if (!TextUtils.isEmpty(idCardInfo.getHouseholdAddress())) {
                        //     binding.etJzdz.setText(idCardInfo.getHouseholdAddress());
                        // }
                    }
                }

                @Override
                public void onFailed(int code, String failedMsg) {
                    XToastUtil.showToast(GzryAddActivity.this, "身份证识别失败");
                }

                @Override
                public void onFinished() {
                }
            });
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (from == 2) {
            createFileMuLu();
            if (cameraPhoto != null) {
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                // 设置使用后置摄像头
                intent.putExtra("android.intent.extras.CAMERA_FACING", android.hardware.Camera.CameraInfo.CAMERA_FACING_BACK);
                photoUri = XFileUtil.getUriFromFile(this, cameraPhoto);
                intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                startActivityForResult(intent, 1);
            }
        }
        if (from == 1) {
            createFileMuLu();
            if (cameraPhoto != null) {
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                // 拍照后保存图片，并返回图片文件uri
                photoUri = XFileUtil.getUriFromFile(this, cameraPhoto);
                intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                startActivityForResult(intent, 1);
            }
        }
    }

    /**
     * 创建文件
     */
    private void createFileMuLu() {
        if ((ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED)
                && (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED)) {
            cameraPhoto = new File(PhotoUtil.createPath(this));
            try {
                if (cameraPhoto.exists()) {
                    cameraPhoto.delete();
                }
                cameraPhoto.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            // 权限未授权，需要申请权限
            requestPermission(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

}
