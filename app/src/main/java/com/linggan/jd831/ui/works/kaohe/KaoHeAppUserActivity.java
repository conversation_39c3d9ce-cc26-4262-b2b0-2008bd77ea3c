package com.linggan.jd831.ui.works.kaohe;


import android.net.Uri;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.linggan.jd831.utils.XHttpUtils;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.adapter.KhAppUserListHolder;
import com.linggan.jd831.bean.AppUserListEntity;
import com.linggan.jd831.databinding.ActivityKhZxUserListBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;

import org.xutils.http.RequestParams;

import java.util.ArrayList;

/**
 * 类  名：APP使用列表
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/9/19 9:21
 * 版  权：凌感科技
 */
public class KaoHeAppUserActivity extends XBaseActivity<ActivityKhZxUserListBinding> implements XRefreshLayout.PullLoadMoreListener {

    private String xzqhdm, startDate, endDate;
    private int page = 1, totalPage = 0;
    private boolean isShow = true;

    @Override
    protected ActivityKhZxUserListBinding getViewBinding() {
        return ActivityKhZxUserListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        startDate = getIntent().getStringExtra("kssj");
        endDate = getIntent().getStringExtra("jssj");
        xzqhdm = getIntent().getStringExtra("xzqhdm");
        String xzqhmc = getIntent().getStringExtra("xzqhmc");
        String wcl = getIntent().getStringExtra("wcl");
        binding.recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
        binding.recycler.getAdapter().bindHolder(new KhAppUserListHolder());
        binding.recycler.setOnPullLoadMoreListener(this);
        binding.tvArea.setText("当前辖区：" + xzqhmc);
        binding.tvWcl.setText("完成率：" + wcl);
    }

    @Override
    protected void initListener() {
    }

    @Override
    protected void getData() {
        String canShuString = "startDate=" + startDate + Uri.encode(" 00:00:00", "utf-8") + "&endDate=" + endDate + Uri.encode(" 23:59:59") + "&page=" + page + "&rows=" + XConstantUtils.PAGE_SIZE + "&xzqhdm=" + xzqhdm;
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), canShuString);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.KH_APP_LV_INFO_LIST + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), isShow, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<XResultPageData<AppUserListEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XResultPageData<AppUserListEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        totalPage = xResultData.getData().getTotalPage();
                        if (xResultData.getData().getRecords() != null && xResultData.getData().getRecords().size() > 0) {
                            binding.ivNoData.setVisibility(View.GONE);
                            if (page == 1) {
                                binding.recycler.getAdapter().setData(0, xResultData.getData().getRecords());
                            } else {
                                binding.recycler.getAdapter().addDataAll(0, xResultData.getData().getRecords());
                            }
                        } else {
                            if (page == 1) {
                                binding.ivNoData.setVisibility(View.VISIBLE);
                                binding.recycler.getAdapter().setData(0, new ArrayList());
                            }
                        }
                    } else {
                        if (page == 1) {
                            binding.ivNoData.setVisibility(View.VISIBLE);
                            binding.recycler.getAdapter().setData(0, new ArrayList());
                        }
                    }
                } else {
                    binding.ivNoData.setVisibility(View.VISIBLE);
                    binding.recycler.getAdapter().setData(0, new ArrayList());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                binding.recycler.setPullLoadMoreCompleted();
            }
        });

    }

    @Override
    public void onRefresh() {
        page = 1;
        isShow = false;
        getData();
    }

    @Override
    public boolean onLoadMore() {
        if (page < totalPage) {
            page++;
            isShow = false;
            getData();
            return true;
        }
        return false;
    }
}
