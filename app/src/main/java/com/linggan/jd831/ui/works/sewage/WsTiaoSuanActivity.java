package com.linggan.jd831.ui.works.sewage;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.TaskSpListEntity;
import com.linggan.jd831.databinding.ActivityWuShuiTsBinding;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XHttpUtils;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 类  名：污水调酸
 * 作  者：zxb
 * 说  明：
 * 时  间：2023/3/14 17:26
 */
public class WsTiaoSuanActivity extends XBaseActivity<ActivityWuShuiTsBinding> implements View.OnClickListener {

    private ImageAddUtil imageAddVideoTs;
    private int from = 0;
    private String taskBh;
    private Uri videoUri;
    private File cameraVideo;

    @Override
    protected ActivityWuShuiTsBinding getViewBinding() {
        return ActivityWuShuiTsBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        StrUtils.getTsSlVideoText(binding.tvVideoTsInfo);
        taskBh = getIntent().getStringExtra("bh");
        //
        createVideo();
        //
        imageAddVideoTs = new ImageAddUtil(this, binding.gridVideoTs);
        imageAddVideoTs.setMax(1);
        imageAddVideoTs.setOnImageAddListener(() -> {
            from = 3;
            requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.RECORD_AUDIO, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        });
    }

    /**
     * 创建视频文件
     */
    private void createVideo() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
                && ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {
            cameraVideo = new File(getExternalFilesDir("video"), System.currentTimeMillis() + ".mp4");
            try {
                if (cameraVideo.exists()) {
                    cameraVideo.delete();
                }
                cameraVideo.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            // 权限未授权，需要申请权限
            requestPermission(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        }
    }

    @Override
    protected void initListener() {
        binding.btSubmit.setOnClickListener(this);
        binding.tvVideoTsInfo.setOnClickListener(this);
    }

    @Override
    protected void getData() {
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.bt_submit) {
            //数据提交
            if (ButtonUtils.isFastClick()) {
                postData();
            }
        }
    }

    /**
     * 数据提交
     */
    private void postData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.updateYbTS);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("sjLy", "3");
            objectMap.put("rwBh", taskBh);
            JSONArray jsonArrayTs = new JSONArray();
            if (imageAddVideoTs != null && imageAddVideoTs.getPaths() != null && imageAddVideoTs.getPaths().size() > 0) {
                for (int i = 0; i < imageAddVideoTs.getPaths().size(); i++) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("lj", imageAddVideoTs.getPaths().get(i).getSavePath());
                    jsonObject.put("mc", imageAddVideoTs.getPaths().get(i).getOriginName());
                    jsonObject.put("hz", StrUtils.getFileType(imageAddVideoTs.getPaths().get(i).getOriginName()));
                    jsonObject.put("dx", imageAddVideoTs.getPaths().get(i).getFileSize());
                    jsonArrayTs.put(jsonObject);
                }
            }
            if (jsonArrayTs.length() <= 0) {
                XToastUtil.showToast(this, "请上传酸碱度调整视频");
                return;
            }
            objectMap.put("tsSpList", jsonArrayTs);
            if (TextUtils.isEmpty(binding.etYy.getText().toString())) {
                XToastUtil.showToast(this, "请输入无法送达原因");
                return;
            }
            objectMap.put("wfsdyy", binding.etYy.getText().toString());
        } catch (JSONException e) {
        }
        binding.btSubmit.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJsonNoCan(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(WsTiaoSuanActivity.this, getString(R.string.sub_sucess));
                    EventBus.getDefault().post(new TaskSpListEntity());
                    finish();
                } else {
                    XToastUtil.showToast(WsTiaoSuanActivity.this, xResultData.getErrorInfo());
                    binding.btSubmit.setEnabled(true);
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSubmit.setEnabled(true);
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case 120://脱酸录像返回
                    if (!TextUtils.isEmpty(cameraVideo.getAbsolutePath())) {
                        uploadFileVideoTs(cameraVideo.getAbsolutePath());
                    }
                    break;
            }
        }
    }

    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (from == 3) {
            //视频
            createVideo();
            Intent intent = new Intent(MediaStore.ACTION_VIDEO_CAPTURE);

            videoUri = XFileUtil.getUriFromFile(this, cameraVideo);

            intent.putExtra(MediaStore.EXTRA_OUTPUT, videoUri);
            startActivityForResult(intent, 120);
        }
    }

    //上传视频
    private void uploadFileVideoTs(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        imageAddVideoTs.addImage(xResultData.getData().get(0));
                        imageAddVideoTs.notifyData();
                    }
                } else {
                    XToastUtil.showToast(WsTiaoSuanActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }
}