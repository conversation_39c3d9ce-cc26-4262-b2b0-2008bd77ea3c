package com.linggan.jd831.ui.works.qiye.v2.lab;

import android.Manifest;
import android.app.Dialog;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XDateUtil;
import com.lgfzd.base.utils.XGlideEngine;
import com.lgfzd.base.utils.XImageFileCompressEngine;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.InputEntity;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.XieYiShiEventEntity;
import com.linggan.jd831.bean.ZiDianEntity;
import com.linggan.jd831.bean.ZiDianEventEntity;
import com.linggan.jd831.databinding.ActivityLabPersonAddBinding;
import com.linggan.jd831.ui.works.qiye.v2.lab.entity.FaceAlyBean;
import com.linggan.jd831.ui.works.qiye.v2.lab.entity.ListSysRyEntity;
import com.linggan.jd831.ui.works.qiye.v2.warehouse.InventoryAddActivity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.MdFactoryUtils;
import com.linggan.jd831.utils.PhotoUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.BaseZiDianDialog;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.common.Callback;
import org.xutils.http.RequestParams;
import org.xutils.x;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class PersonAddActivity extends XBaseActivity<ActivityLabPersonAddBinding> implements View.OnClickListener {

    private String lxCode, fangShiCode, xjBh, kssj, jssj, hdLbCode = "";
    private long timeDate1;
    private ZiDianEventEntity ziDianEvent;
    private String warehouseStatus;
    private XieYiShiEventEntity xieYiShiEvent;
    private String from;
    private String fromImage;
    private ImageAddUtil imageAddImg, imageAddImgNo;
    private int imgNum = 1, choiceImg = 1;
    private File cameraPhoto;
    private Uri photoUri;

    private ListSysRyEntity data;

    private ListSysRyEntity regionEvent;

    @Override
    protected ActivityLabPersonAddBinding getViewBinding() {
        return ActivityLabPersonAddBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.REQUEST_CAMERA:
                case PictureConfig.CHOOSE_REQUEST:
                    List<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                    if (null != selectList) {
                        for (LocalMedia media : selectList) {
                            //图片
                            if (media.isCompressed()) {
                                uploadFile(media.getCompressPath());
                            } else if (!TextUtils.isEmpty(media.getRealPath())) {
                                uploadFile(media.getRealPath());
                            }
                        }
                    }
                    break;
                case 119:
                    //图片
//                    boolean isFinish = XFileUtil.bitmapToFilePath(PersonAddActivity.this, BitmapFactory.decodeFile(cameraPhoto.getAbsolutePath()), photoUri, cameraPhoto);
//                    if (isFinish) {
//                    }
                    uploadFile(cameraPhoto.getAbsolutePath());

                    break;

            }
        }
    }

    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (fromImage == "1" || fromImage == "2") {
            createFileMuLu();
            if (cameraPhoto != null) {
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                photoUri = XFileUtil.getUriFromFile(this, cameraPhoto);
                intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                startActivityForResult(intent, 119);
            }
        }
    }


    private void createFileMuLu() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {
            cameraPhoto = new File(PhotoUtil.createPath(this));
            try {
                if (cameraPhoto.exists()) {
                    cameraPhoto.delete();
                }
                cameraPhoto.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            // 权限未授权，需要申请权限
            requestPermission(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        }
    }


    //上传
    private void uploadFile(String path) {
        // 先将图片转为base64进行人脸识别验证
        String base64Str = XFileUtil.imageToBase64(path);

        // 调用人脸识别验证
        faceLogin2(base64Str, new FaceVerifyCallback() {
            @Override
            public void onSuccess() {
                // 人脸验证通过后再上传文件
                uploadFileToServer(path);
                if (Objects.equals(fromImage, "1")) {
                    binding.tvImg.setText("");
                } else if (Objects.equals(fromImage, "2")) {
                    binding.tvImgNo.setText("");
                }
            }

            @Override
            public void onFailed(String message) {
                if (Objects.equals(message, "请检查姓名和身份证号是否输入") || Objects.equals(message, "请先拍照")) {
                    XToastUtil.showToast(PersonAddActivity.this, message);
                    return;
                }

                if (Objects.equals(fromImage, "1")) {
                    binding.tvImg.setText(message);
                } else if (Objects.equals(fromImage, "2")) {
                    binding.tvImgNo.setText(message);
                }
                XToastUtil.showToast(PersonAddActivity.this, message);
            }
        });
    }

    // 将原来的uploadFile方法改名为uploadFileToServer
    private void uploadFileToServer(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            // 原有的上传逻辑保持不变
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        if (fromImage == "1") {
                            imageAddImg.addImage(xResultData.getData().get(0));
                            imageAddImg.notifyData();
                        } else if (fromImage == "2") {
                            imageAddImgNo.addImage(xResultData.getData().get(0));
                            imageAddImgNo.notifyData();
                        }
                    }
                } else {
                    XToastUtil.showToast(PersonAddActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    // 添加回调接口
    private interface FaceVerifyCallback {
        void onSuccess();

        void onFailed(String message);
    }

    // 修改人脸识别方法,添加回调
    private void faceLogin2(String base64Str, FaceVerifyCallback callback) {
        if (TextUtils.isEmpty(base64Str)) {
            callback.onFailed("请先拍照");
            return;
        }

        String name = binding.etWarehouseName.getText().toString();
        String zjhm = binding.tvChemicalStorage.getText().toString();

        if (TextUtils.isEmpty(zjhm) || TextUtils.isEmpty(name)) {
            callback.onFailed("请检查姓名和身份证号是否输入");
            return;
        }

        RequestParams requestParams = new RequestParams(ApiUrlsUtils.FACE_SB_ALY);
        requestParams.addHeader("Authorization", "APPCODE " + "2efae42f033049cdb6af81c475d2b50b");
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        requestParams.addBodyParameter("base64Str", base64Str);
        requestParams.addBodyParameter("liveChk", "0");
        requestParams.addBodyParameter("Threshold", "0.33");
        requestParams.addBodyParameter("number", zjhm.trim());
        requestParams.addBodyParameter("name", name.trim());

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("name", name);
            jsonObject.put("number", zjhm);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        requestParams.setCharset("utf-8");
        Dialog dialog = DialogUtils.showLoadDialog(this, "检测中");
        if (dialog != null && !dialog.isShowing()) {
            dialog.show();
            dialog.setCancelable(false);
            dialog.setCanceledOnTouchOutside(false);
        }
        x.http().post(requestParams, new Callback.CommonCallback<String>() {
            @Override
            public void onSuccess(String result) {
                try {
                    dialog.dismiss();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                FaceAlyBean faceAlyBean = new Gson().fromJson(result, new TypeToken<FaceAlyBean>() {
                }.getType());
                if (faceAlyBean != null && faceAlyBean.getError_code() == 0) {
                    if (faceAlyBean.getResult() != null) {
                        switch (faceAlyBean.getResult().getValidate_Result()) {
                            case 1:
                                //系统分析为同一人
                                MdFactoryUtils.mdSave(ApiUrlsUtils.FACE_SB_ALY + "--cg", base64Str, result + "--" + jsonObject.toString() + StrUtils.getSbxxCj());
                                callback.onSuccess();
                                break;
                            default:
                                callback.onFailed(getFaceVerifyErrorMessage(faceAlyBean.getResult().getValidate_Result()));
                                break;
                        }

                    } else {
                        callback.onFailed(StrUtils.getDev(faceAlyBean.getReason(), getString(R.string.service_erroe)));
                    }
                } else {
                    callback.onFailed(StrUtils.getDev(faceAlyBean.getReason(), getString(R.string.service_erroe)));
                }
                MdFactoryUtils.mdSave(ApiUrlsUtils.FACE_SB_ALY, base64Str, result + "--" + jsonObject.toString() + StrUtils.getSbxxCj());
            }

            @Override
            public void onError(Throwable ex, boolean isOnCallback) {
                try {
                    dialog.dismiss();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                callback.onFailed(ex.getMessage());
                MdFactoryUtils.mdSave(ApiUrlsUtils.FACE_SB_ALY, requestParams.toString(), ex.getMessage() + "--" + StrUtils.getSbxxCj());
            }

            @Override
            public void onCancelled(CancelledException cex) {
                try {
                    dialog.dismiss();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }

            @Override
            public void onFinished() {
                try {
                    dialog.dismiss();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    // 添加错误信息获取方法
    private String getFaceVerifyErrorMessage(int validateResult) {
        switch (validateResult) {
            case -1:
                return "身份证和姓名不一致";
            case -2:
                return "公安库中无此身份证记录";
            case -3:
                return "公安身份证库中没有此号码的照片";
            case -4:
            case -5:
            case -6:
            case -7:
                return "照片参数不合格";
            case 2:
            case 3:
                return "系统分析为不是同人";
            case 4:
                return "没检测到人脸";
            case 5:
                return "疑似非活体";
            case 6:
                return "出现多张脸";
            case 7:
                return "身份证和姓名一致，官方人脸比对失败";
            default:
                return "验证失败";
        }
    }

    @Override
    protected void initView() {
        EventBus.getDefault().register(this);
        setTitle("人员管理");
        imageAddImg = new ImageAddUtil(this, binding.gridImg);
        imageAddImg.setMax(imgNum);
        imageAddImg.setOnImageAddListener(() -> {
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                fromImage = "1";
                if (code.equals("1")) {
                    choiceImg = 1;
                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setCompressEngine(new XImageFileCompressEngine()).setMaxSelectNum(imgNum - imageAddImg.getPaths().size()).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        });

        imageAddImgNo = new ImageAddUtil(this, binding.gridImgNo);
        imageAddImgNo.setMax(imgNum);
        imageAddImgNo.setOnImageAddListener(() -> {
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                fromImage = "2";
                if (code.equals("1")) {
                    choiceImg = 1;
                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false)
                            .setImageEngine(XGlideEngine.createGlideEngine())
                            .setCompressEngine(new XImageFileCompressEngine())
                            .setMaxSelectNum(imgNum - imageAddImgNo.getPaths().size()).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        });

        from = getIntent().getStringExtra("from");
        if (from != null && from.equals("edit")) {
            data = (ListSysRyEntity) getIntent().getSerializableExtra("data");
            initEditUI();
        }
    }

    private void initEditUI() {
        if (data != null) {
            binding.etWarehouseName.setText(data.getXm());
            binding.tvChemicalStorage.setText(data.getSfz());
            // 区域
            if (data.getQyList() != null && data.getQyList().size() > 0) {
                StringBuilder data1 = new StringBuilder();
                for (int i = 0; i < data.getQyList().size(); i++) {
                    data.getQyList().get(i).setChoice(true);
                    data1.append(data.getQyList().get(i).getMc()).append(",");
                }
                if (data1.length() > 0) {
                    data1.deleteCharAt(data1.length() - 1);
                }
                regionEvent = new ListSysRyEntity();
                regionEvent.setQyList(data.getQyList());
                binding.tvRegional.setText(data1);
            }

            // fjList	object []
            // 人脸照片（通过lx区分哦，yzd_sys_kz：戴口罩、yzd_sys_nkz：不戴口罩）
            if (data.getFjList() != null && data.getFjList().size() > 0) {
                for (ListSysRyEntity.FjList item : data.getFjList()) {
                    if (item != null && item.getLj() != null) {
                        if ("yzd_sys_kz".equals(item.getLx())) {
                            // 戴口罩
                            OssFileEntity imageData = new OssFileEntity();
                            imageData.setMc(item.getMc());
                            imageData.setFileSize(item.getDx());
                            imageData.setSavePath(item.getLj());
                            imageData.setOriginName(item.getMc());
                            imageAddImg.addImage(imageData);
                            imageAddImg.notifyData();

                        } else if ("yzd_sys_nkz".equals(item.getLx())) {
                            // 不戴口罩
                            OssFileEntity imageData = new OssFileEntity();
                            imageData.setMc(item.getMc());
                            imageData.setFileSize(item.getDx());
                            imageData.setSavePath(item.getLj());
                            imageData.setOriginName(item.getMc());
                            imageAddImgNo.addImage(imageData);
                            imageAddImgNo.notifyData();

                        }
                    }
                }
            }
        }
    }

    @Override
    protected void initListener() {
        binding.btSure.setOnClickListener(this);
//        binding.tvWarehouseStatus.setOnClickListener(this);
        binding.btSure.setOnClickListener(this);
        // 为其他新增的控件添加点击监听
        binding.tvChemicalStorage.setOnClickListener(this);
        binding.tvRegional.setOnClickListener(this);
//        binding.tvOperationMachine.setOnClickListener(this);
//        binding.tvOperationType.setOnClickListener(this);
    }

    @Override
    protected void getData() {
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_regional) {
            Bundle bundle = new Bundle();
            bundle.putSerializable("regionData", regionEvent);
            XIntentUtil.redirectToNextActivity(this, LabRegionActivity.class, bundle);

        } else if (v.getId() == R.id.bt_sure) {
            if (ButtonUtils.isFastClick()) {
                submitWarehouseData();
            }
        } else if (v.getId() == R.id.tv_warehouse_status) {
            showWarehouseStatusDialog();
        } else if (v.getId() == R.id.tv_operation_machine) {
            // 实现选择操作机的逻辑
            showOperationMachineDialog();
        } else if (v.getId() == R.id.tv_operation_type) {
            // 实现选择操作机类型的逻辑
            showOperationTypeDialog();
        }
    }

    private void showWarehouseStatusDialog() {
        FactoryUtils.getBaseDataType(this, "warehouse_status", result -> {
            BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, (ArrayList<ZiDianEntity>) result);
            dianDialog.setOnClickDataListener(() -> {
//                binding.tvWarehouseStatus.setText(dianDialog.getData().getMc());
                warehouseStatus = dianDialog.getData().getDm();
            });
            dianDialog.show();
        });
    }

    private void showChemicalStorageDialog() {
        // 跳转页面
        Bundle bundle = new Bundle();
        if (xieYiShiEvent != null) {
            bundle.putString("from", from);
            bundle.putSerializable("info", xieYiShiEvent);
        }
        XIntentUtil.redirectToNextActivity(this, InventoryAddActivity.class, bundle);
    }

    private void showOperationMachineDialog() {
        // 实现选择操作机的对话框
    }

    private void showOperationTypeDialog() {
        // 实现选择操作机类型的对话框
    }

    // 修改submitWarehouseData()方法
    private void submitWarehouseData() {
        if (!validateInput()) {
            return;
        }

        // 如果是编辑模式,先进行人脸校验
        if (from != null && from.equals("edit")) {
            verifyExistingFacePhotos(() -> {
                // 校验通过后继续提交
                doSubmitData();
            });
        } else {
            // 新增模式直接提交
            doSubmitData();
        }
    }

    // 添加人脸校验方法
    private void verifyExistingFacePhotos(Runnable onSuccess) {
        if (data == null || data.getFjList() == null || data.getFjList().isEmpty()) {
            onSuccess.run();
            return;
        }

        // 禁用提交按钮
        binding.btSure.setEnabled(false);

        // 显示不可关闭的加载对话框
        android.app.Dialog loadingDialog = null;
        try {
            loadingDialog = DialogUtils.showLoadDialog(this, "校验人脸中");
            if (loadingDialog != null) {
                loadingDialog.setCancelable(false); // 设置不可关闭
                loadingDialog.show();
            } else {
                XToastUtil.showToast(this, "创建对话框失败");
                binding.btSure.setEnabled(true); // 启用按钮
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
            XToastUtil.showToast(this, "显示加载对话框失败");
            binding.btSure.setEnabled(true); // 启用按钮
            return;
        }

        // 保存对话框的最终引用
        final android.app.Dialog finalLoadingDialog = loadingDialog;

        // 用于追踪验证完成的照片数量
        final int[] verifiedCount = {0};
        final int totalPhotos = data.getFjList().size();
        final boolean[] hasError = {false};

        // 创建检查方法
        Runnable checkAllVerified = () -> {
            if (verifiedCount[0] >= totalPhotos) {
                try {
                    // 安全地关闭对话框
                    if (finalLoadingDialog != null && finalLoadingDialog.isShowing()) {
                        finalLoadingDialog.dismiss();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (!hasError[0]) {
                    onSuccess.run();
                } else {
                    // 如果有错误，启用按钮
                    binding.btSure.setEnabled(true);
                }
            }
        };

        for (ListSysRyEntity.FjList item : data.getFjList()) {
            if (item != null && item.getLj() != null) {
                // 使用Glide加载并压缩图片
                Glide.with(this)
                        .asBitmap()
                        .load(item.getLj())
                        .override(1024, 1024)
                        .format(DecodeFormat.PREFER_RGB_565)
                        .encodeQuality(80)
                        .into(new CustomTarget<Bitmap>() {
                            @Override
                            public void onResourceReady(@NonNull Bitmap bitmap, @Nullable Transition<? super Bitmap> transition) {
                                try {
                                    Bitmap compressedBitmap = compressBitmapToMaxSize(bitmap, 4096 * 1024);
                                    if (compressedBitmap != null) {
                                        String base64Str = XFileUtil.bitmapToBase64(compressedBitmap);

                                        // 进行人脸验证
                                        faceLogin2(base64Str, new FaceVerifyCallback() {
                                            @Override
                                            public void onSuccess() {
                                                verifiedCount[0]++;
                                                checkAllVerified.run();
                                                // 验证完成后再回收
                                                if (compressedBitmap != bitmap) {
                                                    compressedBitmap.recycle();
                                                }
                                            }

                                            @Override
                                            public void onFailed(String message) {
                                                hasError[0] = true;
                                                String photoType = "yzd_sys_kz".equals(item.getLx()) ? "戴口罩" : "不戴口罩";
                                                String errorMsg = photoType + "照片人脸验证失败: " + message;

                                                runOnUiThread(() -> {
                                                    if ("yzd_sys_kz".equals(item.getLx())) {
                                                        binding.tvImg.setText(errorMsg);
                                                    } else {
                                                        binding.tvImgNo.setText(errorMsg);
                                                    }

                                                    XToastUtil.showToast(PersonAddActivity.this, errorMsg);
                                                    binding.btSure.setEnabled(true);
                                                });

                                                verifiedCount[0]++;
                                                checkAllVerified.run();
                                                // 验证完成后再回收
                                                if (compressedBitmap != bitmap) {
                                                    compressedBitmap.recycle();
                                                }
                                            }
                                        });
                                    } else {
                                        hasError[0] = true;
                                        runOnUiThread(() -> {
                                            XToastUtil.showToast(PersonAddActivity.this, "图片压缩失败");
                                            binding.btSure.setEnabled(true);
                                        });
                                        verifiedCount[0]++;
                                        checkAllVerified.run();
                                    }
                                } finally {
                                    // 确保原始bitmap被回收
                                    if (!bitmap.isRecycled()) {
                                        bitmap.recycle();
                                    }
                                }
                            }

                            @Override
                            public void onLoadCleared(@Nullable Drawable placeholder) {
                            }

                            @Override
                            public void onLoadFailed(@Nullable Drawable errorDrawable) {
                                hasError[0] = true;
                                runOnUiThread(() -> {
                                    XToastUtil.showToast(PersonAddActivity.this, "获取照片失败");
                                    binding.btSure.setEnabled(true);
                                });
                                verifiedCount[0]++;
                                checkAllVerified.run();
                            }
                        });
            }
        }
    }

    // 添加压缩方法
    private Bitmap compressBitmapToMaxSize(Bitmap bitmap, long maxSizeBytes) {
        if (bitmap == null || bitmap.isRecycled()) {
            return null;
        }

        int quality = 100;
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Bitmap resultBitmap = null;

        try {
            // 先进行质量压缩
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, baos);

            while (baos.toByteArray().length > maxSizeBytes && quality > 10) {
                baos.reset();
                quality -= 10;
                bitmap.compress(Bitmap.CompressFormat.JPEG, quality, baos);
            }

            // 如果质量压缩后仍然超过大小限制，则进行尺寸压缩
            if (baos.toByteArray().length > maxSizeBytes) {
                byte[] bytes = baos.toByteArray();
                BitmapFactory.Options options = new BitmapFactory.Options();
                options.inSampleSize = 2;  // 缩小为原来的1/2

                while (bytes.length > maxSizeBytes && options.inSampleSize <= 8) {
                    baos.reset();
                    Bitmap scaledBitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.length, options);
                    if (scaledBitmap != null) {
                        scaledBitmap.compress(Bitmap.CompressFormat.JPEG, quality, baos);
                        bytes = baos.toByteArray();
                        scaledBitmap.recycle();
                    }
                    options.inSampleSize *= 2;
                }
            }

            // 创建最终的压缩后的Bitmap
            byte[] finalBytes = baos.toByteArray();
            resultBitmap = BitmapFactory.decodeByteArray(finalBytes, 0, finalBytes.length);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                baos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return resultBitmap;
    }

    // 将原有的提交逻辑抽取为单独的方法
    private void doSubmitData() {
        JSONObject objectMap = new JSONObject();
        String url = ApiUrlsUtils.SAVE_SYS_RY;
        if (from != null && from.equals("edit")) {
            url = ApiUrlsUtils.UPDATE_SYS_RY;
            try {
                objectMap.put("bh", data.getBh());
            } catch (Exception e) {
                XToastUtil.showToast(this, "Bh数据获取失败");
                return;
            }
        }

        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + url);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");

        try {
            // 处理戴口罩照片
            if (imageAddImg != null && imageAddImg.getPaths() != null) {
                JSONArray kzFjListArray = new JSONArray();
                for (OssFileEntity file : imageAddImg.getPaths()) {
                    JSONObject kzFj = new JSONObject();
                    kzFj.put("lj", file.getSavePath());
                    kzFj.put("dx", file.getFileSize());
                    kzFj.put("mc", file.getOriginName());
                    kzFj.put("hz", StrUtils.getFileType(file.getOriginName()));
                    kzFj.put("lx", "yzd_sys_kz");  // 戴口罩类型
                    kzFjListArray.put(kzFj);
                }
                objectMap.put("kzFjList", kzFjListArray);
            }

            // 处理不戴口罩照片
            if (imageAddImgNo != null && imageAddImgNo.getPaths() != null) {
                JSONArray nkzFjListArray = new JSONArray();
                for (OssFileEntity file : imageAddImgNo.getPaths()) {
                    JSONObject nkzFj = new JSONObject();
                    nkzFj.put("lj", file.getSavePath());
                    nkzFj.put("dx", file.getFileSize());
                    nkzFj.put("mc", file.getOriginName());
                    nkzFj.put("hz", StrUtils.getFileType(file.getOriginName()));
                    nkzFj.put("lx", "yzd_sys_nkz");  // 不戴口罩类型
                    nkzFjListArray.put(nkzFj);
                }
                objectMap.put("nkzFjList", nkzFjListArray);
            }

            // 处理区域编号列表
            if (regionEvent != null && regionEvent.getQyList() != null) {
                JSONArray qybhList = new JSONArray();
                for (int i = 0; i < regionEvent.getQyList().size(); i++) {
                    if (regionEvent.getQyList().get(i).isChoice()) {
                        qybhList.put(regionEvent.getQyList().get(i).getBh());
                    }
                }
                objectMap.put("qybhList", qybhList);
            }

            // 添加基本信息
            objectMap.put("sfz", binding.tvChemicalStorage.getText().toString()); // 身份证号
            objectMap.put("sjLy", "3"); // Android端固定为3
            objectMap.put("sysBh", XShareCacheUtils.getInstance().getString("sysBh")); // 实验室编号
            objectMap.put("xm", binding.etWarehouseName.getText().toString()); // 姓名

            binding.btSure.setEnabled(false);
            String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());

            XHttpUtils.postJsonNoCan(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), new XHttpResponseCallBack() {
                @Override
                public void onSuccess(String result) {
                    XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                    }.getType());
                    if (xResultData.getStatus() == 0) {
                        if (from != null && from.equals("edit")) {
                            XToastUtil.showToast(PersonAddActivity.this, "编辑成功");
                        } else {
                            XToastUtil.showToast(PersonAddActivity.this, getString(R.string.add_sucess));
                        }
                        EventBus.getDefault().post(new InputEntity());
                        if (from != null && from.equals("edit")) {
                            XIntentUtil.redirectToNextTopActivity(PersonAddActivity.this, PersonListActivity.class);
                        } else {
                            finish();
                        }
                    } else {
                        XToastUtil.showToast(PersonAddActivity.this, xResultData.getErrorInfo());
                        binding.btSure.setEnabled(true);
                    }
                }

                @Override
                public void onFailed(int code, String failedMsg) {
                    binding.btSure.setEnabled(true);
                }

                @Override
                public void onFinished() {
                }
            });

        } catch (JSONException e) {
            e.printStackTrace();
            XToastUtil.showToast(this, "数据处理出错");
            binding.btSure.setEnabled(true);
        }
    }

    private boolean validateInput() {
        if (TextUtils.isEmpty(binding.etWarehouseName.getText().toString())) {
            XToastUtil.showToast(this, "请输入姓名");
            return false;
        }

        // 验证身份证号
        if (TextUtils.isEmpty(binding.etWarehouseName.getText().toString())) {
            XToastUtil.showToast(this, "请输入身份证号");
            return false;
        }

        // 验证照片上传
        if ((imageAddImg == null || imageAddImg.getPaths().isEmpty()) &&
                (imageAddImgNo == null || imageAddImgNo.getPaths().isEmpty())) {
            XToastUtil.showToast(this, "请上传不戴口罩照片");
            return false;
        }

        if (imageAddImgNo == null || imageAddImgNo.getPaths().isEmpty()) {
            XToastUtil.showToast(this, "请上传戴口罩照片");
            return false;
        }

        // 验证区域选择
        if (TextUtils.isEmpty(binding.tvRegional.getText().toString())) {
            XToastUtil.showToast(this, "请选择实验室区域");
            return false;
        }

        return true;
    }

    /**
     * 判断开始时间
     *
     * @param kssj
     * @param timeDate1
     * @return
     */
    private boolean jumpKssTime(String kssj, long timeDate1) {
        long rwKssj = XDateUtil.getDateByFormat(kssj, XConstantUtils.YMD).getTime();
        if (timeDate1 < rwKssj) {
            return true;
        }
        return false;
    }

    /**
     * 判断开始时间
     *
     * @param jssj
     * @param timeDate2
     * @return
     */
    private boolean jumpJssTime(String jssj, long timeDate2) {
        long rwJssj = XDateUtil.getDateByFormat(jssj, XConstantUtils.YMD).getTime();
        if (timeDate2 > rwJssj) {
            return true;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(InputEntity event) {
        if (event != null) {
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(ListSysRyEntity event) {
        if (event != null) {
            if (event.getQyList() != null && event.getQyList().size() > 0) {
                regionEvent = new ListSysRyEntity();
                regionEvent.setQyList(event.getQyList());
                // 遍历区域 显示选择名称
                StringBuilder data = new StringBuilder();
                for (int i = 0; i < event.getQyList().size(); i++) {
                    if (event.getQyList().get(i).isChoice()) {
                        data.append(event.getQyList().get(i).getMc()).append(",");
                    }
                }
                // 去掉最后一个的","
                if (data.length() > 0) {
                    data.deleteCharAt(data.length() - 1);
                }
                binding.tvRegional.setText(data);
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
