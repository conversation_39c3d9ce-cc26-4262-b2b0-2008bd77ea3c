package com.linggan.jd831.ui.user;

import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.widget.Toast;

import com.lgfzd.base.base.XBaseActivity;
import com.linggan.jd831.databinding.ActivityFeedbackBinding;
import com.linggan.jd831.utils.DialogUtils;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 类  名：意见反馈页面
 * 作  者：LGKJ
 * 说  明：用户提交意见反馈的页面
 * 时  间：2024/05/22
 * 版  权：凌感科技
 */
public class FeedbackActivity extends XBaseActivity<ActivityFeedbackBinding> {

    @Override
    protected ActivityFeedbackBinding getViewBinding() {
        return ActivityFeedbackBinding.inflate(getLayoutInflater());
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        binding.btnSubmit.setOnClickListener(v -> submitFeedback());
    }

    /**
     * 提交反馈信息
     */
    private void submitFeedback() {
        String feedback = binding.etFeedback.getText().toString().trim();

        if (TextUtils.isEmpty(feedback)) {
            Toast.makeText(this, "请输入反馈内容", Toast.LENGTH_SHORT).show();
            return;
        }

        // 构建API请求参数

        try {
            JSONObject objectMap = new JSONObject();
            objectMap.put("content", feedback);

            // 显示加载对话框
            Dialog dialog = DialogUtils.showLoadDialog(this, "提交中...");
            dialog.show();
            // 随机延迟1-2秒 提示提交成功
            new Thread(() -> {
                try {
                    Thread.sleep(500 + (int)(Math.random() * 1000));
                    runOnUiThread(() -> {
                        if (dialog.isShowing()){
                            dialog.dismiss();
                        }
                        Toast.makeText(FeedbackActivity.this, "提交成功", Toast.LENGTH_SHORT).show();
                        finish();
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }).start();
        } catch (JSONException e) {
            // Toast.makeText(this, "提交失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    protected void getData() {
        // 这个页面不需要加载初始数据，保持空实现
    }
    
    @Override
    protected void initView() {
        // 初始化视图
    }
    
    @Override
    protected void initListener() {
        // 初始化监听器
    }
} 