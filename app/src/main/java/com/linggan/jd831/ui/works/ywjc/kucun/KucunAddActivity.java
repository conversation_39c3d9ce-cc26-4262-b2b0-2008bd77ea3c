package com.linggan.jd831.ui.works.ywjc.kucun;

import android.Manifest;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.view.TimePickerView;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XDateUtil;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.CodeNameEntity;
import com.linggan.jd831.bean.InputEntity;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.PeoStatusEntity;
import com.linggan.jd831.bean.XuanJiaoListEntity;
import com.linggan.jd831.bean.ZiDianEventEntity;
import com.linggan.jd831.databinding.ActivityKucunAddBinding;
import com.linggan.jd831.ui.common.InputInfoActivity;
import com.linggan.jd831.ui.common.MulChoiceList1Activity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.CameraUtil;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.CodeNameDialog;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.entity.LocalMedia;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 类  名：宣教活动-新增
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/12/23 13:11
 */
public class KucunAddActivity extends XBaseActivity<ActivityKucunAddBinding> implements View.OnClickListener {

    private String lxCode, fangShiCode, xjBh, kssj, jssj, hdLbCode = "";
    private long timeDate1;
    private ZiDianEventEntity ziDianEvent;

    private ImageAddUtil imageAddXCZP;
    private int from = 1;
    
    // 添加相机工具类
    private CameraUtil cameraUtil;

    // 添加字段变量
    private final String systemStock = ""; // 系统剩余库存
    private final String actualStock = ""; // 实际剩余库存
    private final String checker = ""; // 检查人员
    private final String remark = ""; // 检查情况备注

    @Override
    protected ActivityKucunAddBinding getViewBinding() {
        return ActivityKucunAddBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        EventBus.getDefault().register(this);
        xjBh = getIntent().getStringExtra("bh");
        kssj = getIntent().getStringExtra("kssj");
        jssj = getIntent().getStringExtra("jssj");
        if (!TextUtils.isEmpty(xjBh)) {
            //任务中心过来
//            binding.tvKsTime.setText(kssj);
//            timeDate1 = XDateUtil.getDateByFormat(kssj, XConstantUtils.YMD).getTime();
//            binding.tvJsTime.setText(jssj);
        }

        setTitle("新增报损记录");

        StrUtils.getPhotoVideoText2(binding.tvSjskInfo, null);
        // 初始化图片上传
        imageAddXCZP = new ImageAddUtil(this, binding.gridXczp);
        imageAddXCZP.setMax(1);
        imageAddXCZP.setOnImageAddListener(() -> {
            from = 2;
            requestPermission(new String[]{android.Manifest.permission.CAMERA, Manifest.permission.RECORD_AUDIO, android.Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        });

        // 初始化检查人员点击事件
        binding.tvChecker.setOnClickListener(this);
        // 初始化检查情况备注点击事件
        binding.tvNoDxRemark.setOnClickListener(this);

        // 初始化相机工具类
        cameraUtil = new CameraUtil(this, path -> {
            if (!TextUtils.isEmpty(path)) {
                uploadFile(path);
            }
        });
    }

    @Override
    protected void initListener() {
        binding.btSure.setOnClickListener(this);
    }

    @Override
    protected void getData() {
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_ks_time) {
            //开始时间
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                timeDate1 = XDateUtil.getDateByFormat(timeDate, XConstantUtils.YMD).getTime();
                if (!TextUtils.isEmpty(xjBh) && !TextUtils.isEmpty(kssj) && jumpKssTime(kssj, timeDate1)) {
                    //任务中心过来--选择的时间必须比任务开始时间大于等于
                    XToastUtil.showToast(this, "开始日期须大于(或等于)任务开始日期");
                }
//                binding.tvKsTime.setText(timeDate);

            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_js_time) {
            //结束时间
//            if (TextUtils.isEmpty(binding.tvKsTime.getText().toString())) {
//                XToastUtil.showToast(this, "请先选择开始日期");
//                return;
//            }
//            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
//                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
//                long timeDate2 = XDateUtil.getDateByFormat(timeDate, XConstantUtils.YMD).getTime();
//                if (timeDate2 < timeDate1) {
//                    XToastUtil.showToast(this, "结束日期须大于(或等于)开始日期");
//                    return;
//                }
//                if (!TextUtils.isEmpty(xjBh) && !TextUtils.isEmpty(jssj) && jumpJssTime(jssj, timeDate2)) {
//                    //任务中心过来--选择的时间必须比任务开始时间小于等于
//                    XToastUtil.showToast(this, "结束日期须小于(或等于)任务结束日期");
//                    return;
//                }
//                binding.tvJsTime.setText(timeDate);
//            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setSubmitColor(R.color.black).build();
//            pvTime.show();
        } else if (v.getId() == R.id.tv_info) {
            //活动内容
            Bundle bundle = new Bundle();
            bundle.putString("title", "活动内容");
//            bundle.putString("info", binding.tvInfo.getText().toString());
            bundle.putInt("tab", 2);
            bundle.putString("yy", "yy");//判断是否有语音
            String xzqhdm = UserInfoUtils.getUserInfo().getYhXzqhdm();
            if (!TextUtils.isEmpty(xzqhdm) && xzqhdm.startsWith("510121")) {//金堂限制为200-500
                bundle.putString("len", "200-500");
                bundle.putString("hint", "请输入活动内容，字数要求200-500");
            } else {
                bundle.putString("len", "500");
                bundle.putString("hint", "请输入活动内容");
            }
            XIntentUtil.redirectToNextActivity(this, InputInfoActivity.class, bundle);
        } else if (v.getId() == R.id.bt_sure) {
            if (ButtonUtils.isFastClick()) {
                postData();
            }
        } else if (v.getId() == R.id.tv_zxr_name) {
            //选择执行人
            if (ButtonUtils.isFastClick()) {
                Bundle bundle = new Bundle();
                bundle.putInt("from", 1);
                bundle.putString("param", "xjzxr");
                bundle.putString("title", "指定执行人");
                if (ziDianEvent != null) {
                    bundle.putSerializable("val", ziDianEvent);
                }
                XIntentUtil.redirectToNextActivity(this, MulChoiceList1Activity.class, bundle);
            }
        } else if (v.getId() == R.id.tv_hd_lb) {
            //活动类别
            FactoryUtils.getXjhdList(this, result -> {
                CodeNameDialog dianDialog = new CodeNameDialog(this, (ArrayList<CodeNameEntity>) result);
                dianDialog.setOnClickDataListener(() -> {
//                    binding.tvHdLb.setText(dianDialog.getCode().getName());
                    hdLbCode = dianDialog.getCode().getCode();
                });
                dianDialog.show();
            });
        } else if (v.getId() == R.id.tv_checker) {
            //检查人员
            Bundle bundle = new Bundle();
            bundle.putString("title", "检查人员");
            bundle.putString("hint", "请输入检查的全部人员");
            bundle.putString("info", binding.tvChecker.getText().toString());
            bundle.putInt("tab", 1);
            XIntentUtil.redirectToNextActivity(this, InputInfoActivity.class, bundle);
        } else if (v.getId() == R.id.tv_no_dx_remark) {
            //检查情况备注
            Bundle bundle = new Bundle();
            bundle.putString("title", "检查情况备注");
            bundle.putString("hint", "请对现场的情况进行备注，包括是否有处罚等");
            bundle.putString("info", binding.tvNoDxRemark.getText().toString());
            bundle.putInt("tab", 2);
            XIntentUtil.redirectToNextActivity(this, InputInfoActivity.class, bundle);
        }
    }

    private void postData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.XJHD_ADD);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");

        // 验证必填字段
        if (TextUtils.isEmpty(binding.etZhuTi.getText().toString())) {
            XToastUtil.showToast(this, "请输入实际剩余库存");
            return;
        }
        if (TextUtils.isEmpty(binding.tvChecker.getText().toString())) {
            XToastUtil.showToast(this, "请选择检查人员");
            return;
        }
        if (TextUtils.isEmpty(binding.tvNoDxRemark.getText().toString())) {
            XToastUtil.showToast(this, "请输入检查情况备注");
            return;
        }

        JSONObject objectMap = new JSONObject();
        try {
            // 添加基本字段
            objectMap.put("lx", lxCode);
            if (!TextUtils.isEmpty(fangShiCode)) {
                objectMap.put("fs", fangShiCode);
            }
            objectMap.put("sjLy", "3");
            objectMap.put("hdlb", hdLbCode);
            if (!TextUtils.isEmpty(xjBh)) {
                objectMap.put("rwbh", xjBh);
            }

            // 添加新增字段
            objectMap.put("systemStock", binding.tvJbr.getText().toString()); // 系统剩余库存
            objectMap.put("actualStock", binding.etZhuTi.getText().toString()); // 实际剩余库存
            objectMap.put("checker", binding.tvChecker.getText().toString()); // 检查人员
            objectMap.put("remark", binding.tvNoDxRemark.getText().toString()); // 检查情况备注

            // 添加图片数据
            JSONArray jsonArray = new JSONArray();
            if (imageAddXCZP != null && imageAddXCZP.getPaths() != null && imageAddXCZP.getPaths().size() > 0) {
                for (int i = 0; i < imageAddXCZP.getPaths().size(); i++) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("lj", imageAddXCZP.getPaths().get(i).getSavePath());
                    jsonObject.put("mc", imageAddXCZP.getPaths().get(i).getOriginName());
                    jsonObject.put("hz", StrUtils.getFileType(imageAddXCZP.getPaths().get(i).getOriginName()));
                    jsonObject.put("dx", imageAddXCZP.getPaths().get(i).getFileSize());
                    jsonArray.put(jsonObject);
                }
                objectMap.put("fjList", jsonArray);
            } else {
                XToastUtil.showToast(this, "请上传现场照片");
                return;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        binding.btSure.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJsonNoCan(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(KucunAddActivity.this, getString(R.string.add_sucess));
                    EventBus.getDefault().post(new XuanJiaoListEntity());
                    EventBus.getDefault().post(new PeoStatusEntity());
                    finish();
                } else {
                    XToastUtil.showToast(KucunAddActivity.this, xResultData.getErrorInfo());
                    binding.btSure.setEnabled(true);
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSure.setEnabled(true);
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 判断开始时间
     *
     * @param kssj
     * @param timeDate1
     * @return
     */
    private boolean jumpKssTime(String kssj, long timeDate1) {
        long rwKssj = XDateUtil.getDateByFormat(kssj, XConstantUtils.YMD).getTime();
        return timeDate1 < rwKssj;
    }

    /**
     * 判断开始时间
     *
     * @param jssj
     * @param timeDate2
     * @return
     */
    private boolean jumpJssTime(String jssj, long timeDate2) {
        long rwJssj = XDateUtil.getDateByFormat(jssj, XConstantUtils.YMD).getTime();
        return timeDate2 > rwJssj;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(InputEntity event) {
        if (event != null) {
            if (event.getTab() == 1) {
                binding.tvChecker.setText(event.getInfo());
            } else if (event.getTab() == 2) {
                binding.tvNoDxRemark.setText(event.getInfo());
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(ZiDianEventEntity event) {
        if (event != null) {
            if (event.getFrom() == 1) {
                //执行人
                ziDianEvent = event;
//                binding.tvZxrName.setText(StrUtils.listToZiDianText2(event.getZiDianEntityList()));
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.REQUEST_CAMERA:
                case PictureConfig.CHOOSE_REQUEST:
                    ArrayList<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                    if (null != selectList) {
                        for (LocalMedia media : selectList) {
                            if (!TextUtils.isEmpty(media.getCompressPath())) {
                                boolean isFinish = XFileUtil.bitmapToFilePathAlbum(KucunAddActivity.this, BitmapFactory.decodeFile(media.getCompressPath()), new File(media.getCompressPath()));
                                if (isFinish) {
                                    uploadFile(media.getCompressPath());
                                }
                            } else if (!TextUtils.isEmpty(media.getRealPath())) {
                                boolean isFinish = XFileUtil.bitmapToFilePathAlbum(KucunAddActivity.this, BitmapFactory.decodeFile(media.getRealPath()), new File(media.getRealPath()));
                                if (isFinish) {
                                    uploadFile(media.getRealPath());
                                }
                            } else if (!TextUtils.isEmpty(media.getPath())) {
                                boolean isFinish = XFileUtil.bitmapToFilePathAlbum(KucunAddActivity.this, BitmapFactory.decodeFile(media.getPath()), new File(media.getPath()));
                                if (isFinish) {
                                    uploadFile(media.getPath());
                                }
                            }
                        }
                    }
                    break;
                case 119:
                    //图片
                    cameraUtil.handleCameraResult();
                    break;
            }
        }
    }

    //上传
    private void uploadFile(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        imageAddXCZP.addImage(xResultData.getData().get(0));
                        imageAddXCZP.notifyData();
                    }
                } else {
                    XToastUtil.showToast(KucunAddActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (from == 2) {
            cameraUtil.createFileMuLu();
            cameraUtil.captureImage();
        }
    }

}
