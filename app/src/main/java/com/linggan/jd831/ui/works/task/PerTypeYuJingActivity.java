package com.linggan.jd831.ui.works.task;


import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.RadioGroup;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.YanQiPzTimeListAdapter;
import com.linggan.jd831.bean.CodeNameEntity;
import com.linggan.jd831.bean.PeoTypeYuJingEntity;
import com.linggan.jd831.bean.PeopleInfoEntity;
import com.linggan.jd831.bean.TaskSpListEntity;
import com.linggan.jd831.bean.TimeKsJsEntity;
import com.linggan.jd831.databinding.ActivityPerTypeYujingSureBinding;
import com.linggan.jd831.ui.user.edit.PeopleTypeInfoActivity;
import com.linggan.jd831.ui.user.edit.xian.PeopleTypeInfoNewActivity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.BaseStringDialog;

import org.greenrobot.eventbus.EventBus;
import org.xutils.http.RequestParams;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类  名：人员类型转换预警--排查详情
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/11/22  9:37
 * 版  权：凌感科技
 */
public class PerTypeYuJingActivity extends XBaseActivity<ActivityPerTypeYujingSureBinding> implements RadioGroup.OnCheckedChangeListener, View.OnClickListener {

    private String bh, yjztbh, xyrbh, jssj, isJieChu, gkxxbh;
    private int yjcNUm, xyJcNum;
    private CodeNameEntity ryyjztEntity;

    @Override
    protected ActivityPerTypeYujingSureBinding getViewBinding() {
        return ActivityPerTypeYujingSureBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        bh = getIntent().getStringExtra("bh");
    }

    @Override
    protected void initListener() {
        binding.radioGroup.setOnCheckedChangeListener(this);
        binding.btSubmit.setOnClickListener(this);
        binding.tvYlNum.setOnClickListener(this);
        binding.tvYqPl.setOnClickListener(this);
        binding.mRecycle.setLayoutManager(new LinearLayoutManager(this));
    }

    @Override
    protected void getData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + bh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PEOPLE_ZHUAN_YUJING + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<PeoTypeYuJingEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<PeoTypeYuJingEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    binding.peo.tvName.setText(xResultData.getData().getXm());
                    if (xResultData.getData().getRyyjzt() != null) {
                        ryyjztEntity = xResultData.getData().getRyyjzt();
                        binding.peo.tvPeoType.setText(xResultData.getData().getRyyjzt().getName());
                    }
                    binding.peo.tvIdCard.setText(StrUtils.hideIdCard(xResultData.getData().getZjhm()));
                    binding.peo.tvDate.setText(xResultData.getData().getKssj() + " 至 " + xResultData.getData().getJssj());
                    jssj = xResultData.getData().getJssj();
                    binding.peo.tvJcNum.setText(xResultData.getData().getXyjcNum() + "");
                    binding.peo.tvWcNum.setText(xResultData.getData().getSjjcNum() + "");
                    binding.peo.tvCaoZuo.setText(xResultData.getData().getJycz());
                    yjztbh = xResultData.getData().getYjztbh();
                    xyrbh = xResultData.getData().getXyrbh();
                    yjcNUm = xResultData.getData().getSjjcNum();
                    xyJcNum = xResultData.getData().getXyjcNum();
                    gkxxbh = xResultData.getData().getGkxxbh();
                    if (xResultData.getData().getSjjcNum() >= xResultData.getData().getXyjcNum()) {
                        //实际检测次数>=需要检测次数：期满解除
                        binding.radioGroup.check(R.id.rb_no);
                        binding.linYq.setVisibility(View.GONE);
                        isJieChu = "1";
                    } else {
                        binding.radioGroup.check(R.id.rb_yes);
                        binding.linYq.setVisibility(View.VISIBLE);
                        isJieChu = "2";
                    }
                } else {
                    XToastUtil.showToast(PerTypeYuJingActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_yl_num) {
            //请选择遗漏次数
            int ks = xyJcNum - yjcNUm;
            ArrayList<String> data = new ArrayList<>();
            for (int i = ks; i <= 22; i++) {
                data.add(i + "");
            }
            BaseStringDialog baseStringDialog = new BaseStringDialog(this, data);
            baseStringDialog.setOnClickDataListener(() -> {
                binding.tvYlNum.setText(baseStringDialog.getWork());
            });
            baseStringDialog.show();
        } else if (v.getId() == R.id.tv_yq_pl) {
            //延期检测频率
            BaseStringDialog baseStringDialog = new BaseStringDialog(this, StrUtils.getYanQiPinLvList());
            baseStringDialog.setOnClickDataListener(() -> {
                binding.tvYqPl.setText(baseStringDialog.getWork());
                binding.linYqPz.setVisibility(View.VISIBLE);
                getYanQiResult();
            });
            baseStringDialog.show();
        } else if (v.getId() == R.id.bt_submit) {
            //提交
            if (TextUtils.isEmpty(isJieChu)) {
                XToastUtil.showToast(this, "请选择延期提醒还是期满解除");
                return;
            }
            if (isJieChu.equals("2") && TextUtils.isEmpty(binding.tvYlNum.getText().toString())) {
                XToastUtil.showToast(this, "请选择遗漏次数");
                return;
            }
            if (isJieChu.equals("2") && TextUtils.isEmpty(binding.tvYqPl.getText().toString())) {
                XToastUtil.showToast(this, "请选择延期检测频率");
                return;
            }
            if (ButtonUtils.isFastClick()) {
                if (!TextUtils.isEmpty(isJieChu) && isJieChu.equals("1")) {
                    Bundle bundle = new Bundle();
                    PeopleInfoEntity peopleListEntity = new PeopleInfoEntity();
                    peopleListEntity.setGkxxbh(gkxxbh);
                    if (ryyjztEntity != null) {
                        peopleListEntity.setRyyjzt(ryyjztEntity);
                    }
                    bundle.putSerializable("info", peopleListEntity);
                    bundle.putString("id", xyrbh);
                    bundle.putString("rwbh", bh);
                    bundle.putString("yjztbh", yjztbh);
                    try {
                        Class<?> targetActivity = StrUtils.isHuNan() ?
                                PeopleTypeInfoActivity.class : PeopleTypeInfoNewActivity.class;
                        XIntentUtil.redirectToNextActivity(this, targetActivity, bundle);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
//                    XIntentUtil.redirectToNextActivity(PerTypeYuJingActivity.this, PeopleTypeInfoNewActivity.class, bundle);
                } else {
                    //延期--完成任务
                    postData();
                }
            }
        }
    }

    /**
     * 延期结果查询
     */
    private void getYanQiResult() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "ylNum=" + binding.tvYlNum.getText().toString() + "&plNum=" + binding.tvYqPl.getText().toString() + "&jssj=" + jssj);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PEOPLE_YUJING_RESULT + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<TimeKsJsEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<TimeKsJsEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    if (xResultData.getData().size() > 0) {
                        binding.tvKsTimes.setText(xResultData.getData().get(0).getBeginDate());
                        binding.tvJsTimes.setText(xResultData.getData().get(xResultData.getData().size() - 1).getEndDate());
                        binding.mRecycle.setAdapter(new YanQiPzTimeListAdapter(PerTypeYuJingActivity.this, xResultData.getData()));
                    }
                } else {
                    XToastUtil.showToast(PerTypeYuJingActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 延期保存接口
     * 延期：就直接完成任务
     * 期满：就去修改人员类型
     */
    private void postData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PEOPLE_YUJING_SAVE);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("xyrbh", xyrbh);
        objectMap.put("yjztbh", yjztbh);
        objectMap.put("kssj", binding.tvKsTimes.getText().toString());
        objectMap.put("jssj", binding.tvJsTimes.getText().toString());
        objectMap.put("bh", bh);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    //完成任务
                    finishTask();
                } else {
                    XToastUtil.showToast(PerTypeYuJingActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        switch (checkedId) {
            case R.id.rb_yes:
                //延期解除
                isJieChu = "2";
                binding.linYq.setVisibility(View.VISIBLE);
                break;
            case R.id.rb_no:
                //期满解除
                isJieChu = "1";
                binding.linYq.setVisibility(View.GONE);
                break;
        }
    }

    /**
     * 任务完成后，变更任务状态
     */
    private void finishTask() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + bh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PEOPLE_YUJING_UPDATE + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(PerTypeYuJingActivity.this, getString(R.string.sub_sucess));
                    EventBus.getDefault().post(new TaskSpListEntity());
                    finish();
                } else {
                    XToastUtil.showToast(PerTypeYuJingActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }
}
