package com.linggan.jd831.ui.works.qiye.v2;


import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.view.TimePickerView;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XDateUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.VideoTimeListAdapter;
import com.linggan.jd831.bean.Bfdzv0;
import com.linggan.jd831.bean.JkVideoTimeEntity;
import com.linggan.jd831.databinding.ActivityVideoInfoBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.XHttpUtils;

import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.text.SimpleDateFormat;
import java.util.List;

import xyz.doikki.videocontroller.StandardVideoController;
import xyz.doikki.videocontroller.component.CompleteView;
import xyz.doikki.videocontroller.component.GestureView;
import xyz.doikki.videocontroller.component.VodControlView;
import xyz.doikki.videoplayer.player.BaseVideoView;

/**
 * 类  名:监控详情--回放页面
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2024/3/25 10:00
 * 版  权：凌感科技
 */
public class JkHfVideoActivity extends XBaseActivity<ActivityVideoInfoBinding> {

    private String gbtdh, gbxlh, video, stream;
    private String startDate, endTime, yjsj;

    // 视频播放-控件
    private StandardVideoController mController;
    //
    private List<JkVideoTimeEntity> entityList;//播放时刻列表
    private int position = -1;//记录播放位置

    @Override
    protected ActivityVideoInfoBinding getViewBinding() {
        return ActivityVideoInfoBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        binding.recycle.setLayoutManager(new LinearLayoutManager(this));
        gbtdh = getIntent().getStringExtra("gbtdh");
        gbxlh = getIntent().getStringExtra("gbxlh");
        video = getIntent().getStringExtra("video");
        startDate = getIntent().getStringExtra("startDate");
        endTime = getIntent().getStringExtra("endTime");
        yjsj = getIntent().getStringExtra("yjsj");

        //播放器设置
        mController = new StandardVideoController(this);
        mController.addControlComponent(new CompleteView(this));
        mController.addControlComponent(new GestureView(this));
        mController.addControlComponent(new VodControlView(this));//点播控制条
        binding.mVideo.setVideoController(mController);

        if (TextUtils.isEmpty(yjsj)) {
            binding.tvDate.setText(XDateUtil.getCurrentDate(XDateUtil.dateFormatYMD));
        } else {
            binding.tvDate.setText(yjsj);//回放展示预警时间
            binding.ivDate.setVisibility(View.GONE);
        }
    }

    @Override
    protected void initListener() {
        binding.btDate.setOnClickListener(v -> {
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XDateUtil.dateFormatYMD).format(date);
                startDate = XDateUtil.getStringByFormat(date, XDateUtil.dateFormatYMD) + " 00:00:00";
                endTime = XDateUtil.getStringByFormat(date, XDateUtil.dateFormatYMD) + " 23:59:59";
                binding.tvDate.setText(timeDate);
                //请求地址
                getData();
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setSubmitColor(R.color.black).build();
            pvTime.show();
        });
        //回放时候监听播放完成自动播放下一个视频

        binding.mVideo.addOnStateChangeListener(new BaseVideoView.OnStateChangeListener() {
            @Override
            public void onPlayerStateChanged(int playerState) {
            }

            @Override
            public void onPlayStateChanged(int playState) {
                if (playState == BaseVideoView.STATE_BUFFERED) {//播放完成
                    if (entityList != null && entityList.size() > 0) {
                        stopPlay(stream, (code, id) -> {
                            if (position < (entityList.size() - 1)) {
                                position++;
                                getVideoUrl(entityList.get(position).getStartTime(), entityList.get(position).getEndTime(), "1");
                            } else if (position == (entityList.size() - 1)) {
                                getVideoUrl(entityList.get(position).getStartTime(), entityList.get(position).getEndTime(), "1");
                            }
                        });
                    }
                }
            }
        });
    }

    @Override
    protected void getData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.YZD_JK_INFO);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("gbxlh", gbxlh);
            objectMap.put("gbtdh", gbtdh);
            if (TextUtils.isEmpty(video)) {
                //回放过来
                objectMap.put("startTime", startDate + ":00");
                objectMap.put("endTime", endTime + ":59");
            } else {
                objectMap.put("startTime", startDate);
                objectMap.put("endTime", endTime);
            }
        } catch (JSONException e) {
        }
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJsonNoCan(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<JkVideoTimeEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<JkVideoTimeEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        binding.ivNoData.setVisibility(View.GONE);
                        binding.recycle.setVisibility(View.VISIBLE);
                        entityList = xResultData.getData();
                        VideoTimeListAdapter videoTimeListAdapter = new VideoTimeListAdapter(JkHfVideoActivity.this, xResultData.getData());
                        binding.recycle.setAdapter(videoTimeListAdapter);
                        videoTimeListAdapter.setClickListener((videoTimeEntity, time, position) -> {
                            if (TextUtils.isEmpty(stream)) {
                                getVideoUrl(videoTimeEntity.getStartTime(), videoTimeEntity.getEndTime(), "1");
                            } else {
                                stopPlay(stream, (code, id) -> {
                                    getVideoUrl(videoTimeEntity.getStartTime(), videoTimeEntity.getEndTime(), "1");
                                });
                            }
                        });
                        if (xResultData.getData() != null && xResultData.getData().size() > 0 && TextUtils.isEmpty(video)) {
                            //表示回放 默认获取第一个视频
                            position = 0;
                            getVideoUrl(xResultData.getData().get(0).getStartTime(), xResultData.getData().get(0).getEndTime(), "");
                        }
                    } else {
                        if (TextUtils.isEmpty(video)) {
                            XToastUtil.showToast(JkHfVideoActivity.this, "录像存储时长有限，已被新录像覆盖，不支持查看");
                            finish();
                        } else {
                            binding.ivNoData.setVisibility(View.VISIBLE);
                            binding.recycle.setVisibility(View.GONE);
                        }
                    }
                } else {
                    if (TextUtils.isEmpty(video)) {
                        XToastUtil.showToast(JkHfVideoActivity.this, "录像存储时长有限，已被新录像覆盖，不支持查看");
                        finish();
                    } else {
                        XToastUtil.showToast(JkHfVideoActivity.this, xResultData.getErrorInfo());
                        binding.ivNoData.setVisibility(View.VISIBLE);
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 获取视频地址
     */
    private void getVideoUrl(String startDate, String endTime, String isHf) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.YZD_JK_SK_VIDEO);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("gbxlh", gbxlh);
            objectMap.put("gbtdh", gbtdh);
            objectMap.put("startTime", startDate);
            objectMap.put("endTime", endTime);
        } catch (JSONException e) {
        }
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJsonNoCan(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<Bfdzv0> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<Bfdzv0>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    stream = xResultData.getData().getStream();
                    if (!TextUtils.isEmpty(isHf)) {
                        binding.mVideo.release();
                    }
                    binding.mVideo.setUrl(xResultData.getData().getBfdz());
                    binding.mVideo.start();
                } else {
                    XToastUtil.showToast(JkHfVideoActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 通知播放
     */
    private void stopPlay(String stream, DialogUtils.OnResult onResult) {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "gbxlh=" + gbxlh + "&gbtdh=" + gbtdh + "&stream=" + stream);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.YZD_JK_VIDEO_TZBF + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    onResult.onSuccess("", "");
                } else {
                    XToastUtil.showToast(JkHfVideoActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void onPause() {
        super.onPause();
        binding.mVideo.pause();
    }

    @Override
    protected void onResume() {
        super.onResume();
        binding.mVideo.resume();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding.mVideo.release();
    }

    @Override
    public void onBackPressed() {
        if (!binding.mVideo.onBackPressed()) {
            super.onBackPressed();
        }
    }
}
