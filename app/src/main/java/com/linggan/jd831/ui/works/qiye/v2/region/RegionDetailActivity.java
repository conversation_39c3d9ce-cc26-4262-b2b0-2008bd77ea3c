package com.linggan.jd831.ui.works.qiye.v2.region;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.utils.XIntentUtil;
import com.linggan.jd831.R;
import com.linggan.jd831.databinding.ActivityRegionDetailBinding;
import com.linggan.jd831.ui.works.qiye.v2.lab.entity.AreaInfo;
import com.linggan.jd831.ui.works.qiye.v2.lab.entity.ListSysQyEntity;
import com.linggan.jd831.utils.StrUtils;

import java.util.List;

/**
 * 类  名： 新增排查记录-详情
 * 描   述：
 * 作  者：zxb
 * 时  间：
 * 版  权：凌感科技
 */
public class RegionDetailActivity extends XBaseActivity<ActivityRegionDetailBinding> {

    private String id;
    private List<AreaInfo> areaList; // 区域信息列表
    private ListSysQyEntity data; // 区域 ID

    @Override
    protected ActivityRegionDetailBinding getViewBinding() {
        return ActivityRegionDetailBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        data = (ListSysQyEntity) getIntent().getSerializableExtra("data");
        setTitle("区域管理");
        binding.base.tvRight.setText(getString(R.string.edit));
        binding.base.tvRight.setTextColor(getResources().getColor(R.color.color_main));
        id = getIntent().getStringExtra("id");

    }

    @Override
    protected void initListener() {
        binding.base.tvRight.setOnClickListener(v -> {
            Bundle bundle = new Bundle();
            bundle.putSerializable("data", data);
            bundle.putString("from", "edit");
            XIntentUtil.redirectToNextActivity(this, RegionManagementAddActivity.class, bundle);
        });
    }

    @Override
    protected void getData() {
        if (data != null) {
            binding.tvWarehouseName.setText(StrUtils.getDev(data.getMc(), "-"));
            // tv_camera_access
            if (data != null && !TextUtils.isEmpty(data.getSfJrSxj())) {
                if (data.getSfJrSxj().equals("1")) {
                    binding.llDetailInfo.setVisibility(View.VISIBLE);
                    binding.tvCameraAccess.setText("是");
                } else {
                    binding.llDetailInfo.setVisibility(View.GONE);
                    binding.tvCameraAccess.setText("否");
                }
            } else {
                binding.llDetailInfo.setVisibility(View.GONE);
                binding.tvCameraAccess.setText("-");
            }

            if (data.getSfJrSxj().equals("1")) {
//                判断sxtList是否为空列表
                if (data.getSxtList() != null && data.getSxtList().size() > 0) {
                    binding.tvCameraType.setText(data.getSxtList().get(0).getSxjLx());
                    // 设备序列号tv_device_sn -> sbxlh
                    binding.tvDeviceSn.setText(data.getSxtList().get(0).getSbxlh());
                    // tv_view_lib_sn	视图库序列号	stkxlh
                    binding.tvViewLibSn.setText(data.getSxtList().get(0).getStkxlh());
                    //  gbxlh 国标序列号 tv_national_sn
                    binding.tvNationalSn.setText(data.getSxtList().get(0).getGbxlh());
                    //  gbtdh 国标通道号 tv_national_channel
                    binding.tvNationalChannel.setText(data.getSxtList().get(0).getGbtdh());
                }
            }
            if (data.getZt().equals("0")) {
                binding.tvRegionStatus.setText("正常");
            } else if (data.getZt().equals("1")) {
                binding.tvRegionStatus.setText("停用");
            } else {
                binding.tvRegionStatus.setText("-");
            }
            String starTime = StrUtils.getDev(data.getKqKssj(), "-");
            String endTime = StrUtils.getDev(data.getKqJssj(), "-");
            if (starTime.equals("-") && endTime.equals("-")) {
                binding.tvOperationTime.setText("-");
            } else {
                binding.tvOperationTime.setText(starTime + "-" + endTime);
            }

        }
    }
}
