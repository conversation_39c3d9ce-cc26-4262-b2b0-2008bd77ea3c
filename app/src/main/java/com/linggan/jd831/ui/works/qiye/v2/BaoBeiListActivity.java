package com.linggan.jd831.ui.works.qiye.v2;

import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData1;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.BaoBeiListHolder;
import com.linggan.jd831.bean.BaiBeiListEntity;
import com.linggan.jd831.bean.ZiDianEntity;
import com.linggan.jd831.databinding.ActivityBaoBeiListBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.BaseZiDianDialog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类  名：报备列表
 * 作  者：zxb
 * 说  明：
 * 时  间：2024/3/25 10:00
 */
public class BaoBeiListActivity extends XBaseActivity<ActivityBaoBeiListBinding> implements XRefreshLayout.PullLoadMoreListener {

    private String title, zt, kssj, jssj;
    private int page = 1, totalPage = 0;
    private boolean isShow = true;
    private List<ZiDianEntity> arrayList;

    @Override
    protected ActivityBaoBeiListBinding getViewBinding() {
        return ActivityBaoBeiListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        title = getIntent().getStringExtra("title");
        binding.recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
        binding.recycler.getAdapter().bindHolder(new BaoBeiListHolder(title));
        binding.recycler.setOnPullLoadMoreListener(this);
        if (!TextUtils.isEmpty(title)) {//确认列表
            setTitle(title);
            binding.btBb.setText("确认状态");
        } else {
            //报备列表
            binding.base.tvRight.setText(getString(R.string.add));
            binding.base.tvRight.setTextColor(getResources().getColor(R.color.color_main));
        }
        EventBus.getDefault().register(this);
    }

    @Override
    protected void initListener() {
        binding.base.btRight.setOnClickListener(v -> {
            DialogUtils.showBaoBeiZysx(this, "", (code, id) -> {
                XIntentUtil.redirectToNextActivity(this, BaoBeiCrkAddActivity.class);
            });
        });
        binding.tvTime.setOnClickListener(v -> {
            DialogUtils.showBaoBeiTime(this, kssj, jssj, (ksTime, endTime) -> {
                kssj = ksTime;
                jssj = endTime;
                if (!TextUtils.isEmpty(ksTime)) {
                    binding.tvTime.setText(ksTime + "至" + endTime);
                } else {
                    binding.tvTime.setText("");
                }
                page = 1;
                isShow = true;
                getData();
            });
        });
        binding.btBb.setOnClickListener(v -> {
            //报备状态
            if (arrayList != null && arrayList.size() > 0) {
                BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, (ArrayList<ZiDianEntity>) arrayList);
                dianDialog.setOnClickDataListener(() -> {
                    binding.btBb.setText(dianDialog.getData().getMc());
                    zt = dianDialog.getData().getDm();
                    page = 1;
                    isShow = true;
                    getData();
                });
                dianDialog.show();
            } else {
                FactoryUtils.getBaseDataType(this, "crkbb_zt", result -> {
                    ZiDianEntity ziDianEntity = new ZiDianEntity();
                    ziDianEntity.setMc("全部");
                    result.add(0, ziDianEntity);
                    arrayList = result;
                    BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, (ArrayList<ZiDianEntity>) result);
                    dianDialog.setOnClickDataListener(() -> {
                        binding.btBb.setText(dianDialog.getData().getMc());
                        zt = dianDialog.getData().getDm();
                        page = 1;
                        isShow = true;
                        getData();
                    });
                    dianDialog.show();
                });
            }
        });
    }

    @Override
    protected void getData() {
        String url = "";
        if (!TextUtils.isEmpty(title)) {//确认列表
            url = ApiUrlsUtils.YZD_BB_SURE_LIST;
        } else {
            url = ApiUrlsUtils.YZD_BB_LIST;
        }
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + url);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("page", page);
        objectMap.put("rows", XConstantUtils.PAGE_SIZE);
        if (!TextUtils.isEmpty(kssj)) {
            objectMap.put("startTime", kssj);
        }
        if (!TextUtils.isEmpty(jssj)) {
            objectMap.put("endTime", jssj);
        }
        if (!TextUtils.isEmpty(zt)) {
            objectMap.put("zt", zt);
        }
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), isShow, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<XResultPageData1<BaiBeiListEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XResultPageData1<BaiBeiListEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        totalPage = xResultData.getData().getTotalPage();
                        if (xResultData.getData().getRecords() != null && xResultData.getData().getRecords().size() > 0) {
                            binding.tvNoData.setVisibility(View.GONE);
                            if (page == 1) {
                                binding.recycler.getAdapter().setData(0, xResultData.getData().getRecords());
                            } else {
                                binding.recycler.getAdapter().addDataAll(0, xResultData.getData().getRecords());
                            }
                        } else {
                            if (page == 1) {
                                binding.tvNoData.setVisibility(View.VISIBLE);
                                binding.recycler.getAdapter().setData(0, new ArrayList());
                            }
                        }
                    } else {
                        if (page == 1) {
                            binding.tvNoData.setVisibility(View.VISIBLE);
                            binding.recycler.getAdapter().setData(0, new ArrayList());
                        }
                    }
                } else {
                    if (page == 1) {
                        binding.tvNoData.setVisibility(View.VISIBLE);
                        binding.recycler.getAdapter().setData(0, new ArrayList());
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                binding.recycler.setPullLoadMoreCompleted();
            }
        });
    }

    @Override
    public void onRefresh() {
        page = 1;
        isShow = false;
        getData();
    }

    @Override
    public boolean onLoadMore() {
        if (page < totalPage) {
            page++;
            isShow = false;
            getData();
            return true;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaiBeiListEntity event) {
        if (event != null) {
            page = 1;
            isShow = false;
            getData();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
