package com.linggan.jd831.ui.works.ywjc.baosun.adapter;

import android.content.Intent;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.ui.common.PhotoActivity;
import com.linggan.jd831.ui.works.ywjc.baosun.BaosunRecordBean;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XImageUtils;
import com.makeramen.roundedimageview.RoundedImageView;

/**
 * 类  名：报损记录适配器
 * 说  明：显示报损记录列表
 */
public class BaoSunRecordAdapter extends IViewHolder {

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_baosun_record;
    }

    public class ViewHolder extends XViewHolder<BaosunRecordBean> {
        private RoundedImageView ivAvatar;
        private TextView tvTime;
        private TextView tvAmount;
        private TextView tvPoster;
        private TextView tvStorageAmount;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            ivAvatar = view.findViewById(R.id.iv_avatar);
            tvTime = view.findViewById(R.id.tv_time);
            tvAmount = view.findViewById(R.id.tv_amount);
            tvPoster = view.findViewById(R.id.tv_poster);
            tvStorageAmount = view.findViewById(R.id.tv_storage_amount);
        }

        @Override
        protected void onBindData(final BaosunRecordBean itemData) {
            if (itemData != null) {
                // 设置报损时间
                tvTime.setText(StrUtils.getDev(itemData.getBbsj(), "-"));
                
                // 设置报损量
                if (itemData.getSl() != null) {
                    tvAmount.setText(itemData.getSl() + "ml");
                } else {
                    tvAmount.setText("-");
                }
                
                // 设置经办人
                tvPoster.setText(StrUtils.getDev(itemData.getJdbXm(), "-"));
                
                // 设置报损原因
                tvStorageAmount.setText(StrUtils.getDev(itemData.getBsyy(), "-"));
                if (itemData.getZp() != null && !itemData.getZp().isEmpty()) {
                    XImageUtils.loadNoCache1(mContext, itemData.getZp(), ivAvatar);
                    ivAvatar.setOnClickListener(v->{
                        mContext.startActivity(new Intent(mContext, PhotoActivity.class).putExtra("path", itemData.getZp()));
                    });
                }
                // 设置默认头像
//                XImageUtils.load(itemView.getContext(), R.mipmap.ic_def_head, ivAvatar);
            }
        }
    }
}