package com.linggan.jd831.ui.works.transport.event;

import com.linggan.jd831.ui.works.transport.model.RouteCity;

import java.util.List;

public class RouteSelectedEvent {
    private String routeString;
    private List<RouteCity> routeCities;
    private List<String> deletedBhs;

    public RouteSelectedEvent(String routeString, List<RouteCity> routeCities, List<String> deletedBhs) {
        this.routeString = routeString;
        this.routeCities = routeCities;
        this.deletedBhs = deletedBhs;
    }

    public String getRouteString() {
        return routeString;
    }

    public List<RouteCity> getRouteCities() {
        return routeCities;
    }

    public List<String> getDeletedBhs() {
        return deletedBhs;
    }
} 