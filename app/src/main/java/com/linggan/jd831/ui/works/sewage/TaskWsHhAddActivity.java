package com.linggan.jd831.ui.works.sewage;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.linggan.jd831.utils.XHttpUtils;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.WuShuiTimeListAdapter;
import com.linggan.jd831.bean.TaskSpListEntity;
import com.linggan.jd831.bean.WsRwInfoEntity;
import com.linggan.jd831.databinding.ActivityWuShuiShjcAddBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

/**
 * 类  名：新增污水检测--混合样本
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class TaskWsHhAddActivity extends XBaseActivity<ActivityWuShuiShjcAddBinding> implements View.OnClickListener {

    private WuShuiTimeListAdapter wuShuiTimeListAdapter;//采样时刻
    private String taskBh, xzqhdm, cydBh;
    private WsRwInfoEntity wsRwInfoEntity;
    private int finishCy = 0;
    private boolean isShow = true;

    @Override
    protected ActivityWuShuiShjcAddBinding getViewBinding() {
        return ActivityWuShuiShjcAddBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        taskBh = getIntent().getStringExtra("bh");
        xzqhdm = getIntent().getStringExtra("xzqhdm");//这里是选择点位来的
        EventBus.getDefault().register(this);
        binding.mRecycle.setLayoutManager(new LinearLayoutManager(this));
    }

    @Override
    protected void initListener() {
        binding.btHyCj.setOnClickListener(this);
        //定位过来
        if (!TextUtils.isEmpty(xzqhdm)) {
            binding.top.tvDwMc.setText(getIntent().getStringExtra("mc"));
            binding.top.tvDwDz.setText(getIntent().getStringExtra("dz"));
        }
        //机构过来-隐藏送样信息
        String fromJg = getIntent().getStringExtra("from");
        if (!TextUtils.isEmpty(fromJg)) {
            binding.linSy.setVisibility(View.GONE);
        }
    }

    @Override
    protected void getData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + taskBh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.SEWAGE_WS_GR_TASK_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), isShow, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<WsRwInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<WsRwInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    wsRwInfoEntity = xResultData.getData();
                    if (TextUtils.isEmpty(xzqhdm)) {
                        if (!TextUtils.isEmpty(xResultData.getData().getCydBh())) {
                            binding.top.tvDwMc.setText(xResultData.getData().getCydMc());
                            binding.top.tvDwDz.setText(xResultData.getData().getCydDz());
                        } else {
                            binding.top.tvDwMc.setText(xResultData.getData().getCsMc());
                            binding.top.tvDwDz.setText(xResultData.getData().getCsDz());
                        }
                    }
//                    if (!TextUtils.isEmpty(xResultData.getData().getCyRqJssj())) {
//                        if (xResultData.getData().getWsCyLx() != null && xResultData.getData().getWsCyLx().getCode().equals("2")) {
//                            binding.top.tvQyRq.setText(xResultData.getData().getCyRqKssj());
//                        } else {
                    binding.top.tvQyRq.setText(xResultData.getData().getCyRqKssj() + " 至 " + xResultData.getData().getCyRqJssj());
//                        }
//                    } else {
//                        binding.top.tvQyRq.setText(xResultData.getData().getCyRqKssj());
//                    }
                    //采样类型
                    if (xResultData.getData().getWsCyLx() != null) {
                        if (xResultData.getData().getWsCyLx().getCode().equals("1")) {
                            //1瞬时采样-1.瞬时 时段取第一项日期截取 时分秒显示   可以多天
                            if (xResultData.getData().getQysjd() != null && xResultData.getData().getQysjd().size() > 0) {
                                String[] ks = xResultData.getData().getQysjd().get(0).getKsSj().split(" ");
                                String[] js = xResultData.getData().getQysjd().get(0).getJsSj().split(" ");
                                binding.top.tvQySd.setText("" + ks[1] + " 至 " + js[1]);
                            }
                        } else if (xResultData.getData().getWsCyLx().getCode().equals("2")) {
                            //2混合采样-2.混合 时段取每一项 日期截取 时分秒显示 必定在同一天
                            if (xResultData.getData().getQysjd() != null && xResultData.getData().getQysjd().size() > 0) {
                                StringBuffer stringBuffer = new StringBuffer();
                                for (int k = 0; k < xResultData.getData().getQysjd().size(); k++) {
                                    String[] ks = xResultData.getData().getQysjd().get(k).getKsSj().split(" ");
                                    String[] js = xResultData.getData().getQysjd().get(k).getJsSj().split(" ");
                                    stringBuffer.append(ks[1] + " 至 " + js[1] + "\n");
                                }
                                if (stringBuffer.length() > 0) {
                                    binding.top.tvQySd.setText("" + stringBuffer.substring(0, stringBuffer.length() - 1));
                                } else {
                                    binding.top.tvQySd.setText("-");
                                }
                            }
                        } else if (xResultData.getData().getWsCyLx().getCode().equals("3")) {
                            //3连续采样-3连续 无时段 显示“-” 必定在同一天
                            binding.top.tvQySd.setText("-");
                        } else {
                            binding.top.tvQySd.setText("-");
                        }
                    }
                    binding.tvSyrName.setText(xResultData.getData().getSyXm());
                    binding.tvSyrDh.setText(xResultData.getData().getSyDh());
                    binding.tvSyDzz.setText(xResultData.getData().getSyDz());
                    //时间段
                    wuShuiTimeListAdapter = new WuShuiTimeListAdapter(TaskWsHhAddActivity.this, xResultData.getData().getQysjd(),
                            xResultData.getData().getCyRqKssj(), xResultData.getData().getCyRqJssj());
                    binding.mRecycle.setAdapter(wuShuiTimeListAdapter);
                    int num = 0;
                    if (xResultData.getData().getQysjd() != null && xResultData.getData().getQysjd().size() > 0) {
                        for (int d = 0; d < xResultData.getData().getQysjd().size(); d++) {
                            if (xResultData.getData().getQysjd().get(d).getSfcy() == 1) {
                                num++;
                            }
                        }
                        if (num == xResultData.getData().getQysjd().size()) {
                            finishCy = 1;
                        } else {
                            finishCy = 0;
                        }
                    }
                    wuShuiTimeListAdapter.setOnItemClickListener((hxpGgBean, position) -> {
                        Bundle bundle = new Bundle();
                        bundle.putString("title", getString(R.string.wu_shui_cy_add));
                        bundle.putString("fromT", "cy");
                        bundle.putSerializable("sjInfo", hxpGgBean);
                        bundle.putSerializable("jcInfo", wsRwInfoEntity);
                        bundle.putSerializable("bh", taskBh);
                        bundle.putSerializable("xzqhdm", xzqhdm);
                        bundle.putSerializable("mc", getIntent().getStringExtra("mc"));
                        bundle.putSerializable("xzbh", !TextUtils.isEmpty(cydBh) ? cydBh : getIntent().getStringExtra("xzbh"));
                        bundle.putString("from", getIntent().getStringExtra("from"));
                        XIntentUtil.redirectToNextActivity(TaskWsHhAddActivity.this, TaskWsHhTwoAddActivity.class, bundle);
                    });
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.bt_hy_cj) {
            //混样采集
            if (finishCy == 1) {
                Bundle bundle = new Bundle();
                bundle.putString("title", getString(R.string.wu_shui_hy_add));
                bundle.putString("fromT", "hy");
                bundle.putSerializable("jcInfo", wsRwInfoEntity);
                bundle.putSerializable("bh", taskBh);
                bundle.putSerializable("xzqhdm", xzqhdm);
                bundle.putSerializable("mc", getIntent().getStringExtra("mc"));
                bundle.putSerializable("xzbh", !TextUtils.isEmpty(cydBh) ? cydBh : getIntent().getStringExtra("xzbh"));
                bundle.putString("from", getIntent().getStringExtra("from"));
                XIntentUtil.redirectToNextActivity(this, TaskWsHhTwoAddActivity.class, bundle);
            } else {
                XToastUtil.showToast(this, "请完成所有时刻采样后再进行混合采样");
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(WsRwInfoEntity event) {
        if (event != null) {
            isShow = false;
            getData();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(TaskSpListEntity event) {
        if (event != null) {
            finish();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}