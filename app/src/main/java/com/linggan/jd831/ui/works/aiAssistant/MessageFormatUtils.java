package com.linggan.jd831.ui.works.aiAssistant;

import android.util.Log;

/**
 * 消息格式化工具类
 * 提供处理和格式化AI回复消息的方法
 */
public class MessageFormatUtils {
    private static final String TAG = "MessageFormatUtils";

    /**
     * 处理特殊格式的文本内容
     *
     * @param content 原始内容
     * @return 处理后的内容
     */
    public static String processSpecialFormat(String content) {
        if (content == null) return "";
        // 优化运行状态显示
        content = content.replaceAll("is running\\.{3}🕞", "处理中...");

        // 使用正则表达式替换 ##数字$$ 格式的内容
        content = content.replaceAll("(?:[\\(（]|^|(?<=\\s))(?:(?:##\\d+\\$\\$)(?:\\s*(?:##\\d+\\$\\$))*)(?:[\\)）]|$|(?=\\s))", "");
        // 移除旧的重复正则表达式
        content = content.replaceAll("##\\d+\\$\\$", "");

        // 替换 ID数字$$
        content = content.replaceAll("ID\\d+\\$\\$", "");

        // 平安江西 -> 禁毒行动
        content = content.replaceAll("平安江西", "禁毒行动");
        // 明月E站 -> 无毒家园
        content = content.replaceAll("明月E站", "无毒家园");

        content = content.replaceAll("null", "无");

        // </think><think> => </think>
        content = content.replaceAll("</think><think>[\\s\\S]*", "</think>");

        content = content.replaceAll("(?<!\\|)\\n\\s*---\\s*\n", "\n\n");

        Log.d(TAG, "尝试使用更宽松的匹配模式");
        String loosePattern = "(\\d+\\.\\s*)\\*\\*([^*]+)\\*\\*";
        content = content.replaceAll(loosePattern, "\n\n$1 **$2**\n");

        Log.d(TAG, "使用宽松模式后的文本: " + content);
        return content;
    }
} 