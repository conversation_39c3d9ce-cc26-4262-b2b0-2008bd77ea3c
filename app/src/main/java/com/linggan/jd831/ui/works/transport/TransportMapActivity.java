package com.linggan.jd831.ui.works.transport;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.ViewFlipper;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;

import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.maps.AMap;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.LocationSource;
import com.amap.api.maps.UiSettings;
import com.amap.api.maps.model.CameraPosition;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.Marker;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.InputEntity;
import com.linggan.jd831.databinding.ActivityTransportMapBinding;
import com.linggan.jd831.service.LocationService;
import com.linggan.jd831.ui.permission.LocationPermissionActivity;
import com.linggan.jd831.ui.works.transport.model.RouteCity;
import com.linggan.jd831.ui.works.transport.model.TransportDetail;
import com.linggan.jd831.ui.works.transport.model.WarningItem;
import com.linggan.jd831.utils.LocationServiceUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.SlideToConfirmView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class TransportMapActivity extends XBaseActivity<ActivityTransportMapBinding> {

    private static final int STATUS_NOT_STARTED = 0; // 未发起
    private static final int STATUS_IN_TRANSIT = 1;  // 运输中
    private static final int STATUS_TEMP_STOP = 2;   // 临停中

    private static final String YSZT_IN_TRANSIT = "1";  // 运输状态-运输中
    private static final String FCZT_RUNNING = "1";     // 发车状态-运输中
    private static final String FCZT_TEMP_STOP = "2";   // 发车状态-临停中

    private int currentStatus = STATUS_NOT_STARTED;  // 默认为未发起状态

    private String transportId;
    private TransportDetail transportDetail;
    private AMap aMap;
    private AMapLocationClient locationClient;
    private Marker locationMarker;
    private boolean isUserScrolling = false;

    private static final int REQUEST_SERVED = 104;
    private static final int REQUEST_PARKING = 105;

    private ActivityResultLauncher<Intent> parkingLauncher;

    private AMap.OnCameraChangeListener cameraChangeListener = new AMap.OnCameraChangeListener() {
        private long lastUpdateTime = 0;
        private static final long UPDATE_INTERVAL = 1000; // 100ms的最小更新间隔

        @Override
        public void onCameraChange(CameraPosition cameraPosition) {
            isUserScrolling = true;
            // 添加节流，减少更新频率
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastUpdateTime < UPDATE_INTERVAL) {
                return;
            }
            lastUpdateTime = currentTime;

            if (locationClient != null && locationClient.getLastKnownLocation() != null) {
                LatLng location = new LatLng(
                        locationClient.getLastKnownLocation().getLatitude(),
                        locationClient.getLastKnownLocation().getLongitude()
                );
                updateMarkerPosition(location);  // 只更新位置，不移动地图
            }
        }

        @Override
        public void onCameraChangeFinish(CameraPosition cameraPosition) {
            if (isUserScrolling) {
                isUserScrolling = false;
                // 地图停止时，延迟500ms后回到当前位置
                binding.mapView.postDelayed(() -> {
                    if (locationClient != null && locationClient.getLastKnownLocation() != null) {
                        LatLng location = new LatLng(
                                locationClient.getLastKnownLocation().getLatitude(),
                                locationClient.getLastKnownLocation().getLongitude()
                        );
                        // 使用动画移动回当前位置
                        aMap.animateCamera(CameraUpdateFactory.newLatLngZoom(location, 14), 1000, null);
                    }
                }, 500);
            }
        }
    };

    private ScheduledExecutorService scheduledExecutorService;
    private static final int REFRESH_INTERVAL = 10000; // 10秒刷新一次

    private volatile boolean isActivityActive = true;

    public static void start(Context context, String transportId, String status) {
        Intent intent = new Intent(context, TransportMapActivity.class);
//        intent.putExtra("transport_detail", detail);  // 确保 TransportDetail 实现了 Parcelable 或 Serializable
        intent.putExtra("transport_id", transportId);
        intent.putExtra("status", status);
        context.startActivity(intent);
    }

    @Override
    protected ActivityTransportMapBinding getViewBinding() {
        return ActivityTransportMapBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding.mapView.onCreate(savedInstanceState);
        setTitle("运输管理");
        EventBus.getDefault().register(this);

        parkingLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == REQUEST_PARKING) {
                        // 临时停车记录保存成功后，重新获取运输详情
                        getTransportGuides("1", () -> {
                            updateStatusFromTransportDetail();
                        });
                    }
                }
        );

        // 初始化定时任务执行器
        scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();

        // 启动定时刷新任务
        startRefreshTask();
    }

    private void startRefreshTask() {
        scheduledExecutorService.scheduleWithFixedDelay(() -> {
            // 只在 Activity 活跃时执行刷新
            if (isActivityActive) {
                runOnUiThread(() -> {
                    getData();
                });
            }
        }, 0, REFRESH_INTERVAL, TimeUnit.MILLISECONDS);
    }

    private void initMap() {
        if (aMap == null) {
            aMap = binding.mapView.getMap();
        }

        // 设置地图UI控件
        UiSettings uiSettings = aMap.getUiSettings();
        uiSettings.setZoomControlsEnabled(false); // 隐藏缩放按钮
        uiSettings.setMyLocationButtonEnabled(true); // 显示定位按钮

        // 启用定位层
        aMap.setMyLocationEnabled(true);
        // 设置定位按钮点击事件
        aMap.setLocationSource(new LocationSource() {
            @Override
            public void activate(OnLocationChangedListener onLocationChangedListener) {
                // 当定位按钮被点击时触发
                if (locationClient != null && locationClient.getLastKnownLocation() != null) {
                    isUserScrolling = false; // 重置用户滑动状态
                    LatLng location = new LatLng(
                            locationClient.getLastKnownLocation().getLatitude(),
                            locationClient.getLastKnownLocation().getLongitude()
                    );

                    // 使用动画移动到当前位置
                    aMap.animateCamera(CameraUpdateFactory.newLatLngZoom(location, 14), 1000, new AMap.CancelableCallback() {
                        @Override
                        public void onFinish() {
                            updateMarkerPosition(location);
                        }

                        @Override
                        public void onCancel() {
                        }
                    });
                }
            }

            @Override
            public void deactivate() {
                // 停止定位
            }
        });

        // 设置地图样式
        aMap.setMapType(AMap.MAP_TYPE_NORMAL);

        // 初始化定位
        initLocation();
    }

    private void getTransportGuides(String type, Runnable onComplete) {
        if (Objects.equals(type, "2")) {
            onComplete.run();
            return;
        }
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + "/precursor/ysgl/sjzys/xq");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(TransportMapActivity.this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<TransportDetail> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<TransportDetail>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    transportDetail = xResultData.getData();
                    transportId = String.valueOf(transportDetail.getBh());
                    // location_upload_interval

                    startLocationService(transportId);
                    getTransportGuides(() -> {
                    });

                } else {
                    transportDetail = null;
//                    XToastUtil.showToast(TransportMapActivity.this, "获取运输状态失败");
                }
                onComplete.run();
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                transportDetail = null;
//                XToastUtil.showToast(TransportMapActivity.this, "获取运输指南失败");
                onComplete.run();
            }

            @Override
            public void onFinished() {
                onComplete.run();
            }
        });
    }

    // 获取情况说明条数
    private void getTransportGuides(Runnable onComplete) {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "ysglbh=" + transportDetail.getBh() + "&zt=0");
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + "/precursor/ysgl/qksm/lb" + "?query=" + jiaMiString);
//        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(TransportMapActivity.this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<WarningItem>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<WarningItem>>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    binding.tvSituationCount.setText(String.valueOf(xResultData.getData().size()));
                } else {
//                    XToastUtil.showToast(TransportMapActivity.this, "获取运输状态失败");
                }
                onComplete.run();
            }

            @Override
            public void onFailed(int code, String failedMsg) {
//                XToastUtil.showToast(TransportMapActivity.this, "获取运输指南失败");
                onComplete.run();
            }

            @Override
            public void onFinished() {
                onComplete.run();
            }
        });
    }

    private void initLocation() {
        try {
            locationClient = new AMapLocationClient(getApplicationContext());
            AMapLocationClientOption option = new AMapLocationClientOption();
            option.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
            option.setInterval(10000); // 10秒定位一次
            option.setLocationCacheEnable(false); // 不使用缓存
            option.setHttpTimeOut(10000);

            locationClient.setLocationListener(location -> {
                if (location != null) {
                    LatLng latLng = new LatLng(location.getLatitude(), location.getLongitude());
                    updateLocationMarker(latLng);
                }
            });

            locationClient.setLocationOption(option);
            locationClient.startLocation();
            isFirst = false;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void updateLocationMarker(LatLng latLng) {
        if (!isUserScrolling) {  // 只在非用户滑动状态下移动地图
            aMap.animateCamera(CameraUpdateFactory.newLatLngZoom(latLng, 14), new AMap.CancelableCallback() {
                @Override
                public void onFinish() {
                    updateMarkerPosition(latLng);
                }

                @Override
                public void onCancel() {
                }
            });
        } else {
            // 用户滑动时只更新标记位置
            updateMarkerPosition(latLng);
        }
    }

    private void updateMarkerPosition(LatLng latLng) {
        if (binding.statusMarker != null) {
            // 将经纬度转换为屏幕坐标
            android.graphics.Point screenPosition = aMap.getProjection().toScreenLocation(latLng);

            // 获取 RelativeLayout.LayoutParams
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) binding.statusMarker.getLayoutParams();

            // 计算新的 margins
            params.leftMargin = screenPosition.x - binding.statusMarker.getWidth() / 2;
            params.topMargin = screenPosition.y - binding.statusMarker.getHeight() / 2;

            // 清除其他定位属性
            params.removeRule(RelativeLayout.ALIGN_BOTTOM);
            params.removeRule(RelativeLayout.CENTER_HORIZONTAL);

            // 应用新的布局参数
            binding.statusMarker.setLayoutParams(params);
            binding.statusMarker.setVisibility(View.VISIBLE);
        }
    }

    private void initViews() {
        if (transportDetail == null) {
            return;
        }
        try {
            if (transportDetail.getBh() == null) {
                return;
            }
        } catch (Exception e) {
            // 结束后面的执行
            return;
        }
        // 初始化轮播
        ViewFlipper routeFlipper = binding.routeFlipper;

        // 只创建包含完整路线的视图
        View routeView = LayoutInflater.from(this).inflate(R.layout.item_route_info, null);
        TextView tvRoute = routeView.findViewById(R.id.tv_route);

        List<RouteCity> sortedCities = new ArrayList<>(transportDetail.getYslx());
        Collections.sort(sortedCities, (a, b) -> a.getXh().compareTo(b.getXh()));
        // 构路线文本（不包含"途径："前缀）
        StringBuilder routeText = new StringBuilder();
        for (int i = 0; i < sortedCities.size(); i++) {
            if (i > 0) {
                routeText.append(" → ");
            }
            routeText.append(sortedCities.get(i).getCsmc());
        }

        tvRoute.setText(routeText.toString());
        // 请求焦点以开始滚动
        tvRoute.requestFocus();
        tvRoute.setSelected(true);

        routeFlipper.addView(routeView);
        // 关闭自动切换
        routeFlipper.setAutoStart(false);

        // 初始化运输按钮
        binding.btnStartTransport.setOnClickListener(v -> toggleTransportStatus());
        binding.slideToConfirm.setOnSlideCompleteListener(new SlideToConfirmView.OnSlideCompleteListener() {
            @Override
            public void onSlideComplete() {
                switch (currentStatus) {
                    case STATUS_NOT_STARTED:
                        // 未发起状态,复原滑块并提示
                        binding.slideToConfirm.reset();
                        XToastUtil.showToast(TransportMapActivity.this, "请先点击立即发车");
                        break;

                    case STATUS_IN_TRANSIT:
                        // 运输中状态,跳转到完成页面
                        binding.slideToConfirm.reset(); // 重置滑块
                        openServedActivity();
                        break;

                    case STATUS_TEMP_STOP:
                        // 临停中状态,复原滑块并提示
                        binding.slideToConfirm.reset();
                        XToastUtil.showToast(TransportMapActivity.this, "请先恢复运输状态");
                        break;
                }
            }
        });
        updateTransportButtonUI();

        // 添加异常情况说明按钮点击事件
        binding.btnAbnormal.setOnClickListener(v -> {
            TransportAbnormalActivity.start(this, String.valueOf(transportDetail.getBh()));
        });
        binding.btnStudy.setOnClickListener(v -> {
            EventBus.getDefault().post(new InputEntity());
            RegulationDetailActivity.start(
                    this,
                    "运输教程",
                    transportDetail.getYsjc().getLj(),
                    false,  // 不显示确认按钮
                    null,    // 底部列表项不需要 transportId
                    false,
                    true// 是否允许弹窗
            );
        });
    }

    private void toggleTransportStatus() {
        switch (currentStatus) {
            case STATUS_NOT_STARTED:
                // 未发起 -> 运输中，调用发车接口
                driver(() -> {
                    if (transportDetail != null &&
                            transportDetail.getYszt() != null &&
                            YSZT_IN_TRANSIT.equals(transportDetail.getYszt().getCode())) {
                        currentStatus = STATUS_IN_TRANSIT;
                        updateTransportButtonUI();
                    }
                });
                break;

            case STATUS_IN_TRANSIT:
                // 运输中 -> 打开临时停车页面
                Intent intent = new Intent(this, TransportParkingActivity.class);
                intent.putExtra("ysglBh", transportId);
                parkingLauncher.launch(intent);
                break;

            case STATUS_TEMP_STOP:
                // 临停中 -> 运输中，调用恢复发车接口
                driver(() -> {
                    if (transportDetail != null &&
                            transportDetail.getFczt() != null &&
                            FCZT_RUNNING.equals(transportDetail.getFczt().getCode())) {
                        currentStatus = STATUS_IN_TRANSIT;
                        updateTransportButtonUI();
                    }
                });
                break;
        }
    }

    private void updateTransportButtonUI() {
        switch (currentStatus) {
            case STATUS_NOT_STARTED:
                // 未发起状态
                binding.btnStartTransport.setBackgroundResource(R.drawable.ic_start_transport);
                binding.ivStatusMarker.setImageResource(R.drawable.ic_truck);
                binding.tvStatusText.setText("待发车");
                // 显示滑动确认,隐藏主按钮
//                binding.slideToConfirm.setVisibility(View.VISIBLE);
//                binding.btnStartTransport.setVisibility(View.GONE);
                break;

            case STATUS_IN_TRANSIT:
                // 运输中状态
                binding.btnStartTransport.setBackgroundResource(R.drawable.ic_temporary_parking);
                binding.ivStatusMarker.setImageResource(R.drawable.ic_truck_running);
                binding.tvStatusText.setText("运输中");
                // 显示主按钮,隐藏滑动确认
//                binding.slideToConfirm.setVisibility(View.GONE);
//                binding.btnStartTransport.setVisibility(View.VISIBLE);
                break;

            case STATUS_TEMP_STOP:
                // 临停中状态
                binding.btnStartTransport.setBackgroundResource(R.drawable.ic_start_transport);
                binding.ivStatusMarker.setImageResource(R.drawable.ic_truck);
                binding.tvStatusText.setText("临停中");
                // 显示主按钮,隐藏滑动确认
//                binding.slideToConfirm.setVisibility(View.GONE);
//                binding.btnStartTransport.setVisibility(View.VISIBLE);
                break;
        }
    }

    @Override
    protected void initView() {
    }

    @Override
    protected void initListener() {
    }

    private boolean isFirst = true;

    @Override
    protected void getData() {
        getTransportGuides("1", () -> {
            // 根据 transportDetail 设置初始状态
            updateStatusFromTransportDetail();

            // 确保 status_marker 初始不可见
            if (isFirst) {
//                binding.statusMarker.setVisibility(View.INVISIBLE);
                initMap();
            }

            initViews();
            // 添加地图移动监听
            aMap.setOnCameraChangeListener(cameraChangeListener);
//            startLocationService();
        });
    }

    @Override
    protected void onDestroy() {
        if (scheduledExecutorService != null) {
            scheduledExecutorService.shutdown();
            try {
                if (!scheduledExecutorService.awaitTermination(1, TimeUnit.SECONDS)) {
                    scheduledExecutorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduledExecutorService.shutdownNow();
            }
        }

        if (aMap != null) {
            aMap.setOnCameraChangeListener(null);
        }
        EventBus.getDefault().unregister(this);
        super.onDestroy();
        binding.mapView.onDestroy();
        if (locationClient != null) {
            locationClient.onDestroy();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        runOnUiThread(() -> {
            getData();
        });
        isActivityActive = true;
        binding.mapView.onResume();
        LocationServiceUtils.checkAndRestoreService(this);
        if (binding.slideToConfirm != null) {
            binding.slideToConfirm.reset();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        isActivityActive = false;
        binding.mapView.onPause();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        binding.mapView.onSaveInstanceState(outState);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            if (requestCode == REQUEST_SERVED) {
                // 送达记录保存成功后，返回首页
                finish();
            }
        }
    }

    // 打开送达页面的方法
    private void openServedActivity() {
        TransportServedActivity.start(this, String.valueOf(transportDetail.getBh()));
    }

    // 发车
    private void driver(Runnable onComplete) {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "ysglbh=" + transportDetail.getBh());
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + "/precursor/ysgl/hffc" + "?query=" + jiaMiString);
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result,
                        new TypeToken<XResultData>() {
                        }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    getTransportGuides("1", onComplete);
                } else {
                    XToastUtil.showToast(TransportMapActivity.this, "发车失败，服务器错误");
                }
                onComplete.run();
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                XToastUtil.showToast(TransportMapActivity.this, failedMsg);
                onComplete.run();
            }

            @Override
            public void onFinished() {
                onComplete.run();
            }
        });
    }

    // 添加新方法用于从 transportDetail 更新状态
    private void updateStatusFromTransportDetail() {
        if (transportDetail == null) {
            return;
        }
        if (transportDetail.getYszt() != null) {
            String ysztCode = transportDetail.getYszt().getCode();
            if (YSZT_IN_TRANSIT.equals(ysztCode)) {
                if (transportDetail.getFczt() != null) {
                    String fcztCode = transportDetail.getFczt().getCode();
                    if (FCZT_RUNNING.equals(fcztCode)) {
                        currentStatus = STATUS_IN_TRANSIT;
                    } else if (FCZT_TEMP_STOP.equals(fcztCode)) {
                        currentStatus = STATUS_TEMP_STOP;
                    }
                } else {
                    currentStatus = STATUS_IN_TRANSIT;
                }
            } else {
                currentStatus = STATUS_NOT_STARTED;
            }
        } else {
            currentStatus = STATUS_NOT_STARTED;
        }
        updateTransportButtonUI();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(InputEntity item) {
        if (item != null) {
        }
    }

    private void startLocationService() {
        //         XShareCacheUtils.getInstance().putString(transportId+"_yszt", "已送达");
        // 获取这个值如果为空则不开启服务
        String yszt = XShareCacheUtils.getInstance().getString(transportId + "_yszt");

        if (yszt == null) {
            return;
        }
        if (yszt.equals("已送达") || yszt.isEmpty()) {
            return;
        }
        Intent intent = new Intent(this, LocationPermissionActivity.class);
        intent.putExtra("transport_id", Long.parseLong(transportId));
        startActivity(intent);
    }

    private void startLocationService(String transportId) {
        try {
            // 1. 先检查transportId是否有效
            if (transportId == null || transportId.isEmpty()) {
                XToastUtil.showToast(this, "运输编号无效");
                return;
            }

            // 2. 保存transportId到SharedPreferences
            long transportIdLong = Long.parseLong(transportId);
            XShareCacheUtils.getInstance().put("current_transport_id", transportIdLong);
            try {
                XShareCacheUtils.getInstance().put(transportDetail.getBh() + "_yszt", "运输中");
            } catch (Exception e) {

            }

            // 3. 检查权限
            if (!XXPermissions.isGranted(this,
                    Permission.ACCESS_FINE_LOCATION,
                    Permission.ACCESS_COARSE_LOCATION,
                    Permission.POST_NOTIFICATIONS)) {  // Android 13需要通知权限
                XXPermissions.with(this)
                        .permission(Permission.ACCESS_FINE_LOCATION)
                        .permission(Permission.ACCESS_COARSE_LOCATION)
                        .permission(Permission.POST_NOTIFICATIONS)
                        .request(new OnPermissionCallback() {  // 使用正确的回调接口
                            @Override
                            public void onGranted(List<String> permissions, boolean all) {
                                if (all) {
                                    // 获得所有权限后启动服务
                                    actuallyStartService(transportId);
                                } else {
                                    XToastUtil.showToast(TransportMapActivity.this, "需要所有权限才能启动定位服务");
                                }
                            }

                            @Override
                            public void onDenied(List<String> permissions, boolean never) {
                                XToastUtil.showToast(TransportMapActivity.this, "需要定位权限和通知权限才能启动定位服务");
                            }
                        });
            } else {
                // 有权限直接启动服务
                actuallyStartService(transportId);
            }
        } catch (Exception e) {
            e.printStackTrace();
            XToastUtil.showToast(this, "启动定位服务失败：" + e.getMessage());
        }
    }

    private void actuallyStartService(String transportId) {
        try {
            // 4. 启动服务
            LocationServiceUtils.startLocationService(this, Long.parseLong(transportId));

            // 5. 验证服务是否成功启动
            boolean isRunning = LocationServiceUtils.isServiceRunning(this);
            if (isRunning) {
//                XToastUtil.showToast(this, "定位服务已启动");
            } else {
                // 如果服务没有运行，尝试使用不同的方式启动
                Intent serviceIntent = new Intent(this, LocationService.class);
                serviceIntent.putExtra("transport_id", Long.parseLong(transportId));
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    this.startForegroundService(serviceIntent);
                } else {
                    this.startService(serviceIntent);
                }

                // 再次检查服务是否启动
                isRunning = LocationServiceUtils.isServiceRunning(this);
                if (!isRunning) {
                    XToastUtil.showToast(this, "定位服务启动失败，请重试");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            XToastUtil.showToast(this, "启动服务出错：" + e.getMessage());
        }
    }


}