package com.linggan.jd831.ui.works.sewage;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.adapter.ImaVideoShowAdapter;
import com.linggan.jd831.bean.AreaEntity;
import com.linggan.jd831.bean.ShouYInfoEntity;
import com.linggan.jd831.databinding.ActivityShouYangInfoBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.XHttpUtils;

import org.xutils.http.RequestParams;

import java.util.List;

/**
 * 类  名：收样信息
 * 作  者：zxb
 * 说  明：
 * 时  间：2024/3/5 13:11
 */
public class ShouYangInfoActivity extends XBaseActivity<ActivityShouYangInfoBinding> {

    private String ybBh;

    @Override
    protected ActivityShouYangInfoBinding getViewBinding() {
        return ActivityShouYangInfoBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        ybBh = getIntent().getStringExtra("bh");
    }

    @Override
    protected void initListener() {
    }

    @Override
    protected void getData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "ybCode=" + ybBh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.syInfo + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<ShouYInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<ShouYInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        binding.tvYbbh.setText(xResultData.getData().getYbCodeJoint());
                        binding.tvSySj.setText(xResultData.getData().getLrsj());
                        binding.tvSyr.setText(xResultData.getData().getSyrXm());
                        binding.gridZp.setAdapter(new ImaVideoShowAdapter(xResultData.getData().getFjList()));
                    }
                } else {
                    XToastUtil.showToast(ShouYangInfoActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

}