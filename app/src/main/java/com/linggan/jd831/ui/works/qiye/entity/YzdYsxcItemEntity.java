package com.linggan.jd831.ui.works.qiye.entity;

import java.io.Serializable;
import java.util.List;

public class YzdYsxcItemEntity implements Serializable {
    private String bh;
    /**
     * 附件数
     */
    private String fjs;
    /**
     * 经度
     */
    private String jd;
    /**
     * 巡查人
     */
    private String lrrmc;
    /**
     * 巡查时间
     */
    private String lrsj;
    /**
     * 其他说明
     */
    private String qtsm;
    /**
     * 纬度
     */
    private String wd;
    /**
     * 巡查地点
     */
    private String xcdd;
    /**
     * 巡查对象类型
     */
    private Xcdx xcdx;
    /**
     * 巡查对象编号
     */
    private String xcdxbh;
    /**
     * 巡查对象名称
     */
    private String xcdxmc;
    /**
     * 巡查内容
     */
    private String xcnr;
    /**
     * 附件列表
     */
    private List<Attachment> fjList;

    public String getBh() {
        return bh;
    }

    public void setBh(String value) {
        this.bh = value;
    }

    public String getFjs() {
        return fjs;
    }

    public void setFjs(String value) {
        this.fjs = value;
    }

    public String getJd() {
        return jd;
    }

    public void setJd(String value) {
        this.jd = value;
    }

    public String getLrrmc() {
        return lrrmc;
    }

    public void setLrrmc(String value) {
        this.lrrmc = value;
    }

    public String getLrsj() {
        return lrsj;
    }

    public void setLrsj(String value) {
        this.lrsj = value;
    }

    public String getQtsm() {
        return qtsm;
    }

    public void setQtsm(String value) {
        this.qtsm = value;
    }

    public String getWd() {
        return wd;
    }

    public void setWd(String value) {
        this.wd = value;
    }

    public String getXcdd() {
        return xcdd;
    }

    public void setXcdd(String value) {
        this.xcdd = value;
    }

    public Xcdx getXcdx() {
        return xcdx;
    }

    public void setXcdx(Xcdx value) {
        this.xcdx = value;
    }

    public String getXcdxbh() {
        return xcdxbh;
    }

    public void setXcdxbh(String value) {
        this.xcdxbh = value;
    }

    public String getXcdxmc() {
        return xcdxmc;
    }

    public void setXcdxmc(String value) {
        this.xcdxmc = value;
    }

    public String getXcnr() {
        return xcnr;
    }

    public void setXcnr(String value) {
        this.xcnr = value;
    }

    public List<Attachment> getFjList() {
        return fjList;
    }

    public void setFjList(List<Attachment> value) {
        this.fjList = value;
    }

    public static class Xcdx implements Serializable {
        private String code;
        private String name;

        public String getCode() {
            return code;
        }

        public void setCode(String value) {
            this.code = value;
        }

        public String getName() {
            return name;
        }

        public void setName(String value) {
            this.name = value;
        }
    }

    /**
     * 附件信息实体类
     */
    public static class Attachment implements Serializable {
        /**
         * 附件编号
         */
        private long bh;

        /**
         * 附件名称
         */
        private String mc;

        /**
         * 附件路径
         */
        private String lj;

        /**
         * 后缀
         */
        private String hz;

        /**
         * 大小
         */
        private String dx;

        /**
         * 类型
         */
        private String lx;

        /**
         * 录入人编号
         */
        private long lrrbh;

        public long getBh() {
            return bh;
        }

        public void setBh(long value) {
            this.bh = value;
        }

        public String getMc() {
            return mc;
        }

        public void setMc(String value) {
            this.mc = value;
        }

        public String getLj() {
            return lj;
        }

        public void setLj(String value) {
            this.lj = value;
        }

        public String getHz() {
            return hz;
        }

        public void setHz(String value) {
            this.hz = value;
        }

        public String getDx() {
            return dx;
        }

        public void setDx(String value) {
            this.dx = value;
        }

        public String getLx() {
            return lx;
        }

        public void setLx(String value) {
            this.lx = value;
        }

        public long getLrrbh() {
            return lrrbh;
        }

        public void setLrrbh(long value) {
            this.lrrbh = value;
        }
    }
}
