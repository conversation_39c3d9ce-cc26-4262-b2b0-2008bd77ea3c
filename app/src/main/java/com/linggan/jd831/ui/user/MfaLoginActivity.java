package com.linggan.jd831.ui.user;

import android.os.CountDownTimer;
import android.text.InputType;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.lgfzd.base.base.XBase2Activity;
import com.linggan.jd831.R;
import com.linggan.jd831.databinding.ActivityMfaDeviceBinding;
import com.linggan.jd831.widget.VerificationCodeEditText;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

public class MfaLoginActivity extends XBase2Activity<ActivityMfaDeviceBinding> {

    private VerificationCodeEditText[] editTexts = new VerificationCodeEditText[6];
    private BottomSheetDialog mfaDialog;
    private RecyclerView recyclerView;
    private MfaCodeAdapter adapter;

    @Override
    protected ActivityMfaDeviceBinding getViewBinding() {
        return ActivityMfaDeviceBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        initViews();
        setupVerificationCodeInputs();
    }

    @Override
    protected void initListener() {
        binding.llMfaHelp.setOnClickListener(v -> showMfaDialog());
    }

    @Override
    protected void getData() {

    }

    private void initViews() {
        editTexts[0] = findViewById(R.id.et_code_1);
        editTexts[1] = findViewById(R.id.et_code_2);
        editTexts[2] = findViewById(R.id.et_code_3);
        editTexts[3] = findViewById(R.id.et_code_4);
        editTexts[4] = findViewById(R.id.et_code_5);
        editTexts[5] = findViewById(R.id.et_code_6);

        for (int i = 0; i < editTexts.length; i++) {
            editTexts[i].setIndex(i);
            editTexts[i].setInputType(InputType.TYPE_CLASS_NUMBER);
        }

        // 页面进入时，让第一个输入框获得焦点
        editTexts[0].requestFocus();
    }

    private void setupVerificationCodeInputs() {
        for (int i = 0; i < editTexts.length; i++) {
            final int currentIndex = i;
            editTexts[i].setOnInputListener((index, text, type) -> {
                switch (type) {
                    case INPUT:
                        // 输入数字时，跳转到下一个输入框
                        if (currentIndex < editTexts.length - 1) {
                            editTexts[currentIndex + 1].requestFocus();
                        }
                        break;
                    case DELETE_EMPTY:
                        // 当前框为空时按删除键，跳转到上一个输入框
                        if (currentIndex > 0) {
                            editTexts[currentIndex - 1].requestFocus();
                        }
                        break;
                    case DELETE_CONTENT:
                        // 当前框有内容时按删除键，保持在当前输入框
                        break;
                }
            });
        }
    }

    private String getVerificationCode() {
        StringBuilder code = new StringBuilder();
        for (VerificationCodeEditText editText : editTexts) {
            code.append(editText.getText().toString());
        }
        return code.toString();
    }

    private void showMfaDialog() {
        if (mfaDialog == null) {
            mfaDialog = new BottomSheetDialog(this);
            View view = getLayoutInflater().inflate(R.layout.dialog_mfa_code, null);
            mfaDialog.setContentView(view);

            view.findViewById(R.id.iv_close).setOnClickListener(v -> mfaDialog.dismiss());

            recyclerView = view.findViewById(R.id.recycler_view);
            recyclerView.setLayoutManager(new LinearLayoutManager(this));
            adapter = new MfaCodeAdapter(code -> {
                fillVerificationCode(code);
                mfaDialog.dismiss();
            });
            recyclerView.setAdapter(adapter);

//            EditText etSearch = view.findViewById(R.id.et_search);
//            etSearch.addTextChangedListener(new TextWatcher() {
//                @Override
//                public void beforeTextChanged(CharSequence s, int start, int count, int after) {
//
//                }
//
//                @Override
//                public void onTextChanged(CharSequence s, int start, int before, int count) {
//
//                }
//
//                @Override
//                public void afterTextChanged(Editable s) {
//                    adapter.filter(s.toString());
//                }
//                // ... 其他必要的空实现
//            });
        }

        adapter.startTimer();
        mfaDialog.show();
    }

    private void fillVerificationCode(String code) {
        for (int i = 0; i < editTexts.length && i < code.length(); i++) {
            editTexts[i].setText(String.valueOf(code.charAt(i)));
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (adapter != null) {
            adapter.stopTimer();
        }
    }

    private static class MfaCodeAdapter extends RecyclerView.Adapter<MfaCodeAdapter.ViewHolder> {
        private List<MfaCodeItem> items = new ArrayList<>();
        private List<MfaCodeItem> filteredItems = new ArrayList<>();
        private OnCodeClickListener listener;
        private Map<String, CountDownTimer> timerMap = new HashMap<>();

        public MfaCodeAdapter(OnCodeClickListener listener) {
            this.listener = listener;
            // 添加模拟数据
            items.add(new MfaCodeItem("测试账号1", "719617", "2024-10-29 10:32:43"));
//            items.add(new MfaCodeItem("aliyun4781648870", "104926", "2024-10-29 09:50:58"));
//            items.add(new MfaCodeItem("测试账号1", "123456", "2024-10-29 08:30:00"));
//            items.add(new MfaCodeItem("测试账号2", "654321", "2024-10-29 07:20:00"));
            filteredItems.addAll(items);
        }

        @Override
        public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_mfa_code, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(ViewHolder holder, int position) {
            MfaCodeItem item = filteredItems.get(position);
            holder.tvAccount.setText(item.account);
            holder.tvCode.setText(item.code);
            holder.tvTime.setText("添加时间: " + item.time);
            holder.btnFill.setOnClickListener(v -> listener.onCodeClick(item.code));

            startItemTimer(holder, item);
        }

        @Override
        public int getItemCount() {
            return filteredItems.size();
        }

        private void startItemTimer(ViewHolder holder, MfaCodeItem item) {
            if (timerMap.containsKey(item.account)) {
                timerMap.get(item.account).cancel();
            }

            holder.progressBar.setMax(60);
            holder.progressBar.setProgress(60);

            CountDownTimer timer = new CountDownTimer(60000, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    int seconds = (int) (millisUntilFinished / 1000);
                    holder.progressBar.setProgress(seconds);
                }

                @Override
                public void onFinish() {
                    item.code = String.format("%06d", new Random().nextInt(1000000));
                    notifyItemChanged(holder.getAdapterPosition());
                    startItemTimer(holder, item);
                }
            };

            timerMap.put(item.account, timer);
            timer.start();
        }

        public void filter(String query) {
            filteredItems.clear();
            if (TextUtils.isEmpty(query)) {
                filteredItems.addAll(items);
            } else {
                for (MfaCodeItem item : items) {
                    if (item.account.toLowerCase().contains(query.toLowerCase())) {
                        filteredItems.add(item);
                    }
                }
            }
            notifyDataSetChanged();
        }

        public void startTimer() {
            for (CountDownTimer timer : timerMap.values()) {
                timer.start();
            }
        }

        public void stopTimer() {
            for (CountDownTimer timer : timerMap.values()) {
                timer.cancel();
            }
            timerMap.clear();
        }

        static class ViewHolder extends RecyclerView.ViewHolder {
            TextView tvAccount;
            TextView tvCode;
            TextView tvTime;
            TextView btnFill;
            ProgressBar progressBar;

            ViewHolder(View view) {
                super(view);
                tvAccount = view.findViewById(R.id.tv_account);
                tvCode = view.findViewById(R.id.tv_code);
                tvTime = view.findViewById(R.id.tv_time);
                btnFill = view.findViewById(R.id.btn_fill);
                progressBar = view.findViewById(R.id.progress_bar);
            }
        }

        interface OnCodeClickListener {
            void onCodeClick(String code);
        }

        static class MfaCodeItem {
            String account;
            String code;
            String time;

            MfaCodeItem(String account, String code, String time) {
                this.account = account;
                this.code = code;
                this.time = time;
            }
        }
    }
}
