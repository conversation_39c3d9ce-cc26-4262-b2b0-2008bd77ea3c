package com.linggan.jd831.ui.works.qiye.v2.alert.adapter;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.ui.works.qiye.v2.alert.AlertDetailActivity;
import com.linggan.jd831.ui.works.qiye.v2.alert.entity.ListSysYcEntity;
import com.linggan.jd831.utils.StrUtils;

/**
 * 类  名：宣教列表
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class AlertListHolder extends IViewHolder {

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_alert_manage_list;
    }


    public class ViewHolder extends XViewHolder<ListSysYcEntity> {

        private TextView mTvTitle;
        private TextView mtvIdCard;
        private TextView mtvRegional;


        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mTvTitle = view.findViewById(R.id.tv_title);
            mtvIdCard = view.findViewById(R.id.tv_id_card);
            mtvRegional = view.findViewById(R.id.tv_regional);
        }

        @Override
        protected void onBindData(final ListSysYcEntity itemData) {
            mTvTitle.setText(StrUtils.getDev(itemData.getSysQyMc(), "-"));
            String yjLx = itemData.getYjLx();
            if ("5".equals(yjLx)) {
                mtvIdCard.setText("白名单非开放时间出入");
            } else if ("6".equals(yjLx)) {
                mtvIdCard.setText("非白名单出入");
            } else {
                mtvIdCard.setText("-");
            }
            mtvRegional.setText(StrUtils.getDev(itemData.getYjSj(), "-"));
            itemView.setOnClickListener(v -> {
                Bundle bundle = new Bundle();
                bundle.putSerializable("data", itemData);
                XIntentUtil.redirectToNextActivity(mContext, AlertDetailActivity.class, bundle);
            });
        }
    }
}


