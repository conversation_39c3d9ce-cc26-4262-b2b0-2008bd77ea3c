package com.linggan.jd831.ui.user;

import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.linggan.jd831.utils.XHttpUtils;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData;
import com.lgfzd.base.utils.XAppUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.adapter.PeoRenLingHolder;
import com.linggan.jd831.bean.PeoRLListEntity;
import com.linggan.jd831.databinding.ActivitySearchListBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 类  名：人员认领
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2023/3/1 11:21
 * 版  权：LGKJ
 */
public class PeoClaimActivity extends XBaseActivity<ActivitySearchListBinding> implements XRefreshLayout.PullLoadMoreListener {

    private int page = 1, totalPage = 0;
    private boolean isShow = true;
    private boolean icCheck = true;

    @Override
    protected ActivitySearchListBinding getViewBinding() {
        return ActivitySearchListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        binding.recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
        binding.recycler.getAdapter().bindHolder(new PeoRenLingHolder());
        binding.recycler.setOnPullLoadMoreListener(this);
        EventBus.getDefault().register(this);
    }

    @Override
    protected void initListener() {
        binding.listSearch.setOnEditorActionListener((v, actionId, event) -> {
            //以下方法防止两次发送请求
            if (icCheck) {
                isShow = true;
                getData();
                icCheck = false;
            }
            XAppUtil.closeSoftInput(PeoClaimActivity.this);
            return false;
        });

        binding.listSearchArea.setOnEditorActionListener((v, actionId, event) -> {
            //以下方法防止两次发送请求
            if (icCheck) {
                isShow = true;
                getData();
                icCheck = false;
            }
            XAppUtil.closeSoftInput(PeoClaimActivity.this);
            return false;
        });
    }

    @Override
    protected void getData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PEO_REN_LING);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("page", page);
        objectMap.put("rows", XConstantUtils.PAGE_SIZE);
        if (!TextUtils.isEmpty(binding.listSearch.getText().toString())) {
            objectMap.put("xm", binding.listSearch.getText().toString());
        }
        if (!TextUtils.isEmpty(binding.listSearchArea.getText().toString())) {
            objectMap.put("hjdzDzmc", binding.listSearchArea.getText().toString());
        }
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), isShow, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<XResultPageData<PeoRLListEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XResultPageData<PeoRLListEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        totalPage = xResultData.getData().getTotalPage();
                        if (xResultData.getData().getRecords() != null && xResultData.getData().getRecords().size() > 0) {
                            binding.tvNoData.setVisibility(View.GONE);
                            if (page == 1) {
                                binding.recycler.getAdapter().setData(0, xResultData.getData().getRecords());
                            } else {
                                binding.recycler.getAdapter().addDataAll(0, xResultData.getData().getRecords());
                            }
                        } else {
                            if (page == 1) {
                                binding.recycler.getAdapter().setData(0, new ArrayList());
                                binding.tvNoData.setVisibility(View.VISIBLE);
                            }
                        }
                    } else {
                        if (page == 1) {
                            binding.recycler.getAdapter().setData(0, new ArrayList());
                            binding.tvNoData.setVisibility(View.VISIBLE);
                        }
                    }
                } else {
                    XToastUtil.showToast(PeoClaimActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                binding.recycler.setPullLoadMoreCompleted();
                icCheck = true;
            }
        });
    }


    @Override
    public void onRefresh() {
        XAppUtil.closeSoftInput(PeoClaimActivity.this);
        page = 1;
        isShow = false;
        getData();
    }

    @Override
    public boolean onLoadMore() {
        XAppUtil.closeSoftInput(PeoClaimActivity.this);
        if (page < totalPage) {
            isShow = false;
            page++;
            getData();
            return true;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PeoRLListEntity event) {
        if (event != null) {
            page = 1;
            isShow = false;
            getData();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
