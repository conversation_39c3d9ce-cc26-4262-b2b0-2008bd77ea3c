package com.linggan.jd831.ui.works.qiye.v2.warehouse.entity;

import java.io.Serializable;

/**
 * 仓库库存实体类
 */
public class WareHouseInventoryEntity implements Serializable {
    /**
     * 库存编号
     */
    private String bh;

    /**
     * 仓库编号
     */
    private String ckbh;

    /**
     * 化学品名称
     */
    private String hxpmc;

    /**
     * 库存单位
     */
    private Kcdw kcdw;

    /**
     * 库存数量
     */
    private String kcsl;

    public String getBh() {
        return bh;
    }

    public void setBh(String bh) {
        this.bh = bh;
    }

    public String getCkbh() {
        return ckbh;
    }

    public void setCkbh(String ckbh) {
        this.ckbh = ckbh;
    }

    public String getHxpmc() {
        return hxpmc;
    }

    public void setHxpmc(String hxpmc) {
        this.hxpmc = hxpmc;
    }

    public Kcdw getKcdw() {
        return kcdw;
    }

    public void setKcdw(Kcdw kcdw) {
        this.kcdw = kcdw;
    }

    public String getKcsl() {
        return kcsl;
    }

    public void setKcsl(String kcsl) {
        this.kcsl = kcsl;
    }

    /**
     * 库存单位
     */
    public static class Kcdw implements Serializable {
        /**
         * 单位编码
         */
        private String code;

        /**
         * 单位名称
         */
        private String name;

        public Kcdw(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
