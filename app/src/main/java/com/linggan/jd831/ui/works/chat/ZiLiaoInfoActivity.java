package com.linggan.jd831.ui.works.chat;


import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.bean.ZlkEntity;
import com.linggan.jd831.databinding.ActivityZiliaoInfoBinding;
import com.linggan.jd831.ui.common.FileShowActivity;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.XHttpUtils;
import com.tencent.smtt.export.external.TbsCoreSettings;
import com.tencent.smtt.sdk.QbSdk;
import com.tencent.smtt.sdk.WebSettings;
import com.tencent.smtt.sdk.WebView;
import com.tencent.smtt.sdk.WebViewClient;

import org.greenrobot.eventbus.EventBus;
import org.xutils.http.RequestParams;

import java.util.HashMap;

/**
 * 类  名：资料详情
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class ZiLiaoInfoActivity extends XBaseActivity<ActivityZiliaoInfoBinding> {

    private String bh, fjDz;

    @Override
    protected ActivityZiliaoInfoBinding getViewBinding() {
        return ActivityZiliaoInfoBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        HashMap map = new HashMap();
        map.put(TbsCoreSettings.TBS_SETTINGS_USE_SPEEDY_CLASSLOADER, true);
        map.put(TbsCoreSettings.TBS_SETTINGS_USE_DEXLOADER_SERVICE, true);
        QbSdk.initTbsSettings(map);
    }

    @Override
    protected void initListener() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + bh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.GPT_ZLK_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<ZlkEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<ZlkEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (!TextUtils.isEmpty(xResultData.getData().getFjDz())) {
                        fjDz = xResultData.getData().getFjDz();
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void getData() {
        bh = getIntent().getStringExtra("bh");
        setInfo("");
        addRead();
    }

    protected void setInfo(String info) {
        WebSettings webseting = binding.mWebView.getSettings();
        webseting.setJavaScriptEnabled(true);
        webseting.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.SINGLE_COLUMN);
        webseting.setLoadWithOverviewMode(true);
        webseting.setDatabaseEnabled(true);
        webseting.setAllowFileAccess(true);
        webseting.setDomStorageEnabled(true);
        webseting.setLoadsImagesAutomatically(true);    //设置自动加载图片
//        webseting.setUseWideViewPort(true);

        binding.mWebView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                Log.i("zl", "shouldOverrideUrlLoading: " + url);
                view.loadUrl(url);
                return true;
            }
        });
        binding.mWebView.setDownloadListener((url, userAgent, contentDisposition, mimetype, contentLength) -> {
//            // 处理下载事件
            Log.i("zl", "setDownloadListener: " + url);
            Bundle bundle = new Bundle();
            bundle.putString("title", "详情");
            bundle.putString("url", fjDz);
            XIntentUtil.redirectToNextActivity(ZiLiaoInfoActivity.this, FileShowActivity.class, bundle);
        });
        Log.i("zl", "url: " + "https://jdzl.lgfzd.com:30443/pageone/index.html?bh=" + bh);
        binding.mWebView.loadUrl("https://jdzl.lgfzd.com:30443/pageone/index.html?bh=" + bh);
    }

    protected void addRead() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + bh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.GPT_ZLK_NUM + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    EventBus.getDefault().post(new ZlkEntity());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

}
