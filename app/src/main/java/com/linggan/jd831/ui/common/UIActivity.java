package com.linggan.jd831.ui.common;

import android.Manifest;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.PermissionChecker;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.XXPermissions;
import com.lgfzd.base.XBaseApp;
import com.linggan.jd831.utils.JDAlertDialog;

import java.util.ArrayList;
import java.util.List;

/**
 * 基类
 *
 * @date 2018/7/10
 */
public abstract class UIActivity extends AppCompatActivity {

    protected final int PERMISSION_REQUEST_CODE = 0x831;//权限请求码

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        XBaseApp.getActivities().add(this);
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
    }

    private long mLastClickTime;// 用户判断多次点击的时间

    /**
     * 判断是否点击过快
     *
     * @return 是否点击过快
     */
    private boolean isFastDoubleClick() {
        long time = System.currentTimeMillis();
        if (Math.abs(time - mLastClickTime) > 500) {
            return true;
        }
        mLastClickTime = time;
        return false;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (isFastDoubleClick()) {
            return super.onTouchEvent(event);
        } else {
            return true;
        }
    }


    /**
     * 检查是否全部授权权限
     *
     * @param permissions 权限组
     */
    protected void requestPermission(String[] permissions) {
        XXPermissions.with(this)
                .permission(permissions)
                .request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(List<String> permissions, boolean all) {
                        if (all) {
                            //获取全部权限成功
                            onGrantedAll();
                        } else {
                            //获取部分权限成功，但部分权限未正常授予
                            List<String> list = new ArrayList<>();
                            for (String permission : permissions) {
                                if (PermissionChecker.checkSelfPermission(UIActivity.this, permission) != PermissionChecker.PERMISSION_GRANTED) {
                                    list.add(permission);
                                }
                            }
                            int size = list.size();
                            if (size > 0) {
                                ActivityCompat.requestPermissions(UIActivity.this, list.toArray(new String[size]), PERMISSION_REQUEST_CODE);
                            }
                        }
                    }

                    @Override
                    public void onDenied(List<String> permissions, boolean never) {
                        if (never) {
                            // 如果是被永久拒绝就跳转到应用权限系统设置页面
                            XXPermissions.startPermissionActivity(UIActivity.this, permissions);
                        }
                    }
                });
    }

    /**
     * 显示弹框(打开设置权限页面)
     *
     * @param message 提示语
     * @param finish  是否关闭当前界面
     */
    protected void go2setting(String message, final boolean finish) {
        try {
            new JDAlertDialog.Builder(this)
                    .setTitle("提示")
                    .setMessage(message)
                    .setCancelable(false)
                    .setPositiveButton("设置", (d, w) -> {
                        d.dismiss();
                        Intent intent = new Intent();
                        intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                        Uri uri = Uri.fromParts("package", getPackageName(), null);
                        intent.setData(uri);
                        startActivity(intent);
                    })
                    .setNegativeButton("取消", (d, w) -> {
                        d.dismiss();
                        if (finish) finish();
                    }).create().show();
        } catch (Exception e) {
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode != PERMISSION_REQUEST_CODE) return;
        List<String> list = new ArrayList<>();
        for (String permission : permissions) {
            if (permission.equals(Manifest.permission.READ_PHONE_STATE) && Build.VERSION.SDK_INT >= 29) {
                //如果是安卓10则不检验这个权限
                continue;
            }
            //再一次检测权限是否被授权成功
            if (PermissionChecker.checkSelfPermission(this, permission)
                    != PermissionChecker.PERMISSION_GRANTED) {
                list.add(permission);
            }
        }
        if (list.isEmpty()) {
            onGrantedAll();
        } else {
            onDenied(list);
        }
    }


    /**
     * 已经授权
     */
    protected void onGrantedAll() {
        //子类调用
    }

    /**
     * 授权失败
     *
     * @param denied 授权失败的权限集
     */
    protected void onDenied(List<String> denied) {
        //子类调用
    }

    /**
     * 获取一般通用的权限信息提示（非绝密隐私权限）
     *
     * @param denied 未授权的权限
     * @return 提示语
     */
    protected String getPermissionToast(List<String> denied) {
        StringBuilder builder = new StringBuilder();
        int i = 0;
        if (denied.contains(Manifest.permission.ACCESS_COARSE_LOCATION)
                || denied.contains(Manifest.permission.ACCESS_FINE_LOCATION)) {
            i++;
            builder.append(i).append(".获取位置信息;\n");
        }
        if (denied.contains(Manifest.permission.READ_EXTERNAL_STORAGE)
                || denied.contains(Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
            i++;
            builder.append(i).append(".文件读写;\n");
        }
        if (denied.contains(Manifest.permission.CAMERA)) {
            i++;
            builder.append(i).append(".相机权限（摄像头）;\n");
        }
        if (denied.contains(Manifest.permission.RECORD_AUDIO)) {
            i++;
            builder.append(i).append(".录音权限（麦克风）;\n");
        }
        return builder.toString();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        XBaseApp.getActivities().remove(this);
    }
}
