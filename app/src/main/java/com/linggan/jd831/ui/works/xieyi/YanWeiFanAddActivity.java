package com.linggan.jd831.ui.works.xieyi;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.view.TimePickerView;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseLocaActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XGlideEngine;
import com.lgfzd.base.utils.XImageFileCompressEngine;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XPermissionUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.InputEntity;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.PeopleInfoEntity;
import com.linggan.jd831.bean.WeiFanListEntity;
import com.linggan.jd831.bean.XieYiShiEventEntity;
import com.linggan.jd831.bean.ZiDianEntity;
import com.linggan.jd831.databinding.ActivityYanWeiFanAddBinding;
import com.linggan.jd831.ui.common.InputInfoActivity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.PhotoUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.BaseZiDianDialog;
import com.linggan.jd831.widget.CodeNameDialog;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 类  名：违反协议新建
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class YanWeiFanAddActivity extends XBaseLocaActivity<ActivityYanWeiFanAddBinding> implements View.OnClickListener {

    private String peoId = "", renWu;
    private PeopleInfoEntity peopleListEntity;
    private String yjBh = "", wfTypeCode = "", sfPcs = "";
    private ImageAddUtil imageAddImg;
    private XieYiShiEventEntity xieYiShiEventEntity;
    private int from = 0;
    private Uri photoUri;
    private File cameraPhoto;

    @Override
    protected ActivityYanWeiFanAddBinding getViewBinding() {
        return ActivityYanWeiFanAddBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        XPermissionUtil.initPermission(this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        peoId = getIntent().getStringExtra("id");
        renWu = getIntent().getStringExtra("tag");
        yjBh = getIntent().getStringExtra("yjztbh");
        peopleListEntity = (PeopleInfoEntity) getIntent().getSerializableExtra("info");
        if (peopleListEntity != null) {
            if (peopleListEntity.getYjztbhList() != null && peopleListEntity.getYjztbhList().size() > 0 && peopleListEntity.getYjztbhList().get(0) != null) {
                if (TextUtils.isEmpty(yjBh)) {
                    yjBh = peopleListEntity.getYjztbhList().get(0).getYjztbh();
                }
            }
            binding.tvName.setText(peopleListEntity.getXm());
        }
        StrUtils.getPhotoVideoText(binding.tvImgInfo, null);

        imageAddImg = new ImageAddUtil(this, binding.gridImg);
        imageAddImg.setOnImageAddListener(() -> {
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                if (code.equals("1")) {
                    from = 1;
                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setCompressEngine(new XImageFileCompressEngine()).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        });
        EventBus.getDefault().register(this);
        /**
         *这里是任务中心跳转过来
         */
        if (!TextUtils.isEmpty(renWu)) {
            FactoryUtils.getUserData(this, peoId, result -> {
                if (result.getYjztbhList() != null && result.getYjztbhList().size() > 0 && result.getYjztbhList().get(0) != null) {
                    if (TextUtils.isEmpty(yjBh)) {
                        yjBh = result.getYjztbhList().get(0).getYjztbh();
                    }
                }
                binding.tvName.setText(result.getXm());
            });
        }
    }

    @Override
    protected void initListener() {
        binding.tvPgDate.setOnClickListener(this);
        binding.btSure.setOnClickListener(this);
        binding.tvDeal.setOnClickListener(this);
        binding.tvWfShi.setOnClickListener(this);
        binding.tvRemark.setOnClickListener(this);
        binding.tvWfType.setOnClickListener(this);
        binding.tvSfYjPcs.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_pg_date) {
            //评估时间
            Calendar startDate = Calendar.getInstance();
            startDate.set(1900, 0, 1);
            Calendar selectedDate = Calendar.getInstance();
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.tvPgDate.setText(timeDate);
            }).setRangDate(startDate, selectedDate).setDate(selectedDate).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.bt_sure) {
            //提交
            if (ButtonUtils.isFastClick()) {

                postData();
            }
        } else if (v.getId() == R.id.tv_wf_shi) {
            //违法协议事实
            if (TextUtils.isEmpty(binding.tvWfType.getText().toString())) {
                XToastUtil.showToast(this, "请先选择违反协议类型");
                return;
            }
            Bundle bundle = new Bundle();
            bundle.putString("info", binding.tvWfShi.getText().toString());
            bundle.putInt("tab", 1);
            bundle.putString("bh", peoId);
            bundle.putString("code", wfTypeCode);
            XIntentUtil.redirectToNextActivity(this, YanInputActivity.class, bundle);

        } else if (v.getId() == R.id.tv_deal) {
            //联系人陈述
            Bundle bundle = new Bundle();
            bundle.putString("title", "处理情况");
            bundle.putString("hint", "请输入处理情况");
            bundle.putString("info", binding.tvDeal.getText().toString());
            bundle.putInt("tab", 2);
            XIntentUtil.redirectToNextActivity(this, InputInfoActivity.class, bundle);
        } else if (v.getId() == R.id.tv_remark) {
            //联系人陈述
            Bundle bundle = new Bundle();
            bundle.putString("title", "备注");
            bundle.putString("hint", "请输入备注");
            bundle.putString("info", binding.tvRemark.getText().toString());
            XIntentUtil.redirectToNextActivity(this, InputInfoActivity.class, bundle);
        } else if (v.getId() == R.id.tv_wf_type) {
            //违反协议类型
            FactoryUtils.getBaseDataType(this, "yzwfxylx", result -> {
                BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, (ArrayList<ZiDianEntity>) result);
                dianDialog.setOnClickDataListener(() -> {
                    binding.tvWfType.setText(dianDialog.getData().getMc());
                    wfTypeCode = dianDialog.getData().getDm();
                });
                dianDialog.show();
            });
        } else if (v.getId() == R.id.tv_sf_yj_pcs) {
            //是否移交派出所--0否，1是
            CodeNameDialog dianDialog = new CodeNameDialog(this, StrUtils.getZeroNoList());
            dianDialog.setOnClickDataListener(() -> {
                binding.tvSfYjPcs.setText(dianDialog.getCode().getName());
                sfPcs = dianDialog.getCode().getCode();
                if (dianDialog.getCode().getCode().equals("0")) {
                    binding.linPcs.setVisibility(View.GONE);
                } else {
                    binding.linPcs.setVisibility(View.VISIBLE);
                }
            });
            dianDialog.show();
        }
    }

    @Override
    protected void getData() {
    }

    /**
     * 提交数据
     */
    private void postData() {
        if (TextUtils.isEmpty(binding.tvPgDate.getText().toString())) {
            XToastUtil.showToast(this, "请选择违反协议时间");
            return;
        }
        if (TextUtils.isEmpty(binding.tvWfType.getText().toString())) {
            XToastUtil.showToast(this, "请选择违反协议类型");
            return;
        }
        if (TextUtils.isEmpty(binding.tvWfShi.getText().toString())) {
            XToastUtil.showToast(this, "请输入违反协议事实");
            return;
        }
        if (TextUtils.isEmpty(binding.tvDeal.getText().toString())) {
            XToastUtil.showToast(this, "请输入处理情况");
            return;
        }
        if (TextUtils.isEmpty(binding.tvSfYjPcs.getText().toString())) {
            XToastUtil.showToast(this, "请选择是否移交派出所");
            return;
        }
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.WEI_FAN_XIE_YI_SAVE);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("xyrbh", peoId);
            objectMap.put("xyrxm", binding.tvName.getText().toString());
            objectMap.put("yjztbh", yjBh);
            objectMap.put("sjLy", "3");
            objectMap.put("wfxysj", binding.tvPgDate.getText().toString());
            objectMap.put("wfxyss", binding.tvWfShi.getText().toString());
            objectMap.put("clqk", binding.tvDeal.getText().toString());
            objectMap.put("yzwfxylx", wfTypeCode);
            objectMap.put("bz", binding.tvRemark.getText().toString());
            objectMap.put("lng", XShareCacheUtils.getInstance().getString("lon"));
            objectMap.put("lat", XShareCacheUtils.getInstance().getString("lat"));
            objectMap.put("sfPcs", sfPcs);
            objectMap.put("pcsmc", binding.etSfYjPcs.getText().toString());
            if (xieYiShiEventEntity != null && xieYiShiEventEntity.getEntityList() != null && xieYiShiEventEntity.getEntityList().size() > 0) {
                JSONArray jsonArray = new JSONArray();
                for (int i = 0; i < xieYiShiEventEntity.getEntityList().size(); i++) {
                    jsonArray.put(xieYiShiEventEntity.getEntityList().get(i).getBh());
                }
                objectMap.put("yzwfxybhs", jsonArray);
            }
            //违反协议通知
            JSONArray jsonArray = new JSONArray();
            if (imageAddImg != null && imageAddImg.getPaths() != null && imageAddImg.getPaths().size() > 0) {
                for (int i = 0; i < imageAddImg.getPaths().size(); i++) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("lj", imageAddImg.getPaths().get(i).getSavePath());
                    jsonObject.put("mc", imageAddImg.getPaths().get(i).getOriginName());
                    jsonObject.put("hz", StrUtils.getFileType(imageAddImg.getPaths().get(i).getSavePath()));
                    jsonObject.put("dx", imageAddImg.getPaths().get(i).getFileSize());
                    jsonArray.put(jsonObject);
                }
            }
            if (jsonArray.length() <= 0) {
                XToastUtil.showToast(this, "请上传劝诫书");
                return;
            }
            objectMap.put("qjs", jsonArray);
            binding.btSure.setEnabled(false);
            String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
            XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), true, true, new XHttpResponseCallBack() {
                @Override
                public void onSuccess(String result) {
                    XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                    }.getType());
                    if (xResultData.getStatus() == 0) {
                        XToastUtil.showToast(YanWeiFanAddActivity.this, getString(R.string.add_sucess));
                        EventBus.getDefault().post(new WeiFanListEntity());
                        finish();
                    } else {
                        XToastUtil.showToast(YanWeiFanAddActivity.this, xResultData.getErrorInfo());
                    }
                }

                @Override
                public void onFailed(int code, String failedMsg) {
                    binding.btSure.setEnabled(true);
                }

                @Override
                public void onFinished() {
                    binding.btSure.setEnabled(true);
                }
            });
        } catch (JSONException e) {
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.REQUEST_CAMERA:
                case PictureConfig.CHOOSE_REQUEST:
                    ArrayList<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                    if (null != selectList) {
                        for (LocalMedia media : selectList) {
                            if (media.isCompressed()) {
                                uploadFile(media.getCompressPath());
                            } else if (!TextUtils.isEmpty(media.getRealPath())) {
                                uploadFile(media.getRealPath());
                            }
                        }
                    }
                    break;
                case 1:
                    //图片
//                    uploadFile(data.getStringExtra("path"));
                    try {
                        boolean isFinish = XFileUtil.bitmapToFilePath(this, BitmapFactory.decodeFile(cameraPhoto.getAbsolutePath()), photoUri, cameraPhoto);
                        if (isFinish) {
                            uploadFile(cameraPhoto.getAbsolutePath());
                        }
                    } catch (Exception e) {
                    }
                    break;
            }
        }
    }

    /**
     * 上传
     *
     * @param path
     */
    private void uploadFile(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));

        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        imageAddImg.addImage(xResultData.getData().get(0));
                        imageAddImg.notifyData();
                    }
                } else {
                    XToastUtil.showToast(YanWeiFanAddActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }


    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (from == 1) {
//            Intent intent = new Intent(this, CameraActivity.class);//调用系统相机
//            startActivityForResult(intent, 1);

            createFileMuLu();
            if (cameraPhoto != null) {
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                photoUri = XFileUtil.getUriFromFile(this, cameraPhoto);
                intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                startActivityForResult(intent, 1);
            }
        }
    }

    /**
     * 创建文件
     */
    private void createFileMuLu() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
                && ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {
            cameraPhoto = new File(PhotoUtil.createPath(this));
            try {
                if (cameraPhoto.exists()) {
                    cameraPhoto.delete();
                }
                cameraPhoto.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            // 权限未授权，需要申请权限
            requestPermission(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        }
    }


//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onEvent(XieYiShiEventEntity event) {
//        if (event != null) {
//            xieYiShiEventEntity = event;
//            if (event.getEntityList() != null && event.getEntityList().size() > 0) {
//                StringBuffer stringBuffer = new StringBuffer();
//                for (int i = 0; i < event.getEntityList().size(); i++) {
//                    stringBuffer.append(event.getEntityList().get(i).getWfxylx().getName());
//                }
//                if (TextUtils.isEmpty(binding.tvWfShi.getText().toString())) {
//                    binding.tvWfShi.setText(stringBuffer);
//                }
//            }
//        }
//    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(InputEntity item) {
        if (item != null) {
            if (item.getTab() == 1) {
                //违法协议事实
                binding.tvWfShi.setText(item.getInfo());
            } else if (item.getTab() == 2) {
                //处理情况
                binding.tvDeal.setText(item.getInfo());
            } else {
                //备注
                binding.tvRemark.setText(item.getInfo());
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

}