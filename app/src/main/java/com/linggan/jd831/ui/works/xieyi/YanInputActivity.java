package com.linggan.jd831.ui.works.xieyi;

import android.os.Bundle;
import android.text.TextUtils;

import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.utils.XAppUtil;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.R;
import com.linggan.jd831.databinding.ActivityYanXieyiInputBinding;
import com.linggan.jd831.bean.InputEntity;
import com.linggan.jd831.bean.XieYiShiEventEntity;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * 类  名：严重违反协议--事实录入页面
 * 作  者：zxb
 * 说  明：标题，输入内容
 * 时  间：2022/9/13  10:17
 * 版  权：凌感科技
 */
public class YanInputActivity extends XBaseActivity<ActivityYanXieyiInputBinding> {
    private String info, bh, code;

    @Override
    protected ActivityYanXieyiInputBinding getViewBinding() {
        return ActivityYanXieyiInputBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        binding.baseTop.tvRight.setText(getString(R.string.save));
        binding.baseTop.tvRight.setTextColor(getResources().getColor(R.color.color_main));
        EventBus.getDefault().register(this);
    }

    @Override
    protected void getData() {
        info = getIntent().getStringExtra("info");
        bh = getIntent().getStringExtra("bh");
        code = getIntent().getStringExtra("code");
        if (!TextUtils.isEmpty(info)) {
            //回显展示
            binding.etInfo.setText(info);
        }
    }

    @Override
    protected void initListener() {
        binding.baseTop.tvRight.setOnClickListener(v -> {
            //保存
            if (TextUtils.isEmpty(binding.etInfo.getText().toString())) {
                XToastUtil.showToast(this, getString(R.string.input_wfxy_shi));
                return;
            }
            InputEntity inputEntity = new InputEntity(binding.etInfo.getText().toString(), 1);
            EventBus.getDefault().post(inputEntity);
            XAppUtil.closeSoftInput(this);
            finish();
        });
        binding.btChoice.setOnClickListener(v -> {
            Bundle bundle = new Bundle();
            bundle.putString("bh", bh);
            bundle.putString("code", code);
            XIntentUtil.redirectToNextActivity(this, YanXieYiShiShiListActivity.class, bundle);
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(XieYiShiEventEntity event) {
        if (event != null && event.getEntityList() != null && event.getEntityList().size() > 0) {
            StringBuffer stringBuffer = new StringBuffer();
            for (int i = 0; i < event.getEntityList().size(); i++) {
                stringBuffer.append(event.getEntityList().get(i).getWfxysj() +
                        event.getEntityList().get(i).getWfxylx().getName()
                        + "确认人：" + event.getEntityList().get(i).getTjrxm());
            }
            binding.etInfo.setText(stringBuffer);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
