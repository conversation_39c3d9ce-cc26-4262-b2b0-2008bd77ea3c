package com.linggan.jd831.ui.works.fragment;


import android.app.Dialog;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseFragment;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XDateUtil;
import com.lgfzd.base.utils.XDialogUtils;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XNetworkUtil;
import com.lgfzd.base.utils.XPermissionUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.WorkMenuAllAdapter;
import com.linggan.jd831.bean.HomeMenuEntity;
import com.linggan.jd831.bean.KaoHeLvEntity;
import com.linggan.jd831.bean.KaoHeLvEventEntity;
import com.linggan.jd831.bean.MenuEntity;
import com.linggan.jd831.bean.PeoTypeListEntity;
import com.linggan.jd831.bean.SyVOListBean;
import com.linggan.jd831.bean.TaskNumEntity;
import com.linggan.jd831.bean.WenJuanBean;
import com.linggan.jd831.bean.WjTzHomeBean;
import com.linggan.jd831.ui.user.PeopleListActivity;
import com.linggan.jd831.ui.works.aiAssistant.AIAssistantListActivity;
import com.linggan.jd831.ui.works.care.CareTuiListActivity;
import com.linggan.jd831.ui.works.kaohe.KaoHeInfoActivity;
import com.linggan.jd831.ui.works.task.TaskCenListActivity;
import com.linggan.jd831.ui.works.task.TaskJdbListActivity;
import com.linggan.jd831.ui.works.yujing.YuJingCenActivity;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.MdFactoryUtils;
import com.linggan.jd831.utils.MenuUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XHttpUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;
import org.xutils.view.annotation.ContentView;
import org.xutils.view.annotation.Event;
import org.xutils.view.annotation.ViewInject;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类  名：工作人员页面
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/9/1 15:28
 * 版  权：凌感科技
 */
@ContentView(R.layout.fragment_work)
public class WorkFragment extends XBaseFragment implements RadioGroup.OnCheckedChangeListener {

    @ViewInject(R.id.tv_dwc)
    private TextView mTvDwc;
    @ViewInject(R.id.tv_yq)
    private TextView mTvYq;
    @ViewInject(R.id.tv_spth)
    private TextView mTvSpth;
    @ViewInject(R.id.tv_sp)
    private TextView mTvSp;
    @ViewInject(R.id.tv_dsp)
    private TextView mTvDsp;
    @ViewInject(R.id.mRecycle)
    private RecyclerView mRecycle;
    @ViewInject(R.id.tv_care_num)
    private TextView tvCareNum;
    @ViewInject(R.id.tv_fzr_num)
    private TextView tvFzrNum;
    @ViewInject(R.id.tv_fzr_title)
    private TextView tvFzrTitle;
    @ViewInject(R.id.tv_hb)
    private TextView tvHb;
    @ViewInject(R.id.bt_fzr)
    private RelativeLayout btFzr;
    @ViewInject(R.id.bt_care)
    private RelativeLayout btCare;
    @ViewInject(R.id.bt_zh_khl)
    private LinearLayout btZhKhl;

    @ViewInject(R.id.bt_zh_kh)
    private TextView btZhKh;
    @ViewInject(R.id.radio_group)
    private RadioGroup radioGroup;
    @ViewInject(R.id.lin_xia_qu)
    private LinearLayout mLinXiaQu;
    @ViewInject(R.id.lin_me)
    private LinearLayout mLinMe;
    @ViewInject(R.id.rb_xia_qu)
    private RadioButton mRbXiaQu;
    @ViewInject(R.id.rb_my)
    private RadioButton mRbMy;
    @ViewInject(R.id.tv_xq_dwc)
    private TextView mtvXqDwc;
    @ViewInject(R.id.tv_xq_yu_qi)
    private TextView mtvXqYuQi;
    @ViewInject(R.id.bt_zh_kh)
    private TextView mTvZhKh;
    @ViewInject(R.id.tv_gytj_title)
    private TextView mTvGaTjTitle;
    @ViewInject(R.id.mSwipe)
    private SwipeRefreshLayout swipeRefreshLayout;
    @ViewInject(R.id.mNested)
    private NestedScrollView nestedScrollView;
    @ViewInject(R.id.tv_yj_ycz)
    private TextView mTvYjYcz;
    @ViewInject(R.id.tv_yj_dcz)
    private TextView mTvYjDcz;
    @ViewInject(R.id.tv_yj_czz)
    private TextView mTvYjCzz;
    @ViewInject(R.id.tv_rw_title)
    private TextView mTvRwTitle;
    @ViewInject(R.id.tv_yj_title)
    private TextView mTvYjTitle;
    @ViewInject(R.id.lin_yj)
    private LinearLayout mLinYj;
    @ViewInject(R.id.bt_ling_qi_qi)
    private TextView tvLingQiQi;
    @ViewInject(R.id.bt_search)
    private TextView tvSearch;
    private KaoHeLvEventEntity lvEventEntity;
    private List<MenuEntity> listTask;
    private boolean isFirst = true;
    private WorkMenuAllAdapter peoInfoMenuAdapter;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mRecycle.setLayoutManager(new GridLayoutManager(getActivity(), 4));
        radioGroup.setOnCheckedChangeListener(this);
        swipeRefreshLayout.setColorSchemeColors(Color.BLUE);
//        实现滑倒顶端才能下拉刷新（即解决滑动冲突）
        nestedScrollView.getViewTreeObserver().addOnScrollChangedListener(() -> swipeRefreshLayout.setEnabled(nestedScrollView.getScrollY() == 0));
        swipeRefreshLayout.setOnRefreshListener(() -> {
            swipeRefreshLayout.setRefreshing(false);
            getMenu();
            careNum();
            getKaoHeLv();
            if (mRbXiaQu.isChecked()) {
                taskNum("other");
            } else {
                taskNum("self");
            }
            if (!StrUtils.isHuNan()) {
                getYujingList();
            }
        });
        //湖南不需要
        String rysflxdm = UserInfoUtils.getUserInfo().getRysflxdm();
        if (StrUtils.isHuNan() || !TextUtils.isEmpty(rysflxdm) && rysflxdm.equals("xtgly")) {
            tvLingQiQi.setVisibility(View.GONE);
        }
    }

    @Override
    protected void lazyLoad() {
        getMenu();
        isOpenGps();
        getKaoHeLv();
    }

    @Override
    public void onResume() {
        super.onResume();
        //数量刷新
        careNum();
        if (mRbXiaQu.isChecked()) {
            taskNum("other");
        } else if (mRbMy.isChecked()) {
            taskNum("self");
        }
        if (!StrUtils.isHuNan()) {
            getYujingList();
        }
    }

    @Event(type = View.OnClickListener.class, value = {R.id.bt_search, R.id.bt_dai_fin, R.id.bt_yuqi, R.id.bt_sp, R.id.bt_spth, R.id.bt_dsp, R.id.bt_care, R.id.bt_zh_kh, R.id.bt_zh_khl, R.id.bt_fzr, R.id.bt_xq_dai_fin, R.id.bt_xq_yu_qi, R.id.bg_dcz, R.id.bg_ycz, R.id.bg_ccz, R.id.bt_ling_qi_qi})
    private void onClick(View view) {
        Bundle bundle = new Bundle();
        if (view.getId() == R.id.bt_search) {
            //搜索
            XIntentUtil.redirectToNextActivity(getActivity(), PeopleListActivity.class, "cxType", "2");
        } else if (view.getId() == R.id.bt_dai_fin) {
            //待完成
            if (listTask != null && listTask.size() > 0) {
                bundle.putSerializable("info", (Serializable) listTask);
            }
            bundle.putString("from", "1");
            XIntentUtil.redirectToNextActivity(getActivity(), TaskCenListActivity.class, bundle);
        } else if (view.getId() == R.id.bt_sp) {
            //审批中
            if (listTask != null && listTask.size() > 0) {
                bundle.putSerializable("info", (Serializable) listTask);
            }
            bundle.putString("from", "4");
            XIntentUtil.redirectToNextActivity(getActivity(), TaskCenListActivity.class, bundle);
        } else if (view.getId() == R.id.bt_yuqi) {
            //逾期
            if (listTask != null && listTask.size() > 0) {
                bundle.putSerializable("info", (Serializable) listTask);
            }
            bundle.putString("from", "2");
            XIntentUtil.redirectToNextActivity(getActivity(), TaskCenListActivity.class, bundle);
        } else if (view.getId() == R.id.bt_spth) {
            //审批退回
            if (listTask != null && listTask.size() > 0) {
                bundle.putSerializable("info", (Serializable) listTask);
            }
            bundle.putString("from", "3");
            XIntentUtil.redirectToNextActivity(getActivity(), TaskCenListActivity.class, bundle);
        } else if (view.getId() == R.id.bt_dsp) {
            //待审批
            if (listTask != null && listTask.size() > 0) {
                bundle.putSerializable("info", (Serializable) listTask);
            }
            bundle.putString("from", "5");
            XIntentUtil.redirectToNextActivity(getActivity(), TaskCenListActivity.class, bundle);
        } else if (view.getId() == R.id.bt_care) {
            //平安关爱推荐
            XIntentUtil.redirectToNextActivity(getActivity(), CareTuiListActivity.class);
        } else if (view.getId() == R.id.bt_zh_kh || view.getId() == R.id.bt_zh_khl) {
            //考核率
            if (lvEventEntity != null) {
                bundle.putSerializable("info", lvEventEntity);
            }
            XIntentUtil.redirectToNextActivity(getActivity(), KaoHeInfoActivity.class, bundle);
        } else if (view.getId() == R.id.bt_fzr) {
            //负责人
            XIntentUtil.redirectToNextActivity(getActivity(), PeopleListActivity.class, "cxType", "1");
        } else if (view.getId() == R.id.bt_xq_dai_fin) {
            //禁毒办--待完成
            if (listTask != null && listTask.size() > 0) {
                bundle.putSerializable("info", (Serializable) listTask);
            }
            bundle.putString("type", "1");
            bundle.putString("sfyq", "0");
            XIntentUtil.redirectToNextActivity(getActivity(), TaskJdbListActivity.class, bundle);
        } else if (view.getId() == R.id.bt_xq_yu_qi) {
            //禁毒办--逾期
            if (listTask != null && listTask.size() > 0) {
                bundle.putSerializable("info", (Serializable) listTask);
            }
            bundle.putString("type", "2");
            bundle.putString("sfyq", "1");
            XIntentUtil.redirectToNextActivity(getActivity(), TaskJdbListActivity.class, bundle);
        } else if (view.getId() == R.id.bg_dcz) {
            //待处置
            XIntentUtil.redirectToNextActivity(getActivity(), YuJingCenActivity.class, "czzt", "1");
        } else if (view.getId() == R.id.bg_ccz) {
            //处置中
            XIntentUtil.redirectToNextActivity(getActivity(), YuJingCenActivity.class, "czzt", "2");
        } else if (view.getId() == R.id.bg_ycz) {
            //已处置
            XIntentUtil.redirectToNextActivity(getActivity(), YuJingCenActivity.class, "czzt", "3");
        } else if (view.getId() == R.id.bt_ling_qi_qi) {
            //GPT问答
//            XIntentUtil.redirectToNextActivity(getActivity(), GptChatActivity.class);
            XIntentUtil.redirectToNextActivity(getActivity(), AIAssistantListActivity.class);

        }
    }

    /**
     * 获取任务数量
     */
    private void taskNum(String cxfw) {
        if (!TextUtils.isEmpty(XShareCacheUtils.getInstance().getString(XConstantUtils.AUTO_TOKEN))) {
            String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "cxfw=" + cxfw);
            RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.TASK_CEN_NUM + "?query=" + jiaMiString);
            requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
            requestParams.addHeader("Origin-Content-Type", "application/json");
            XHttpUtils.getN(getActivity(), requestParams, null, false, new XHttpResponseCallBack() {
                @Override
                public void onSuccess(String result) {
                    XResultData<TaskNumEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<TaskNumEntity>>() {
                    }.getType());
                    if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                        if (cxfw.equals("other")) {
                            mtvXqYuQi.setText(xResultData.getData().getYqrw());
                            mtvXqDwc.setText(xResultData.getData().getDwcrw());
                        } else {
                            mTvYq.setText(xResultData.getData().getYqrw());
                            mTvDwc.setText(xResultData.getData().getDwcrw());
                        }
                    } else {
                        XToastUtil.showToast(getActivity(), xResultData.getErrorInfo());
                    }
                }

                @Override
                public void onFailed(int code, String failedMsg) {
                }

                @Override
                public void onFinished() {
                }
            });
        }
        //
        RequestParams requestParams1 = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.TASK_CEN_SP_NUM);
        requestParams1.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams1.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.getN(getActivity(), requestParams1, null, false, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<TaskNumEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<TaskNumEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    mTvSpth.setText(xResultData.getData().getRejectedToMe());
                    mTvDsp.setText(xResultData.getData().getMytask());
                    mTvSp.setText(xResultData.getData().getStartByMe());
                } else {
                    XToastUtil.showToast(getActivity(), xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 获取首页菜单
     */
    private void getMenu() {
        if (getActivity() != null && !XNetworkUtil.isNetworkAvailable(getActivity())) {
            getOffLineMenu();
            return;
        }
        //获取主页功能接口
        Dialog dialog = DialogUtils.showLoadDialog(getActivity(), "");
        if (getActivity() != null && dialog != null) {
            dialog.show();
        }
        XHttpUtils.getOkHttp(getActivity(), ApiHostUtils.getHostUrl() + ApiUrlsUtils.WORK_INDEX_MENU_NEW, null, false, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                try {
                    getActivity().runOnUiThread(() -> {
                        if (dialog != null) {
                            dialog.dismiss();
                        }
                        if (StrUtils.isJSONValid(result)) {
                            XResultData<HomeMenuEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<HomeMenuEntity>>() {
                            }.getType());
                            if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                                if (xResultData.getData().getRwzxs() != null && xResultData.getData().getRwzxs().size() > 0) {
                                    listTask = xResultData.getData().getRwzxs();
                                }
                                //菜单去重
                                peoInfoMenuAdapter = new WorkMenuAllAdapter(getActivity(), StrUtils.removeDuplicateWithOrder(xResultData.getData().getHomes()));
                                mRecycle.setAdapter(peoInfoMenuAdapter);
                                peoInfoMenuAdapter.setOnItemClickListener((menu, position) -> {
                                    MenuUtil.workJumpActivity(getActivity(), menu.getGnbsf(), null, "", "");
                                });
                                //首页搜索
                                if (!StrUtils.isHuNan()) {
                                    getWjTz();
                                    if (xResultData.getData().getRycxes() != null && xResultData.getData().getRycxes().size() > 0) {
                                        tvSearch.setVisibility(View.VISIBLE);
                                    } else {
                                        tvSearch.setVisibility(View.INVISIBLE);
                                    }
                                } else {
                                    tvSearch.setVisibility(View.VISIBLE);
                                }
                                //预警中心
                                if (xResultData.getData().getYjzxs() != null && xResultData.getData().getYjzxs().size() > 0) {
                                    mLinYj.setVisibility(View.VISIBLE);
                                    mTvYjTitle.setVisibility(View.VISIBLE);
                                } else {
                                    mLinYj.setVisibility(View.GONE);
                                    mTvYjTitle.setVisibility(View.GONE);
                                }
                                //首页顶部
                                if (xResultData.getData().getBanners() != null && xResultData.getData().getBanners().size() > 0) {
                                    for (int i = 0; i < xResultData.getData().getBanners().size(); i++) {
                                        MenuEntity menuEntity = xResultData.getData().getBanners().get(i);
                                        if (menuEntity != null) {
                                            if (menuEntity.getGnbsf().equals("zhkhzs")) {
                                                mTvZhKh.setText("整体任务完成率");
                                                btZhKhl.setVisibility(View.VISIBLE);
                                                btZhKh.setVisibility(View.VISIBLE);
                                                tvHb.setVisibility(View.VISIBLE);
                                            } else if (menuEntity.getGnbsf().equals("pggatj")) {
                                                mTvGaTjTitle.setText(menuEntity.getGnmc());
                                                btZhKhl.setVisibility(View.VISIBLE);
                                                btCare.setVisibility(View.VISIBLE);
                                            } else if (menuEntity.getGnbsf().equals("fzrs")) {
                                                tvFzrTitle.setText(menuEntity.getGnmc());
                                                btZhKhl.setVisibility(View.VISIBLE);
                                                btFzr.setVisibility(View.VISIBLE);
                                            }
                                        }
                                    }
                                }
                                //辖区/我的
                                if (xResultData.getData().getHomeRwzxs() != null && xResultData.getData().getHomeRwzxs().size() > 0) {
                                    mTvRwTitle.setVisibility(View.VISIBLE);
                                    if (xResultData.getData().getHomeRwzxs().size() >= 2) {
                                        for (int i = 0; i < xResultData.getData().getHomeRwzxs().size(); i++) {
                                            MenuEntity menuEntity = xResultData.getData().getHomeRwzxs().get(i);
                                            if (menuEntity != null) {
                                                if (menuEntity.getGnbsf().equals("xq")) {
                                                    mRbXiaQu.setText(menuEntity.getGnmc());
                                                } else if (menuEntity.getGnbsf().equals("wd")) {
                                                    mRbMy.setText(menuEntity.getGnmc());
                                                }
                                            }
                                        }
                                        radioGroup.setVisibility(View.VISIBLE);
                                        mLinXiaQu.setVisibility(View.GONE);
                                        mLinMe.setVisibility(View.VISIBLE);
                                        mRbXiaQu.setTypeface(Typeface.DEFAULT);
                                        mRbMy.setTypeface(Typeface.DEFAULT_BOLD);
                                        mRbMy.setChecked(true);
                                        mRbMy.setVisibility(View.VISIBLE);
                                        mRbXiaQu.setVisibility(View.VISIBLE);
                                    } else {
                                        MenuEntity menuEntity = xResultData.getData().getHomeRwzxs().get(0);
                                        if (menuEntity != null) {
                                            if (menuEntity.getGnbsf().equals("xq")) {
                                                mLinXiaQu.setVisibility(View.VISIBLE);
                                                mLinMe.setVisibility(View.GONE);
                                                mRbMy.setTypeface(Typeface.DEFAULT);
                                                mRbXiaQu.setTypeface(Typeface.DEFAULT_BOLD);
                                                mRbXiaQu.setChecked(true);
                                            } else if (menuEntity.getGnbsf().equals("wd")) {
                                                mLinXiaQu.setVisibility(View.GONE);
                                                mLinMe.setVisibility(View.VISIBLE);
                                                mRbXiaQu.setTypeface(Typeface.DEFAULT);
                                                mRbMy.setTypeface(Typeface.DEFAULT_BOLD);
                                                mRbMy.setChecked(true);
                                            }
                                        }
                                    }
                                } else {
                                    mTvRwTitle.setVisibility(View.GONE);
                                    radioGroup.setVisibility(View.GONE);
                                }
                            }
                        }
                    });
                } catch (Exception e) {
                    MdFactoryUtils.mdSave(ApiHostUtils.getHostUrl() + ApiUrlsUtils.WORK_INDEX_MENU_NEW, result, "");
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {

                MdFactoryUtils.mdSave(ApiHostUtils.getHostUrl() + ApiUrlsUtils.WORK_INDEX_MENU_NEW, failedMsg, failedMsg);
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        if (getActivity() != null && dialog != null) {
                            dialog.dismiss();
                            if (code == -1) {
                                getOffLineMenu();
                            }
                        }
                    });
                }
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 离线模式菜单
     */
    private void getOffLineMenu() {
        String areaIDs = XShareCacheUtils.getInstance().getString(XConstantUtils.CHANGE_WORK_AREA_ID);
        if (!TextUtils.isEmpty(areaIDs)) {
            if (areaIDs.startsWith("43")) {
                return;
            }
        }

        MenuEntity menuItem = new MenuEntity();
        menuItem.setId("0001");
        menuItem.setGnmc("离线排查");
        menuItem.setGnfl("");
        menuItem.setSyry("");
        menuItem.setGnbsf("lxpc");
        menuItem.setSfzzsy("");
        menuItem.setLbSl("");
        List<MenuEntity> offlineMenu = new ArrayList<>();
        offlineMenu.add(menuItem);

        peoInfoMenuAdapter = new WorkMenuAllAdapter(getActivity(), StrUtils.removeDuplicateWithOrder(offlineMenu));
        mRecycle.setAdapter(peoInfoMenuAdapter);
        peoInfoMenuAdapter.setOnItemClickListener((menu, position) -> {
            MenuUtil.workJumpActivity(getActivity(), menu.getGnbsf(), null, "", "");
        });
    }

    /**
     * 平安关爱推荐人数，负责人数
     */
    private void careNum() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.CARE_INDEX_NUM);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.getN(getActivity(), requestParams, null, false, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<TaskNumEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<TaskNumEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    tvCareNum.setText(xResultData.getData().getTjrs());
                    tvFzrNum.setText(xResultData.getData().getFzrs());
                } else {
                    XToastUtil.showToast(getActivity(), xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 获取考核率
     */
    private void getKaoHeLv() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 1);
        Date date = calendar.getTime();
        // 最后一天
        calendar.roll(Calendar.DATE, -1);
        Date date1 = calendar.getTime();
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.KAO_HE_LV);
        requestParams.addHeader("Content-Type", "text/plain");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("startDate", XDateUtil.getStringByFormat(date, XDateUtil.dateFormatYMD) + " 00:00:00");
        objectMap.put("endDate", XDateUtil.getStringByFormat(date1, XDateUtil.dateFormatYMD) + " 23:59:59");
        objectMap.put("yhlx", "1");
        String areaId = UserInfoUtils.getUserInfo().getYhXzqhdm();
        if (!TextUtils.isEmpty(areaId) && areaId.startsWith("43")) {
            objectMap.put("xzqhdm", UserInfoUtils.getUserInfo().getYhXzqhdm());
        }
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJsonN(getActivity(), requestParams, jiaMiString, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                try {
                    XResultData<KaoHeLvEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<KaoHeLvEntity>>() {
                    }.getType());
                    if (xResultData.getStatus() == 0 && xResultData.getData() != null && xResultData.getData().getList() != null && xResultData.getData().getList().size() > 0) {
                        lvEventEntity = new KaoHeLvEventEntity();
                        lvEventEntity.setList(xResultData.getData().getList());
                        for (int i = 0; i < xResultData.getData().getList().size(); i++) {
                            if (xResultData.getData().getList().get(i).getSfzh() == 1) {
                                tvHb.setText(xResultData.getData().getList().get(i).getKhv() + "");
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 预警数
     */
    private void getYujingList() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "xzqhdm=" + UserInfoUtils.getUserInfo().getYhXzqhdm());
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.YUJING_INDEX_NUM + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.getN(getActivity(), requestParams, null, false, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<TaskNumEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<TaskNumEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    mTvYjCzz.setText(xResultData.getData().getCzz());
                    mTvYjDcz.setText(xResultData.getData().getDcz());
                    mTvYjYcz.setText(xResultData.getData().getYcz());
                } else {
                    XToastUtil.showToast(getActivity(), xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 问卷通知
     */
    private void getWjTz() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.TZWJ_TC);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(getActivity(), requestParams, null, false, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<WjTzHomeBean> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<WjTzHomeBean>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().getSfHd() == 0 && xResultData.getData().getSyVOList() != null && !xResultData.getData().getSyVOList().isEmpty()) {
                        if (isFirst) {
                            DialogUtils.showWenJuanDialog(getActivity(), xResultData.getData().getSyVOList(), ((code, id) -> {
                                if (code == "1") {
                                    batch_read(xResultData.getData().getSyVOList());
                                }
                            }));
                            isFirst = false;
                        }
                        if (peoInfoMenuAdapter != null) {
                            peoInfoMenuAdapter.setRedNum("1", "tzhwj");
                        }
                    } else {
                        if (peoInfoMenuAdapter != null) {
                            peoInfoMenuAdapter.setRedNum("", "tzhwj");
                        }
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    private void batch_read(List<SyVOListBean> message) {
        Map<String, Object> objectMap = new HashMap<>();
        // 判断message是否为空
        if (message == null || message.isEmpty()) {
            return;
        }
        List<String> ids = new ArrayList<>();
        for (SyVOListBean bean : message) {
            ids.add(bean.getBh());
        }
        objectMap.put("xxbhList", ids);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.BATCH_READ);

        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));

        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.postJson(getActivity(), requestParams, jiaMiString, DialogUtils.showLoadDialog(getActivity(), ""), false, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                MdFactoryUtils.mdSave(ApiHostUtils.getHostUrl() + ApiUrlsUtils.LOGIN_URL, new Gson().toJson(objectMap), failedMsg);
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PeoTypeListEntity event) {
        if (event != null) {
            lazyLoad();
        }
    }

    /**
     * 判断是否打开定位服务
     */
    protected void isOpenGps() {
        if (!XPermissionUtil.isLocServiceEnable(getActivity())) {
            XDialogUtils.showOpenGps(getActivity(), (code, value) -> {
            });
        }
    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        switch (checkedId) {
            case R.id.rb_xia_qu:
                //辖区
                mLinXiaQu.setVisibility(View.VISIBLE);
                mLinMe.setVisibility(View.GONE);
                mRbXiaQu.setTypeface(Typeface.DEFAULT_BOLD);
                mRbMy.setTypeface(Typeface.DEFAULT);
                taskNum("other");
                break;
            case R.id.rb_my:
                //我的
                mLinXiaQu.setVisibility(View.GONE);
                mLinMe.setVisibility(View.VISIBLE);
                mRbMy.setTypeface(Typeface.DEFAULT_BOLD);
                mRbXiaQu.setTypeface(Typeface.DEFAULT);
                taskNum("self");
                break;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(WenJuanBean event) {
        if (event != null) {//更新红点
            getWjTz();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
    }
}
