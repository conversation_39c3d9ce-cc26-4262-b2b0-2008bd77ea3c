package com.linggan.jd831.ui.works.task;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.text.TextUtils;
import android.view.View;
import android.widget.RadioGroup;

import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.view.TimePickerView;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XGlideEngine;
import com.lgfzd.base.utils.XImageFileCompressEngine;
import com.lgfzd.base.utils.XPermissionUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.FileAllTypeAdapter;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.PagaInfoEntity;
import com.linggan.jd831.bean.TaskSpListEntity;
import com.linggan.jd831.bean.ZiDianEntity;
import com.linggan.jd831.databinding.ActivityTaskCareTransferBinding;
import com.linggan.jd831.ui.common.CameraActivity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.BaseZiDianDialog;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 类  名：平安关爱推荐转办
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2023/7/13 10:20
 * 版  权：LGKJ
 */
public class TaskCareTransferActivity extends XBaseActivity<ActivityTaskCareTransferBinding> implements View.OnClickListener, RadioGroup.OnCheckedChangeListener {

    private String gaBh, isPass = "0", wfblCode;
    private ImageAddUtil imageAddWcFile, imageAddWfWcFile;
    private int from = 0;

    @Override
    protected ActivityTaskCareTransferBinding getViewBinding() {
        return ActivityTaskCareTransferBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        gaBh = getIntent().getStringExtra("gaBh");
        imageAddWcFile = new ImageAddUtil(this, binding.gridWcFile);
        imageAddWfWcFile = new ImageAddUtil(this, binding.gridWfwcFile);

        StrUtils.getPhotoVideoText(binding.tvImgInfo, null);
        StrUtils.getPhotoVideoText(binding.tvVideoInfo, null);
    }

    @Override
    protected void initListener() {
        binding.btSubmit.setOnClickListener(this);
        binding.radioGroup.setOnCheckedChangeListener(this);
        binding.tvBjTime.setOnClickListener(this);
        binding.tvWfblCare.setOnClickListener(this);

        imageAddWcFile.setOnImageAddListener(() -> {
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                from = 1;
                if (code.equals("1")) {
                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setCompressEngine(new XImageFileCompressEngine()).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        });
        imageAddWfWcFile.setOnImageAddListener(() -> {
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                from = 2;
                if (code.equals("1")) {
                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setCompressEngine(new XImageFileCompressEngine()).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        });
    }

    @Override
    protected void getData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "gaBh=" + gaBh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.TASK_PAGA_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<PagaInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<PagaInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        //基础信息
                        binding.peo.tvName.setText(xResultData.getData().getXm());
                        binding.peo.tvIdCard.setText(StrUtils.hideIdCard(xResultData.getData().getZjhm()));
                        binding.peo.tvMobile.setText(xResultData.getData().getLxdh());
                        binding.peo.tvHjdArea.setText(xResultData.getData().getHjdzDzmc());
                        //推荐信息
                        if (xResultData.getData().getBfCx() != null) {
                            binding.tvTjLb.setText(xResultData.getData().getBfCx().getBfCxMc());
                        }
                        binding.tvTjCare.setText(xResultData.getData().getTjyy());
                        //关爱协同转办
                        binding.tvXtdwXzqh.setText(xResultData.getData().getDwXzqhmc());
                        binding.tvXtDw.setText(xResultData.getData().getDwmc());
                        binding.tvSqr.setText(xResultData.getData().getLrrxm());
                        binding.tvSqrDh.setText(xResultData.getData().getLxdh());
                        binding.gridImg.setAdapter(new FileAllTypeAdapter(TaskCareTransferActivity.this, xResultData.getData().getXtzbWj()));
                        binding.tvBeiZhu.setText(xResultData.getData().getBz());
                    }
                } else {
                    XToastUtil.showToast(TaskCareTransferActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.bt_submit) {
            if (ButtonUtils.isFastClick()) {
                postData();
            }
        } else if (v.getId() == R.id.tv_bj_time) {
            //办结时间
            Calendar startDate = Calendar.getInstance();
            startDate.set(1900, 0, 1);
            Calendar selectedDate = Calendar.getInstance();
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.tvBjTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setRangDate(startDate, selectedDate).setDate(selectedDate).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_wfbl_care) {
            //无法办理原因
            FactoryUtils.getBaseDataType(this, "wfblyy", result -> {
                BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, (ArrayList<ZiDianEntity>) result);
                dianDialog.setOnClickDataListener(() -> {
                    binding.tvWfblCare.setText(dianDialog.getData().getMc());
                    wfblCode = dianDialog.getData().getDm();
                });
                dianDialog.show();
            });
        }
    }

    /**
     * 提交数据
     */
    private void postData() {
        if (isPass.equals("0") && TextUtils.isEmpty(binding.tvBjTime.getText().toString())) {
            XToastUtil.showToast(this, "请选择办结时间");
            return;
        }
        if (isPass.equals("1") && TextUtils.isEmpty(binding.tvWfblCare.getText().toString())) {
            XToastUtil.showToast(this, "请选择办无法办理原因");
            return;
        }
        if (isPass.equals("1") && TextUtils.isEmpty(binding.etWfwcRemark.getText().toString())) {
            XToastUtil.showToast(this, "请输入备注");
            return;
        }
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.TASK_PAGA_SUBMIT);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("gaBh", gaBh);
            objectMap.put("sjLy", "3");
            //完成时候相关文件
            JSONArray jsonArraySP = new JSONArray();
            if (imageAddWcFile != null && imageAddWcFile.getPaths() != null && imageAddWcFile.getPaths().size() > 0) {
                for (int i = 0; i < imageAddWcFile.getPaths().size(); i++) {
                    JSONObject jsonObjectSP = new JSONObject();
                    jsonObjectSP.put("lj", imageAddWcFile.getPaths().get(i).getSavePath());
                    jsonObjectSP.put("mc", imageAddWcFile.getPaths().get(i).getOriginName());
                    jsonObjectSP.put("hz", StrUtils.getFileType(imageAddWcFile.getPaths().get(i).getOriginName()));
                    jsonObjectSP.put("dx", imageAddWcFile.getPaths().get(i).getFileSize());
                    jsonObjectSP.put("sjLy", "3");
                    jsonObjectSP.put("lrrbh", UserInfoUtils.getUserInfo().getUserId());
                    jsonObjectSP.put("bjrbh", UserInfoUtils.getUserInfo().getUserId());
                    jsonArraySP.put(jsonObjectSP);
                }
            }
            if (isPass.equals("0") && jsonArraySP.length() <= 0) {
                XToastUtil.showToast(this, "请上传相关文件");
                return;
            }
            //完成时候
            if (isPass.equals("0")) {
                objectMap.put("cxsxsj", binding.tvBjTime.getText().toString());
                objectMap.put("bz", binding.etWcRemark.getText().toString());
                objectMap.put("bfcgFj", jsonArraySP);
            } else {
                objectMap.put("bz", binding.etWfwcRemark.getText().toString());
                objectMap.put("wfblyy", wfblCode);
                //无法完成相关文件
                if (imageAddWfWcFile != null && imageAddWfWcFile.getPaths() != null && imageAddWfWcFile.getPaths().size() > 0) {
                    JSONArray jsonArrayWfwcFile = new JSONArray();
                    for (int i = 0; i < imageAddWfWcFile.getPaths().size(); i++) {
                        JSONObject jsonObjectSP = new JSONObject();
                        jsonObjectSP.put("lj", imageAddWfWcFile.getPaths().get(i).getSavePath());
                        jsonObjectSP.put("mc", imageAddWfWcFile.getPaths().get(i).getOriginName());
                        jsonObjectSP.put("hz", StrUtils.getFileType(imageAddWfWcFile.getPaths().get(i).getOriginName()));
                        jsonObjectSP.put("dx", imageAddWfWcFile.getPaths().get(i).getFileSize());
                        jsonObjectSP.put("sjLy", "3");
                        jsonArrayWfwcFile.put(jsonObjectSP);
                    }
                    objectMap.put("wfblFj", jsonArrayWfwcFile);
                }
            }
        } catch (JSONException e) {
        }
        binding.btSubmit.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), true, true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(TaskCareTransferActivity.this, getString(R.string.submit_scess));
                    EventBus.getDefault().post(new TaskSpListEntity());
                    finish();
                } else {
                    XToastUtil.showToast(TaskCareTransferActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSubmit.setEnabled(true);
            }

            @Override
            public void onFinished() {
                binding.btSubmit.setEnabled(true);
            }
        });
    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        switch (checkedId) {
            case R.id.rb_yes:
                //通过
                binding.linData.setVisibility(View.VISIBLE);
                binding.linNo.setVisibility(View.GONE);
                isPass = "0";
                //
                wfblCode = "";
                binding.tvWfblCare.setText("");
                binding.etWfwcRemark.setText("");
                break;
            case R.id.rb_no:
                //不通过
                isPass = "1";
                binding.linData.setVisibility(View.GONE);
                binding.linNo.setVisibility(View.VISIBLE);
                //
                binding.tvBjTime.setText("");
                binding.etWcRemark.setText("");
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.REQUEST_CAMERA:
                case PictureConfig.CHOOSE_REQUEST:
                    ArrayList<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                    if (null != selectList) {
                        for (LocalMedia media : selectList) {
                            if (media.isCompressed()) {
                                uploadFile(media.getCompressPath());
                            } else if (!TextUtils.isEmpty(media.getRealPath())) {
                                uploadFile(media.getRealPath());
                            }
                        }
                    }
                    break;
                case 1:
                    //图片
                    uploadFile(data.getStringExtra("path"));
                    break;

            }
        }
    }

    //上传
    private void uploadFile(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        if (from == 1) {
                            imageAddWcFile.addImage(xResultData.getData().get(0));
                            imageAddWcFile.notifyData();
                        } else if (from == 2) {
                            imageAddWfWcFile.addImage(xResultData.getData().get(0));
                            imageAddWfWcFile.notifyData();
                        }
                    }
                } else {
                    XToastUtil.showToast(TaskCareTransferActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (from == 1 || from == 2) {
            //调用系统相机
            Intent intent = new Intent(this, CameraActivity.class);
            intent.putExtra("info", "info");
            startActivityForResult(intent, 1);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        for (int i = 0; i < permissions.length; i++) {
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED || ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED || ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED || ActivityCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                XPermissionUtil.initPermission(this, new String[]{android.Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
            }
        }
    }
}
