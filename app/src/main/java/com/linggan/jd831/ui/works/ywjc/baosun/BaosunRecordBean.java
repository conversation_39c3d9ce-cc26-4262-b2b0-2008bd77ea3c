package com.linggan.jd831.ui.works.ywjc.baosun;

import com.google.gson.annotations.SerializedName;
import java.io.Serializable;

/**
 * 报损记录实体类
 */
public class BaosunRecordBean implements Serializable {
    /** 入库记录ID */
    @SerializedName("id")
    private Long id;
    
    /** 经办人姓名 */
    @SerializedName("jdbXm")
    private String jdbXm;
    
    /** 数量(ml) */
    @SerializedName("sl")
    private Float sl;
    
    /** 入库时间 */
    @SerializedName("bbsj")
    private String bbsj;
    
    /** 报损原因 */
    @SerializedName("bsyy")
    private String bsyy;

    public String getZp() {
        return zp;
    }

    public void setZp(String zp) {
        this.zp = zp;
    }

    @SerializedName("zp")
    private String zp;

    // 无参构造函数
    public BaosunRecordBean() {
    }

    // 全参构造函数
    public BaosunRecordBean(Long id, String jdbXm, Float sl, String bbsj, String bsyy) {
        this.id = id;
        this.jdbXm = jdbXm;
        this.sl = sl;
        this.bbsj = bbsj;
        this.bsyy = bsyy;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getJdbXm() {
        return jdbXm;
    }

    public void setJdbXm(String jdbXm) {
        this.jdbXm = jdbXm;
    }

    public Float getSl() {
        return sl;
    }

    public void setSl(Float sl) {
        this.sl = sl;
    }

    public String getBbsj() {
        return bbsj;
    }

    public void setBbsj(String bbsj) {
        this.bbsj = bbsj;
    }

    public String getBsyy() {
        return bsyy;
    }

    public void setBsyy(String bsyy) {
        this.bsyy = bsyy;
    }

    @Override
    public String toString() {
        return "BaosunRecordBean{" +
                "id=" + id +
                ", jdbXm='" + jdbXm + '\'' +
                ", sl=" + sl +
                ", bbsj='" + bbsj + '\'' +
                ", bsyy='" + bsyy + '\'' +
                '}';
    }
}