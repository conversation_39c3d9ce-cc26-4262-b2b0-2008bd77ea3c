package com.linggan.jd831.ui.user;


import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.view.TimePickerView;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XGlideEngine;
import com.lgfzd.base.utils.XImageFileCompressEngine;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.CodeNameEntity;
import com.linggan.jd831.bean.LoginEntity;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.PeoTypeListEntity;
import com.linggan.jd831.bean.PeopleInfoEntity;
import com.linggan.jd831.bean.WorksEntity;
import com.linggan.jd831.bean.ZiDianEntity;
import com.linggan.jd831.databinding.ActivityPeopleAddBinding;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.IdCardCheckUtils;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.PhotoUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.utils.XImageUtils;
import com.linggan.jd831.widget.AreaPickerView4Dialog;
import com.linggan.jd831.widget.AreaPickerView5Dialog;
import com.linggan.jd831.widget.BaseWorkDialog;
import com.linggan.jd831.widget.BaseZiDianDialog;
import com.linggan.jd831.widget.CodeNameDialog;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;

/**
 * 类  名：人员新增
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/8/31 15:35
 * 版  权：凌感科技
 */
public class PeopleAddActivity extends XBaseActivity<ActivityPeopleAddBinding> implements View.OnClickListener {

    private String type, headUrl, workId, yhXzqhdm, edit, xyrBh, name, other, sheAnCode;
    private ImageAddUtil imageAddImg;
    private String shengId, cityId, quId, zhenId, jieId;
    private String shengIdh, cityIdh, quIdh, zhenIdh, jieIdh, typeCode, idCardTypeCode, sexCode;
    private int from = 0, swAndKsjyCode = 0;
    private PeopleInfoEntity peopleInfoEntity;
    private Uri photoUri;
    private File cameraPhoto;

    @Override
    protected ActivityPeopleAddBinding getViewBinding() {
        return ActivityPeopleAddBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        type = getIntent().getStringExtra("type");
        name = getIntent().getStringExtra("name");
        edit = getIntent().getStringExtra("edit");
        typeCode = getIntent().getStringExtra("code");
        other = getIntent().getStringExtra("other");
        StrUtils.getPhotoVideoText(binding.tvImgInfo, null);
        //新增时候展示
        if (TextUtils.isEmpty(edit)) {
            LoginEntity loginEntity = UserInfoUtils.getUserInfo();
            if (loginEntity != null) {
                yhXzqhdm = loginEntity.getYhXzqhdm();
                binding.tvWork.setText(loginEntity.getUserName());
                workId = loginEntity.getUserId();
                binding.btPeoType.setEnabled(true);
                if (!TextUtils.isEmpty(loginEntity.getXzqhdj()) && loginEntity.getXzqhdj().equals("5")) {
                    binding.tvHjdArea.setEnabled(false);
                    //根据最后一级地区地址获取整个地区code路径
                    FactoryUtils.getCodeAreaName(this, loginEntity.getYhXzqhdm(), result -> {
                        if (result.size() >= 1) {
                            shengIdh = result.get(0).getXzqhdm();
                        }
                        if (result.size() >= 2) {
                            cityIdh = result.get(1).getXzqhdm();
                        }
                        if (result.size() >= 3) {
                            quIdh = result.get(2).getXzqhdm();
                        }
                        if (result.size() >= 4) {
                            zhenIdh = result.get(3).getXzqhdm();
                        }
                        if (result.size() >= 5) {
                            jieIdh = result.get(4).getXzqhdm();
                        }
                        binding.tvHjdArea.setText(result.get(0).getXzqhmc() + result.get(1).getXzqhmc() + result.get(2).getXzqhmc() + result.get(3).getXzqhmc() + result.get(4).getXzqhmc());
                    });
                }
            }
        } else {
            setTitle("人员信息编辑");
        }
        binding.tvPeoType.setText(name);
        //选了吸毒和涉毒
        if (!TextUtils.isEmpty(other)) {
            binding.linSdskTime.setVisibility(View.VISIBLE);
        }
        //这里表示选择了吸毒人员类型
        if (!TextUtils.isEmpty(typeCode)) {
            uiShow(typeCode);
        }
        EventBus.getDefault().register(this);
    }

    @Override
    protected void initListener() {
        binding.btSure.setOnClickListener(this);
        binding.tvArea.setOnClickListener(this);
        binding.tvHjdArea.setOnClickListener(this);
        binding.tvJdTime.setOnClickListener(this);
        binding.tvBdTime.setOnClickListener(this);
        binding.tvWork.setOnClickListener(this);
        binding.tvIdCardType.setOnClickListener(this);
        binding.btPeoType.setOnClickListener(this);
        binding.ivHead.setOnClickListener(this);
        binding.tvChaHuoTime.setOnClickListener(this);
        binding.tvRyTime.setOnClickListener(this);
        binding.tvZhiXingTime.setOnClickListener(this);
        binding.tvRusuoTime.setOnClickListener(this);
        binding.tvSex.setOnClickListener(this);
        binding.tvSdskTime.setOnClickListener(this);
        binding.tvSheAnType.setOnClickListener(this);
        binding.tvScxdTime.setOnClickListener(this);
        binding.tvBrith.setOnClickListener(this);

        binding.tvIdCard.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(binding.tvIdCard.getText().toString()) && binding.tvIdCard.getText().toString().length() == 18) {
                    if (!IdCardCheckUtils.check(binding.tvIdCard.getText().toString())) {
                        binding.tvBrith.setText("");
                        XToastUtil.showToast(PeopleAddActivity.this, "请输入正确的身份证号");
                        return;
                    }
                    binding.tvBrith.setText(IdCardCheckUtils.getBirthday(binding.tvIdCard.getText().toString()));
                }
            }
        });
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.bt_sure) {
            if (ButtonUtils.isFastClick()) {
                loadData();
            }
        } else if (v.getId() == R.id.iv_head) {
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                from = 1;
                if (code.equals("1")) {
                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setCompressEngine(new XImageFileCompressEngine()).setMaxSelectNum(1).setMinSelectNum(1).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        } else if (v.getId() == R.id.tv_area) {
            //居住地
            if (ButtonUtils.isFastClick()) {
                AreaPickerView5Dialog areaPickerView = new AreaPickerView5Dialog(this, "999999");
                areaPickerView.show();
                areaPickerView.setAreaPickerViewCallback((areaName, ids) -> {
                    Log.i("ppp", "onClick: " + new Gson().toJson(ids));
//                    shengId = ids.get(0);
//                    cityId = ids.get(1);
//                    quId = ids.get(2);
//                    zhenId = ids.get(3);
//                    jieId = ids.get(4);
                    if (ids.size() >= 1) {
                        shengId = ids.get(0);
                    }
                    if (ids.size() >= 2) {
                        cityId = ids.get(1);
                    }
                    if (ids.size() >= 3) {
                        quId = ids.get(2);
                    }
                    if (ids.size() >= 4) {
                        zhenId = ids.get(3);
                    }
                    if (ids.size() >= 5) {
                        jieId = ids.get(4);
                    }
                    binding.tvArea.setText(StrUtils.listToStringText(areaName));
                });
            }
        } else if (v.getId() == R.id.tv_hjd_area) {
            //户籍地行政区域 --2023-11-09修改为同时支持平级选择--区分湖南与非湖南
            if (ButtonUtils.isFastClick()) {
                String areaIds = UserInfoUtils.getUserInfo().getYhXzqhdm();
                if (!TextUtils.isEmpty(areaIds) && !areaIds.startsWith("43")) {
                    AreaPickerView4Dialog areaPickerView = new AreaPickerView4Dialog(this, "", "");
                    areaPickerView.show();
                    areaPickerView.setAreaPickerViewCallback((areaName, ids) -> {
                        if (ids != null) {
                            //根据最后一级地区地址获取整个地区code路径
                            String code = (ids.size() > 1) ? ids.get(ids.size() - 1) : ids.get(0);
                            FactoryUtils.getCodeAreaName(this, code, result -> {
                                binding.tvHjdArea.setText(StrUtils.getShowAreaListText(result));
                                if (result.size() >= 1) {
                                    shengIdh = result.get(0).getXzqhdm();
                                }
                                if (result.size() >= 2) {
                                    cityIdh = result.get(1).getXzqhdm();
                                }
                                if (result.size() >= 3) {
                                    quIdh = result.get(2).getXzqhdm();
                                }
                                if (result.size() >= 4) {
                                    zhenIdh = result.get(3).getXzqhdm();
                                }
                                if (result.size() >= 5) {
                                    jieIdh = result.get(4).getXzqhdm();
                                }
                            });
                        }
                    });
                } else {
                    AreaPickerView4Dialog areaPickerView = new AreaPickerView4Dialog(this, UserInfoUtils.getUserInfo().getYhXzqhdm(), "peo_add");
                    areaPickerView.show();
                    areaPickerView.setAreaPickerViewCallback((areaName, ids) -> {
                        if (ids != null) {
                            //根据最后一级地区地址获取整个地区code路径
                            String code = (ids.size() > 1) ? ids.get(ids.size() - 1) : ids.get(0);
                            FactoryUtils.getCodeAreaName(this, code, result -> {
                                binding.tvHjdArea.setText(StrUtils.getShowAreaListText(result));
                                if (result.size() >= 1) {
                                    shengIdh = result.get(0).getXzqhdm();
                                }
                                if (result.size() >= 2) {
                                    cityIdh = result.get(1).getXzqhdm();
                                }
                                if (result.size() >= 3) {
                                    quIdh = result.get(2).getXzqhdm();
                                }
                                if (result.size() >= 4) {
                                    zhenIdh = result.get(3).getXzqhdm();
                                }
                                if (result.size() >= 5) {
                                    jieIdh = result.get(4).getXzqhdm();
                                }
                            });
                        }
                    });
                }
            }
        } else if (v.getId() == R.id.tv_work) {
            //2023-11-09修改为可查询同时支持平级选择
            String areaIds = UserInfoUtils.getUserInfo().getYhXzqhdm();
            if (!TextUtils.isEmpty(areaIds) && areaIds.startsWith("43")) {
                FactoryUtils.getWorkList(this, result -> {
                    BaseWorkDialog dianDialog = new BaseWorkDialog(this, (ArrayList<WorksEntity>) result);
                    dianDialog.setOnClickDataListener(() -> {
                        binding.tvWork.setText(dianDialog.getData().getYhXm());
                        workId = dianDialog.getData().getId();
                    });
                    dianDialog.show();
                });
            } else {
                XIntentUtil.redirectToNextActivity(this, WorkChoiceListActivity.class);
            }
        } else if (v.getId() == R.id.tv_bd_time) {
            //请选择报到日期
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.tvBdTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_jd_time) {
            //请选择决定日期
            Calendar startDate = Calendar.getInstance();
            startDate.set(1900, 0, 1);
            Calendar selectedDate = Calendar.getInstance();
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.tvJdTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setRangDate(startDate, selectedDate).setDate(selectedDate).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.bt_peo_type) {
            //人员类型--修改为不能更换了
//            sendType();
        } else if (v.getId() == R.id.tv_id_card_type) {
            //证件类型
            FactoryUtils.getBaseDataType(this, "zjlx", result -> {
                BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, (ArrayList<ZiDianEntity>) result);
                dianDialog.setOnClickDataListener(() -> {
                    binding.tvIdCardType.setText(dianDialog.getData().getMc());
                    idCardTypeCode = dianDialog.getData().getDm();
                });
                dianDialog.show();
            });
        } else if (v.getId() == R.id.tv_sex) {
            //性别
            FactoryUtils.getBaseDataType(this, "xbdm", result -> {
                BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, (ArrayList<ZiDianEntity>) result);
                dianDialog.setOnClickDataListener(() -> {
                    binding.tvSex.setText(dianDialog.getData().getMc());
                    sexCode = dianDialog.getData().getDm();
                });
                dianDialog.show();
            });
        } else if (v.getId() == R.id.tv_rusuo_time) {
            //入所时间
            Calendar startDate = Calendar.getInstance();
            startDate.set(1900, 0, 1);
            Calendar selectedDate = Calendar.getInstance();
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.tvRusuoTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setRangDate(startDate, selectedDate).setDate(selectedDate).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_ry_time) {
            //入狱时间
            Calendar startDate = Calendar.getInstance();
            startDate.set(1900, 0, 1);
            Calendar selectedDate = Calendar.getInstance();
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.tvRyTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setRangDate(startDate, selectedDate).setDate(selectedDate).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_cha_huo_time) {
            //查获时间
            Calendar startDate = Calendar.getInstance();
            startDate.set(1900, 0, 1);
            Calendar selectedDate = Calendar.getInstance();
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.tvChaHuoTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setRangDate(startDate, selectedDate).setDate(selectedDate).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_zhi_xing_time) {
            //执行时间
            Calendar startDate = Calendar.getInstance();
            startDate.set(1900, 0, 1);
            Calendar selectedDate = Calendar.getInstance();
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.tvZhiXingTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setRangDate(startDate, selectedDate).setDate(selectedDate).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_sdsk_time) {
            //涉毒管控时间
            Calendar startDate = Calendar.getInstance();
            startDate.set(1900, 0, 1);
            Calendar selectedDate = Calendar.getInstance();
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.tvSdskTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setRangDate(startDate, selectedDate).setDate(selectedDate).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_she_an_type) {
            //涉案类型
            CodeNameDialog codeNameDialog = new CodeNameDialog(this, StrUtils.getSheAnTypeList());
            codeNameDialog.setOnClickDataListener(() -> {
                binding.tvSheAnType.setText(codeNameDialog.getCode().getName());
                sheAnCode = codeNameDialog.getCode().getCode();
            });
            codeNameDialog.show();
        } else if (v.getId() == R.id.tv_scxd_time) {
            //首次吸毒时间
            Calendar startDate = Calendar.getInstance();
            startDate.set(1900, 0, 1);
            Calendar selectedDate = Calendar.getInstance();
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.tvScxdTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setRangDate(startDate, selectedDate).setDate(selectedDate).setSubmitColor(R.color.black).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_brith) {
            //出生日期
            Calendar startDate = Calendar.getInstance();
            startDate.set(1900, 0, 1);
            Calendar selectedDate = Calendar.getInstance();
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.tvBrith.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setRangDate(startDate, selectedDate).setDate(selectedDate).setSubmitColor(R.color.black).build();
            pvTime.show();
        }
    }

    @Override
    protected void getData() {
        imageAddImg = new ImageAddUtil(this, binding.gridImg);
        imageAddImg.setMax(4);
        imageAddImg.setOnImageAddListener(() -> {
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                from = 2;
                if (code.equals("1")) {
                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).setImageEngine(XGlideEngine.createGlideEngine())
                            .setCompressEngine(new XImageFileCompressEngine()).isDisplayCamera(false).setMinSelectNum(1).setMaxSelectNum(4 - imageAddImg.getPaths().size()).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        });
        /**
         * 编辑时候回显
         */
        peopleInfoEntity = (PeopleInfoEntity) getIntent().getSerializableExtra("info");
        if (peopleInfoEntity != null) {
            setTitle("人员信息编辑");
            XImageUtils.load(this, peopleInfoEntity.getXp(), binding.ivHead, R.mipmap.ic_xiangji);
            if (peopleInfoEntity.getJds() != null && peopleInfoEntity.getJds().size() > 0) {
                for (int i = 0; i < peopleInfoEntity.getJds().size(); i++) {
                    OssFileEntity ossFileEntity = new OssFileEntity();
                    ossFileEntity.setSavePath(peopleInfoEntity.getJds().get(i).getLj());
                    ossFileEntity.setOriginName(peopleInfoEntity.getJds().get(i).getMc());
                    ossFileEntity.setFileSize(peopleInfoEntity.getJds().get(i).getDx());
                    imageAddImg.addImage(ossFileEntity);
                    imageAddImg.notifyData();
                }
            }
            binding.etName.setText(peopleInfoEntity.getXm());
            headUrl = peopleInfoEntity.getXp();
            xyrBh = peopleInfoEntity.getXyrbh();
            if (peopleInfoEntity.getRyyjzt() != null) {
                binding.tvPeoType.setText(peopleInfoEntity.getRyyjzt().getName());
                typeCode = peopleInfoEntity.getRyyjzt().getCode();
                uiShow(peopleInfoEntity.getRyyjzt().getCode());
            } else {
                if (peopleInfoEntity.getYjztbhList() != null && peopleInfoEntity.getYjztbhList().size() > 0) {
                    binding.linSdskTime.setVisibility(View.VISIBLE);
                    binding.tvPeoType.setText(peopleInfoEntity.getYjztbhList().get(0).getRyyjzt().getName());
                    List<String> list = new ArrayList<>();
                    for (int i = 0; i < peopleInfoEntity.getYjztbhList().size(); i++) {
                        list.add(peopleInfoEntity.getYjztbhList().get(i).getYjztbh());
                    }
                    type = StrUtils.listToString(list);
                }
            }
            binding.iv.setVisibility(View.GONE);
            binding.btPeoType.setEnabled(false);

            binding.tvIdCard.setText(peopleInfoEntity.getZjhm());
            binding.etMobile.setText(peopleInfoEntity.getLxdh());
            binding.etJzdz.setText(peopleInfoEntity.getSjjzdDzmc());
            binding.etHjdz.setText(peopleInfoEntity.getHjdzDzmc());
            if (peopleInfoEntity.getCyzjdm() != null) {
                binding.tvIdCardType.setText(peopleInfoEntity.getCyzjdm().getName());
                idCardTypeCode = peopleInfoEntity.getCyzjdm().getCode();
            }
            binding.tvWork.setText(peopleInfoEntity.getFzrxm());
            workId = peopleInfoEntity.getFzrbh();
            shengId = peopleInfoEntity.getJzdShenQhdm();
            cityId = peopleInfoEntity.getJzdShiQhdm();
            quId = peopleInfoEntity.getJzdQxQhdm();
            zhenId = peopleInfoEntity.getJzdXzQhdm();
            jieId = peopleInfoEntity.getJzdSqQhdm();

            shengIdh = peopleInfoEntity.getHjdShenQhdm();
            cityIdh = peopleInfoEntity.getHjdShiQhdm();
            quIdh = peopleInfoEntity.getHjdQxQhdm();
            zhenIdh = peopleInfoEntity.getHjdXzQhdm();
            jieIdh = peopleInfoEntity.getHjdSqQhdm();

            if (peopleInfoEntity.getXbdm() != null) {
                sexCode = peopleInfoEntity.getXbdm().getCode();
                binding.tvSex.setText(peopleInfoEntity.getXbdm().getName());
            }
            binding.tvJdTime.setText(peopleInfoEntity.getJdsj());
            binding.tvBdTime.setText(peopleInfoEntity.getBdrq());
            binding.tvRyTime.setText(peopleInfoEntity.getRyrq());
            binding.tvRusuoTime.setText(peopleInfoEntity.getRsrq());
            binding.tvChaHuoTime.setText(peopleInfoEntity.getChrq());
            binding.tvZhiXingTime.setText(peopleInfoEntity.getZxsj());
            binding.tvSdskTime.setText(peopleInfoEntity.getSdgksj());
            binding.tvScxdTime.setText(peopleInfoEntity.getCcxdsj());
            binding.tvBrith.setText(peopleInfoEntity.getCsrq());
            if (peopleInfoEntity.getSalx() != null) {
                binding.tvSheAnType.setText(peopleInfoEntity.getSalx().getName());
                sheAnCode = peopleInfoEntity.getSalx().getCode();
            }
            //编辑时候地区回显
            if (!TextUtils.isEmpty(peopleInfoEntity.getJzdSqQhdm())) {
                FactoryUtils.getCodeAreaName(this, peopleInfoEntity.getJzdSqQhdm(), result -> {
                    binding.tvArea.setText(StrUtils.getShowAreaListText(result));
                });
            } else if (!TextUtils.isEmpty(peopleInfoEntity.getJzdXzQhdm())) {
                FactoryUtils.getCodeAreaName(this, peopleInfoEntity.getJzdXzQhdm(), result -> {
                    binding.tvArea.setText(StrUtils.getShowAreaListText(result));
                });
            }
            if (!TextUtils.isEmpty(peopleInfoEntity.getHjdSqQhdm())) {
                FactoryUtils.getCodeAreaName(this, peopleInfoEntity.getHjdSqQhdm(), result -> {
                    binding.tvHjdArea.setText(StrUtils.getShowAreaListText(result));
                });
            }
            //死亡5、看守羁押1--没得电话（2024-05-17）
            if (peopleInfoEntity.getRyejzt() != null && peopleInfoEntity.getRyejzt().size() > 0) {
                for (int i = 0; i < peopleInfoEntity.getRyejzt().size(); i++) {
                    CodeNameEntity codeName = peopleInfoEntity.getRyejzt().get(0);
                    if (codeName != null && (TextUtils.equals("1", codeName.getCode()) || TextUtils.equals("5", codeName.getCode()))) {
                        binding.ivLxfsTag.setVisibility(View.INVISIBLE);
                        swAndKsjyCode = 1;
                    }
                }
            }
        }
    }

    /**
     * 数据提交
     */
    private void loadData() {
        if (TextUtils.isEmpty(binding.tvPeoType.getText().toString())) {
            XToastUtil.showToast(this, "请选择评估周期");
            return;
        }
        if (!TextUtils.isEmpty(edit)) {
            if (!TextUtils.isEmpty(typeCode) && (typeCode.equals("1") || typeCode.equals("2"))) {
                if (TextUtils.isEmpty(headUrl)) {
                    XToastUtil.showToast(this, "请上传照片");
                    return;
                }
            }
        } else {
            if (TextUtils.isEmpty(headUrl)) {
                XToastUtil.showToast(this, "请上传照片");
                return;
            }
        }

        if (TextUtils.isEmpty(binding.etName.getText().toString())) {
            XToastUtil.showToast(this, "请输入姓名");
            return;
        }
        if (TextUtils.isEmpty(binding.tvSex.getText().toString())) {
            XToastUtil.showToast(this, "请选择性别");
            return;
        }
        if (TextUtils.isEmpty(binding.tvIdCardType.getText().toString())) {
            XToastUtil.showToast(this, "请选择证件类型");
            return;
        }
        if (TextUtils.isEmpty(binding.tvIdCard.getText().toString())) {
            XToastUtil.showToast(this, "请输入证件号码");
            return;
        }
        if (!TextUtils.isEmpty(typeCode) && !typeCode.equals("11") && !typeCode.equals("3") && swAndKsjyCode != 1 &&
                TextUtils.isEmpty(binding.etMobile.getText().toString())) {
            XToastUtil.showToast(this, "请输入联系方式");
            return;
        }

        if (TextUtils.isEmpty(binding.tvArea.getText().toString())) {
            XToastUtil.showToast(this, "请选择居住地");
            return;
        }
        if (TextUtils.isEmpty(binding.etJzdz.getText().toString())) {
            XToastUtil.showToast(this, "请输入居住地址");
            return;
        }
        if (TextUtils.isEmpty(binding.tvHjdArea.getText().toString())) {
            XToastUtil.showToast(this, "请选择户籍地");
            return;
        }
        if (TextUtils.isEmpty(binding.etHjdz.getText().toString())) {
            XToastUtil.showToast(this, "请输入户籍地址");
            return;
        }
        if (TextUtils.isEmpty(binding.tvWork.getText().toString())) {
            XToastUtil.showToast(this, "请选择管理人员");
            return;
        }
        if (binding.tvSdskTime.getVisibility() == View.VISIBLE && TextUtils.isEmpty(binding.tvWork.getText().toString())) {
            XToastUtil.showToast(this, "请选择涉毒管控时间");
            return;
        }
        if (!TextUtils.isEmpty(typeCode)) {
            if (showToastError(typeCode)) {
                return;
            }
        }
        RequestParams requestParams;
        if (TextUtils.isEmpty(edit)) {
            requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PERSON_INFO_ADD);
        } else {
            requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PERSON_INFO_UPDATE);
        }
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("xm", binding.etName.getText().toString());
            objectMap.put("xp", headUrl);
            objectMap.put("zjhm", binding.tvIdCard.getText().toString().trim());
            objectMap.put("lxdh", binding.etMobile.getText().toString().trim());
            objectMap.put("sjjzdDzmc", binding.etJzdz.getText().toString().trim());
            objectMap.put("hjdzDzmc", binding.etHjdz.getText().toString().trim());
            objectMap.put("cyzjdm", idCardTypeCode);
            objectMap.put("fzrxm", binding.tvWork.getText().toString());
            objectMap.put("fzrbh", workId);
            objectMap.put("xbdm", sexCode);
            if (!TextUtils.isEmpty(type)) {
                List result = Arrays.asList(type.split(","));
                if (result != null && result.size() > 0) {
                    JSONArray jsonArray = new JSONArray();
                    for (int i = 0; i < result.size(); i++) {
                        jsonArray.put(result.get(i));
                    }
                    objectMap.put("ryyjzt", jsonArray);
                }
            }
            objectMap.put("sdgksj", binding.tvSdskTime.getText().toString());
            if (TextUtils.isEmpty(yhXzqhdm)) {
                objectMap.put("hjdShenQhdm", shengIdh);
                objectMap.put("hjdShiQhdm", cityIdh);
                objectMap.put("hjdQxQhdm", quIdh);
                objectMap.put("hjdXzQhdm", zhenIdh);
                objectMap.put("hjdSqQhdm", jieIdh);

                objectMap.put("jzdShenQhdm", shengId);
                objectMap.put("jzdShiQhdm", cityId);
                objectMap.put("jzdQxQhdm", quId);
                objectMap.put("jzdXzQhdm", zhenId);
                objectMap.put("jzdSqQhdm", jieId);
            } else {
                objectMap.put("hjdShenQhdm", shengIdh);
                objectMap.put("hjdShiQhdm", cityIdh);
                objectMap.put("hjdQxQhdm", quIdh);
                objectMap.put("hjdXzQhdm", zhenIdh);
                objectMap.put("hjdSqQhdm", jieIdh);
                objectMap.put("jzdShenQhdm", shengId);
                objectMap.put("jzdShiQhdm", cityId);
                objectMap.put("jzdQxQhdm", quId);
                objectMap.put("jzdXzQhdm", zhenId);
                objectMap.put("jzdSqQhdm", jieId);
            }
            objectMap.put("jdsj", binding.tvJdTime.getText().toString());
            objectMap.put("bdrq", binding.tvBdTime.getText().toString());
            objectMap.put("xyrbh", xyrBh);
            objectMap.put("ryrq", binding.tvRyTime.getText().toString());
            objectMap.put("rsrq", binding.tvRusuoTime.getText().toString());
            objectMap.put("chrq", binding.tvChaHuoTime.getText().toString());
            objectMap.put("zxsj", binding.tvZhiXingTime.getText().toString());
//            objectMap.put("salx", sheAnCode);//服刑人员--涉案类型去掉
            objectMap.put("ccxdsj", binding.tvScxdTime.getText().toString());
            objectMap.put("csrq", binding.tvBrith.getText().toString());
            JSONArray jsonArray = new JSONArray();
            if (imageAddImg != null && imageAddImg.getPaths() != null && imageAddImg.getPaths().size() > 0) {
                for (int i = 0; i < imageAddImg.getPaths().size(); i++) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("lj", imageAddImg.getPaths().get(i).getSavePath());
                    jsonObject.put("mc", imageAddImg.getPaths().get(i).getOriginName());
                    jsonObject.put("hz", StrUtils.getFileType(imageAddImg.getPaths().get(i).getSavePath()));
                    jsonObject.put("dx", imageAddImg.getPaths().get(i).getFileSize());
                    jsonArray.put(jsonObject);
                }
            }
            if ((!TextUtils.isEmpty(typeCode) && typeCode.equals("1") || !TextUtils.isEmpty(typeCode) && typeCode.equals("2")) && jsonArray.length() <= 0) {
                XToastUtil.showToast(this, "请上传决定书");
                return;
            } else {
                objectMap.put("jds", jsonArray);
            }
        } catch (JSONException e) {
        }
        binding.btSure.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), true, true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(PeopleAddActivity.this, getString(R.string.save_sucess));
                    EventBus.getDefault().post(new PeoTypeListEntity());
                    finish();
                } else {
                    XToastUtil.showToast(PeopleAddActivity.this, xResultData.getErrorInfo());
                    binding.btSure.setEnabled(true);
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSure.setEnabled(true);
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.REQUEST_CAMERA:
                case PictureConfig.CHOOSE_REQUEST:
                    if (data != null) {
                        ArrayList<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                        if (null != selectList) {
                            for (LocalMedia media : selectList) {
                                if (media.isCompressed()) {
                                    uploadFile(media.getCompressPath());
                                } else if (!TextUtils.isEmpty(media.getRealPath())) {
                                    uploadFile(media.getRealPath());
                                }
                            }
                        }
                    }
                    break;
                case 1:
                    //图片
                    try {
                        boolean isFinish = XFileUtil.bitmapToFilePathNo(this, BitmapFactory.decodeFile(cameraPhoto.getAbsolutePath()), photoUri, cameraPhoto);
                        if (isFinish) {
                            uploadFile(cameraPhoto.getAbsolutePath());
                        }
                    } catch (Exception e) {
                    }
                    break;
            }
        }
    }

    //上传
    private void uploadFile(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        if (from == 2) {
                            imageAddImg.addImage(xResultData.getData().get(0));
                            imageAddImg.notifyData();
                        } else {
                            headUrl = xResultData.getData().get(0).getSavePath();
                            XImageUtils.load(PeopleAddActivity.this, headUrl, binding.ivHead);
                            XToastUtil.showToast(PeopleAddActivity.this, getString(R.string.upload_sucess));
                        }
                    }
                } else {
                    XToastUtil.showToast(PeopleAddActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (from != 0) {
            createFileMuLu();
            if (cameraPhoto != null) {
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                photoUri = XFileUtil.getUriFromFile(this, cameraPhoto);
                intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                startActivityForResult(intent, 1);
            }
        }
    }

    /**
     * 创建文件
     */
    private void createFileMuLu() {
        if ((ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED)
                && (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED)) {
            cameraPhoto = new File(PhotoUtil.createPath(this));
            try {
                if (cameraPhoto.exists()) {
                    cameraPhoto.delete();
                }
                cameraPhoto.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            // 权限未授权，需要申请权限
            requestPermission(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        }
    }

    /**
     * 页面展示
     *
     * @param code 2024-01-18需求：全国 吸毒人员新增，初次吸毒时间非必填（湖南--大同必填）
     */
    private void uiShow(String code) {
        String areaIds = UserInfoUtils.getUserInfo().getYhXzqhdm();
        if (!TextUtils.isEmpty(areaIds) && areaIds.startsWith("43") || !TextUtils.isEmpty(areaIds) && areaIds.startsWith("1402")) {
            binding.ivScxdsjTime.setVisibility(View.VISIBLE);
        } else {
            binding.ivScxdsjTime.setVisibility(View.GONE);
        }
        binding.ivPic.setVisibility(View.GONE);

        switch (code) {
            case "1"://社区戒毒
            case "2"://社区康复
                binding.ivPic.setVisibility(View.VISIBLE);
                binding.linJd.setVisibility(View.VISIBLE);
                binding.linScxdTime.setVisibility(View.VISIBLE);
                break;
            case "11"://服刑
                binding.linRy.setVisibility(View.VISIBLE);
                binding.linJd.setVisibility(View.GONE);
                binding.linSheAn.setVisibility(View.GONE);//2023/02/17服刑人员--新增编辑涉案类型-不需要
                binding.linScxdTime.setVisibility(View.VISIBLE);
                binding.ivLxfsTag.setVisibility(View.INVISIBLE);
                break;
            case "3"://强制隔离戒毒
                binding.linJd.setVisibility(View.GONE);
                binding.linRuSuo.setVisibility(View.VISIBLE);
                binding.linScxdTime.setVisibility(View.VISIBLE);
                binding.ivLxfsTag.setVisibility(View.INVISIBLE);
                break;
            case "4"://强戒未执行
                binding.linJd.setVisibility(View.GONE);
                binding.linRuSuo.setVisibility(View.VISIBLE);
                binding.linScxdTime.setVisibility(View.VISIBLE);
                break;
            case "10"://查获登记
                binding.linJd.setVisibility(View.GONE);
                binding.linChaHuo.setVisibility(View.VISIBLE);
                binding.linScxdTime.setVisibility(View.VISIBLE);
                break;
            case "5"://社会面有吸毒史
            case "6"://自愿戒毒
            case "7"://戒断三年未复吸
            case "8"://其他重点关注
            case "9"://强戒出所未执行社区康复
                binding.linRy.setVisibility(View.GONE);
                binding.linZhiXing.setVisibility(View.VISIBLE);
                binding.linScxdTime.setVisibility(View.VISIBLE);
                break;
        }
    }

    /**
     * 提示参数必传
     */
    private boolean showToastError(String code) {
        switch (code) {
            case "1"://社区戒毒
            case "2"://社区康复
                if (binding.ivScxdsjTime.getVisibility() == View.VISIBLE && TextUtils.isEmpty(binding.tvScxdTime.getText().toString())) {
                    XToastUtil.showToast(this, "请选择初次吸毒时间");
                    return true;
                }
                if (TextUtils.isEmpty(binding.tvJdTime.getText().toString())) {
                    XToastUtil.showToast(this, "请选择决定日期");
                    return true;
                }
                if (TextUtils.isEmpty(binding.tvBdTime.getText().toString())) {
                    XToastUtil.showToast(this, "请选择报到日期");
                    return true;
                }

                break;
            case "11"://服刑
                if (binding.ivScxdsjTime.getVisibility() == View.VISIBLE && TextUtils.isEmpty(binding.tvScxdTime.getText().toString())) {
                    XToastUtil.showToast(this, "请选择初次吸毒时间");
                    return true;
                }
                if (TextUtils.isEmpty(binding.tvRyTime.getText().toString())) {
                    XToastUtil.showToast(this, "请选择入狱时间");
                    return true;
                }
//                if (TextUtils.isEmpty(binding.tvSheAnType.getText().toString())) {
//                    XToastUtil.showToast(this, "请选择涉案类型");
//                    return true;
//                }

                break;
            case "3"://强戒人员
                if (binding.ivScxdsjTime.getVisibility() == View.VISIBLE && TextUtils.isEmpty(binding.tvScxdTime.getText().toString())) {
                    XToastUtil.showToast(this, "请选择初次吸毒时间");
                    return true;
                }
                if (TextUtils.isEmpty(binding.tvRusuoTime.getText().toString())) {
                    XToastUtil.showToast(this, "请选择入所日期");
                    return true;
                }
                break;
            case "4"://强戒未执行人员
                if (binding.ivScxdsjTime.getVisibility() == View.VISIBLE && TextUtils.isEmpty(binding.tvScxdTime.getText().toString())) {
                    XToastUtil.showToast(this, "请选择初次吸毒时间");
                    return true;
                }
                if (TextUtils.isEmpty(binding.tvRusuoTime.getText().toString())) {
                    XToastUtil.showToast(this, "请选择执行时间日期");
                    return true;
                }
                break;
            case "10"://查获登记
                if (binding.ivScxdsjTime.getVisibility() == View.VISIBLE && TextUtils.isEmpty(binding.tvScxdTime.getText().toString())) {
                    XToastUtil.showToast(this, "请选择初次吸毒时间");
                    return true;
                }
                if (TextUtils.isEmpty(binding.tvChaHuoTime.getText().toString())) {
                    XToastUtil.showToast(this, "请选择查获时间");
                    return true;
                }

                break;
            case "5"://社会面有吸毒史
            case "6"://自愿戒毒
            case "7"://戒断三年未复吸
            case "8"://其他重点关注
            case "9"://强戒出所未执行社区康复
                if (binding.ivScxdsjTime.getVisibility() == View.VISIBLE && TextUtils.isEmpty(binding.tvScxdTime.getText().toString())) {
                    XToastUtil.showToast(this, "请选择初次吸毒时间");
                    return true;
                }
                if (TextUtils.isEmpty(binding.tvZhiXingTime.getText().toString())) {
                    XToastUtil.showToast(this, "请选择执行时间");
                    return true;
                }

                break;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(WorksEntity event) {
        if (event != null) {
            binding.tvWork.setText(event.getYhXm());
            workId = event.getId();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

}
