package com.linggan.jd831.ui.works.yujing;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.view.TimePickerView;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XGlideEngine;
import com.lgfzd.base.utils.XImageFileCompressEngine;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.TaskSpListEntity;
import com.linggan.jd831.databinding.ActivityYjczResultEndBinding;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.PhotoUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.XHttpUtils;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 类  名：预警处置-任务提交-处置结果
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2024/03/04 10:17
 * 版  权：凌感科技
 */
public class TaskYjCzEndActivity extends XBaseActivity<ActivityYjczResultEndBinding> {

    private String status, bh, jxywwczl;
    private ImageAddUtil imageAddImg;
    private int from = 1;
    private Uri photoUri;
    private File cameraPhoto;

    @Override
    protected ActivityYjczResultEndBinding getViewBinding() {
        return ActivityYjczResultEndBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        status = getIntent().getStringExtra("status");
        bh = getIntent().getStringExtra("bh");
        if (!TextUtils.isEmpty(status)) {
            switch (status) {
                case "1"://保持
                    binding.tvTitle.setVisibility(View.GONE);
                    binding.tvTsTime.setVisibility(View.GONE);
                    binding.view1.setVisibility(View.GONE);
                    binding.linJsyy.setVisibility(View.VISIBLE);
                    binding.tvYyBt.setText("保持原因");
                    binding.etJsyy.setHint("请填写保持原因");
                    break;
                case "2": //结束
                    binding.linJsyy.setVisibility(View.VISIBLE);
                    binding.linJxzl.setVisibility(View.VISIBLE);
                    break;
                case "3"://暂停
                    binding.tvTitle.setVisibility(View.GONE);
                    binding.tvTsTime.setVisibility(View.GONE);
                    binding.view1.setVisibility(View.GONE);
                    binding.linJsyy.setVisibility(View.VISIBLE);
                    binding.tvYyBt.setText("暂停原因");
                    binding.etJsyy.setHint("请填写结束原因");
                    break;
            }
        }
    }

    @Override
    protected void getData() {
        imageAddImg = new ImageAddUtil(this, binding.gridImg);
        imageAddImg.setMax(4);
        imageAddImg.setOnImageAddListener(() -> {
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                if (code.equals("1")) {
                    from = 0;
                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setMaxSelectNum(1).setMaxSelectNum(4 - imageAddImg.getSize()).setImageEngine(XGlideEngine.createGlideEngine()).setCompressEngine(new XImageFileCompressEngine()).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        });
    }

    @Override
    protected void initListener() {
        binding.btEnd.setOnClickListener(v -> {
            if (ButtonUtils.isFastClick()) {
                postData();
            }
        });
        binding.tvTsTime.setOnClickListener(v -> {
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.tvTsTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setSubmitColor(R.color.black).build();
            pvTime.show();
        });
        binding.radioGroup.setOnCheckedChangeListener((group, checkedId) -> {
            //jxywwczl （继续药物维持治疗   1是  2否）
            if (checkedId == R.id.rb_yes) {
                jxywwczl = "1";
                binding.linJxShi.setVisibility(View.VISIBLE);
            } else if (checkedId == R.id.rb_no) {
                binding.linJxShi.setVisibility(View.GONE);
                jxywwczl = "2";
            }
        });
    }

    /**
     * 数据提交
     */
    private void postData() {
        JSONObject objectMap = new JSONObject();
        try {
            if (!TextUtils.isEmpty(status) && status.equals("1") && TextUtils.isEmpty(binding.etJsyy.getText().toString())) {
                XToastUtil.showToast(this, "请填写保持原因");
                return;
            }
            if (!TextUtils.isEmpty(status) && status.equals("2") && TextUtils.isEmpty(binding.tvTsTime.getText().toString())) {
                XToastUtil.showToast(this, "请选择结束药物维持治疗脱失时间");
                return;
            }
            if (!TextUtils.isEmpty(status) && status.equals("2") && TextUtils.isEmpty(binding.etJsyy.getText().toString())) {
                XToastUtil.showToast(this, "请填写结束原因");
                return;
            }
            if (!TextUtils.isEmpty(status) && status.equals("2") && TextUtils.isEmpty(jxywwczl)) {
                XToastUtil.showToast(this, "请选择是否继续药物治疗");
                return;
            }
            JSONArray jsonArrayZp = new JSONArray();
            if (imageAddImg != null && imageAddImg.getPaths() != null && imageAddImg.getPaths().size() > 0) {
                for (int i = 0; i < imageAddImg.getPaths().size(); i++) {
                    JSONObject jsonObjectSP = new JSONObject();
                    jsonObjectSP.put("lj", imageAddImg.getPaths().get(i).getSavePath());
                    jsonObjectSP.put("mc", imageAddImg.getPaths().get(i).getOriginName());
                    jsonObjectSP.put("hz", StrUtils.getFileType(imageAddImg.getPaths().get(i).getSavePath()));
                    jsonObjectSP.put("dx", imageAddImg.getPaths().get(i).getFileSize());
                    jsonArrayZp.put(jsonObjectSP);
                }
            }
            if (jsonArrayZp.length() > 0) {
                objectMap.put("smwj", jsonArrayZp);
            }
            objectMap.put("bh", bh);
            objectMap.put("tscz", status);
            if (!TextUtils.isEmpty(status) && status.equals("1")) {
                objectMap.put("bcyy", binding.etJsyy.getText().toString());
            }
            if (!TextUtils.isEmpty(status) && status.equals("2")) {
                objectMap.put("jstssj", binding.tvTsTime.getText().toString() + " 00:00:00");
                objectMap.put("jsyy", binding.etJsyy.getText().toString());
                objectMap.put("jxywwczl", jxywwczl);
                objectMap.put("fyjg", binding.etFyjg.getText().toString());
                objectMap.put("bz", binding.etBz.getText().toString());
            }
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.YU_JING_TASK_FIN);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        binding.btEnd.setEnabled(false);
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), true, true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(TaskYjCzEndActivity.this, getString(R.string.sub_sucess));
                    EventBus.getDefault().post(new TaskSpListEntity());
                    finish();
                } else {
                    XToastUtil.showToast(TaskYjCzEndActivity.this, xResultData.getErrorInfo());
                    binding.btEnd.setEnabled(true);
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btEnd.setEnabled(true);
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.REQUEST_CAMERA:
                case PictureConfig.CHOOSE_REQUEST:
                    if (data != null) {
                        ArrayList<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                        if (null != selectList) {
                            for (LocalMedia media : selectList) {
                                if (!TextUtils.isEmpty(media.getCompressPath())) {
                                    uploadFile(media.getCompressPath());
                                } else if (!TextUtils.isEmpty(media.getRealPath())) {
                                    uploadFile(media.getRealPath());
                                } else if (!TextUtils.isEmpty(media.getPath())) {
                                    uploadFile(media.getPath());
                                }
                            }
                        }
                    }
                    break;
                case 1:
                    //图片
//                    uploadFile(data.getStringExtra("path"));
                    try {
                        boolean isFinish = XFileUtil.bitmapToFilePath(this, BitmapFactory.decodeFile(cameraPhoto.getAbsolutePath()), photoUri, cameraPhoto);
                        if (isFinish) {
                            uploadFile(cameraPhoto.getAbsolutePath());
                        }
                    } catch (Exception e) {
                    }
                    break;
            }
        }
    }

    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (from == 0) {
//            Intent intent = new Intent(this, CameraActivity.class);//调用系统相机
//            startActivityForResult(intent, 1);

            createFileMuLu();
            if (cameraPhoto != null) {
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                photoUri = XFileUtil.getUriFromFile(this, cameraPhoto);
                intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                startActivityForResult(intent, 1);
            }
        }
    }

    /**
     * 创建文件
     */
    private void createFileMuLu() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
                && ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {
            cameraPhoto = new File(PhotoUtil.createPath(this));
            try {
                if (cameraPhoto.exists()) {
                    cameraPhoto.delete();
                }
                cameraPhoto.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            // 权限未授权，需要申请权限
            requestPermission(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        }
    }

    /**
     * 上传
     *
     * @param path
     */
    private void uploadFile(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        imageAddImg.addImage(xResultData.getData().get(0));
                        imageAddImg.notifyData();
                    }
                } else {
                    XToastUtil.showToast(TaskYjCzEndActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }
}
