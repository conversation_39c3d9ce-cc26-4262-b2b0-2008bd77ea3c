package com.linggan.jd831.ui.works.qiye;


import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.linggan.jd831.bean.QyXxBean;
import com.linggan.jd831.utils.XHttpUtils;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XPermissionUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.CangKuAddListAdapter;
import com.linggan.jd831.adapter.CangKuYiCunZaiListAdapter;
import com.linggan.jd831.bean.CanKuMsgEntity;
import com.linggan.jd831.bean.MapChoiceEntity;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.PeoTypeListEntity;
import com.linggan.jd831.bean.QiYeInitEntity;
import com.linggan.jd831.bean.ZiDianEventEntity;
import com.linggan.jd831.databinding.ActivityMsgerRegOneBinding;
import com.linggan.jd831.ui.MainActivity;
import com.linggan.jd831.ui.common.CameraActivity;
import com.linggan.jd831.ui.common.maps.MapPickerActivity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.lgfzd.base.utils.XGlideEngine;
import com.lgfzd.base.utils.XImageFileCompressEngine;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XImageUtils;
import com.linggan.jd831.widget.AreaPickerViewDialog;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类  名:单位信息--信息员注册
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/11/28 10:00
 * 版  权：凌感科技
 */
public class QiYeXxRegActivity extends XBaseActivity<ActivityMsgerRegOneBinding> implements View.OnClickListener {

    private OssFileEntity headUrl, yyZzUrl;
    private String mLongitude, mLatitude;
    private String areaId;
    private int page = 1, from = 2, oldNum = 0;
    private CangKuAddListAdapter listAdapter;
    private List<CanKuMsgEntity> canKuMsgEntityList = new ArrayList<>();
    private Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            if (msg.what == 1) {
                searchQyXx();
            }
        }
    };

    @Override
    protected ActivityMsgerRegOneBinding getViewBinding() {
        return ActivityMsgerRegOneBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
//        location();
        EventBus.getDefault().register(this);
        binding.three.recycleCk.setLayoutManager(new LinearLayoutManager(this));
        listAdapter = new CangKuAddListAdapter(this, canKuMsgEntityList);
        binding.three.recycleCk.setAdapter(listAdapter);
        binding.two.tvZpTitle.setText("信息员照片上传");
        binding.three.recycleOldCk.setLayoutManager(new LinearLayoutManager(this));
    }

    @Override
    protected void initListener() {
        binding.btNext.setOnClickListener(this);
        binding.one.ivHead.setOnClickListener(this);
        binding.one.tvJyDz.setOnClickListener(this);
        binding.one.tvSsXzQy.setOnClickListener(this);
        binding.two.ivTwoHead.setOnClickListener(this);
        binding.btAddCk.setOnClickListener(this);

        binding.one.etShtyDm.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(s.toString())) {
                    Message msg = Message.obtain();
                    msg.what = 1;
                    msg.obj = s.toString(); //携带当前值
                    mHandler.sendMessageDelayed(msg, 2000);//隔3s之后发送msg
                }
            }
        });
    }

    @Override
    protected void getData() {
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.iv_head) {
            //营业执照
            from = 0;
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                if (code.equals("1")) {
                    PictureSelector.create(this).openCamera(SelectMimeType.ofImage()).setCompressEngine(new XImageFileCompressEngine()).forResultActivity(PictureConfig.REQUEST_CAMERA);
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setCompressEngine(new XImageFileCompressEngine()).setMaxSelectNum(1).setMinSelectNum(1).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        } else if (v.getId() == R.id.tv_jy_dz) {
            //营业地址
            XIntentUtil.redirectToNextActivity(this, MapPickerActivity.class);
        } else if (v.getId() == R.id.tv_ss_xz_qy) {
            //所属行政区域
            AreaPickerViewDialog areaPickerView = new AreaPickerViewDialog(this, "999999", 4);
            areaPickerView.show();
            areaPickerView.setAreaPickerViewCallback((areaName, ids) -> {
                areaId = ids.get(ids.size() - 1);
                binding.one.tvSsXzQy.setText(StrUtils.listToStringText(areaName));
            });
        } else if (v.getId() == R.id.iv_two_head) {
            //个人头像
            from = 1;
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                if (code.equals("1")) {
                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setCompressEngine(new XImageFileCompressEngine()).setMaxSelectNum(1).setMinSelectNum(1).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        } else if (v.getId() == R.id.bt_next) {
            //下一步
            if (ButtonUtils.isFastClick()) {
                if (page > 3) {
                    page = 3;
                }
                selectTabJian();
            }
        } else if (v.getId() == R.id.bt_add_ck) {
            //新增
            CanKuMsgEntity muBanEntity = new CanKuMsgEntity();
            muBanEntity.setMc("");
            muBanEntity.setSfSxt(0);
            muBanEntity.setOldNum(oldNum);
            canKuMsgEntityList.add(muBanEntity);
            listAdapter.notifyDataSetChanged();
        }
    }

    /**
     * 哏据社会统一信用代码获取-企业是否以前存在仓库
     */
    private void getYiYouCangKuList() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "tyshxydm=" + binding.one.etShtyDm.getText().toString().trim());
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.YZDQY_CANG_KU_ONTHER_LIST + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<CanKuMsgEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<CanKuMsgEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        binding.three.recycleOldCk.setAdapter(new CangKuYiCunZaiListAdapter(QiYeXxRegActivity.this, xResultData.getData()));
                        oldNum = xResultData.getData().size();
//                        CanKuMsgEntity muBanEntity = new CanKuMsgEntity();
//                        muBanEntity.setMc("");
//                        muBanEntity.setSfSxt(0);
//                        muBanEntity.setOldNum(xResultData.getData().size());
//                        canKuMsgEntityList.addAll(xResultData.getData());
//                        listAdapter.notifyDataSetChanged();
                    } else {
                        //新增一个
                        CanKuMsgEntity muBanEntity = new CanKuMsgEntity();
                        muBanEntity.setMc("");
                        muBanEntity.setSfSxt(0);
                        muBanEntity.setOldNum(0);
                        canKuMsgEntityList.add(muBanEntity);
                        listAdapter.notifyDataSetChanged();
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 提交数据
     */
    private void addInfo() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.YZDQY_MSG_USER_REG);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("mc", binding.one.etUnit.getText().toString());
            objectMap.put("tyshxydm", binding.one.etShtyDm.getText().toString().trim());
            objectMap.put("jydz", binding.one.tvJyDz.getText().toString().trim());
            objectMap.put("lng", mLongitude);
            objectMap.put("lat", mLatitude);
            objectMap.put("xzqhdm", areaId);
            if (yyZzUrl != null) {
                JSONArray jsonArray = new JSONArray();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("lj", yyZzUrl.getSavePath());
                jsonObject.put("mc", yyZzUrl.getOriginName());
                jsonObject.put("hz", StrUtils.getFileType(yyZzUrl.getOriginName()));
                jsonObject.put("dx", yyZzUrl.getFileSize());
                jsonObject.put("sjLy", "3");
                if (UserInfoUtils.getUserInfo() != null) {
                    jsonObject.put("lrrbh", UserInfoUtils.getUserInfo().getUserId());
                    jsonObject.put("bjrbh", UserInfoUtils.getUserInfo().getUserId());
                }
                jsonArray.put(jsonObject);
                objectMap.put("yyzz", jsonArray);
            }
            if (headUrl != null) {
                JSONArray jsonArray = new JSONArray();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("lj", headUrl.getSavePath());
                jsonObject.put("mc", headUrl.getOriginName());
                jsonObject.put("hz", StrUtils.getFileType(headUrl.getOriginName()));
                jsonObject.put("dx", headUrl.getFileSize());
                jsonObject.put("sjLy", "3");
                if (UserInfoUtils.getUserInfo() != null) {
                    jsonObject.put("lrrbh", UserInfoUtils.getUserInfo().getUserId());
                    jsonObject.put("bjrbh", UserInfoUtils.getUserInfo().getUserId());
                }
                jsonArray.put(jsonObject);
                objectMap.put("ryzp", jsonArray);
            }
            JSONArray jsonArray = new JSONArray();
            if (canKuMsgEntityList != null && canKuMsgEntityList.size() > 0) {
                for (int k = 0; k < canKuMsgEntityList.size(); k++) {
                    if (!TextUtils.isEmpty(canKuMsgEntityList.get(k).getMc())) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("mc", canKuMsgEntityList.get(k).getMc());
                        jsonObject.put("sfSxt", canKuMsgEntityList.get(k).getSfSxt());
                        jsonObject.put("sxtMc", canKuMsgEntityList.get(k).getSxtMc());
                        jsonObject.put("sxtXlh", canKuMsgEntityList.get(k).getSxtXlh());
                        jsonObject.put("sxtYzm", canKuMsgEntityList.get(k).getSxtYzm());
                        if (canKuMsgEntityList.get(k).getSfSxt() == 1 && TextUtils.isEmpty(canKuMsgEntityList.get(k).getSxtXlh())) {
                            int num = k + 1;
                            XToastUtil.showToast(this, "请填写仓库" + num + "中摄像头信息");
                            return;
                        }
                        jsonArray.put(jsonObject);
                        Log.i("qiye", "addInfo:" + jsonArray.toString());
                    } else {
                        int num = k + 1;
                        XToastUtil.showToast(this, "请填写仓库" + num + "中仓库名称");
                        return;
                    }
                }
            }
            if ((oldNum == 0 && jsonArray.length() <= 0) || (oldNum == 0 && canKuMsgEntityList != null && canKuMsgEntityList.size() > 0 && canKuMsgEntityList.size() != jsonArray.length())) {
                XToastUtil.showToast(this, "请填写仓库信息");
                return;
            }
            objectMap.put("ckXx", jsonArray);
        } catch (JSONException e) {
        }
        binding.btNext.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), true, true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(QiYeXxRegActivity.this, getString(R.string.save_sucess));
                    EventBus.getDefault().post(new PeoTypeListEntity());
                    getQiYeCangKuInit();
                } else {
                    XToastUtil.showToast(QiYeXxRegActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btNext.setEnabled(true);
            }

            @Override
            public void onFinished() {
                binding.btNext.setEnabled(true);
            }
        });

    }

    /**
     * 获取审核状态
     */
    private void getQiYeCangKuInit() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.YZDQY_MSG_IS_QY);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(QiYeXxRegActivity.this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<QiYeInitEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<QiYeInitEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    //{"status":0,"errorInfo":"成功","data":{"zc":1,"csh":1,"ck":0}}
                    if (xResultData.getData().getZc().equals("2")) {
                        //注册企业审批中
                        XIntentUtil.redirectToNextActivity(QiYeXxRegActivity.this, QiYeSpActivity.class);
                        finish();
                    }
                    if (xResultData.getData().getZc().equals("1") && xResultData.getData().getCsh().equals("0")) {
                        //初始化仓库
                        XIntentUtil.redirectToNextActivity(QiYeXxRegActivity.this, ChuShiCkActivity.class);
                        finish();
                    }
                    if (xResultData.getData().getZc().equals("1") && xResultData.getData().getCsh().equals("1")) {
                        //注册审批过-初始话审核过
                        XIntentUtil.redirectToNextActivity(QiYeXxRegActivity.this, MainActivity.class, "from", "from");
                        finish();
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && data != null) {
            switch (requestCode) {
                case PictureConfig.REQUEST_CAMERA:
                case PictureConfig.CHOOSE_REQUEST:
                    ArrayList<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                    if (null != selectList) {
                        for (LocalMedia media : selectList) {
                            if (from == 1) {
                                if (media.isCompressed()) {
                                    faceZc(media.getCompressPath());
                                } else if (!TextUtils.isEmpty(media.getRealPath())) {
                                    faceZc(media.getRealPath());
                                }
                            } else {
                                if (media.isCompressed()) {
                                    uploadFile(media.getCompressPath());
                                } else if (!TextUtils.isEmpty(media.getRealPath())) {
                                    uploadFile(media.getRealPath());
                                }
                            }
                        }
                    }
                    break;
                case 1:
                    //图片
                    faceZc(data.getStringExtra("path"));
                    break;
            }
        }
    }

    //上传
    private void uploadFile(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        if (from == 0) {
                            yyZzUrl = xResultData.getData().get(0);
                            XImageUtils.load(QiYeXxRegActivity.this, xResultData.getData().get(0).getSavePath(), binding.one.ivHead);
                        } else {
                            headUrl = xResultData.getData().get(0);
                            XImageUtils.load(QiYeXxRegActivity.this, xResultData.getData().get(0).getSavePath(), binding.two.ivTwoHead);
                        }
                    }
                } else {
                    XToastUtil.showToast(QiYeXxRegActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 人脸注册
     */
    private void faceZc(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FACE_REG);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("image", XFileUtil.imageToBase64(path));
        objectMap.put("image_type", "BASE64");
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<Boolean> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<Boolean>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData()) {
                    uploadFile(path);
                } else {
                    XToastUtil.showToast(QiYeXxRegActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 通过社会统一信用代码查询企业信息
     */
    private void searchQyXx() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "tyshxydm=" + binding.one.etShtyDm.getText().toString());
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.TYSHDM_CQY + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(QiYeXxRegActivity.this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<QyXxBean> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<QyXxBean>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    binding.one.etUnit.setText(xResultData.getData().getMc());
                    binding.one.tvJyDz.setText(xResultData.getData().getJydz());
                    mLongitude = xResultData.getData().getLng();
                    mLatitude = xResultData.getData().getLat();
                    List<String> list = StrUtils.getXzqhDmList(xResultData.getData().getShenXzqhdm(), xResultData.getData().getShiXzqhdm(), xResultData.getData().getQxXzqhdm(), xResultData.getData().getXzXzqhdm(), "");
                    if (list != null && list.size() > 0) {
                        areaId = list.get(list.size() - 1);
                    }
                    binding.one.tvSsXzQy.setText(xResultData.getData().getXzqhdz());
                } else {
                    binding.one.etUnit.setText("");
                    binding.one.tvJyDz.setText("");
                    mLongitude = "";
                    mLatitude = "";
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (from == 1) {
            Intent intent = new Intent(this, CameraActivity.class);//调用系统相机
            intent.putExtra("info", "info");
            startActivityForResult(intent, 1);
        }
    }

    /**
     * 定位初始化
     */
    private void location() {
        //初始化AMapLocationClientOption对象
//        AMapLocationClient client = new AMapLocationClient(getApplicationContext());
//        AMapLocationClientOption option = MapUtils.getDefaultOption();
//        option.setMockEnable(true);
//        //给定位客户端对象设置定位参数
//        client.setLocationOption(option);
//        //设置定位回调监听
//        client.setLocationListener(aMapLocation -> {
//            if (aMapLocation != null) {
//                if (aMapLocation.getErrorCode() == 0) {
////                    mLongitude = aMapLocation.getLongitude() + "";
////                    mLatitude = aMapLocation.getLatitude() + "";
//                    client.stopLocation();
//                }
//            }
//        });
//        //启动定位
//        client.startLocation();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(MapChoiceEntity item) {
        if (item != null) {
            //地址回显
            binding.one.tvJyDz.setText(item.getAddress());
            mLatitude = item.getLat() + "";
            mLongitude = item.getLon() + "";
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(ZiDianEventEntity event) {
        if (event != null) {
            finish();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        for (int i = 0; i < permissions.length; i++) {
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED || ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED || ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED || ActivityCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                XPermissionUtil.initPermission(this, new String[]{android.Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
            }
        }
    }

    private void selectTabJian() {
        if (page == 1) {
            binding.one.linOne.setVisibility(View.VISIBLE);
            binding.two.linTwo.setVisibility(View.GONE);
            binding.three.linThree.setVisibility(View.GONE);
            binding.btNext.setText(getString(R.string.next));
            binding.viewOne.setBackgroundColor(getResources().getColor(R.color.color_line));
            binding.viewTwo.setBackgroundColor(getResources().getColor(R.color.color_line));
            binding.tvTwo.setBackgroundResource(R.drawable.bg_gray_yuan);
            binding.tvThree.setBackgroundResource(R.drawable.bg_gray_yuan);

            if (TextUtils.isEmpty(binding.one.etShtyDm.getText().toString())) {
                XToastUtil.showToast(this, "请输入统一社会信用代码");
                return;
            }
            if (TextUtils.isEmpty(binding.one.etUnit.getText().toString())) {
                XToastUtil.showToast(this, "请输入单位名称");
                return;
            }
            if (TextUtils.isEmpty(binding.one.tvJyDz.getText().toString())) {
                XToastUtil.showToast(this, "请填写实际经营地址");
                return;
            }
            if (TextUtils.isEmpty(binding.one.tvSsXzQy.getText().toString())) {
                XToastUtil.showToast(this, "请选择所属行政区划");
                return;
            }
            if (yyZzUrl == null) {
                XToastUtil.showToast(this, "请上传营业执照");
                return;
            }
            page++;
            binding.one.linOne.setVisibility(View.GONE);
            binding.two.linTwo.setVisibility(View.VISIBLE);
            binding.three.linThree.setVisibility(View.GONE);
            binding.btNext.setText(getString(R.string.next));
            binding.viewOne.setBackgroundColor(getResources().getColor(R.color.color_main));
            binding.viewTwo.setBackgroundColor(getResources().getColor(R.color.color_line));
            binding.tvTwo.setBackgroundResource(R.drawable.bg_blue_yuan);
            binding.tvThree.setBackgroundResource(R.drawable.bg_gray_yuan);
        } else if (page == 2) {
            if (headUrl == null) {
                XToastUtil.showToast(this, "请上传照片");
                return;
            }
            page++;
            binding.one.linOne.setVisibility(View.GONE);
            binding.two.linTwo.setVisibility(View.GONE);
            binding.three.linThree.setVisibility(View.VISIBLE);
            binding.btAddCk.setVisibility(View.VISIBLE);
            //
            getYiYouCangKuList();
            //
            binding.btNext.setText(getString(R.string.submit));
            binding.viewOne.setBackgroundColor(getResources().getColor(R.color.color_main));
            binding.viewTwo.setBackgroundColor(getResources().getColor(R.color.color_main));
            binding.tvTwo.setBackgroundResource(R.drawable.bg_blue_yuan);
            binding.tvThree.setBackgroundResource(R.drawable.bg_blue_yuan);
        } else if (page == 3) {
            addInfo();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        if (mHandler != null) {
            mHandler.removeMessages(1);
        }
    }
}
