package com.linggan.jd831.ui.works.care;


import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.bean.CareTuiListEntity;
import com.linggan.jd831.databinding.ActivityBaseListBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.XHttpUtils;

import org.xutils.http.RequestParams;

/**
 * 类  名：平安关爱进度
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class CareSpeedActivity extends XBaseActivity<ActivityBaseListBinding> {

    private String careId;

    @Override
    protected ActivityBaseListBinding getViewBinding() {
        return ActivityBaseListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        careId = getIntent().getStringExtra("bh");
        binding.recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
    }

    @Override
    protected void initListener() {
    }

    @Override
    protected void getData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "xyrbh=" + careId);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.CARE_TUI_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<CareTuiListEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<CareTuiListEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                } else {
                    XToastUtil.showToast(CareSpeedActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }


}
