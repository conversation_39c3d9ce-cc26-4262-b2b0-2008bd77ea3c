package com.linggan.jd831.ui.works.kaohe;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseFragment;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.view.recycle.XRecyclerView;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.KhMbYwcListHolder;
import com.linggan.jd831.adapter.KhPlaceYwcListHolder;
import com.linggan.jd831.bean.KhMbEntity;
import com.linggan.jd831.bean.KhlvPlaceEntity;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.XHttpUtils;

import org.xutils.http.RequestParams;
import org.xutils.view.annotation.ContentView;
import org.xutils.view.annotation.ViewInject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 考核--目标任务-已完成
 */
@ContentView(R.layout.fragment_kh_list)
public class KhMbYwcListFragment extends XBaseFragment implements XRefreshLayout.PullLoadMoreListener {

    @ViewInject(R.id.recycler)
    private XRecyclerView recycler;
    @ViewInject(R.id.iv_no_data)
    private ImageView ivNoData;
    private int page = 1, totalPage = 0;
    private boolean isShow = true;
    private String startDate, endDate, xzqhdm, type, dwdm;


    public static KhMbYwcListFragment newInstance(String kssj, String jssj, String xzqhdm, String type, String dwdm) {
        KhMbYwcListFragment benefitTabFragment = new KhMbYwcListFragment();
        Bundle bundle = new Bundle();
        bundle.putString("kssj", kssj);
        bundle.putString("jssj", jssj);
        bundle.putString("xzqhdm", xzqhdm);
        bundle.putString("type", type);
        bundle.putString("dwdm", dwdm);
        benefitTabFragment.setArguments(bundle);
        return benefitTabFragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle bundle = getArguments();
        if (bundle != null) {
            startDate = bundle.getString("kssj");
            endDate = bundle.getString("jssj");
            xzqhdm = bundle.getString("xzqhdm");
            type = bundle.getString("type");
            dwdm = bundle.getString("dwdm");
        }

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(getActivity()));
        recycler.getAdapter().bindHolder(new KhMbYwcListHolder(dwdm));
        recycler.setOnPullLoadMoreListener(this);
    }

    @Override
    protected void lazyLoad() {

        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.listMbRw);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("dwdm", dwdm);
        objectMap.put("startDate", startDate + " 00:00:00");
        objectMap.put("endDate", endDate + " 23:59:59");
        objectMap.put("wcqk", "1");
        objectMap.put("page", page);
        objectMap.put("rows", XConstantUtils.PAGE_SIZE);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));

        XHttpUtils.postJson(getActivity(), requestParams, jiaMiString, DialogUtils.showLoadDialog(getActivity(), ""), isShow, false, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<XResultPageData<KhMbEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XResultPageData<KhMbEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        totalPage = xResultData.getData().getTotalPage();
                        if (xResultData.getData().getRecords() != null && xResultData.getData().getRecords().size() > 0) {
                            ivNoData.setVisibility(View.GONE);
                            if (page == 1) {
                                recycler.getAdapter().setData(0, xResultData.getData().getRecords());
                            } else {
                                recycler.getAdapter().addDataAll(0, xResultData.getData().getRecords());
                            }
                        } else {
                            if (page == 1) {
                                ivNoData.setVisibility(View.VISIBLE);
                                recycler.getAdapter().setData(0, new ArrayList());
                            }
                        }
                    } else {
                        if (page == 1) {
                            ivNoData.setVisibility(View.VISIBLE);
                            recycler.getAdapter().setData(0, new ArrayList());
                        }
                    }
                } else {
                    if (page == 1) {
                        ivNoData.setVisibility(View.VISIBLE);
                        recycler.getAdapter().setData(0, new ArrayList());
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                recycler.setPullLoadMoreCompleted();
            }
        });
    }


    @Override
    public void onRefresh() {
        page = 1;
        isShow = false;
        lazyLoad();
    }

    @Override
    public boolean onLoadMore() {
        if (page < totalPage) {
            page++;
            isShow = false;
            lazyLoad();
            return true;
        }
        return false;
    }
}
