package com.linggan.jd831.ui.works.sign;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.linggan.jd831.R;
import com.linggan.jd831.utils.XHttpUtils;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.adapter.ImaVideoShowAdapter;
import com.linggan.jd831.adapter.PeoStatusInfoListAdapter;
import com.linggan.jd831.adapter.SignTimeListAdapter;
import com.linggan.jd831.bean.PeopleInfoEntity;
import com.linggan.jd831.bean.SignInfoEntity;
import com.linggan.jd831.bean.TaskSpListEntity;
import com.linggan.jd831.bean.ZHouQiEntity;
import com.linggan.jd831.databinding.ActivitySignInfoBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.TaskApprovalFactory;
import com.linggan.jd831.utils.XImageUtils;

import org.greenrobot.eventbus.EventBus;
import org.xutils.http.RequestParams;

import java.util.ArrayList;
import java.util.List;

/**
 * 类  名： 签到-详情
 * 描   述：
 * 作  者：zxb
 * 时  间：
 * 版  权：凌感科技
 */
public class SignInfoActivity extends XBaseActivity<ActivitySignInfoBinding> {

    private String bh, peoId, tag, key, taskId, aid, pro, spls;
    private SignInfoEntity signInfoEntity;

    @Override
    protected ActivitySignInfoBinding getViewBinding() {
        return ActivitySignInfoBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        bh = getIntent().getStringExtra("id");
        peoId = getIntent().getStringExtra("pid");
        //这里是标识从任务审批跳转过来--下面三个参数
        tag = getIntent().getStringExtra("tag");
        taskId = getIntent().getStringExtra("tid");
        key = getIntent().getStringExtra("key");
        pro = getIntent().getStringExtra("pro");
        aid = getIntent().getStringExtra("aid");
        spls = getIntent().getStringExtra("spls");//审批历史过来

        binding.top.recyclerPeoStatus.setLayoutManager(new GridLayoutManager(this, 3));
        binding.recycleZq.setLayoutManager(new LinearLayoutManager(this));
    }

    @Override
    protected void initListener() {
        //非审批过来
        if (TextUtils.isEmpty(tag)) {
            getUserData();
        } else {
            //审批数据
            binding.linPopup.linInfo.setVisibility(View.VISIBLE);
            TaskApprovalFactory.getSpData(this, binding.linPopup.ivOnDown, binding.linPopup.recycleSp, binding.linPopup.etSpRemark, binding.linPopup.btNo, binding.linPopup.btTg, binding.linPopup.linDspShow, binding.linPopup.linSpth, binding.linPopup.btFangQi, binding.linPopup.btTgEdit, key, pro, aid, taskId, tag, spls, code -> {
                switch (code) {
                    case "pass":
                        //审批提交完成
                        EventBus.getDefault().post(new TaskSpListEntity());
                        finish();
                        break;
                    case "fin":
                        //放弃审批
                        finish();
                        break;
                    case "edit":
                        //编辑
                        Bundle bundle = new Bundle();
                        bundle.putString("id", peoId);
                        bundle.putString("tag", tag);
                        bundle.putString("pro", pro);
                        bundle.putString("name", binding.top.tvName.getText().toString());
                        bundle.putSerializable("msg", signInfoEntity);
                        XIntentUtil.redirectToNextActivity(this, SignAddActivity.class, bundle);
                        break;
                }
            });
        }
    }

    @Override
    protected void getData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + bh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.SIGN_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<SignInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<SignInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        signInfoEntity = xResultData.getData();
                        binding.tvTime.setText(xResultData.getData().getLrsj());
                        binding.tvLx.setText(xResultData.getData().getLx() == 1 ? "代签到" : "自签到");
                        if (xResultData.getData().getLgXdryQdZqs() != null && xResultData.getData().getLgXdryQdZqs().size() > 0) {
                            binding.recycleZq.setAdapter(new SignTimeListAdapter(mContext, xResultData.getData().getLgXdryQdZqs()));
                        } else {
                            List<ZHouQiEntity> list = new ArrayList<>();
                            ZHouQiEntity zHouQi = new ZHouQiEntity();
                            zHouQi.setZqKssj("非周期签到");
                            zHouQi.setZqJssj("");
                            list.add(zHouQi);
                            binding.recycleZq.setAdapter(new SignTimeListAdapter(mContext, list));
                        }
                        binding.tvRemark.setText(xResultData.getData().getBz());
                        binding.tvAddress.setText(xResultData.getData().getDz());
                        //视频
                        binding.gridVideo.setAdapter(new ImaVideoShowAdapter(xResultData.getData().getSp()));
                        // 图片
                        binding.gridImg.setAdapter(new ImaVideoShowAdapter(xResultData.getData().getZp()));

                        //审批--获取获取详情
                        if (!TextUtils.isEmpty(tag)) {
                            peoId = xResultData.getData().getXyrbh();
                            getUserData();
                        }
                    }
                } else {
                    XToastUtil.showToast(SignInfoActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 人员行信息
     */
    private void getUserData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "xyrbh=" + peoId);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PEOPLE_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<PeopleInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<PeopleInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    XImageUtils.load(mContext, xResultData.getData().getXp(), binding.top.ivHead, R.mipmap.ic_def_head);
                    binding.top.tvName.setText(xResultData.getData().getXm());
                    binding.top.recyclerPeoStatus.setAdapter(new PeoStatusInfoListAdapter(SignInfoActivity.this, xResultData.getData().getRyejzt()));
                    if (xResultData.getData().getRyyjzt() != null) {
                        if (xResultData.getData().getXbdm() != null) {
                            binding.top.tvSex.setText(xResultData.getData().getXbdm().getName() + " " + xResultData.getData().getRyyjzt().getName());
                        } else {
                            binding.top.tvSex.setText(xResultData.getData().getRyyjzt().getName());
                        }
                    } else {
                        if (xResultData.getData().getXbdm() != null) {
                            binding.top.tvSex.setText(xResultData.getData().getXbdm().getName());
                        }
                    }
                    if (xResultData.getData().getMzdm() != null) {
                        binding.peo.tvMingZu.setText(xResultData.getData().getMzdm().getName());
                    }
                    binding.peo.tvIdCard.setText(StrUtils.hideIdCard(xResultData.getData().getZjhm()));
                    binding.peo.tvMobile.setText(xResultData.getData().getLxdh());
                    if (xResultData.getData().getZzmm() != null) {
                        binding.peo.tvZzmm.setText(xResultData.getData().getZzmm().getName());
                    }
                    binding.peo.tvBrith.setText(xResultData.getData().getCsrq());
                    if (xResultData.getData().getXldm() != null) {
                        binding.peo.tvEdu.setText(xResultData.getData().getXldm().getName());
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }
}
