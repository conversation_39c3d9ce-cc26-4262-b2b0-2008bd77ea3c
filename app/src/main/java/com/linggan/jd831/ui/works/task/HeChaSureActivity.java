package com.linggan.jd831.ui.works.task;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.View;
import android.widget.RadioGroup;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XGlideEngine;
import com.lgfzd.base.utils.XImageFileCompressEngine;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.ImaVideoShowAdapter;
import com.linggan.jd831.bean.HeChaInfoEntity;
import com.linggan.jd831.bean.HeChaJumpBean;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.PeoStatusEntity;
import com.linggan.jd831.bean.PeopleTypeInfoEntity;
import com.linggan.jd831.bean.TaskDataNewEntity;
import com.linggan.jd831.bean.TaskSpListEntity;
import com.linggan.jd831.bean.ZiDianEntity;
import com.linggan.jd831.databinding.ActivityHechaSureBinding;
import com.linggan.jd831.ui.user.edit.PeopleStatusAddActivity;
import com.linggan.jd831.ui.user.edit.PeopleTypeInfoActivity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.PhotoUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XFileUtil;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.AreaPickerViewDialog;
import com.linggan.jd831.widget.BaseZiDianDialog;
import com.linggan.jd831.widget.CodeNameDialog;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 类  名：核查请求-确认(任务中心)
 * 作  者：LGKJ
 * 说  明：
 * 时  间：2022/11/17 9:46
 * 版  权：凌感科技
 */
public class HeChaSureActivity extends XBaseActivity<ActivityHechaSureBinding> implements RadioGroup.OnCheckedChangeListener, View.OnClickListener {

    private String bh, xyrbh, nfDw = "0", dwQk, gyqkCode, gyqkNum, yjztbh, dqlx, dqlxMc, rylx;
    private String shengQhdm, shiQhdm, quQhdm, xzQhdm, sqQhdm, shengName, shiName, quName, xzName, sqName;
    private ImageAddUtil imageAddImg;
    private int from = 0;

    private Uri photoUri;
    private File cameraPhoto;


    @Override
    protected ActivityHechaSureBinding getViewBinding() {
        return ActivityHechaSureBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        bh = getIntent().getStringExtra("ywbh");
        xyrbh = getIntent().getStringExtra("id");
        yjztbh = getIntent().getStringExtra("yjztbh");
        dqlx = getIntent().getStringExtra("dqlx");
        dqlxMc = getIntent().getStringExtra("dqlxmc");
        StrUtils.getPhotoVideoText2(binding.tvImgInfo, null);
        imageAddImg = new ImageAddUtil(this, binding.gridImg);
        imageAddImg.setMax(4);
        imageAddImg.setOnImageAddListener(() -> {
            from = 1;
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                if (code.equals("1")) {
                    requestPermission(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setMaxSelectNum(4 - imageAddImg.getSize()).setCompressEngine(new XImageFileCompressEngine()).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        });
        EventBus.getDefault().register(this);
    }

    @Override
    protected void initListener() {
        binding.radioGroup.setOnCheckedChangeListener(this);
        binding.tvDwCase.setOnClickListener(this);
        binding.btSubmit.setOnClickListener(this);
        binding.tvShmXq.setOnClickListener(this);
        binding.tvGyQk.setOnClickListener(this);
    }

    @Override
    protected void getData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "bh=" + bh);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.HE_CHA_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, DialogUtils.showLoadDialog(this, ""), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<HeChaInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<HeChaInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        binding.peo.tvHcTime.setText(xResultData.getData().getLrsj());
                        binding.peo.tvHcMs.setText(xResultData.getData().getHcMs());
                        //
                        binding.peo.tvPeoType.setText(xResultData.getData().getRylxString());
                        binding.peo.tvName.setText(xResultData.getData().getXm());
                        binding.peo.tvIdCard.setText(StrUtils.hideIdCard(xResultData.getData().getZjhm()));
                        binding.peo.tvMobile.setText(xResultData.getData().getLxdh());

                        binding.peo.tvTjr.setText(xResultData.getData().getLrrxm());
                        binding.peo.tvLxMobile.setText(xResultData.getData().getLrrDh());
                        binding.peo.tvSsdw.setText(xResultData.getData().getLrrDw());
                        //
                        rylx = xResultData.getData().getRylx();
                        //附件
                        binding.peo.gridWfdwFj.setAdapter(new ImaVideoShowAdapter(xResultData.getData().getFjList()));
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_dw_case) {
            //能定位--定位情况
            CodeNameDialog codeNameDialog = new CodeNameDialog(this, StrUtils.getHeChaDwqkList());
            codeNameDialog.setOnClickDataListener(() -> {
                dwQk = codeNameDialog.getCode().getCode();
                binding.tvDwCase.setText(codeNameDialog.getCode().getName());
                if (codeNameDialog.getCode().getCode().equals("0")) {
                    //已关押
                    binding.linYgy.setVisibility(View.VISIBLE);
                    binding.btSubmit.setText(getString(R.string.next));
                    binding.linShm.setVisibility(View.GONE);
                    //切换类型-清楚数据
                    binding.tvShmXq.setText("");
                    binding.etShmLxfs.setText("");
                    binding.etShmDz.setText("");
                    gyqkCode = "";
                    shengQhdm = "";
                    shiQhdm = "";
                    quQhdm = "";
                    xzQhdm = "";
                    sqQhdm = "";
                    shengName = "";
                    shiName = "";
                    quName = "";
                    xzName = "";
                    sqName = "";
                } else {
                    //社会面
                    binding.linShm.setVisibility(View.VISIBLE);
                    binding.linYgy.setVisibility(View.GONE);
                    binding.btSubmit.setText(getString(R.string.shang_bao));
                    //切换类型-清楚数据
                    binding.tvGyQk.setText("");
                    gyqkCode = "";
                }
            });
            codeNameDialog.show();
        } else if (v.getId() == R.id.tv_shm_xq) {
            //社会面辖区--省市区
            AreaPickerViewDialog areaPickerView = new AreaPickerViewDialog(this, "999999");
            areaPickerView.show();
            areaPickerView.setAreaPickerViewCallback((areaName, ids) -> {
                try {
//                    shengQhdm = ids.get(0);
//                    shiQhdm = ids.get(1);
//                    quQhdm = ids.get(2);
//                    xzQhdm = ids.get(3);
//                    sqQhdm = ids.get(4);
//                    shengName = areaName.get(0);
//                    shiName = areaName.get(1);
//                    quName = areaName.get(2);
//                    xzName = areaName.get(3);
//                    sqName = areaName.get(4);
                    if (ids.size() >= 1) {
                        shengQhdm = ids.get(0);
                        shengName = areaName.get(0);
                    }
                    if (ids.size() >= 2) {
                        shiQhdm = ids.get(1);
                        shiName = areaName.get(1);
                    }
                    if (ids.size() >= 3) {
                        quQhdm = ids.get(2);
                        quName = areaName.get(2);
                    }
                    if (ids.size() >= 4) {
                        xzQhdm = ids.get(3);
                        xzName = areaName.get(3);
                    }
                    if (ids.size() >= 5) {
                        sqQhdm = ids.get(4);
                        sqName = areaName.get(4);
                    }
                    binding.tvShmXq.setText(StrUtils.listToStringText(areaName));
                } catch (Exception e) {
                }
            });
        } else if (v.getId() == R.id.tv_gy_qk) {
            //关押情况--排除当前人员已是得类型
            FactoryUtils.getGyqkType(this, result -> {
                List<ZiDianEntity> entityList = new ArrayList<>();
                for (int k = 0; k < result.size(); k++) {
                    if (!result.get(k).getDm().equals(rylx)) {
                        entityList.add(result.get(k));
                    }
                }
                BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, (ArrayList<ZiDianEntity>) entityList);
                dianDialog.setOnClickDataListener(() -> {
                    binding.tvGyQk.setText(dianDialog.getData().getMc());
                    gyqkCode = dianDialog.getData().getBz();
                    gyqkNum = dianDialog.getData().getDm();
                });
                dianDialog.show();
            });
        } else if (v.getId() == R.id.bt_submit) {
            //提交
            if (TextUtils.isEmpty(nfDw)) {
                XToastUtil.showToast(this, "请选择能否定位");
                return;
            }
            if (nfDw.equals("1")) {
                //不能确定时候直接提交
                if (ButtonUtils.isFastClick()) {
                    postData();
                }
            } else {
                if (TextUtils.isEmpty(dwQk)) {
                    XToastUtil.showToast(this, "请选择定位情况");
                    return;
                }
                if (dwQk.equals("1")) {
                    //社会面
                    if (TextUtils.isEmpty(binding.tvShmXq.getText().toString())) {
                        XToastUtil.showToast(this, "请选择社会面居住辖区");
                        return;
                    }
                    if (TextUtils.isEmpty(binding.etShmDz.getText().toString())) {
                        XToastUtil.showToast(this, "请输入社会面居住地址");
                        return;
                    }
                    if (ButtonUtils.isFastClick()) {
                        postData();
                    }
                } else {
                    //能确定--如服刑/强戒 弹出【人员类型变更】，且人员类型锁死对应的选项，其他的选项则弹出【人员状态变更】，且人员状态锁死对应的选项
                    if (TextUtils.isEmpty(gyqkCode)) {
                        XToastUtil.showToast(this, "请选择关押情况");
                        return;
                    }
                    Bundle bundle = new Bundle();
                    if (gyqkCode.equals("11") || gyqkCode.equals("3")) {
                        //服刑/强戒 弹出【人员类型变更】
                        bundle.putString("id", xyrbh);
                        bundle.putString("yjztbh", yjztbh);
                        bundle.putString("ryzt", dqlx);
                        bundle.putString("ryztmc", dqlxMc);
                        HeChaJumpBean heChaJumpBean = new HeChaJumpBean();
                        heChaJumpBean.setNfDw(nfDw);
                        heChaJumpBean.setDwQk(dwQk);
                        heChaJumpBean.setBh(bh);
                        heChaJumpBean.setGyQk(gyqkCode);
                        heChaJumpBean.setGyQkDm(gyqkNum);
                        heChaJumpBean.setHcMs(binding.peo.tvHcMs.getText().toString());
                        heChaJumpBean.setType(binding.tvGyQk.getText().toString());
                        bundle.putSerializable("param", heChaJumpBean);
                        XIntentUtil.redirectToNextActivity(HeChaSureActivity.this, PeopleTypeInfoActivity.class, bundle);
                    } else {
                        //其他的选项则弹出【人员状态变更】
                        bundle.putString("code", gyqkCode);
                        bundle.putString("name", binding.tvGyQk.getText().toString());
                        bundle.putString("bh", xyrbh);
                        bundle.putString("yjzt", yjztbh);
                        HeChaJumpBean heChaJumpBean = new HeChaJumpBean();
                        heChaJumpBean.setNfDw(nfDw);
                        heChaJumpBean.setDwQk(dwQk);
                        heChaJumpBean.setBh(bh);
                        heChaJumpBean.setGyQk(gyqkCode);
                        heChaJumpBean.setGyQkDm(gyqkNum);
                        heChaJumpBean.setHcMs(binding.peo.tvHcMs.getText().toString());
                        heChaJumpBean.setType(binding.tvGyQk.getText().toString());
                        bundle.putSerializable("param", heChaJumpBean);
                        XIntentUtil.redirectToNextActivity(this, PeopleStatusAddActivity.class, bundle);
                    }
                }
            }
        }
    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        switch (checkedId) {
            case R.id.rb_yes:
                //能确认动向
                binding.linDw.setVisibility(View.VISIBLE);
                binding.linNoDx.setVisibility(View.GONE);
                binding.linShm.setVisibility(View.GONE);
                binding.linYgy.setVisibility(View.GONE);
                nfDw = "0";
                break;
            case R.id.rb_no:
                //不能确认动向
                binding.linNoDx.setVisibility(View.VISIBLE);
                binding.linDw.setVisibility(View.GONE);
                binding.linShm.setVisibility(View.GONE);
                binding.linYgy.setVisibility(View.GONE);
                nfDw = "1";
                dwQk = "";
                binding.tvDwCase.setText("");
                binding.tvGyQk.setText("");
                gyqkCode = "";
                gyqkNum = "";
                shengQhdm = "";
                shiQhdm = "";
                quQhdm = "";
                xzQhdm = "";
                sqQhdm = "";
                shengName = "";
                shiName = "";
                quName = "";
                xzName = "";
                sqName = "";
                binding.tvShmXq.setText("");
                break;
        }
    }

    /**
     * 数据提交
     */
    private void postData() {
        if (TextUtils.isEmpty(binding.tvNoDxRemark.getText().toString())) {
            XToastUtil.showToast(this, "请输入备注");
            return;
        }
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.HE_CHA_TASK_SURE);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        JSONObject objectMap = new JSONObject();
        try {
            objectMap.put("bh", bh);
            objectMap.put("xyrbh", xyrbh);
            objectMap.put("sjLy", "3");
            objectMap.put("nfDw", nfDw);
            objectMap.put("hcMs", binding.peo.tvHcMs.getText().toString());
            if (nfDw.equals("1")) {
                //不能定位
                objectMap.put("hcBz", binding.tvNoDxRemark.getText().toString());
                if (imageAddImg != null && imageAddImg.getPaths() != null && imageAddImg.getPaths().size() > 0) {
                    JSONArray jsonArray = new JSONArray();
                    for (int i = 0; i < imageAddImg.getPaths().size(); i++) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("lj", imageAddImg.getPaths().get(i).getSavePath());
                        jsonObject.put("mc", imageAddImg.getPaths().get(i).getOriginName());
                        jsonObject.put("hz", StrUtils.getFileType(imageAddImg.getPaths().get(i).getOriginName()));
                        jsonObject.put("dx", imageAddImg.getPaths().get(i).getFileSize());
                        jsonArray.put(jsonObject);
                    }
                    objectMap.put("fjList", jsonArray);
                }
            }
            //定位-社会面
            if (!TextUtils.isEmpty(dwQk) && dwQk.equals("1")) {
                objectMap.put("shmShengQhdm", shengQhdm);
                objectMap.put("shmShengQhmc", shengName);
                objectMap.put("shmShiQhdm", shiQhdm);
                objectMap.put("shmShiQhmc", shiName);
                objectMap.put("shmQuQhdm", quQhdm);
                objectMap.put("shmQuQhmc", quName);
                objectMap.put("shmXzQhdm", xzQhdm);
                objectMap.put("shmXzQhmc", xzName);
                objectMap.put("shmSqQhdm", sqQhdm);
                objectMap.put("shmSqQhmc", sqName);
                objectMap.put("shmDz", binding.etShmDz.getText().toString());
                objectMap.put("shmDh", binding.etShmLxfs.getText().toString());
            }
        } catch (JSONException e) {
        }
        binding.btSubmit.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), true, true, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    XToastUtil.showToast(HeChaSureActivity.this, getString(R.string.sub_sucess));
                    EventBus.getDefault().post(new TaskDataNewEntity());
                    EventBus.getDefault().post(new TaskSpListEntity());
                    finish();
                } else {
                    XToastUtil.showToast(HeChaSureActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSubmit.setEnabled(true);
            }

            @Override
            public void onFinished() {
                binding.btSubmit.setEnabled(true);
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.CHOOSE_REQUEST:
                    List<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                    if (null != selectList) {
                        for (LocalMedia media : selectList) {
                            if (!TextUtils.isEmpty(media.getCompressPath())) {
                                uploadFile(media.getCompressPath());
                            } else if (!TextUtils.isEmpty(media.getRealPath())) {
                                uploadFile(media.getRealPath());
                            } else if (!TextUtils.isEmpty(media.getPath())) {
                                uploadFile(media.getPath());
                            }
                        }
                    }
                    break;
                case 1:
                    //图片
//                    uploadFile(data.getStringExtra("path"));
                    try {
                        boolean isFinish = XFileUtil.bitmapToFilePath(this, BitmapFactory.decodeFile(cameraPhoto.getAbsolutePath()), photoUri, cameraPhoto);
                        if (isFinish) {
                            uploadFile(cameraPhoto.getAbsolutePath());
                        }
                    } catch (Exception e) {
                    }
                    break;
            }
        }
    }

    @Override
    protected void onPermissionOpenAll() {
        super.onPermissionOpenAll();
        if (from == 1) {
//            Intent intent = new Intent(this, CameraActivity.class);//调用系统相机
//            startActivityForResult(intent, 1);
            createFileMuLu();
            if (cameraPhoto != null) {
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                photoUri = XFileUtil.getUriFromFile(this, cameraPhoto);
                intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                startActivityForResult(intent, 1);
            }
        }
    }

    /**
     * 创建文件
     */
    private void createFileMuLu() {
        if ((ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED)
                && (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED)) {
            cameraPhoto = new File(PhotoUtil.createPath(this));
            try {
                if (cameraPhoto.exists()) {
                    cameraPhoto.delete();
                }
                cameraPhoto.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            // 权限未授权，需要申请权限
            requestPermission(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        }
    }

    //上传
    private void uploadFile(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        imageAddImg.addImage(xResultData.getData().get(0));
                        imageAddImg.notifyData();
                    }
                } else {
                    XToastUtil.showToast(HeChaSureActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PeopleTypeInfoEntity event) {
        if (event != null) {
            finish();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PeoStatusEntity event) {
        if (event != null) {
            finish();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
