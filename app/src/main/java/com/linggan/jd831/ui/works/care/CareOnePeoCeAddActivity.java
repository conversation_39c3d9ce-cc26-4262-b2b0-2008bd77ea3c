package com.linggan.jd831.ui.works.care;

import android.Manifest;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.RadioGroup;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.view.TimePickerView;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XGlideEngine;
import com.lgfzd.base.utils.XImageFileCompressEngine;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XPermissionUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.AssistanceTeamAdapter;
import com.linggan.jd831.bean.InputEntity;
import com.linggan.jd831.bean.OnePeoCeInfoEntity;
import com.linggan.jd831.bean.OnePeoCeListEntity;
import com.linggan.jd831.bean.OssFileEntity;
import com.linggan.jd831.bean.PeopleInfoEntity;
import com.linggan.jd831.bean.TaskCenListEntity;
import com.linggan.jd831.bean.TrainingInfoEntity;
import com.linggan.jd831.bean.ZiDianEntity;
import com.linggan.jd831.bean.ZiDianEventEntity;
import com.linggan.jd831.bean.assistance.AssistanceNrBean;
import com.linggan.jd831.bean.assistance.AssistanceXieYiShiEventEntity;
import com.linggan.jd831.databinding.ActivityOnePeoCeAddBinding;
import com.linggan.jd831.ui.common.InputInfoActivity;
import com.linggan.jd831.ui.common.MulChoiceListActivity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.ImageAddUtil;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.BaseZiDianDialog;
import com.linggan.jd831.widget.CodeNameDialog;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.xutils.http.RequestParams;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 类  名：谈话记录新增
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class CareOnePeoCeAddActivity extends XBaseActivity<ActivityOnePeoCeAddBinding> implements View.OnClickListener, RadioGroup.OnCheckedChangeListener {
    private String peoId, sfBfjh = "0", yjBh, onePeoCeBh, jlBh, bfLx, bfLxName, pagaLbCode, zlSPCode = "0", zlGaCode, sfWcbfCode = "0", sfTj = "0";
    private PeopleInfoEntity peopleListEntity;
    private ImageAddUtil imageAddImg;
    private ImageAddUtil imageAddZiFj, imageAddHelpFj;
    private int from = 0, size = 0;
    private ZiDianEventEntity helpCxEntity;
    private String careBh;//用来回显展示详情

    private AssistanceXieYiShiEventEntity xieYiShiEvent;
    private TrainingInfoEntity trainingInfoEntity;

    private String jyqkCode;//就业情况code


    @Override
    protected ActivityOnePeoCeAddBinding getViewBinding() {
        return ActivityOnePeoCeAddBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        XPermissionUtil.initPermission(this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE});
        peoId = getIntent().getStringExtra("id");
        bfLxName = getIntent().getStringExtra("bf");//这里是推荐关爱过来的
        bfLx = getIntent().getStringExtra("bfCode");//这里是推荐关爱过来的
        careBh = getIntent().getStringExtra("bh");//这里是推荐关爱过来的
        size = getIntent().getIntExtra("size", 0);//这里是推荐关爱过来的
        peopleListEntity = (PeopleInfoEntity) getIntent().getSerializableExtra("info");
        if (peopleListEntity != null) {
            if (peopleListEntity.getYjztbhList() != null && peopleListEntity.getYjztbhList().size() > 0 && peopleListEntity.getYjztbhList().get(0) != null) {
                yjBh = peopleListEntity.getYjztbhList().get(0).getYjztbh();
            }
            binding.tvName.setText(peopleListEntity.getXm());
            binding.tvWorkName.setText(peopleListEntity.getFzrxm());
            binding.tvIdCard.setText(StrUtils.hideIdCard(peopleListEntity.getZjhm()));
        } else {
            yjBh = getIntent().getStringExtra("yjBh");
            binding.tvName.setText(getIntent().getStringExtra("name"));
            binding.tvWorkName.setText(getIntent().getStringExtra("work"));
            binding.tvIdCard.setText(StrUtils.hideIdCard(getIntent().getStringExtra("card")));
        }
        StrUtils.getPhotoVideoText2(binding.plan.tvPhotoInfo, null);
        StrUtils.getPhotoVideoText2(binding.zl.tvPhotoInfo, null);
        StrUtils.getPhotoVideoText2(binding.help.tvPhotoInfo, null);

        binding.help.assistanceGroupRecycleNr.setLayoutManager(new LinearLayoutManager(this));

        imageAddImg = new ImageAddUtil(this, binding.plan.gridImg);
        imageAddImg.setOnImageAddListener(() -> {
            from = 0;
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                if (code.equals("1")) {
                    PictureSelector.create(this).openCamera(SelectMimeType.ofImage()).setCompressEngine(new XImageFileCompressEngine()).forResultActivity(PictureConfig.REQUEST_CAMERA);
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setCompressEngine(new XImageFileCompressEngine()).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        });
        imageAddZiFj = new ImageAddUtil(this, binding.zl.gridImg);
        imageAddZiFj.setOnImageAddListener(() -> {
            from = 1;
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                if (code.equals("1")) {
                    PictureSelector.create(this).openCamera(SelectMimeType.ofImage()).setCompressEngine(new XImageFileCompressEngine()).forResultActivity(PictureConfig.REQUEST_CAMERA);
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setCompressEngine(new XImageFileCompressEngine()).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        });
        imageAddHelpFj = new ImageAddUtil(this, binding.help.gridImg);
        imageAddHelpFj.setOnImageAddListener(() -> {
            from = 2;
            DialogUtils.showPhotoDialog(this, (code, id) -> {
                if (code.equals("1")) {
                    PictureSelector.create(this).openCamera(SelectMimeType.ofImage()).setCompressEngine(new XImageFileCompressEngine()).forResultActivity(PictureConfig.REQUEST_CAMERA);
                } else {
                    PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setImageEngine(XGlideEngine.createGlideEngine()).setCompressEngine(new XImageFileCompressEngine()).forResult(PictureConfig.CHOOSE_REQUEST);
                }
            });
        });
        EventBus.getDefault().register(this);
        //这里是推荐关爱过来-修为可以修改（2023-10-11）
        if (!TextUtils.isEmpty(careBh)) {
            binding.tvYrycType.setText(bfLxName);
            binding.tvYrycType.setEnabled(false);
            binding.tvYrycType.setCompoundDrawables(null, null, null, null);
            sfTj = "1";
            getUserData();
            binding.radioGroup.check(R.id.rb_help);
            sfWcbfCode = "1";
            binding.help.tvSfHelp.setText(StrUtils.getIsWanChengText("1"));
            binding.help.tvSfHelp.setEnabled(false);
            binding.plan.linOne.setVisibility(View.GONE);
            binding.help.linIs.setVisibility(View.VISIBLE);
            binding.help.linThree.setVisibility(View.VISIBLE);
//            binding.help.tvHelpCx.setEnabled(false);
            ZiDianEntity bagaTjsBean = (ZiDianEntity) getIntent().getSerializableExtra("bfcx");
            if (bagaTjsBean != null) {
                binding.help.tvHelpCx.setText(bagaTjsBean.getBfCxMc());
                List<ZiDianEntity> ziDianEntityList = new ArrayList<>();
                ZiDianEntity ziDianEntity = new ZiDianEntity();
                ziDianEntity.setBfCxMc(bagaTjsBean.getBfCxMc());
                ziDianEntity.setBfCx(bagaTjsBean.getBfCx());
                ziDianEntity.setBfLx(bagaTjsBean.getBfLx());
                ziDianEntity.setBfLxMc(bagaTjsBean.getBfLxMc());
                ziDianEntity.setChoice(true);
                ziDianEntityList.add(ziDianEntity);
                helpCxEntity = new ZiDianEventEntity();
                helpCxEntity.setZiDianEntityList(ziDianEntityList);
            }
        }
        binding.help.trainingInfo.getRoot().setVisibility(View.GONE);
        binding.help.etOtherHelpTrain.setVisibility(View.VISIBLE);
    }

    @Override
    protected void initListener() {
        binding.tvYrycType.setOnClickListener(this);
        binding.radioGroup.setOnCheckedChangeListener(this);
        binding.plan.tvOnePfjh.setOnClickListener(this);
        binding.plan.tvOnePlanTime.setOnClickListener(this);
        binding.btSure.setOnClickListener(this);
        binding.plan.tvGaType.setOnClickListener(this);
        //
        binding.zl.tvSpzl.setOnClickListener(this);
        binding.zl.tvPagaType.setOnClickListener(this);
        binding.zl.tvZlscTime.setOnClickListener(this);
        //
        binding.help.tvSfHelp.setOnClickListener(this);
        binding.help.tvCxTime.setOnClickListener(this);
        binding.help.tvRemark.setOnClickListener(this);
        binding.help.tvHelpCx.setOnClickListener(this);
        binding.help.etOtherHelpCx.setOnClickListener(this);
        binding.help.tvAssistanceGroup.setOnClickListener(this);
        binding.help.tipsAssistanceGroup.setOnClickListener(this);
        binding.help.etOtherHelpTrain.setOnClickListener(this);
        binding.help.trainingInfo.getRoot().setOnClickListener(this);
        binding.help.etOtherHelpEmployment.setOnClickListener(this);
    }

    @Override
    protected void getData() {
        //编辑展示
        OnePeoCeInfoEntity onePeoCeInfo = (OnePeoCeInfoEntity) getIntent().getSerializableExtra("msg");
        if (onePeoCeInfo != null) {
            sfTj = onePeoCeInfo.getSfTj();
            careBh = onePeoCeInfo.getTjBh();
            onePeoCeBh = onePeoCeInfo.getBh();
            jlBh = onePeoCeInfo.getJlBh();
            peoId = onePeoCeInfo.getXyrbh();
            getUserData();
            if (onePeoCeInfo.getBfLx() != null) {
                bfLx = onePeoCeInfo.getBfLx().getCode();
                binding.tvYrycType.setText(onePeoCeInfo.getBfLx().getName());
            }

            // 处理就业帮扶类型相关UI
            if (!TextUtils.isEmpty(bfLx) && bfLx.equals("1")) {
                binding.help.llTrainingSituation.setVisibility(View.VISIBLE);
                
                // 处理培训情况数据
                if (onePeoCeInfo.getPxbh() != null) {
                    // 有培训信息，显示培训详情
                    trainingInfoEntity = new TrainingInfoEntity();
                    trainingInfoEntity.setTrainingContent(onePeoCeInfo.getPxnr() != null ? onePeoCeInfo.getPxnr() : "");
                    trainingInfoEntity.setTrainingTime(onePeoCeInfo.getPxsj() != null ? onePeoCeInfo.getPxsj() : "");
                    trainingInfoEntity.setTrainingAddress(onePeoCeInfo.getPxdz() != null ? onePeoCeInfo.getPxdz() : "");
                    trainingInfoEntity.setJd(onePeoCeInfo.getJd() != null ? onePeoCeInfo.getJd() : "");
                    trainingInfoEntity.setWd(onePeoCeInfo.getWd() != null ? onePeoCeInfo.getWd() : "");
                    
                    binding.help.trainingInfo.getRoot().setVisibility(View.VISIBLE);
                    binding.help.etOtherHelpTrain.setVisibility(View.GONE);
                    binding.help.trainingInfo.tvTrainingContent.setText(onePeoCeInfo.getPxnr() != null ? onePeoCeInfo.getPxnr() : "");
                    binding.help.trainingInfo.tvTrainingTime.setText(onePeoCeInfo.getPxsj() != null ? onePeoCeInfo.getPxsj() : "");
                    binding.help.trainingInfo.tvTrainingLocation.setText(onePeoCeInfo.getPxdz() != null ? onePeoCeInfo.getPxdz() : "");
                }
                
                // 处理就业情况数据
                if (!TextUtils.isEmpty(onePeoCeInfo.getJyqk())) {
                    jyqkCode = onePeoCeInfo.getJyqk();
                    if ("1".equals(jyqkCode)) {
                        binding.help.etOtherHelpEmployment.setText("已就业");
                    } else if ("2".equals(jyqkCode)) {
                        binding.help.etOtherHelpEmployment.setText("未就业");
                    }
                }
            } else {
                binding.help.llTrainingSituation.setVisibility(View.GONE);
            }

            if (!TextUtils.isEmpty(onePeoCeInfo.getSfBfjh())) {
                sfBfjh = onePeoCeInfo.getSfBfjh();
                binding.plan.tvOnePfjh.setText(StrUtils.getIsQiYongText(onePeoCeInfo.getSfBfjh()));
                if (onePeoCeInfo.getSfBfjh().equals("0")) {
                    binding.help.llSupportGroup.setVisibility(View.GONE);
                    binding.plan.linIs.setVisibility(View.GONE);
                } else {
                    binding.help.llSupportGroup.setVisibility(View.VISIBLE);
                    binding.plan.linIs.setVisibility(View.VISIBLE);
                }
            }
            binding.plan.etPlanDec.setText(onePeoCeInfo.getBfjhsm());
            binding.plan.etPlanName.setText(onePeoCeInfo.getCyry());
            if (onePeoCeInfo.getJhBfCx() != null) {
                pagaLbCode = onePeoCeInfo.getJhBfCx().getBfCx();
                binding.plan.tvGaType.setText(onePeoCeInfo.getJhBfCx().getBfCxMc());
            }
            binding.plan.tvOnePlanTime.setText(onePeoCeInfo.getJhwcsj());
            if (onePeoCeInfo.getJhzdFj() != null && onePeoCeInfo.getJhzdFj().size() > 0) {
                for (int i = 0; i < onePeoCeInfo.getJhzdFj().size(); i++) {
                    OssFileEntity ossFileEntity = new OssFileEntity();
                    ossFileEntity.setSavePath(onePeoCeInfo.getJhzdFj().get(i).getLj());
                    ossFileEntity.setOriginName(onePeoCeInfo.getJhzdFj().get(i).getMc());
                    ossFileEntity.setFileSize(onePeoCeInfo.getJhzdFj().get(i).getDx());
                    imageAddImg.addImage(ossFileEntity);
                    imageAddImg.notifyData();
                }
            }
            //
            zlSPCode = onePeoCeInfo.getSfTjzl();
            binding.zl.tvSpzl.setText(StrUtils.getIsXuYaoText(onePeoCeInfo.getSfTjzl()));
            if (onePeoCeInfo.getSfTjzl().equals("0")) {
                binding.zl.linIs.setVisibility(View.GONE);
            } else {
                binding.zl.linIs.setVisibility(View.VISIBLE);
            }
            binding.zl.etUnit.setText(onePeoCeInfo.getSpdw());
            if (onePeoCeInfo.getZlBfCx() != null) {
                zlGaCode = onePeoCeInfo.getZlBfCx().getBfCx();
                binding.zl.tvPagaType.setText(onePeoCeInfo.getZlBfCx().getBfCxMc());
            }
            binding.zl.etZlDec.setText(onePeoCeInfo.getZlsm());
            binding.zl.tvZlscTime.setText(onePeoCeInfo.getTjsj());
            if (onePeoCeInfo.getZltjFj() != null && onePeoCeInfo.getZltjFj().size() > 0) {
                for (int i = 0; i < onePeoCeInfo.getZltjFj().size(); i++) {
                    OssFileEntity ossFileEntity = new OssFileEntity();
                    ossFileEntity.setSavePath(onePeoCeInfo.getZltjFj().get(i).getLj());
                    ossFileEntity.setOriginName(onePeoCeInfo.getZltjFj().get(i).getMc());
                    ossFileEntity.setFileSize(onePeoCeInfo.getZltjFj().get(i).getDx());
                    imageAddZiFj.addImage(ossFileEntity);
                    imageAddZiFj.notifyData();
                }
            }
            //
            sfWcbfCode = onePeoCeInfo.getSfWcbf();
            binding.help.tvSfHelp.setText(StrUtils.getIsWanChengText(onePeoCeInfo.getSfWcbf()));
            if (onePeoCeInfo.getSfWcbf().equals("0")) {
                binding.help.linIs.setVisibility(View.GONE);
            } else {
                binding.help.linIs.setVisibility(View.VISIBLE);
            }
            binding.help.tvCxTime.setText(onePeoCeInfo.getCxsxsj());
            binding.help.tvRemark.setText(onePeoCeInfo.getBfcgbz());

            if (onePeoCeInfo.getBfcxs() != null && onePeoCeInfo.getBfcxs().size() > 0) {
                binding.help.tvHelpCx.setText(StrUtils.listToZiDianText(onePeoCeInfo.getBfcxs()));

                List<ZiDianEntity> ziDianEntityList = new ArrayList<>();
                for (int i = 0; i < onePeoCeInfo.getBfcxs().size(); i++) {
                    ZiDianEntity ziDianEntity = new ZiDianEntity();
                    ziDianEntity.setBfCxMc(onePeoCeInfo.getBfcxs().get(i).getBfCxMc());
                    ziDianEntity.setBfCx(onePeoCeInfo.getBfcxs().get(i).getBfCx());
                    ziDianEntity.setBfLx(onePeoCeInfo.getBfcxs().get(i).getBfLx());
                    ziDianEntity.setBfLxMc(onePeoCeInfo.getBfcxs().get(i).getBfLxMc());
                    ziDianEntity.setChoice(true);
                    ziDianEntityList.add(ziDianEntity);
                }
                helpCxEntity = new ZiDianEventEntity();
                helpCxEntity.setZiDianEntityList(ziDianEntityList);
            }
            if (onePeoCeInfo.getBfcgFj() != null && onePeoCeInfo.getBfcgFj().size() > 0) {
                for (int i = 0; i < onePeoCeInfo.getBfcgFj().size(); i++) {
                    OssFileEntity ossFileEntity = new OssFileEntity();
                    ossFileEntity.setSavePath(onePeoCeInfo.getBfcgFj().get(i).getLj());
                    ossFileEntity.setOriginName(onePeoCeInfo.getBfcgFj().get(i).getMc());
                    ossFileEntity.setFileSize(onePeoCeInfo.getBfcgFj().get(i).getDx());
                    imageAddHelpFj.addImage(ossFileEntity);
                    imageAddHelpFj.notifyData();
                }
            }
            // 处理帮扶小组数据
            if (onePeoCeInfo.getBfxzList() != null && !onePeoCeInfo.getBfxzList().isEmpty()) {
                // 创建AssistanceXieYiShiEventEntity对象
                xieYiShiEvent = new AssistanceXieYiShiEventEntity();
                
                // 将bfxzList转换为AssistanceNrBean列表
                List<AssistanceNrBean> assistanceNrBeanList = new ArrayList<>();
                for (OnePeoCeInfoEntity.BfxzBean bfxzBean : onePeoCeInfo.getBfxzList()) {
                    AssistanceNrBean assistanceNrBean = new AssistanceNrBean();
                    // 对于long类型，直接转换为字符串，不需要判空
                    assistanceNrBean.setBh(String.valueOf(bfxzBean.getBh()));
                    assistanceNrBean.setXm(bfxzBean.getXm() != null ? bfxzBean.getXm() : "");
                    assistanceNrBean.setLxdh(bfxzBean.getLxdh() != null ? bfxzBean.getLxdh() : "");
                    assistanceNrBean.setZw(bfxzBean.getZw() != null ? bfxzBean.getZw() : "");
                    assistanceNrBean.setBz(bfxzBean.getBz() != null ? bfxzBean.getBz() : "");
                    assistanceNrBeanList.add(assistanceNrBean);
                }
                
                // 设置到xieYiShiEvent中
                xieYiShiEvent.setNrBeanList(assistanceNrBeanList);
                
                // 更新UI显示
                binding.help.assistanceGroupRecycleNr.setVisibility(View.VISIBLE);
                binding.help.tipsAssistanceGroup.setVisibility(View.GONE);
                AssistanceTeamAdapter assistanceTeamAdapter = new AssistanceTeamAdapter(this, assistanceNrBeanList, "");
                binding.help.assistanceGroupRecycleNr.setAdapter(assistanceTeamAdapter);
                assistanceTeamAdapter.setOnItemClickListener(position -> {
                    Bundle bundle = new Bundle();
                    if (xieYiShiEvent != null) {
                        bundle.putSerializable("info", xieYiShiEvent);
                    }
                    XIntentUtil.redirectToNextActivity(this, AssistanceTeamActivity.class, bundle);
                });
            } else {
                binding.help.assistanceGroupRecycleNr.setVisibility(View.GONE);
                binding.help.tipsAssistanceGroup.setVisibility(View.VISIBLE);
            }
        }
    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        switch (checkedId) {
            case R.id.rb_plan:
                binding.plan.linOne.setVisibility(View.VISIBLE);
                binding.zl.linTwo.setVisibility(View.GONE);
                binding.help.linThree.setVisibility(View.GONE);
                break;
            case R.id.rb_zl:
                binding.zl.linTwo.setVisibility(View.VISIBLE);
                binding.help.linThree.setVisibility(View.GONE);
                binding.plan.linOne.setVisibility(View.GONE);
                break;
            case R.id.rb_help:
                binding.help.linThree.setVisibility(View.VISIBLE);
                binding.plan.linOne.setVisibility(View.GONE);
                binding.zl.linTwo.setVisibility(View.GONE);
                break;
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.bt_sure) {
            if (ButtonUtils.isFastClick()) {
                postData();
            }
        } else if (v.getId() == R.id.et_other_help_employment) {
            // 就业情况字典
            // 1已就业、2未就业
            ArrayList<ZiDianEntity> ziDianEntityList = new ArrayList<ZiDianEntity>() {{
                add(new ZiDianEntity() {{
                    setDm("1");
                    setMc("已就业");
                }});
                add(new ZiDianEntity() {{
                    setDm("2");
                    setMc("未就业");
                }});
            }};

            BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, ziDianEntityList);
            dianDialog.setOnClickDataListener(() -> {
                jyqkCode = dianDialog.getData().getDm();
                binding.help.etOtherHelpEmployment.setText(dianDialog.getData().getMc());
            });
            dianDialog.show();
        } else if (v.getId() == R.id.tv_yryc_type) {
            //一人一策帮扶类型--数据字典
            FactoryUtils.getBaseDataType(this, "bf_lx", result -> {
                BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, (ArrayList<ZiDianEntity>) result);
                dianDialog.setOnClickDataListener(() -> {
                    binding.tvYrycType.setText(dianDialog.getData().getMc());
                    bfLx = dianDialog.getData().getDm();
                    //清除已选择数据
                    binding.plan.tvGaType.setText("");
                    pagaLbCode = "";
                    binding.zl.tvPagaType.setText("");
                    zlGaCode = "";
                    if (helpCxEntity != null) {
                        helpCxEntity = null;
                    }
                    binding.help.tvHelpCx.setText("");

                    if (!TextUtils.isEmpty(bfLx) && bfLx.equals("1")) {
                        binding.help.llTrainingSituation.setVisibility(View.VISIBLE);
                    } else {
                        binding.help.llTrainingSituation.setVisibility(View.GONE);
                    }
                });
                dianDialog.show();
            });
        } else if (v.getId() == R.id.tv_one_pfjh) {
            //是否启用帮扶计划
            CodeNameDialog dianDialog = new CodeNameDialog(this, StrUtils.getIsQiYongList());
            dianDialog.setOnClickDataListener(() -> {
                binding.plan.tvOnePfjh.setText(dianDialog.getCode().getName());
                sfBfjh = dianDialog.getCode().getCode();
                if (dianDialog.getCode().getCode().equals("0")) {
                    binding.help.llSupportGroup.setVisibility(View.GONE);
                    binding.plan.linIs.setVisibility(View.GONE);
                } else {
                    binding.help.llSupportGroup.setVisibility(View.VISIBLE);
                    binding.plan.linIs.setVisibility(View.VISIBLE);
                }
            });
            dianDialog.show();

        } else if (v.getId() == R.id.tv_one_plan_time) {
            //帮扶计划（预计）完成时间
            Calendar startDate = Calendar.getInstance();
            startDate.set(1900, 0, 1);
            Calendar selectedDate = Calendar.getInstance();
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.plan.tvOnePlanTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setSubmitColor(R.color.black).setRangDate(startDate, selectedDate).setDate(selectedDate).build();
            pvTime.show();

        } else if (v.getId() == R.id.tv_ga_type) {
            //关爱类别
            if (TextUtils.isEmpty(bfLx)) {
                XToastUtil.showToast(this, "请先选择一人一策帮扶类型");
                return;
            }
            FactoryUtils.getCareHelpType(this, bfLx, result -> {
                BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, (ArrayList<ZiDianEntity>) result);
                dianDialog.setOnClickDataListener(() -> {
                    binding.plan.tvGaType.setText(dianDialog.getData().getBfCxMc());
                    pagaLbCode = dianDialog.getData().getBfCx();
                });
                dianDialog.show();
            });
        } else if (v.getId() == R.id.tv_spzl) {
            //是否需要提交资料审批
            CodeNameDialog dianDialog = new CodeNameDialog(this, StrUtils.getIsXuYaoList());
            dianDialog.setOnClickDataListener(() -> {
                binding.zl.tvSpzl.setText(dianDialog.getCode().getName());
                zlSPCode = dianDialog.getCode().getCode();
                if (dianDialog.getCode().getCode().equals("0")) {
                    binding.zl.linIs.setVisibility(View.GONE);
                } else {
                    binding.zl.linIs.setVisibility(View.VISIBLE);
                }
            });
            dianDialog.show();
        } else if (v.getId() == R.id.tv_paga_type) {
            //资料提交的平安关爱的类别
            if (TextUtils.isEmpty(bfLx)) {
                XToastUtil.showToast(this, "请先选择一人一策帮扶类型");
                return;
            }
            FactoryUtils.getCareHelpType(this, bfLx, result -> {
                BaseZiDianDialog dianDialog = new BaseZiDianDialog(this, (ArrayList<ZiDianEntity>) result);
                dianDialog.setOnClickDataListener(() -> {
                    binding.zl.tvPagaType.setText(dianDialog.getData().getBfCxMc());
                    zlGaCode = dianDialog.getData().getBfCx();
                });
                dianDialog.show();
            });
        } else if (v.getId() == R.id.tv_zlsc_time) {
            //资料上传时间
            Calendar startDate = Calendar.getInstance();
            startDate.set(1900, 0, 1);
            Calendar selectedDate = Calendar.getInstance();
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.zl.tvZlscTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setSubmitColor(R.color.black).setRangDate(startDate, selectedDate).setDate(selectedDate).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_sf_help) {
            //是否已经完成帮扶
            CodeNameDialog dianDialog = new CodeNameDialog(this, StrUtils.getIsWanChengList());
            dianDialog.setOnClickDataListener(() -> {
                binding.help.tvSfHelp.setText(dianDialog.getCode().getName());
                sfWcbfCode = dianDialog.getCode().getCode();
                if (dianDialog.getCode().getCode().equals("0")) {
                    binding.help.linIs.setVisibility(View.GONE);
                } else {
                    binding.help.linIs.setVisibility(View.VISIBLE);
                }
            });
            dianDialog.show();
        } else if (v.getId() == R.id.tv_cx_time) {
            //成效时间
            Calendar startDate = Calendar.getInstance();
            startDate.set(1900, 0, 1);
            Calendar selectedDate = Calendar.getInstance();
            TimePickerView pvTime = new TimePickerBuilder(this, (date, v1) -> {
                String timeDate = new SimpleDateFormat(XConstantUtils.YMD).format(date);
                binding.help.tvCxTime.setText(timeDate);
            }).setType(new boolean[]{true, true, true, false, false, false}).setCancelColor(R.color.black).setSubmitColor(R.color.black).setRangDate(startDate, selectedDate).setDate(selectedDate).build();
            pvTime.show();
        } else if (v.getId() == R.id.tv_remark) {
            //备注
            Bundle bundle = new Bundle();
            bundle.putString("title", "备注");
            bundle.putString("hint", "请输入备注");
            bundle.putInt("tab", 1);
            bundle.putString("info", binding.help.tvRemark.getText().toString());
            XIntentUtil.redirectToNextActivity(this, InputInfoActivity.class, bundle);
        } else if (v.getId() == R.id.tv_help_cx) {
            //帮扶成效（可多选)
            if (TextUtils.isEmpty(bfLx)) {
                XToastUtil.showToast(this, "请先选择一人一策帮扶类型");
                return;
            }
            Bundle bundle = new Bundle();
            bundle.putInt("from", 1);
            bundle.putString("param", "kdx");
            bundle.putString("val", bfLx);
            bundle.putString("title", "帮扶成效（可多选)");
            if (helpCxEntity != null) {
                bundle.putSerializable("data", helpCxEntity);
            }
            XIntentUtil.redirectToNextActivity(this, MulChoiceListActivity.class, bundle);
        } else if (v.getId() == R.id.et_other_help_cx) {
            //在其他帮扶成效
            Bundle bundle = new Bundle();
            bundle.putString("title", "其他帮扶成效");
            bundle.putString("hint", "请输入在其他帮扶成效");
            bundle.putString("info", binding.help.etOtherHelpCx.getText().toString());
            bundle.putString("len", "200");
            bundle.putInt("tab", 2);
            XIntentUtil.redirectToNextActivity(this, InputInfoActivity.class, bundle);
        } else if (v.getId() == R.id.tv_assistance_group || v.getId() == R.id.tips_assistance_group) {
            //谈话内容
            Bundle bundle = new Bundle();
            if (xieYiShiEvent != null) {
                bundle.putSerializable("info", xieYiShiEvent);
            }
            XIntentUtil.redirectToNextActivity(this, AssistanceTeamActivity.class, bundle);
        } else if (v.getId() == R.id.et_other_help_train) {
            // 培训情况
            Bundle bundle = new Bundle();
            bundle.putString("id", peoId);
            bundle.putSerializable("info", peopleListEntity);
            if (trainingInfoEntity != null) {
                bundle.putSerializable("trainingInfo", trainingInfoEntity);
            }
            XIntentUtil.redirectToSubActivity(this, TrainingSituationActivity.class, bundle, 1001);
        } else if (v.getId() == R.id.training_info) {
            // 培训情况
            Bundle bundle = new Bundle();
            bundle.putString("id", peoId);
            bundle.putSerializable("info", peopleListEntity);
            if (trainingInfoEntity != null) {
                bundle.putSerializable("trainingInfo", trainingInfoEntity);
            }
            XIntentUtil.redirectToSubActivity(this, TrainingSituationActivity.class, bundle, 1001);
        }
    }

    // 帮扶小组接受数据
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(AssistanceXieYiShiEventEntity item) {
        if (item != null) {
            xieYiShiEvent = item;
            binding.help.assistanceGroupRecycleNr.setVisibility(View.VISIBLE);
            binding.help.tipsAssistanceGroup.setVisibility(View.GONE);
            AssistanceTeamAdapter assistanceTeamAdapter = new AssistanceTeamAdapter(this, item.getNrBeanList(), "");
            binding.help.assistanceGroupRecycleNr.setAdapter(assistanceTeamAdapter);
            assistanceTeamAdapter.setOnItemClickListener(position -> {
                Bundle bundle = new Bundle();
                if (xieYiShiEvent != null) {
                    bundle.putSerializable("info", xieYiShiEvent);
                }
                XIntentUtil.redirectToNextActivity(this, AssistanceTeamActivity.class, bundle);
            });
        }
    }

    /**
     * 数据提交
     */
    private void postData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.ONE_PEO_CE_ADD);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        JSONObject objectMap = new JSONObject();
        try {
            if (!TextUtils.isEmpty(jlBh)) {
                objectMap.put("bh", jlBh);
            }
            objectMap.put("xyrbh", peoId);
            objectMap.put("yjztbh", yjBh);
            objectMap.put("bfLx", bfLx);
            objectMap.put("sjLy", "3");
            JSONObject objectChild = new JSONObject();
            if (!TextUtils.isEmpty(onePeoCeBh)) {
                objectChild.put("bh", onePeoCeBh);
            }
            if (TextUtils.isEmpty(bfLx)) {
                XToastUtil.showToast(this, "请选择一人一策帮扶类型");
                return;
            }
            //计划
            if (TextUtils.isEmpty(sfBfjh)) {
                XToastUtil.showToast(this, "请选择是否启用帮扶计划");
                return;
            }
            if (!TextUtils.isEmpty(sfBfjh) && sfBfjh.equals("1") && TextUtils.isEmpty(binding.plan.etPlanDec.getText().toString())) {
                XToastUtil.showToast(this, "请输入帮扶计划说明");
                return;
            }
//            if (!TextUtils.isEmpty(sfBfjh) && sfBfjh.equals("1") && TextUtils.isEmpty(binding.plan.etPlanName.getText().toString())) {
//                XToastUtil.showToast(this, "请输入计划参与平安关爱的人员");
//                return;
//            }
            if (!TextUtils.isEmpty(sfBfjh) && sfBfjh.equals("1") && TextUtils.isEmpty(pagaLbCode)) {
                XToastUtil.showToast(this, "请选择计划平安关爱的类别");
                return;
            }
            if (!TextUtils.isEmpty(sfBfjh) && sfBfjh.equals("1") && TextUtils.isEmpty(binding.plan.tvOnePlanTime.getText().toString())) {
                XToastUtil.showToast(this, "请选择帮扶计划（预计）完成时间");
                return;
            }
            objectChild.put("sfBfjh", sfBfjh);

            objectChild.put("bfjhsm", binding.plan.etPlanDec.getText().toString());
//            objectChild.put("cyry", binding.plan.etPlanName.getText().toString());

            objectChild.put("jhBfCx", pagaLbCode);
            objectChild.put("qtbfcx", binding.help.etOtherHelpCx.getText());
            objectChild.put("jhwcsj", binding.plan.tvOnePlanTime.getText().toString());
            JSONArray jsonArrayJh = new JSONArray();
            if (imageAddImg != null && imageAddImg.getPaths() != null && imageAddImg.getPaths().size() > 0) {
                for (int i = 0; i < imageAddImg.getPaths().size(); i++) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("lj", imageAddImg.getPaths().get(i).getSavePath());
                    jsonObject.put("mc", imageAddImg.getPaths().get(i).getOriginName());
                    jsonObject.put("hz", StrUtils.getFileType(imageAddImg.getPaths().get(i).getSavePath()));
                    jsonObject.put("dx", imageAddImg.getPaths().get(i).getFileSize());
                    jsonArrayJh.put(jsonObject);
                }
                objectChild.put("jhzdFj", jsonArrayJh);
            }
            if (sfBfjh.equals("1") && jsonArrayJh.length() <= 0) {
                XToastUtil.showToast(this, "请上传计划制定附件");
                return;
            }
            //资料
            if (TextUtils.isEmpty(zlSPCode)) {
                XToastUtil.showToast(this, "请选择是否需要提交资料审批");
                return;
            }
            if (!TextUtils.isEmpty(zlSPCode) && zlSPCode.equals("1") && TextUtils.isEmpty(binding.zl.etUnit.getText().toString())) {
                XToastUtil.showToast(this, "请输入审批单位");
                return;
            }
            if (!TextUtils.isEmpty(zlSPCode) && zlSPCode.equals("1") && TextUtils.isEmpty(zlGaCode)) {
                XToastUtil.showToast(this, "请选择资料提交的平安关爱的类别");
                return;
            }
            if (!TextUtils.isEmpty(zlSPCode) && zlSPCode.equals("1") && TextUtils.isEmpty(pagaLbCode)) {
                XToastUtil.showToast(this, "请选择计划平安关爱的类别");
                return;
            }
            if (!TextUtils.isEmpty(zlSPCode) && zlSPCode.equals("1") && TextUtils.isEmpty(binding.zl.etZlDec.getText().toString())) {
                XToastUtil.showToast(this, "请输入资料说明");
                return;
            }
            if (!TextUtils.isEmpty(zlSPCode) && zlSPCode.equals("1") && TextUtils.isEmpty(binding.zl.tvZlscTime.getText().toString())) {
                XToastUtil.showToast(this, "请选择资料上传时间");
                return;
            }
            objectChild.put("sfTjzl", zlSPCode);
            objectChild.put("spdw", binding.zl.etUnit.getText().toString());
            objectChild.put("zlBfCx", zlGaCode);
            objectChild.put("zlsm", binding.zl.etZlDec.getText().toString());
            objectChild.put("tjsj", binding.zl.tvZlscTime.getText().toString());
            JSONArray jsonArrayZl = new JSONArray();
            if (imageAddZiFj != null && imageAddZiFj.getPaths() != null && imageAddZiFj.getPaths().size() > 0) {
                for (int i = 0; i < imageAddZiFj.getPaths().size(); i++) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("lj", imageAddZiFj.getPaths().get(i).getSavePath());
                    jsonObject.put("mc", imageAddZiFj.getPaths().get(i).getOriginName());
                    jsonObject.put("hz", StrUtils.getFileType(imageAddZiFj.getPaths().get(i).getSavePath()));
                    jsonObject.put("dx", imageAddZiFj.getPaths().get(i).getFileSize());
                    jsonArrayZl.put(jsonObject);
                }
                objectChild.put("zltjFj", jsonArrayZl);
            }
            if (zlSPCode.equals("1") && jsonArrayZl.length() <= 0) {
                XToastUtil.showToast(this, "请上传资料附件");
                return;
            }
            //帮扶
            if (TextUtils.isEmpty(sfWcbfCode)) {
                XToastUtil.showToast(this, "请选择是否已经完成帮扶");
                return;
            }
            JSONArray jsonArray = new JSONArray();
            if (helpCxEntity != null && helpCxEntity.getZiDianEntityList() != null && helpCxEntity.getZiDianEntityList().size() > 0) {
                for (int i = 0; i < helpCxEntity.getZiDianEntityList().size(); i++) {
                    if (helpCxEntity.getZiDianEntityList().get(i).isChoice())
                        jsonArray.put(helpCxEntity.getZiDianEntityList().get(i).getBfCx());
                }
            }
            objectChild.put("bfcxs", jsonArray);
            if (sfWcbfCode.equals("1") && jsonArray.length() <= 0) {
                XToastUtil.showToast(this, "请选择帮扶成效");
                return;
            }
            if (!TextUtils.isEmpty(sfWcbfCode) && sfWcbfCode.equals("1") && TextUtils.isEmpty(binding.help.tvCxTime.getText().toString())) {
                XToastUtil.showToast(this, "请选择成效生效时间");
                return;
            }
            objectChild.put("sfWcbf", sfWcbfCode);
            objectChild.put("cxsxsj", binding.help.tvCxTime.getText().toString());
            objectChild.put("bfcgbz", binding.help.tvRemark.getText().toString());
            JSONArray jsonArrayBF = new JSONArray();
            if (imageAddHelpFj != null && imageAddHelpFj.getPaths() != null && imageAddHelpFj.getPaths().size() > 0) {
                for (int i = 0; i < imageAddHelpFj.getPaths().size(); i++) {
                    JSONObject jsonObjectSP = new JSONObject();
                    jsonObjectSP.put("lj", imageAddHelpFj.getPaths().get(i).getSavePath());
                    jsonObjectSP.put("mc", imageAddHelpFj.getPaths().get(i).getOriginName());
                    jsonObjectSP.put("hz", StrUtils.getFileType(imageAddHelpFj.getPaths().get(i).getOriginName()));
                    jsonObjectSP.put("dx", imageAddHelpFj.getPaths().get(i).getFileSize());
                    jsonArrayBF.put(jsonObjectSP);
                }
            }
            objectChild.put("bfcgFj", jsonArrayBF);
            objectMap.put("bfjdXz", objectChild);
            objectMap.put("sfTj", sfTj);
            JSONArray jsonArrayTrain = new JSONArray();
            if ((!TextUtils.isEmpty(sfBfjh) && sfBfjh.equals("1"))) {
                if (xieYiShiEvent != null && !xieYiShiEvent.getNrBeanList().isEmpty()) {
                    for (AssistanceNrBean item : xieYiShiEvent.getNrBeanList()) {
                        JSONObject jsonObjectTrain = new JSONObject();
                        if (!TextUtils.isEmpty(item.getBh())) {
                            jsonObjectTrain.put("bh", item.getBh());
                        }

                        jsonObjectTrain.put("xm", item.getXm());
                        jsonObjectTrain.put("lxdh", item.getLxdh());
                        jsonObjectTrain.put("zw", item.getZw());
                        jsonObjectTrain.put("bz", item.getBz());
                        jsonArrayTrain.put(jsonObjectTrain);
                    }
                    objectMap.put("bfxzList", jsonArrayTrain);
                }
            }

            // 就业帮扶才有培训情况和就业情况
            if (!TextUtils.isEmpty(bfLx) && bfLx.equals("1")) {
                if (trainingInfoEntity != null) {
                    if (!TextUtils.isEmpty(trainingInfoEntity.getPxbh())) {
                        objectMap.put("pxbh", trainingInfoEntity.getPxbh());
                    }
                    objectMap.put("pxnr", trainingInfoEntity.getTrainingContent());
                    objectMap.put("pxsj", trainingInfoEntity.getTrainingTime());
                    objectMap.put("pxdz", trainingInfoEntity.getTrainingAddress());
                    objectMap.put("jd", trainingInfoEntity.getJd());
                    objectMap.put("wd", trainingInfoEntity.getWd());
                    objectMap.put("button", 1);
                }

                if (!TextUtils.isEmpty(jyqkCode)) {
                    objectMap.put("jyqk", jyqkCode);
                }
            }

            if (!objectMap.has("button")) {
                objectMap.put("button", 0);
            }

            if (!TextUtils.isEmpty(careBh)) {
                objectMap.put("tjBh", careBh);
            }
        } catch (JSONException e) {
        }
        binding.btSure.setEnabled(false);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), objectMap.toString());
        XHttpUtils.postJsonNoCan(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, getString(R.string.submiting)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData xResultData = new Gson().fromJson(result, new TypeToken<XResultData>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (!TextUtils.isEmpty(onePeoCeBh)) {
                        XToastUtil.showToast(CareOnePeoCeAddActivity.this, getString(R.string.edit_sucess));
                    } else {
                        XToastUtil.showToast(CareOnePeoCeAddActivity.this, getString(R.string.add_sucess));
                    }
                    if (size == 1) {
                        EventBus.getDefault().post(new TaskCenListEntity());
                    } else {
                        EventBus.getDefault().post(new OnePeoCeListEntity());
                    }
                    finish();
                } else {
                    XToastUtil.showToast(CareOnePeoCeAddActivity.this, xResultData.getErrorInfo());
                    binding.btSure.setEnabled(true);
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                binding.btSure.setEnabled(true);
            }

            @Override
            public void onFinished() {
            }
        });

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && data != null) {
            switch (requestCode) {
                case 1001:
                    trainingInfoEntity = (TrainingInfoEntity) data.getSerializableExtra("trainingInfo");
                    if (trainingInfoEntity != null) {
                        binding.help.trainingInfo.getRoot().setVisibility(View.VISIBLE);
                        binding.help.etOtherHelpTrain.setVisibility(View.GONE);
                        binding.help.trainingInfo.tvTrainingContent.setText(trainingInfoEntity.getTrainingContent());
                        binding.help.trainingInfo.tvTrainingTime.setText(trainingInfoEntity.getTrainingTime());
                        binding.help.trainingInfo.tvTrainingLocation.setText(trainingInfoEntity.getTrainingAddress());
                    }
                    break;
                case PictureConfig.REQUEST_CAMERA:
                case PictureConfig.CHOOSE_REQUEST:
                    ArrayList<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                    if (null != selectList) {
                        for (LocalMedia media : selectList) {
                            if (media.isCompressed()) {
                                //图片
                                uploadFile(media.getCompressPath());
                            } else if (!TextUtils.isEmpty(media.getRealPath())) {
                                uploadFile(media.getRealPath());
                            }
                        }
                    }
                    break;
            }
        }
    }

    //上传
    private void uploadFile(String path) {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FILE_UPLOAD);
        requestParams.addBodyParameter("file", new File(path));
        XHttpUtils.uploadFile(this, requestParams, DialogUtils.showLoadDialog(this, getString(R.string.uploading)), new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<List<OssFileEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<List<OssFileEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null && xResultData.getData().size() > 0) {
                        if (from == 0) {
                            imageAddImg.addImage(xResultData.getData().get(0));
                            imageAddImg.notifyData();
                        } else if (from == 1) {
                            imageAddZiFj.addImage(xResultData.getData().get(0));
                            imageAddZiFj.notifyData();
                        } else {
                            imageAddHelpFj.addImage(xResultData.getData().get(0));
                            imageAddHelpFj.notifyData();
                        }
                    }
                } else {
                    XToastUtil.showToast(CareOnePeoCeAddActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    /**
     * 人员行信息
     */
    private void getUserData() {
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "xyrbh=" + peoId);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.PEOPLE_INFO + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(this, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<PeopleInfoEntity> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<PeopleInfoEntity>>() {
                }.getType());
                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    if (xResultData.getData().getYjztbhList() != null && xResultData.getData().getYjztbhList().size() > 0 && xResultData.getData().getYjztbhList().get(0) != null) {
                        yjBh = xResultData.getData().getYjztbhList().get(0).getYjztbh();
                    }
                    binding.tvName.setText(xResultData.getData().getXm());
//                    binding.tvWorkName.setText(xResultData.getData().getFzrxm());
                    binding.tvWorkName.setText(UserInfoUtils.getUserInfo().getUserName());
                    binding.tvIdCard.setText(StrUtils.hideIdCard(xResultData.getData().getZjhm()));
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(InputEntity event) {
        if (event != null) {
            if (event.getTab() == 1) {
                binding.help.tvRemark.setText(event.getInfo());
            } else if (event.getTab() == 2) {
                binding.help.etOtherHelpCx.setText(event.getInfo());
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(ZiDianEventEntity event) {
        if (event != null) {
            helpCxEntity = event;
            binding.help.tvHelpCx.setText(StrUtils.listToZiDianText(event.getZiDianEntityList()));
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}