package com.linggan.jd831.ui.works.transport.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.linggan.jd831.R;
import com.linggan.jd831.ui.works.transport.model.WarningItem;
import com.linggan.jd831.utils.StrUtils;

import java.util.List;
import java.util.Objects;

public class AbnormalAdapter extends RecyclerView.Adapter<AbnormalAdapter.ViewHolder> {
    private List<WarningItem> items;
    private Context context;
    private String ysglbh;
    private OnItemClickListener onItemClickListener;

    public interface OnItemClickListener {
        void onItemClick(WarningItem item, String ysglbh);
    }

    public AbnormalAdapter(Context context, List<WarningItem> items, String ysglbh) {
        this.context = context;
        this.items = items;
        this.ysglbh = ysglbh;
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_transport_abnormal, parent, false);
        return new ViewHolder(view);
    }

    public void setItems(List<WarningItem> items) {
        this.items = items;
        notifyDataSetChanged();
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        WarningItem item = items.get(position);
        try {
            if (item.getYjyyLxdm() == null) {
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        holder.tvTitle.setText(StrUtils.getDev(item.getYjyyLx(), "-"));

        if (Objects.equals(item.getYjyyLxdm(), "sfdbf")) {
            holder.tvWarningAddress.setText("许可证始发城市：" + StrUtils.getDev(item.getYdcs(), "-"));
            holder.tvWarningTime.setText("发车地址：" + StrUtils.getDev(item.getDz(), "-"));
        }
        if (Objects.equals(item.getYjyyLxdm(), "zdbf")) {
            holder.tvWarningAddress.setText("许可证终点城市：" + StrUtils.getDev(item.getYdcs(), "-"));
            holder.tvWarningTime.setText("送达地址：" + StrUtils.getDev(item.getDz(), "-"));
        }


        if (Objects.equals(item.getYjyyLxdm(), "zl")) {
            holder.tvWarningAddress.setText("开始时间：" + StrUtils.getDev(item.getYjsj(), "-"));
            holder.tvWarningTime.setText("定位地址：" + StrUtils.getDev(item.getDz(), "-"));
        }

        if (Objects.equals(item.getYjyyLxdm(), "gpsxhds")) {
            holder.tvWarningAddress.setText("中断时间：" + StrUtils.getDev(item.getYjsj(), "-"));
            holder.tvWarningTime.setText("定位地址：" + StrUtils.getDev(item.getDz(), "-"));
        }

//        holder.tvWarningTime.setText("预警时间：" + item.getWarningTime());

        holder.itemView.setOnClickListener(v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(item, ysglbh);
            }
        });
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvTitle;
        TextView tvWarningTime;
        TextView tvWarningAddress;

        ViewHolder(View view) {
            super(view);
            tvTitle = view.findViewById(R.id.tv_title);
            tvWarningAddress = view.findViewById(R.id.tv_warning_address);
            tvWarningTime = view.findViewById(R.id.tv_warning_time);
        }
    }
} 