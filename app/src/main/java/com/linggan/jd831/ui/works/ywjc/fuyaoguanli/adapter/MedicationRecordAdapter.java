package com.linggan.jd831.ui.works.ywjc.fuyaoguanli.adapter;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.ui.works.ywjc.fuyaoguanli.YwryDetailActivity;
import com.linggan.jd831.ui.works.ywjc.fuyaoguanli.YwwczlryBean;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XImageUtils;
import com.makeramen.roundedimageview.RoundedImageView;

/**
 * 类  名：服药记录适配器
 * 说  明：显示服药记录列表
 */
public class MedicationRecordAdapter extends IViewHolder {

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_medication_record;
    }

    public class ViewHolder extends XViewHolder<YwwczlryBean> {
        private RoundedImageView ivAvatar;
        private TextView tvName;
        private TextView tvTime;
        private TextView tvIdNumber;
        private TextView tvDose;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            ivAvatar = view.findViewById(R.id.iv_avatar);
            tvName = view.findViewById(R.id.tv_name);
//            tvTime = view.findViewById(R.id.tv_ime);
            tvIdNumber = view.findViewById(R.id.tv_id_number);
            tvDose = view.findViewById(R.id.tv_dose);
        }

        @Override
        protected void onBindData(final YwwczlryBean itemData) {
            if (itemData == null) {
                return;
            }

            tvName.setText(StrUtils.getDev(itemData.getXm(), "-"));
            tvIdNumber.setText(StrUtils.getDev(StrUtils.hideIdCard(itemData.getZjhm()), "-"));
            tvDose.setText(StrUtils.getDev(StrUtils.hidePhoneNum(itemData.getLxdh()), "-"));

            if (itemData.getXp() != null && !itemData.getXp().isEmpty()) {
                XImageUtils.loadNoCache1(mContext, itemData.getXp(), ivAvatar);
            }
            // 点击
            itemView.setOnClickListener(v -> {
                Bundle bundle = new Bundle();
                bundle.putSerializable("data", itemData);
                XIntentUtil.redirectToNextActivity(mContext, YwryDetailActivity.class, bundle);
            });
        }
    }
}