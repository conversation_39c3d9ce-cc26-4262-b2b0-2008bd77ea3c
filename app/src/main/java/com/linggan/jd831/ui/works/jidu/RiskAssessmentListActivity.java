package com.linggan.jd831.ui.works.jidu;

import android.os.Bundle;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.adapter.AllCodeTopHolder;
import com.linggan.jd831.adapter.PingGuNewListHolder;
import com.linggan.jd831.adapter.PingGuTimeTopHolder;
import com.linggan.jd831.bean.PeopleInfoEntity;
import com.linggan.jd831.bean.PingGuListEntity;
import com.linggan.jd831.databinding.ActivityJiafangListBinding;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.XHttpUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 类  名：季度（半年）列表
 * 作  者：zxb
 * 说  明：
 * 时  间：2022/9/4 13:11
 */
public class RiskAssessmentListActivity extends XBaseActivity<ActivityJiafangListBinding> implements XRefreshLayout.PullLoadMoreListener {

    private String peoId, yjztbh, title;
    private int page = 1, totalPage = 0;
    private PeopleInfoEntity peopleListEntity;
    private boolean isShow = true;

    @Override
    protected ActivityJiafangListBinding getViewBinding() {
        return ActivityJiafangListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        peoId = getIntent().getStringExtra("id");
        yjztbh = getIntent().getStringExtra("yjztbh");
        title = getIntent().getStringExtra("title");
        binding.recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
        binding.recycler.getAdapter().bindHolder(new AllCodeTopHolder());
        binding.recycler.getAdapter().bindHolder(new PingGuTimeTopHolder());
        binding.recycler.getAdapter().bindHolder(new PingGuNewListHolder(title));

        binding.recycler.setOnPullLoadMoreListener(this);
        peopleListEntity = (PeopleInfoEntity) getIntent().getSerializableExtra("info");
        if (peopleListEntity != null) {
            binding.recycler.getAdapter().setData(0, peopleListEntity);
        }
        EventBus.getDefault().register(this);
        //湖南-改名字2024-06-04
        if (StrUtils.isHuNan()) {
            setTitle("社会面吸毒人员风险评估");
        }
    }

    @Override
    protected void initListener() {
        binding.fab.setOnClickListener(v -> {
            if (ButtonUtils.isFastClick()) {
                Bundle bundle = new Bundle();
                bundle.putString("id", peoId);
                bundle.putSerializable("info", peopleListEntity);
                XIntentUtil.redirectToNextActivity(this, PingGuNewAddActivity.class, bundle);
            }
        });
    }

    @Override
    protected void getData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.JIDU_LIST);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("xyrbh", peoId);
        objectMap.put("page", page);
//        objectMap.put("yjztbh", yjztbh);
        objectMap.put("rows", XConstantUtils.PAGE_SIZE);
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), isShow, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<XResultPageData<PingGuListEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XResultPageData<PingGuListEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        totalPage = xResultData.getData().getTotalPage();
                        binding.recycler.getAdapter().setData(1, xResultData.getData().getExtra());
                        if (xResultData.getData().getRecords() != null && xResultData.getData().getRecords().size() > 0) {
                            binding.tvNoData.setVisibility(View.GONE);
                            if (page == 1) {
                                binding.recycler.getAdapter().setData(2, xResultData.getData().getRecords());
                            } else {
                                binding.recycler.getAdapter().addDataAll(2, xResultData.getData().getRecords());
                            }
                        } else {
                            if (page == 1) {
                                binding.tvNoData.setVisibility(View.VISIBLE);
                                binding.recycler.getAdapter().setData(2, new ArrayList());
                            }
                        }
                    }
                } else {
                    XToastUtil.showToast(RiskAssessmentListActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                binding.recycler.setPullLoadMoreCompleted();
            }
        });
    }

    @Override
    public void onRefresh() {
        page = 1;
        isShow = false;
        getData();
    }

    @Override
    public boolean onLoadMore() {
        if (page < totalPage) {
            page++;
            isShow = false;
            getData();
            return true;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PingGuListEntity item) {
        if (item != null) {
            page = 1;
            isShow = false;
            getData();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

}
