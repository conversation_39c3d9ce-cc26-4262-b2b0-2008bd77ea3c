package com.linggan.jd831.ui.works.sewage.sampledevice.adapter;


import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lgfzd.base.view.recycle.IViewHolder;
import com.lgfzd.base.view.recycle.XViewHolder;
import com.linggan.jd831.R;
import com.linggan.jd831.bean.CyDeviceDetailEntity;
import com.linggan.jd831.ui.works.sewage.sampledevice.SampleDeviceHistoryDetailActivity;

/**
 * 类  名：宣教列表
 * 作  者：ZXB
 * 说  明：
 * 时  间：2021/6/28 11:51
 * 版  权：LGKJ
 */
public class SampleDeviceHistoryHolder extends IViewHolder {

    @Override
    protected XViewHolder create(View view, RecyclerView.Adapter adapter) {
        return new ViewHolder(view, adapter);
    }

    @Override
    public int getLayout() {
        return R.layout.item_sampling_device_list;
    }

    public class ViewHolder extends XViewHolder<CyDeviceDetailEntity.Cyrw> {

        private TextView mTvTitle;
        private ImageView mIvIcon;
        private LinearLayout mTopStatus;
        private LinearLayout llContent;
        private LinearLayout llTop;
        private View line;

        private String[] labels;
        private int maxLabelLength;

        public ViewHolder(View itemView, RecyclerView.Adapter adapter) {
            super(itemView, adapter);
        }

        @Override
        protected void initView(View view) {
            mTvTitle = view.findViewById(R.id.tv_title);
            mIvIcon = view.findViewById(R.id.iv_icon);
            mTopStatus = view.findViewById(R.id.top_status);
            llContent = view.findViewById(R.id.ll_content);
            llTop = view.findViewById(R.id.ll_top);
            line = view.findViewById(R.id.line);
        }

        @Override
        protected void onBindData(final CyDeviceDetailEntity.Cyrw itemData) {
            setupBasicInfo();
            addLogTypeSpecificData(itemData);
            setItemClickListener(itemData);
        }

        private void setupBasicInfo() {
//            mTvTitle.setText("logType.getText()");
//            mIvIcon.setImageResource(R.mipmap.ic_cydevice_offline);
            llContent.removeAllViews();
            llTop.setVisibility(View.GONE);
            line.setVisibility(View.GONE);
        }

        private void addLogTypeSpecificData(CyDeviceDetailEntity.Cyrw itemData) {
            addSamplingLogData(itemData);
        }

        private void setItemClickListener(final CyDeviceDetailEntity.Cyrw itemData) {
            itemView.setOnClickListener(v -> handleItemClick(itemData));
        }

        private void addSamplingLogData(CyDeviceDetailEntity.Cyrw itemData) {
            String[] labels = {"下发人：", "占用槽位：", "开始采样时间："};
            // xfrXm 如果为null 显示为-
            String xfrXm = TextUtils.isEmpty(itemData.getXfrXm()) ? "-" : itemData.getXfrXm();

            // 修改这里的代码来处理 pzcws 数组
            String pzcws;
            if (itemData.getPzcws() == null || itemData.getPzcws().length == 0) {
                pzcws = "-";
            } else {
                pzcws = TextUtils.join(",", itemData.getPzcws());
            }

            // startTime 如果为null 显示为-
            String startTime = TextUtils.isEmpty(itemData.getStartTime()) ? "-" : itemData.getStartTime();

            String[] values = {xfrXm, pzcws, startTime};
            int maxWidth = getMaxLabelWidth(labels);

            for (int i = 0; i < labels.length; i++) {
                addContentItem(padLabel(labels[i], maxWidth), values[i]);
            }
        }

        private void handleItemClick(CyDeviceDetailEntity.Cyrw itemData) {
            Intent intent = new Intent(mContext, SampleDeviceHistoryDetailActivity.class);
            intent.putExtra("cyrw", itemData);
            mContext.startActivity(intent);
        }

        private int getMaxLabelWidth(String[] labels) {
            int maxWidth = 0;
            for (String label : labels) {
                maxWidth = Math.max(maxWidth, getStringWidth(label));
            }
            return maxWidth;
        }

        private int getStringWidth(String str) {
            int width = 0;
            for (char c : str.toCharArray()) {
                width += (c <= 127) ? 1 : 2;
            }
            return width;
        }

        private String padLabel(String label, int maxWidth) {
            StringBuilder paddedLabel = new StringBuilder(label);
            int currentWidth = getStringWidth(label);
            while (currentWidth < maxWidth) {
                paddedLabel.append('\u3000');
                currentWidth += 2;
            }
            return paddedLabel.toString();
        }

        private void addContentItem(String label, String value) {
            LinearLayout item = createContentItemLayout();
            TextView labelView = createTextView(label, 0xFF8C8C8C);
            TextView valueView = createTextView(value, 0xFF333333);
            item.setPadding(0, 10, 0, 10);
            item.addView(labelView);
            item.addView(valueView);
            llContent.addView(item);
        }

        private LinearLayout createContentItemLayout() {
            LinearLayout item = new LinearLayout(mContext);
            item.setOrientation(LinearLayout.HORIZONTAL);
            item.setLayoutParams(new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT));
            return item;
        }

        private TextView createTextView(String text, int color) {
            TextView textView = new TextView(mContext);
            textView.setTextColor(color);
            textView.setTextSize(14);
            textView.setText(text);
            return textView;
        }
    }

}



