package com.linggan.jd831.ui.works.kaohe;

import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseFragment;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.view.recycle.XRecyclerView;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.KhPingGuYwcListHolder;
import com.linggan.jd831.bean.AppUserListEntity;
import com.linggan.jd831.ui.works.place.PaiChaPlaceAddActivity;
import com.linggan.jd831.utils.ButtonUtils;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.XHttpUtils;

import org.xutils.http.RequestParams;
import org.xutils.view.annotation.ContentView;
import org.xutils.view.annotation.Event;
import org.xutils.view.annotation.ViewInject;

import java.util.ArrayList;

/**
 * 考核--评估-未完成
 */
@ContentView(R.layout.fragment_kh_list)
public class KhPingGuYwcListFragment extends XBaseFragment implements XRefreshLayout.PullLoadMoreListener {

    @ViewInject(R.id.recycler)
    private XRecyclerView recycler;
    @ViewInject(R.id.iv_no_data)
    private ImageView ivNoData;
    private int page = 1, totalPage = 0;
    private boolean isShow = true;
    private String startDate, endDate, xzqhdm, type;

    public static KhPingGuYwcListFragment newInstance(String startDate, String endDate, String xzqhdm, String type) {
        KhPingGuYwcListFragment benefitTabFragment = new KhPingGuYwcListFragment();
        Bundle bundle = new Bundle();
        bundle.putString("kssj", startDate);
        bundle.putString("jssj", endDate);
        bundle.putString("xzqhdm", xzqhdm);
        bundle.putString("type", type);
        benefitTabFragment.setArguments(bundle);
        return benefitTabFragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle bundle = getArguments();
        if (bundle != null) {
            startDate = bundle.getString("kssj");
            endDate = bundle.getString("jssj");
            xzqhdm = bundle.getString("xzqhdm");
            type = bundle.getString("type");
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(getActivity()));
        recycler.getAdapter().bindHolder(new KhPingGuYwcListHolder(type));
        recycler.setOnPullLoadMoreListener(this);
    }

    @Override
    protected void lazyLoad() {

        String canShuString = "startDate=" + startDate + Uri.encode(" 00:00:00", "utf-8") + "&endDate=" + endDate + Uri.encode(" 23:59:59") + "&page=" + page + "&rows=" + XConstantUtils.PAGE_SIZE + "&xzqhdm=" + xzqhdm + "&sfwc=true";
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), canShuString);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.KH_PINGGU_LV_INFO_LIST + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        XHttpUtils.get(getActivity(), requestParams, DialogUtils.showLoadDialog(getActivity(), ""), isShow, false, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<XResultPageData<AppUserListEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XResultPageData<AppUserListEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        totalPage = xResultData.getData().getTotalPage();
                        if (xResultData.getData().getRecords() != null && xResultData.getData().getRecords().size() > 0) {
                            ivNoData.setVisibility(View.GONE);
                            if (page == 1) {
                                recycler.getAdapter().setData(0, xResultData.getData().getRecords());
                            } else {
                                recycler.getAdapter().addDataAll(0, xResultData.getData().getRecords());
                            }
                        } else {
                            if (page == 1) {
                                ivNoData.setVisibility(View.VISIBLE);
                                recycler.getAdapter().setData(0, new ArrayList());
                            }
                        }
                    } else {
                        if (page == 1) {
                            ivNoData.setVisibility(View.VISIBLE);
                            recycler.getAdapter().setData(0, new ArrayList());
                        }
                    }
                } else {
                    if (page == 1) {
                        ivNoData.setVisibility(View.VISIBLE);
                        recycler.getAdapter().setData(0, new ArrayList());
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                recycler.setPullLoadMoreCompleted();
            }
        });
    }


    @Event(type = View.OnClickListener.class, value = {R.id.fab})
    private void onClick(View view) {
        if (view.getId() == R.id.fab) {
            if (ButtonUtils.isFastClick()) {
                XIntentUtil.redirectToNextActivity(getActivity(), PaiChaPlaceAddActivity.class);
            }
        }
    }

    @Override
    public void onRefresh() {
        page = 1;
        isShow = false;
        lazyLoad();
    }

    @Override
    public boolean onLoadMore() {
        if (page < totalPage) {
            page++;
            isShow = false;
            lazyLoad();
            return true;
        }
        return false;
    }
}
