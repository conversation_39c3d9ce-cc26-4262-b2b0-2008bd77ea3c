package com.linggan.jd831.ui.works.car;

import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.base.XBaseActivity;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.net.XResultPageData;
import com.lgfzd.base.utils.XAppUtil;
import com.lgfzd.base.utils.XIntentUtil;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.lgfzd.base.utils.XToastUtil;
import com.lgfzd.base.view.recycle.XRefreshLayout;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.R;
import com.linggan.jd831.adapter.KaUrineListHolder;
import com.linggan.jd831.bean.KkJcListEntity;
import com.linggan.jd831.bean.LoginEntity;
import com.linggan.jd831.bean.PeoStatusEntity;
import com.linggan.jd831.databinding.ActivityKaUrineListBinding;
import com.linggan.jd831.utils.DialogUtils;
import com.linggan.jd831.utils.FactoryUtils;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.StrUtils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XHttpUtils;
import com.linggan.jd831.widget.AreaPickerView3Dialog;
import com.linggan.jd831.widget.AreaPickerViewDialog;
import com.linggan.jd831.widget.BaseStringDialog;
import com.linggan.jd831.widget.CodeNameDialog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.xutils.http.RequestParams;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 类  名：卡口检测页面
 * 作  者：LGKJ
 * 版  权：凌感科技
 * 时  间：2024/4/8 13:11
 */
public class KaUrineListActivity extends XBaseActivity<ActivityKaUrineListBinding> implements XRefreshLayout.PullLoadMoreListener {

    private int page = 1;
    private boolean isShow = true, isMore = true;
    private String qhdm = "", kkBh, jg;

    @Override
    protected ActivityKaUrineListBinding getViewBinding() {
        return ActivityKaUrineListBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        binding.recycler.getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
        binding.recycler.getAdapter().bindHolder(new KaUrineListHolder());
        binding.recycler.setOnPullLoadMoreListener(this);
        binding.top.etSearch.setHint(getString(R.string.jcry_name));
        binding.top.btBack.setOnClickListener(v -> {
            finish();
        });
        EventBus.getDefault().register(this);
        jg = getIntent().getStringExtra("jg");
        //地区展示
        if (UserInfoUtils.getUserInfo() != null) {
            binding.tvArea.setText(UserInfoUtils.getUserInfo().getXzqhmc());
            qhdm = UserInfoUtils.getUserInfo().getYhXzqhdm();
        }
        if (!TextUtils.isEmpty(jg) && jg.equals("1")) {
            binding.tvStatus.setText("阳性");
        }
    }

    @Override
    protected void initListener() {
        binding.top.etSearch.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_SEND || (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                switch (event.getAction()) {
                    case KeyEvent.ACTION_UP:
                        //发送请求
                        page = 1;
                        isShow = true;
                        getData();
                        XAppUtil.closeSoftInput(KaUrineListActivity.this);
                        return true;
                    default:
                        return true;
                }
            }
            return false;
        });
        binding.linType.setOnClickListener(v -> {
            //地区选择
            AreaPickerView3Dialog areaPickerView = new AreaPickerView3Dialog(this, UserInfoUtils.getUserInfo().getYhXzqhdm(), "1");
            areaPickerView.show();
            areaPickerView.setAreaPickerViewCallback((areaName, ids) -> {
                qhdm = ids.get(ids.size() - 1);
                binding.tvArea.setText(areaName.get(areaName.size() - 1));
                page = 1;
                isShow = true;
                getData();
            });

        });
        binding.linLvl.setOnClickListener(v -> {
            //检测卡口
            if (TextUtils.isEmpty(qhdm)) {
                XToastUtil.showToast(this, "请先选择地区");
                return;
            }
            FactoryUtils.getKaKouFromXzQh(this, qhdm, arrayList -> {
                ArrayList<String> list = new ArrayList<>();
                for (int i = 0; i < arrayList.size(); i++) {
                    list.add(arrayList.get(i).getKkMc());
                }
                BaseStringDialog dianDialog = new BaseStringDialog(this, list);
                dianDialog.setOnClickDataListener(() -> {
                    binding.tvLvl.setText(dianDialog.getWork());
                    kkBh = arrayList.get(dianDialog.getIndex()).getBh();
                    isShow = true;
                    getData();
                });
                dianDialog.show();
            });
        });
        binding.linStatus.setOnClickListener(v -> {
            //检测结果
            CodeNameDialog dianDialog = new CodeNameDialog(this, StrUtils.getJcJgList());
            dianDialog.setOnClickDataListener(() -> {
                binding.tvStatus.setText(dianDialog.getCode().getName());
                jg = dianDialog.getCode().getCode();
                isShow = true;
                getData();
            });
            dianDialog.show();
        });
        binding.fab.setOnClickListener(v -> {
            XIntentUtil.redirectToNextActivity(this, KaUrineSyAddActivity.class);
        });
    }

    @Override
    protected void getData() {
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.CAR_KK_jC_LIST);
        requestParams.addHeader("Content-Type", "application/json");
        requestParams.addHeader("Origin-Content-Type", "application/json");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("page", page);
        objectMap.put("rows", XConstantUtils.PAGE_SIZE);
        if (!TextUtils.isEmpty(binding.top.etSearch.getText().toString())) {
            objectMap.put("jsyXm", binding.top.etSearch.getText().toString());
        }
        if (!TextUtils.isEmpty(qhdm)) {
            objectMap.put("xzqhdm", qhdm);
        }
        if (!TextUtils.isEmpty(kkBh)) {
            objectMap.put("kkBh", kkBh);
        }
        if (!TextUtils.isEmpty(jg)) {
            objectMap.put("jg", jg);
        }
        objectMap.put("lx", "1");
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), new Gson().toJson(objectMap));
        XHttpUtils.postJson(this, requestParams, jiaMiString, DialogUtils.showLoadDialog(this, ""), isShow, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<XResultPageData<KkJcListEntity>> xResultData = new Gson().fromJson(result, new TypeToken<XResultData<XResultPageData<KkJcListEntity>>>() {
                }.getType());
                if (xResultData.getStatus() == 0) {
                    if (xResultData.getData() != null) {
                        if (xResultData.getData().getRecords() != null && xResultData.getData().getRecords().size() > 0) {
                            binding.ivNoData.setVisibility(View.GONE);
                            isMore = xResultData.getData().getRecords().size() == XConstantUtils.PAGE_SIZE;
                            if (page == 1) {
                                binding.recycler.getAdapter().setData(0, xResultData.getData().getRecords());
                            } else {
                                binding.recycler.getAdapter().addDataAll(0, xResultData.getData().getRecords());
                            }
                        } else {
                            if (page == 1) {
                                binding.recycler.getAdapter().setData(0, new ArrayList());
                                binding.ivNoData.setVisibility(View.VISIBLE);
                            }
                        }
                    } else {
                        if (page == 1) {
                            binding.recycler.getAdapter().setData(0, new ArrayList());
                            binding.ivNoData.setVisibility(View.VISIBLE);
                        }
                    }
                } else {
                    XToastUtil.showToast(KaUrineListActivity.this, xResultData.getErrorInfo());
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
            }

            @Override
            public void onFinished() {
                binding.recycler.setPullLoadMoreCompleted();
            }
        });
    }

    @Override
    public void onRefresh() {
        page = 1;
        isShow = false;
        getData();
    }

    @Override
    public boolean onLoadMore() {
        if (isMore) {
            isShow = false;
            page++;
            getData();
            return true;
        } else {
            return false;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PeoStatusEntity event) {
        if (event != null) {
            page = 1;
            isShow = false;
            getData();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
