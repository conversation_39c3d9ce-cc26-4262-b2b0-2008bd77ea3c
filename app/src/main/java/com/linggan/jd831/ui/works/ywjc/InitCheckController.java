package com.linggan.jd831.ui.works.ywjc;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lgfzd.base.XConstantUtils;
import com.lgfzd.base.net.XHttpResponseCallBack;
import com.lgfzd.base.net.XResultData;
import com.lgfzd.base.utils.XShareCacheUtils;
import com.linggan.jd831.ApiHostUtils;
import com.linggan.jd831.ApiUrlsUtils;
import com.linggan.jd831.bean.InitCheckResponse;
import com.linggan.jd831.utils.SM2Utils;
import com.linggan.jd831.utils.UserInfoUtils;
import com.linggan.jd831.utils.XHttpUtils;

import org.xutils.http.RequestParams;

/**
 * 机构信息和库存初始化校验控制器
 */
public class InitCheckController {

    /**
     * 检查机构信息和库存初始化状态
     *
     * @param callback 回调接口
     */
    public static void checkInitStatus(final InitCheckCallback callback) {
        String dwdm = UserInfoUtils.getUserInfo().getYhDwdm();
        String jiaMiString = SM2Utils.encrypt(XShareCacheUtils.getInstance().getString(XConstantUtils.PARAMS_KEY), "dwdm=" + dwdm);
        RequestParams requestParams = new RequestParams(ApiHostUtils.getHostUrl() + ApiUrlsUtils.FYJL_JY + "?query=" + jiaMiString);
        requestParams.addHeader("Content-Type", "application/x-www-form-urlencoded");
        requestParams.addHeader("Origin-Content-Type", "application/json");

        XHttpUtils.get(null, requestParams, null, new XHttpResponseCallBack() {
            @Override
            public void onSuccess(String result) {
                XResultData<InitCheckResponse> xResultData = new Gson().fromJson(result,
                        new TypeToken<XResultData<InitCheckResponse>>() {
                        }.getType());

                if (xResultData.getStatus() == 0 && xResultData.getData() != null) {
                    if (callback != null) {
                        callback.onSuccess(xResultData.getData());
                    }
                } else {
                    if (callback != null) {
                        callback.onFailed(xResultData.getStatus(), xResultData.getErrorInfo());
                    }
                }
            }

            @Override
            public void onFailed(int code, String failedMsg) {
                if (callback != null) {
                    callback.onFailed(code, failedMsg);
                }
            }

            @Override
            public void onFinished() {
                if (callback != null) {
                    callback.onFinished();
                }
            }
        });
    }

    /**
     * 初始化校验回调接口
     */
    public interface InitCheckCallback {
        void onSuccess(InitCheckResponse response);

        void onFailed(int code, String failedMsg);

        void onFinished();
    }
} 